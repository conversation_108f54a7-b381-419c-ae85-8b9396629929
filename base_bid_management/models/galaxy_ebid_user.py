import logging
import uuid
from odoo import fields, models, api, _
from odoo.exceptions import UserError
from odoo.osv import expression

_logger = logging.getLogger(__name__)


class GalaxyEbidUser(models.Model):
    _name = 'galaxy.ebid.user'
    _description = 'Galaxy Ebid User'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _rec_name = 'name'

    # approve_status = fields.Integer('Approval Status', readonly=True)
    name = fields.Char(readonly=True, compute="_compute_get_name")
    active = fields.Boolean(default=True)
    status = fields.Selection(
        [('active', 'Active'), ('inactive', 'Inactive')], default='active')
    communicate_person = fields.Char(readonly=True)
    company_address = fields.Char(readonly=True)
    company_name = fields.Char(readonly=True)
    # ebid_create_by = fields.Char('Created By', readonly=True)
    # ebid_create_date = fields.Datetime('Created Date', readonly=True)
    country_id = fields.Many2one(comodel_name='res.country',
                                 help="Country for which this tag is available, when applied on taxes.")
    currency = fields.Selection(
        [('0', 'Not Set'), ('1', 'HKD'), ('2', 'USD')], readonly=True, default='0')
    ebid_user_id = fields.Char('User ID', readonly=True)
    ebid_last_login_time = fields.Datetime('Last Login Time', readonly=True)
    note = fields.Text()
    tel = fields.Char('Telephone', readonly=True)
    # update_by = fields.Char('Updated By', readonly=True)
    wx = fields.Char('WeChat', readonly=True)
    odoo_customer = fields.Many2one('res.partner',
                                    domain=[('customer_rank', '>', 0), ('ref', '=', False)])
    original_json = fields.Text()
    process_flag = fields.Boolean(default=False)
    state = fields.Selection([('draft', 'Draft'), ('audited', 'Audited'), ('cancel', 'Cancel')],
                             default='draft',
                             tracking=True)
    region = fields.Char()

    @api.depends('communicate_person', 'odoo_customer')
    def _compute_get_name(self):
        for rec in self:
            if rec.communicate_person and rec.odoo_customer:
                rec.name = rec.odoo_customer.bid_user_id + \
                    '(' + rec.communicate_person + ')'
            else:
                rec.name = rec.communicate_person

    def get_customer_info(self):
        """获取客户信息"""
        result_val = {}
        result_val['name'] = self.communicate_person
        result_val['customer_rank'] = 1
        result_val['phone'] = self.tel
        result_val['email'] = self.tel
        result_val['wechat_id'] = self.wx
        result_val['company_type'] = 'company'
        result_val['street'] = self.company_address
        result_val['country_id'] = self.country_id.id
        result_val['company_name'] = self.company_name
        result_val['is_company'] = True
        result_val['status'] = self.status
        result_val['active'] = self.active
        result_val['ref'] = self.ebid_user_id
        pricelist = self.env['customer.data'].get_customer_currency(
            int(self.currency))
        if pricelist:
            result_val['property_product_pricelist'] = pricelist.id
        return result_val

    def action_create_odoo_customer(self):
        """创建odoo客户"""
        partner_sudo = self.env['res.partner'].sudo()
        customer_id = self.ebid_user_id
        partner_id = partner_sudo.with_context(active_test=False).search(
            [('ref', '=', customer_id)], limit=1)
        if self.currency == '0':
            raise UserError(_('Please select currency!'))
        if partner_id:
            raise UserError(_('Customer %s already exists!', partner_id.name))
        result_val = self.get_customer_info()
        new_customer = partner_sudo.create(result_val)
        self.odoo_customer = new_customer.id

    def action_confirm(self):
        """审核用户"""
        if not self.odoo_customer:
            raise UserError(_('Please create odoo customer first!'))
        if self.currency == '0':
            raise UserError(_('Please select currency!'))
        if self.odoo_customer.ref and self.odoo_customer.ref != self.ebid_user_id:
            raise UserError(
                _('Selected odoo customer is not the same as ebid customer!'))
        partner_sudo = self.env['res.partner'].sudo()
        check_cur_customer = partner_sudo.with_context(
            active_test=False).search([('ref', '=', self.ebid_user_id)], limit=1)
        if check_cur_customer and check_cur_customer.id != self.odoo_customer.id:
            raise UserError(
                _('Customer %s already exists!', check_cur_customer.name))
        result_val = self.get_customer_info()
        if self.odoo_customer.property_product_pricelist.id != result_val['property_product_pricelist']:
            raise UserError(
                _('Selected odoo customer currency is not same as ebid customer!'))
        self.odoo_customer.sudo().write(result_val)
        self.state = 'audited'
        # 发送消息到mq,告知ebid用户和odoo客户绑定成功了
        ebid_user_audit = self.env['rabbitmq.server'].search(
            [('name', '=', 'ebid_user_audit')])
        ebid_user_audit.send(
            [{'req_id': str(uuid.uuid1()), 'audit': True, 'odoo_user_id': self.odoo_customer.bid_user_id, 'user_id': self.ebid_user_id}])

    def init_sync_user(self):
        """同步已审核用户到ebid系统"""
        users = self.with_context(active_test=False).search(
            [('state', '=', 'audited')])
        sync_users = [{'req_id': str(uuid.uuid1(
        )), 'audit': True, 'odoo_user_id': user.odoo_customer.bid_user_id, 'user_id': user.ebid_user_id} for user in users]
        ebid_user_audit = self.env['rabbitmq.server'].search(
            [('name', '=', 'ebid_user_audit')])
        ebid_user_audit.send(sync_users)

    def audit_user(self):
        """绑定已存在的客户"""
        users = self.with_context(active_test=False).search(
            [('state', '=', 'draft')], limit=100)
        currency_map = {"1": 'HKD', '2': 'USD'}
        for user in users:
            if user.ebid_user_id:
                odoo_custmer_id = self.with_context(active_test=False).env['res.partner'].search([
                    ('ref', '=', user.ebid_user_id)])
                if odoo_custmer_id:
                    if len(odoo_custmer_id) == 1:
                        if currency_map.get(user.currency) == odoo_custmer_id.property_product_pricelist.currency_id.name:
                            user.odoo_customer = odoo_custmer_id.id
                            user.state = 'audited'
                            _logger.info('ebid_user: %s updated',
                                         user.ebid_user_id)
                        else:
                            _logger.error(
                                'ebid user %s  and odoo customer %d have the different currency', user.ebid_user_id, odoo_custmer_id.id)
                    else:
                        _logger.error(
                            'more than one customer has the same user_id %s', user.ebid_user_id)
                else:
                    _logger.info(' %s not generate odoo customer',
                                 user.ebid_user_id)

    def action_cancel(self):
        """将状态设置为cancel"""
        self.state = 'cancel'

    def action_draft(self):
        """将状态设置为draft"""
        self.state = 'draft'

    @api.model
    def create(self, vals):
        """重写create方法"""
        result = super(GalaxyEbidUser, self).create(vals)
        ebid_responsible_user = int(self.env.ref(
            'quotation_bid.galaxy_ebid_user_create_responsible_user').value)
        if ebid_responsible_user:
            responsible_user = self.env['res.users'].browse(
                ebid_responsible_user)
        for rec in result:
            for _ in result:
                if responsible_user:
                    body = f"New Ebid User {rec.communicate_person} has been created."
                    self._send_sys_message(responsible_user, body)
        return result

    def _send_sys_message(self, user, message):
        """通过OdooBot给指定用户发送消息
        :param user: 'res.users' 对象
        :param message: str, 消息内容
        """
        # 获取OdooBot的partner_id
        odoobot_id = self.env['ir.model.data'].xmlid_to_res_id(
            "base.partner_root")
        # 获取OdooBot和用户的聊天频道
        channel_partners = user.mapped('partner_id').ids
        channel = self.env['mail.channel'].sudo().search([('channel_type', '=', 'chat'),
                                                          ('channel_partner_ids',
                                                           'in', channel_partners),
                                                          ('channel_partner_ids', 'in', [odoobot_id])],
                                                         limit=1)
        # 不存在则初始化聊天频道
        if not channel:
            user.odoobot_state = 'not_initialized'
            channel = self.env['mail.channel'].with_user(user).init_odoobot()
        # 发送消息
        _logger.info("Sending message to user %s", user.name)
        _logger.info("Message: %s", message)
        _logger.info("OdooBot: %s", odoobot_id)
        channel.sudo().message_post(
            body=message,
            author_id=odoobot_id,
            message_type="comment",
            subtype_xmlid="mail.mt_comment",
        )

    def _name_search(self, name, args=None, operator='ilike', limit=100, name_get_uid=None):
        if self._context.get('display_bid_user_id'):
            if operator == 'ilike' and not (name or '').strip():
                domain = []
            else:
                domain = ['|', '|', ('ebid_user_id', operator, name),
                          ('communicate_person', operator, name), ('tel', operator, name)]
            return self._search(expression.AND([domain, args]), limit=limit, access_rights_uid=name_get_uid)
        else:
            return super(GalaxyEbidUser, self)._name_search(name, args, operator, limit, name_get_uid)
