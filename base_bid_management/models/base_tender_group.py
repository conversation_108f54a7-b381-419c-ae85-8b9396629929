# -*- coding: utf-8 -*-
import json
import logging
import odoo
from datetime import datetime
# 2 : imports of odoo
from odoo import api, fields, models, _
# 3 : imports from odoo modules
from odoo.exceptions import UserError

_logger = logging.getLogger(__name__)


class tenderGroup(models.Model):
    _name = "tender.group"
    _description = "Tender Group"
    _order = "id desc"
    _rec_name = 'vendor_group_name'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    name = fields.Char(string='Number', help="Tender Group Number, format TGXXXXX")
    vendor_id = fields.Many2one('res.partner', string="Vendor", index=True)
    vendor_group_name = fields.Char('Vendor Group', index=True)
    vendor_group_no = fields.Char(index=True)
    vendor_reference = fields.Char(string='Partner refernece', help="對應供應商單號等信息，可編輯，用於發票等單據對應")
    tender_type = fields.Selection(selection=[("proxy_bid", "Proxy Bid"), ("entrust_auction", "Entrust Auction")],
                                   string="Tender Type",
                                   help="Proxy Bid 代理投標 Entrust Auction 委託拍賣")
    purchase_currency_id = fields.Many2one(related='vendor_id.property_purchase_currency_id', string="Supplier Currency")
    exchange_rate = fields.Float(string="Exchange Rate")
    note = fields.Text(string="Note")
    lots_ids = fields.One2many('tender.lot', inverse_name="tender_group_id", string="Lots")
    lots_qty = fields.Integer(string="Lots Qty", help="Lots qty in a tender group", compute="_compute_get_lots_qty", store=True)
    product_qty = fields.Integer(string="Product Qty", compute="_compute_get_product_qty", store=True)
    estimated_delivery_time = fields.Datetime(string="Estimated Delivery Time")
    inspection_deadline = fields.Datetime(string="Inspection Deadline")
    inspection_address = fields.Many2many('stock.location', string="Inspection address")
    state = fields.Selection(
        selection=[
            ("draft", "Draft"),
            ("waiting", "Waiting"),
            ("complete", "Complete"),
            ],
        default='waiting', string="State", tracking=True,
    )
    bid_deadline = fields.Datetime(string="Bid Deadline")
    bid_type = fields.Selection(selection=[
        ("lot", "By Lot"),
        ("list", "By List"),
    ],
                                string="Bid Type",
                                help="出价方式 默认从模板获取")
    # auction_type = fields.Selection(string="Auction Type", related="")
    @api.depends('lots_ids')
    def _compute_get_lots_qty(self):
        for rec in self:
            rec.lots_qty = len(rec.lots_ids)
    
    @api.depends('lots_ids.lot_list_ids')
    def _compute_get_product_qty(self):
        for rec in self:
            rec.product_qty = sum(rec.lots_ids.lot_list_ids.mapped('product_qty'))
        
