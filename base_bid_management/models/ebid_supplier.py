# -*- coding: utf-8 -*-

import logging
from odoo import models, fields
_logger = logging.getLogger(__name__)


# pylint: disable=W0703
class EbidSupplier(models.Model):
    '''
      设置投标公司名称 名称需唯一 忽略大小写，忽略首尾空格
    '''
    _name = 'ebid.supplier'
    _description = 'EBID Supplier'

    name = fields.citext(help='Standard Name', required=True)
    alias_name = fields.citext(help='Alias Name')
    vendor_ref_code = fields.citext(
        'EBID Vendor Code', help='Old EBID Supplier Code')
    logo = fields.Image(max_width=500,
                        max_height=500, store=True, required=False)
    active = fields.Boolean(default=True,
                            help="If unchecked, it will allow you to hide the bid standard without removing it.")
    parent_id = fields.Many2one('res.partner', 'Parent')
    remark = fields.citext()
    is_new = fields.Boolean()
    deduction = fields.Boolean(default=True, string='Offer Deducts Credit', help='whether deduct customer credit')

    _sql_constraints = [('bid_ebid_supplier_uniq',
                         'unique(name,vendor_ref_code)', 'Ebid Supplier name must be unique')]

    def action_change_approve_status(self, is_approve):
        """action_change_approve_status"""
        self.is_allow_bid = is_approve

    def sync_ebid_supplier_information(self):
        """同步supplier信息"""
        try:
            self.env['customer.data'].setup_pg_foreign_table_for_mysql()
            query = """
                    select
                        a.CODE,
                        a.NAME,
                        a.ALIAS_NAME,
                        a.REMARK,
                        a.ENABLED,
                        a.IMAGES
                    from mysql_galaxy_proxybid_pxy_supplier a
            """
            self.env.cr.execute(query)
            ebid_supplier_info_rec = self.env.cr.dictfetchall()
            for rec in ebid_supplier_info_rec:
                vendor_ref_code = rec['code']
                ebid_supplier_rec = self.with_context(active_test=False).search(
                    [('vendor_ref_code', '=', vendor_ref_code)])
                ebid_supplier_info_dict = {}
                ebid_supplier_info_dict['name'] = rec['name']
                ebid_supplier_info_dict['alias_name'] = rec['alias_name']
                ebid_supplier_info_dict['vendor_ref_code'] = rec['code']
                ebid_supplier_info_dict['remark'] = rec['remark']
                ebid_supplier_info_dict['active'] = rec['enabled'] == '1'
                if len(ebid_supplier_rec) >= 1:
                    ebid_supplier_rec.write(ebid_supplier_info_dict)
                else:
                    self.create(ebid_supplier_info_dict)
            _logger.info('sync EBID supplier information sucessfully')
        except Exception as e:
            _logger.info('sync EBID supplier information  errpr')
            _logger.info(str(e))
