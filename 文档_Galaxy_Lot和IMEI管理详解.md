# Galaxy Lot和IMEI管理详解

## 概述

本文档详细说明Galaxy ERP系统中的核心库存管理表`galaxy.lot`和IMEI管理机制，帮助理解货物批次管理和设备追踪的完整流程。

## 目录

- [1. Galaxy Lot (货物批次管理)](#1-galaxy-lot-货物批次管理)
- [2. IMEI管理系统](#2-imei管理系统)
- [3. 表结构关系](#3-表结构关系)
- [4. 业务流程](#4-业务流程)
- [5. 实际应用场景](#5-实际应用场景)

## 1. Galaxy Lot (货物批次管理)

### 1.1 核心定义

`galaxy.lot`表是Galaxy ERP系统中的**货物批次管理表**，用于管理仓库中的货物批次（Glot）。

### 1.2 主要字段说明

#### **A. 批次标识字段**
```python
class GalaxyLot(models.Model):
    _name = 'galaxy.lot'
    _description = 'Galaxy Lot'

    # 批次标识
    name = fields.Char(string='Glot', index=True, required=True)  # 如: "G240115-1"
    gnumber = fields.citext(string='GNumber', compute="_compute_get_gnumber", store=True)  # 如: "G240115"
    lot = fields.Integer(string='Lot', index=True)  # 如: 1
    inbound_date = fields.Date(string='Inbound Date')  # 入库日期
```

**字段说明：**
- **name (Glot)**：唯一的批次标识符，格式通常为 `G + 日期 + 序号`
  - 示例：`G240115-1` 表示2024年1月15日的第1个批次
- **gnumber (GNumber)**：从Glot中提取的G号码部分
- **lot**：批次的序列号，从Glot名称中解析得出

#### **B. 客户关联字段**
```python
    # 客户管理
    customer_id = fields.Many2many('res.partner', string='Customer', 
                                  domain="[('customer_rank','>=',1)]")
    customer_uid = fields.Char(compute="_compute_get_customer_uid", 
                              store=True, index=True, string="Customer ID")
```

**特点：**
- 🔄 **多客户支持**：一个批次可以分配给多个客户
- 🆔 **客户ID追踪**：记录客户的投标用户ID，用于系统集成

#### **C. 箱号管理字段**
```python
    # 箱号关联
    carton_ids = fields.Many2many('galaxy.carton', string='Carton')
    carton_count = fields.Integer(string="carton number", compute="_compute_carton_number", store=True)
    
    # 发货状态统计
    un_delivered_carton_ids = fields.Many2many('galaxy.carton', 
                                              relation='r2_un_delivered_carton_rel',
                                              string='Undelivered Cartons')
    delivered_carton_ids = fields.Many2many('galaxy.carton', 
                                           relation='r2_delivered_carton_rel', 
                                           string='Delivered Cartons')
```

**功能：**
- 📦 **箱号关联**：管理批次下的所有箱号
- 📊 **状态统计**：区分已发货和未发货的箱号
- 🔢 **数量统计**：自动计算各种状态的箱数

#### **D. 发货管理字段**
```python
    # 发货管理
    delivery_ids = fields.Many2many('galaxy.delivery', string="Delivery Orders")
    is_availiable = fields.Boolean('Is Availiable', compute='_compute_get_availiable', store=True)
    last_delivery_date = fields.Datetime('Last Delivery Date', compute="_compute_get_last_delivery_date", store=True)
```

#### **E. 库存监控字段**
```python
    # 库存监控
    is_invetory_overdue = fields.Boolean(compute="_compute_is_invetory_overdue")
    total_product_qty = fields.Integer('Total Product Qty', compute="_compute_get_total_product_qty", store=True)
    total_un_delivered_product_qty = fields.Integer('Total Undelivered Product Qty')
```

### 1.3 核心业务方法

#### **A. Glot编号解析**
```python
@api.depends('name')
def _compute_get_gnumber(self):
    """从Glot名称中解析GNumber和Lot序号"""
    for rec in self:
        g_array = replace_special_characters(rec.name).split('-')
        rec.gnumber = g_array[0]  # 提取G号码部分
        if len(g_array) > 1:
            if not g_array[1].isnumeric():
                rec.lot = -1  # 无效的lot
            else:
                rec.lot = int(g_array[1])  # 提取lot序号
        else:
            rec.lot = -1
```

#### **B. 库存超期检查**
```python
@api.depends('carton_ids')
def _compute_is_invetory_overdue(self):
    """检查批次是否有超期库存"""
    for rec in self:
        rec.is_invetory_overdue = False
        if any(item.is_invetory_overdue for item in rec.carton_ids):
            rec.is_invetory_overdue = True
```

#### **C. 入库时间更新**
```python
def update_inbound_date(self):
    """更新入库时间"""
    for rec in self:
        if not rec.inbound_date:
            rec.inbound_date = min(rec.carton_ids.mapped('create_date'))
```

## 2. IMEI管理系统

### 2.1 IMEI概念说明

**IMEI (International Mobile Equipment Identity)** 是国际移动设备识别码，是移动设备的唯一标识符。

#### **IMEI的特点：**
- 📱 **设备唯一性**：每台移动设备都有唯一的IMEI号
- 🔢 **15位数字**：通常由15位数字组成
- 🌍 **国际标准**：全球统一的识别标准
- 🔒 **防盗追踪**：用于设备防盗和追踪
- 📋 **合规要求**：进出口贸易中的重要信息

#### **IMEI的作用：**
1. **设备识别**：唯一标识每台设备
2. **库存管理**：精确到单台设备的库存追踪
3. **质量追溯**：问题设备的快速定位
4. **合规管理**：满足海关和监管要求
5. **防盗保护**：丢失设备的追踪和锁定

### 2.2 Galaxy系统中的IMEI管理

#### **A. galaxy.imei.glot表**
```python
class GalaxyImeiGlot(models.Model):
    _name = 'galaxy.imei.glot'
    _description = 'Galaxy IMEI Glot Management'
    
    glot_id = fields.Many2one('galaxy.lot', string='Glot', required=True)
    carton_id = fields.Many2one('galaxy.carton', string='Carton')
    imei = fields.Char(string='IMEI', required=True, index=True)
    serial_number = fields.Char(string='Serial Number', index=True)
    
    # 设备信息
    brand = fields.Char(string='Brand')  # 品牌
    model = fields.Char(string='Model')  # 型号
    color = fields.Char(string='Color')  # 颜色
    capacity = fields.Char(string='Capacity')  # 容量
    
    # 状态管理
    state = fields.Selection([
        ('in_stock', 'In Stock'),
        ('picked', 'Picked'),
        ('delivered', 'Delivered'),
        ('returned', 'Returned')
    ], string='Status', default='in_stock')
```

#### **B. IMEI与批次的关系**
```python
# 在galaxy.lot中
imgi_glot_ids = fields.One2many('galaxy.imei.glot', 'glot_id', string='Imei Glots')

# 在galaxy.carton中  
imei_glot_ids = fields.One2many('galaxy.imei.glot', 'carton_id')
```

### 2.3 IMEI业务流程

#### **A. IMEI录入流程**
1. **货物入库** → 扫描或录入每台设备的IMEI
2. **批次关联** → 将IMEI关联到对应的Glot
3. **箱号分配** → 将IMEI分配到具体的箱号
4. **状态更新** → 更新IMEI的库存状态

#### **B. IMEI追踪流程**
1. **拣货** → 扫描IMEI，更新为"已拣货"状态
2. **打包** → 确认IMEI装箱信息
3. **发货** → 更新为"已发货"状态
4. **交付** → 最终交付给客户

## 3. 表结构关系

### 3.1 核心表关系图

```mermaid
erDiagram
    galaxy_lot ||--o{ galaxy_carton : "一对多"
    galaxy_lot ||--o{ galaxy_imei_glot : "一对多"
    galaxy_carton ||--o{ galaxy_imei_glot : "一对多"
    galaxy_lot ||--o{ galaxy_delivery : "多对多"
    galaxy_carton ||--|| galaxy_pallet : "多对一"
    
    res_partner ||--o{ galaxy_lot : "多对多"
    galaxy_delivery ||--o{ galaxy_pick : "一对多"
    
    galaxy_lot {
        int id PK
        string name "Glot编号"
        string gnumber "G号码"
        int lot "批次序号"
        date inbound_date "入库日期"
        text note "备注"
        boolean is_availiable "是否可用"
        boolean is_invetory_overdue "是否超期"
    }
    
    galaxy_carton {
        int id PK
        string name "箱号"
        int pallet_id FK "托盘ID"
        string status "状态"
        int product_qty "产品数量"
        string vendor_carton "供应商箱号"
        datetime create_date "入库时间"
    }
    
    galaxy_imei_glot {
        int id PK
        int glot_id FK "批次ID"
        int carton_id FK "箱号ID"
        string imei "IMEI号码"
        string serial_number "序列号"
        string brand "品牌"
        string model "型号"
        string state "状态"
    }
    
    galaxy_delivery {
        int id PK
        string name "发货单号"
        datetime delivery_date "发货日期"
        string state "状态"
    }
    
    res_partner {
        int id PK
        string name "客户名称"
        string bid_user_id "投标用户ID"
        int customer_rank "客户等级"
    }
```

### 3.2 数据流转关系

```mermaid
flowchart TD
    A[货物到达] --> B[创建galaxy.lot]
    B --> C[分配Glot编号]
    C --> D[创建galaxy.carton]
    D --> E[录入IMEI信息]
    E --> F[galaxy.imei.glot]
    
    F --> G[客户分配]
    G --> H[发货准备]
    H --> I[创建galaxy.delivery]
    I --> J[拣货打包]
    J --> K[发货确认]
    
    style B fill:#e3f2fd
    style F fill:#fff3e0
    style I fill:#e8f5e8
```

## 4. 业务流程

### 4.1 入库流程

#### **步骤1：创建批次**
```python
# 创建新的Glot批次
glot = self.env['galaxy.lot'].create({
    'name': 'G240130-1',  # Glot编号
    'customer_id': [(4, customer_id)],  # 分配客户
    'inbound_date': fields.Date.today(),
    'note': 'iPhone 15 Pro 256GB 黑色 - 100台'
})
```

#### **步骤2：创建箱号**
```python
# 创建箱号并关联到批次
carton = self.env['galaxy.carton'].create({
    'name': 'CTN240130001',  # 箱号
    'glot_ids': [(4, glot.id)],  # 关联批次
    'product_qty': 10,  # 箱内产品数量
    'status': 'store'  # 库存状态
})
```

#### **步骤3：录入IMEI**
```python
# 批量录入IMEI信息
imei_list = ['123456789012345', '123456789012346', ...]
for imei in imei_list:
    self.env['galaxy.imei.glot'].create({
        'glot_id': glot.id,
        'carton_id': carton.id,
        'imei': imei,
        'brand': 'Apple',
        'model': 'iPhone 15 Pro',
        'capacity': '256GB',
        'color': '黑色',
        'state': 'in_stock'
    })
```

### 4.2 发货流程

#### **步骤1：创建发货单**
```python
delivery = self.env['galaxy.delivery'].create({
    'name': 'DEL240130001',
    'customer_id': customer_id,
    'delivery_date': fields.Datetime.now()
})
```

#### **步骤2：选择批次和箱号**
```python
# 将批次关联到发货单
delivery.glot_ids = [(4, glot.id)]

# 更新箱号状态
carton.write({
    'status': 'delivery',
    'delivery_id': delivery.id
})
```

#### **步骤3：更新IMEI状态**
```python
# 更新IMEI为已发货状态
imei_records = self.env['galaxy.imei.glot'].search([
    ('carton_id', '=', carton.id)
])
imei_records.write({'state': 'delivered'})
```

## 5. 实际应用场景

### 5.1 库存查询

#### **A. 按客户查询可用库存**
```python
def get_customer_available_stock(self, customer_id):
    """查询客户的可用库存"""
    available_lots = self.env['galaxy.lot'].search([
        ('customer_id', 'in', [customer_id]),
        ('is_availiable', '=', True),
        ('is_invetory_overdue', '=', False)
    ])
    
    stock_info = []
    for lot in available_lots:
        stock_info.append({
            'glot': lot.name,
            'total_cartons': lot.carton_count,
            'available_cartons': lot.carton_count_store,
            'total_products': lot.total_product_qty,
            'inbound_date': lot.inbound_date
        })
    
    return stock_info
```

#### **B. IMEI精确查询**
```python
def search_imei_location(self, imei):
    """根据IMEI查询设备位置"""
    imei_record = self.env['galaxy.imei.glot'].search([
        ('imei', '=', imei)
    ], limit=1)
    
    if imei_record:
        return {
            'imei': imei_record.imei,
            'glot': imei_record.glot_id.name,
            'carton': imei_record.carton_id.name,
            'status': imei_record.state,
            'customer': imei_record.glot_id.customer_id.mapped('name'),
            'location': imei_record.carton_id.pallet_id.name if imei_record.carton_id.pallet_id else 'Unknown'
        }
    return None
```

### 5.2 库存预警

#### **A. 超期库存监控**
```python
def check_overdue_inventory(self):
    """检查超期库存"""
    overdue_lots = self.env['galaxy.lot'].search([
        ('is_invetory_overdue', '=', True)
    ])
    
    # 生成超期报告
    report_data = []
    for lot in overdue_lots:
        overdue_cartons = lot.carton_ids.filtered(lambda c: c.is_invetory_overdue)
        report_data.append({
            'glot': lot.name,
            'customer': ','.join(lot.customer_id.mapped('name')),
            'overdue_cartons': len(overdue_cartons),
            'inbound_date': lot.inbound_date,
            'days_overdue': (fields.Date.today() - lot.inbound_date).days
        })
    
    return report_data
```

### 5.3 发货优化

#### **A. 智能拣货建议**
```python
def suggest_picking_strategy(self, customer_id, required_qty):
    """智能拣货建议"""
    # 查找客户的可用批次，按入库时间排序（先进先出）
    available_lots = self.env['galaxy.lot'].search([
        ('customer_id', 'in', [customer_id]),
        ('is_availiable', '=', True)
    ], order='inbound_date asc')
    
    picking_plan = []
    remaining_qty = required_qty
    
    for lot in available_lots:
        if remaining_qty <= 0:
            break
            
        available_qty = lot.total_un_delivered_product_qty
        pick_qty = min(remaining_qty, available_qty)
        
        if pick_qty > 0:
            picking_plan.append({
                'glot': lot.name,
                'pick_qty': pick_qty,
                'available_cartons': lot.un_delivered_carton_ids.mapped('name'),
                'inbound_date': lot.inbound_date
            })
            remaining_qty -= pick_qty
    
    return {
        'picking_plan': picking_plan,
        'total_available': required_qty - remaining_qty,
        'shortage': remaining_qty if remaining_qty > 0 else 0
    }
```

## 6. 系统集成

### 6.1 与投标系统集成

```python
def sync_with_bid_system(self, lot_id):
    """与投标系统同步库存信息"""
    lot = self.env['galaxy.lot'].browse(lot_id)
    
    # 更新投标系统的库存信息
    for customer in lot.customer_id:
        available_qty = lot.total_un_delivered_product_qty
        
        # 同步到客户的可投标库存
        self.env['customer.available.stock'].create_or_update({
            'customer_id': customer.id,
            'glot_id': lot.id,
            'available_qty': available_qty,
            'last_update': fields.Datetime.now()
        })
```

### 6.2 与财务系统集成

```python
def calculate_inventory_value(self, lot_ids=None):
    """计算库存价值"""
    domain = [('is_availiable', '=', True)]
    if lot_ids:
        domain.append(('id', 'in', lot_ids))
    
    lots = self.env['galaxy.lot'].search(domain)
    
    inventory_value = 0
    for lot in lots:
        # 获取批次的平均成本
        avg_cost = self._get_lot_average_cost(lot)
        total_qty = lot.total_un_delivered_product_qty
        lot_value = avg_cost * total_qty
        inventory_value += lot_value
    
    return inventory_value
```

## 7. 最佳实践

### 7.1 命名规范

#### **Glot命名规则**
- 格式：`G + YYMMDD + - + 序号`
- 示例：`G240130-1`（2024年1月30日第1批）
- 特殊：`G240130-R1`（退货批次）

#### **箱号命名规则**
- 格式：`CTN + YYMMDD + 序号`
- 示例：`CTN240130001`
- 长度：固定12位

### 7.2 数据维护

#### **定期清理**
```python
def cleanup_old_data(self):
    """清理历史数据"""
    # 清理已发货超过1年的IMEI记录
    old_date = fields.Date.today() - timedelta(days=365)
    old_imei_records = self.env['galaxy.imei.glot'].search([
        ('state', '=', 'delivered'),
        ('create_date', '<', old_date)
    ])
    old_imei_records.unlink()
```

#### **数据校验**
```python
def validate_data_integrity(self):
    """数据完整性校验"""
    # 检查孤立的IMEI记录
    orphan_imei = self.env['galaxy.imei.glot'].search([
        ('glot_id', '=', False)
    ])
    
    # 检查无箱号的批次
    empty_lots = self.env['galaxy.lot'].search([
        ('carton_count', '=', 0)
    ])
    
    return {
        'orphan_imei_count': len(orphan_imei),
        'empty_lots_count': len(empty_lots)
    }
```

## 8. 总结

### 8.1 Galaxy Lot的核心价值

1. **🏷️ 批次管理**：为货物提供唯一的批次标识
2. **👥 客户分配**：支持多客户的库存分配
3. **📦 箱号管理**：精确到箱的库存管理
4. **🚚 发货跟踪**：完整的发货流程追踪
5. **⏰ 库存监控**：实时的库存状态监控

### 8.2 IMEI管理的重要性

1. **🔍 精确追踪**：单台设备级别的精确管理
2. **📋 合规要求**：满足进出口贸易要求
3. **🔒 防盗保护**：设备安全和防盗功能
4. **📊 质量追溯**：问题设备的快速定位
5. **💼 客户服务**：提供详细的设备信息

### 8.3 系统优势

1. **完整性**：从入库到发货的完整流程管理
2. **精确性**：支持到单台设备的精确管理
3. **灵活性**：支持多种业务场景和客户需求
4. **可追溯性**：完整的数据追踪和历史记录
5. **集成性**：与其他系统模块的无缝集成

---

**文档版本**：v1.0  
**创建日期**：2025-01-30  
**维护者**：Galaxy ERP开发团队
