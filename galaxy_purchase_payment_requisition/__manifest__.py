# -*- coding: utf-8 -*-
{
    'name': "galaxy_purchase_payment_requisition",

    'summary': """
        Purchase's payment requisition
       """,
    'author': "Mingo",
    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/14.0/odoo/addons/base/data/ir_module_category_data.xml
    # for the full list
    'category': 'Galaxy_ERP/Galaxy_ERP',
    'version': '0.1',

    # any module necessary for this one to work correctly
    'depends': ['base', 'purchase', 'purchase_stock', 'galaxy_payment_requisition', 'documents_quotation_bid', 'galaxy_purchase_order'],
    # always loaded
    'data': [
        # 'security/ir.model.access.csv',
        'views/payment_requisiton_views.xml',
        'views/purchase_order_views.xml',
        'views/menu.xml',
    ],
}
