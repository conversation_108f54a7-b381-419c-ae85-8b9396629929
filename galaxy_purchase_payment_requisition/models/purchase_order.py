# -*- coding: utf-8 -*-

from odoo import models, fields, _


# pylint: disable=R0801,R1710,E0203
class PurchaseOrder(models.Model):
    _inherit = 'purchase.order'

    payment_requisition_id = fields.Many2one('payment.requisition')
    requisition_state = fields.Selection(related='payment_requisition_id.state')

    def unlink(self):
        for order in self:
            if order.state == 'cancel':
                raise models.UserError(_('You cannot delete a purchase order that has been cancelled.'))
        return super(PurchaseOrder, self).unlink()

    def to_cancel_order(self):
        """
            采购订单取消，有别于原生的取消功能，此功能仅在采购订单锁定后，才可以取消
        """
        if self.payment_requisition_id and self.payment_requisition_id.state != 'cancel':
            raise models.UserError(_('you need to cancel the payment requisition firstly'))
        if self.state != 'done':
            raise models.UserError(_('you can not cancel the purchase order '))
        self.state = 'cancel'

    def action_payment_requisition(self):
        """
            跳转到付款申请单页面
        """
        if self.payment_requisition_id:
            return {
                'name': self.payment_requisition_id.name,
                'type': 'ir.actions.act_window',
                'res_model': 'payment.requisition',
                'res_id': self.payment_requisition_id.id,
                'view_mode': 'form',
            }

    def action_create_payment_requisition(self):
        """创建付款通知单"""
        if not self.payment_requisition_id or self.payment_requisition_id.state == 'cancel':
            self.create_payment_requisition()
            self.payment_requisition_id.action_confirm()
        else:
            raise models.UserError(_('payment requisition has not been cancelled'))

    def create_payment_requisition(self):
        """
            创建采购订单的付款申请单
        """
        payment_requisition = self._prepare_payment_requisition()
        payment_requisition_id = self.env['payment.requisition'].create(
            payment_requisition)
        self.payment_requisition_id = payment_requisition_id

    def _prepare_payment_requisition(self):
        return {
            'partner_id': self.partner_id.id,
            'currency_id': self.currency_id.id,
            'order_type': 'purchase',
            'order_number': self.name,
            'purchase_order_id': self.id,
            'total': self.amount_total,
            'state': 'draft',
        }

    def action_confirm(self):
        """
            采购订单确认
        """
        # 下面的创建采购付款申请的，在安装对应的模块以后这个方法才会生效，否则是个空方法
        # 这样可以避免模块依赖的问题
        self.create_payment_requisition()
        res = super(PurchaseOrder, self).action_confirm()
        if self.payment_requisition_id:
            self.payment_requisition_id.action_confirm()
        return res
