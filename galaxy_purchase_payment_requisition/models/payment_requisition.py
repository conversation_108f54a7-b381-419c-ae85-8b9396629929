from odoo import models, fields


class PaymentRequisition(models.Model):
    _inherit = 'payment.requisition'

    purchase_order_id = fields.Many2one('purchase.order')




    def action_purchase_order(self):
        """查看采购订单表单页面"""
        return {
            'name': self.purchase_order_id.name,
            'type': 'ir.actions.act_window',
            'res_model': 'purchase.order',
            'res_id': self.purchase_order_id.id,
            'view_mode': 'form',
        }
