<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="payment_requisiton_action_for_purchase" model="ir.actions.act_window">
        <field name="name">Payment Requisition</field>
        <field name="res_model">payment.requisition</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('order_type', '=', 'purchase')]</field>
        <field name="context">{'default_order_type': 'purchase', 
            'search_view_ref': 'galaxy_purchase_payment_requisition.payment_requisition_view_search_for_purchase',
            'tree_view_ref': 'galaxy_purchase_payment_requisition.payment_requisition_view_tree_for_purchase',
            'form_view_ref': 'galaxy_purchase_payment_requisition.payment_requisition_view_form_for_purchase'}
        </field>
        <field name="help" type="html">
            <p>Not payment requisition</p>
        </field>
    </record>

    <record id="payment_requisition_view_tree_for_purchase" model="ir.ui.view">
        <field name="name">payment_requisition_view_tree</field>
        <field name="model">payment.requisition</field>
        <field name="arch" type="xml">
            <tree delete='0' create='0'>
                <field name='name'/>
                <field name='order_type'/>
                <field name='order_number' optional='hide'/>
                <field name='partner_id'/>
                <field name='total'/>
                <field name='currency_id'/>
                <field name='note'/>
                <field name='state'/>
                <field name='create_uid' optional='hide'/>
                <field name='create_date' optional='hide'/>
                <field name='write_uid' optional='hide'/>
                <field name='write_date' optional='hide'/>

            </tree>
        </field>
    </record>

    <record id="payment_requisition_view_form_for_purchase" model="ir.ui.view">
        <field name="name">payment_requisition_view_form</field>
        <field name="model">payment.requisition</field>
        <field name="arch" type="xml">
            <form create='0' delete='0'>
                <header>
                    <!-- <button string="Back To Draft" name="action_draft" 
                        attrs="{'invisible': [('state', '!=', 'cancel')]}"
                        type="object" class="oe_highlight"/> -->
                    <button string="Confirm" name="action_confirm" 
                        attrs="{'invisible': [('state', 'in', ['approved', 'pending', 'cancel'])]}"
                        type="object" class="oe_highlight"/>
                    <button string="Cancel" name="action_cancel" type="object" 
                        confirm="Are you sure to cancel the payment requisition"
                        attrs="{'invisible': [('state', 'in', ['cancel', 'approved'])]}"/>
                    <field name='state' widget='statusbar' statusbar_visible="draft,pending,approved"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button string='Purchase' name="action_purchase_order" type="object" class="oe_stat_button" icon="fa-bars"/>
                        <field name='approval_request_id' invisible='1'/>
                        <button name="action_approval_request" string='Approval'
                            attrs="{'invisible': [('approval_request_id', '=', False)]}"
                            type="object" class="oe_stat_button" icon="fa-bars">
                        </button>    

                        <field name='payment_id' invisible='1'/>
                        <button name="action_account_payment" type="object" 
                            attrs="{'invisible': [('payment_id', '=', False)]}"
                            class="oe_stat_button" icon="fa-bars">
                            <field name="payment_state" string="Payment" widget="statinfo"/>
                        </button>      
                    </div>
                    <div class="oe_title">
                        <h1><field name='name' readonly='1'/></h1>
                    </div>

                    <group>
                        <group>
                            <field name='partner_id' readonly='1'/>
                            <field name='order_type' readonly='1'/>
                            <field name='note'/>
                        </group>
                        <group>
                            <div class="o_row">
                                <field name='total' readonly='1'/>
                                <field name='currency_id' readonly='1'/>
                            </div>
                            <field name='order_number' readonly='1'/>
                        </group>
                    </group>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>


    <record id="payment_requisition_view_search_for_purchase" model="ir.ui.view">
        <field name="name">payment_requisition_view_search</field>
        <field name="model">payment.requisition</field>
        <field name="arch" type="xml">
            <search>
                <field name='name'/>
                <field name='order_number'/>
                <field name='partner_id'/>
                <!-- <filter name="state_draft" string="Draft" domain="[('state', '=', 'draft')]" /> -->
                <filter name="state_pending" string="To Approval" domain="[('state', '=', 'pending')]" />
                <filter name="state_approved" string="Done" domain="[('state', '=', 'approved')]" />
            </search>
        </field>
    </record>

</odoo>
