<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="purchase_order_form_for_payment_requisition" model="ir.ui.view">
        <field name="name">purchase_order_form_for_payment_requisition</field>
        <field name="model">purchase.order</field>
        <field name="inherit_id" ref="purchase.purchase_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@class='oe_button_box']" position="inside">
                <field name='payment_requisition_id' invisible='1'/>
                <button name="action_payment_requisition"  string='Payment Requisition'
                    attrs="{'invisible': [('payment_requisition_id', '=', False)]}"
                    type="object" class="oe_stat_button" icon="fa-pencil-square-o">
                </button>
            </xpath>

            <xpath expr="//button[@name='button_draft']" position="after">
                <field name='requisition_state' invisible='1'/>
                <button string="Create payment requisition" 
                    attrs="{'invisible': ['|','|', ('requisition_state', 'not in', ('cancel', '')),('purchase_type', '=', 'normal'), ('state', '!=', 'done')]}"
                    name="action_create_payment_requisition" type="object" /> 
            <button string="Cancel" name="to_cancel_order" 
                confirm="Are you sure you want to cancel this order ?"
                type="object" attrs="{'invisible': ['|', ('purchase_type', '=', 'normal'), ('state', '!=', 'done')]}"/>
            </xpath>

            <xpath expr="//button[@name='confirm_reminder_mail']" position="replace">
                <button name="confirm_reminder_mail" string="Confirm Receipt Date" type="object" attrs="{'invisible': ['|','|', '|',('purchase_type', '=', 'proxy'), ('state', 'not in', ('purchase', 'done')), ('mail_reminder_confirmed', '=', True), ('date_planned', '=', False)]}" groups="base.group_no_one"/>
            </xpath>

        </field>
    </record>

    <record id="purchase_order_view_form_inherit_for_action_view_picking" model="ir.ui.view">
        <field name="name">purchase_order_view_form_inherit_for_action_view_picking</field>
        <field name="model">purchase.order</field>
        <field name="inherit_id" ref="purchase_stock.purchase_order_view_form_inherit"/>
        <field name="arch" type="xml">

              <xpath expr="//form" position="inside">
            </xpath>
            <xpath expr="//button[@name='action_view_picking']" position="replace">
                    <button name="action_view_picking" string="Receive Products" class="oe_highlight" type="object" attrs="{'invisible': ['|', '|', '|', ('purchase_type', '=', 'proxy'), ('is_shipped', '=', True), ('state','not in', ('purchase','done')), ('picking_count', '=', 0)]}"/>
            </xpath>

        </field>
    </record>

    <record id="purchase_order_form_inherit_for_button_cancel" model="ir.ui.view">
        <field name="name">model.name.view.form.inherit</field>
        <field name="model">purchase.order</field>
        <field name="inherit_id" ref="purchase.purchase_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="//button" position="inside">
                <button name="button_cancel" attrs="{'invisible': ['|', ('purchase_type', '=', 'proxy'), ('state', 'not in', ['draft', 'to approve', 'sent', 'purchase'])]}" string="Cancel" type="object"/>
            </xpath>
            <xpath expr="//button[@name='button_draft']" position="replace">
                <button name="button_draft" attrs="{'invisible': ['|', ('purchase_type', '=', 'proxy'), ('state', '!=', 'cancel')]}" string="Set to Draft" type="object"/>
            </xpath>
            <xpath expr="//button[@name='button_unlock']" position="replace">
                    <button name="button_unlock" type="object" string="Unlock" 
                   attrs="{'invisible': ['|', ('purchase_type', '=', 'proxy'), '&amp;', ('purchase_type', '=', 'normal'), ('state', '!=', 'done')]}"
                    groups="purchase.group_purchase_manager"/>
            </xpath>
             <xpath expr="//button[@name='action_create_invoice'][1]" position="replace">
                <button name="action_create_invoice" string="Create Bill" type="object" context="{'create_bill':True}" 
                    attrs="{'invisible': ['|', '|', ('purchase_type', '=', 'proxy'), ('state', 'not in', ('purchase', 'done')), ('invoice_status', 'in', ('no', 'invoiced'))]}"/>
            </xpath>
            <xpath expr="//button[@name='action_create_invoice'][2]" position="replace">
                <button name="action_create_invoice" string="Create Bill" type="object" context="{'create_bill':True}" attrs="{'invisible': ['|', '|', '|', ('purchase_type', '=', 'proxy'), ('state', 'not in', ('purchase', 'done')), ('invoice_status', 'not in', ('no', 'invoiced')), ('order_line', '=', [])]}"/>
            </xpath>
           
        
          
        </field>
    </record>

</odoo>
