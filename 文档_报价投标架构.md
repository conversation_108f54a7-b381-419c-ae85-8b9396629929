# Galaxy ERP - 报价投标系统架构详细文档

## 概述

本文档详细说明Galaxy ERP项目中报价投标系统的完整架构，包括核心模型关系、业务流程、数据流转、重点代码位置和表结构关系。

## 目录

- [1. 系统架构概览](#1-系统架构概览)
- [2. 核心数据模型](#2-核心数据模型)
- [3. 表结构关系](#3-表结构关系)
- [4. 业务流程详解](#4-业务流程详解)
- [5. 数据流转机制](#5-数据流转机制)
- [6. 重点代码位置](#6-重点代码位置)
- [7. 系统集成接口](#7-系统集成接口)
- [8. 架构图表](#8-架构图表)

## 1. 系统架构概览

### 1.1 整体架构图

```mermaid
graph TB
    subgraph "供应商端"
        A[供应商询价单<br/>vendor.request.quotation]
        B[供应商标单<br/>vendor.lot]
        C[供应商标单行<br/>vendor.lot.line]
    end
    
    subgraph "拍卖系统核心"
        D[拍卖单<br/>bid.order]
        E[拍卖标单<br/>bid.order.lot]
        F[拍卖标单行<br/>bid.order.lot.line]
    end
    
    subgraph "客户端"
        G[客户投标<br/>customer.bid.line]
        H[客户详单报价<br/>customer.items.quotation]
        I[客户历史报价<br/>customer.items.history.quotation]
    end
    
    subgraph "中标管理"
        J[中标结果<br/>bid.result]
        K[中标订单<br/>bid.won.order]
        L[销售订单<br/>sale.order]
        M[采购订单<br/>purchase.order]
    end
    
    subgraph "外部系统"
        N[Java投标系统]
        O[移动端APP]
        P[供应商网站]
    end
    
    A --> D
    B --> E
    C --> F
    D --> E
    E --> F
    E --> G
    F --> H
    G --> J
    J --> K
    K --> L
    K --> M
    
    N <--> G
    O <--> G
    P <--> A
    
    style D fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    style E fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    style G fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    style J fill:#fce4ec,stroke:#c2185b,stroke-width:2px
```

### 1.2 系统模块分布

| 模块名称 | 主要功能 | 核心模型 |
|---------|---------|---------|
| **quotation_bid** | 核心投标系统 | bid.order, bid.order.lot, customer.bid.line |
| **quotation_bid_supplier** | 供应商扩展 | vendor.request.quotation, vendor.lot |
| **quotation_bid_customer_credit** | 客户信用管理 | customer.lot.credit |
| **quotation_bid_sku_standard** | SKU标准化 | bid.standard, report.bid.order.lot.line |
| **quotation_bid_sale** | 销售集成 | sale.order, bid.result |
| **documents_vendor** | Tender Batch管理 | documents.folder, tender.batch.seqn |
| **documents_quotation_bid** | 文档集成 | bid.won.order, bid.won.result |
| **rest_api_bid** | API接口 | 移动端接口服务 |

## 2. 核心数据模型

### 2.1 客户报价模型对比

#### 2.1.1 customer.items.quotation vs customer.bid.line

Galaxy ERP系统中有两种不同的客户报价模型，它们服务于不同的投标场景：

##### **核心区别**

| 维度 | customer.items.quotation | customer.bid.line |
|------|-------------------------|-------------------|
| **用途** | 针对**具体商品明细**的报价 | 针对**整个标单**的投标 |
| **关联对象** | `bid.order.lot.line` (标单明细行) | `bid.order.lot` (拍卖标单) |
| **报价粒度** | **单个商品明细级别** | **整个标单级别** |
| **使用场景** | 详单投标模式 (`bid_type='items'`) | 标单投标模式 (`bid_type='lot'`) |

##### **表结构对比**

```python
# customer.items.quotation (客户详单报价)
class CustomerItemsQuotation(models.Model):
    _name = 'customer.items.quotation'

    name = fields.Many2one('res.partner', string='Customer', required=True)
    lot_line_id = fields.Many2one('bid.order.lot.line', required=True)  # 关联明细行
    bid_price = fields.Monetary(string='Customer Quotation')
    customer_currency_id = fields.Many2one('res.currency')
    ubid_time = fields.Datetime(string='Quotation Time')
    from_excel = fields.Boolean(default=False)

# customer.bid.line (客户投标线)
class CustomerBidLine(models.Model):
    _name = 'customer.bid.line'

    name = fields.Many2one('res.partner', string='Customer', required=True)
    line_id = fields.Many2one('bid.order.lot', required=True)  # 关联标单
    bid_price = fields.Monetary(string='Customer Quotation')
    customer_currency_id = fields.Many2one('res.currency')
    ubid_time = fields.Datetime(string='Quotation Time')
    state = fields.Selection([('bid', 'Bidding'), ('won', 'Won'), ('failed', 'Lost')])
    from_excel = fields.Boolean(index=True)
```

##### **层级关系图**

```mermaid
graph TB
    A[bid.order<br/>拍卖单] --> B[bid.order.lot<br/>拍卖标单]
    B --> C[bid.order.lot.line<br/>标单明细行]

    B --> D[customer.bid.line<br/>客户投标]
    C --> E[customer.items.quotation<br/>客户详单报价]

    style D fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    style E fill:#fff3e0,stroke:#f57c00,stroke-width:2px
```

##### **业务场景对比**

**场景A：标单投标模式** (`bid_type='lot'`)
```python
# 客户对整个标单进行投标
customer_bid = self.env['customer.bid.line'].create({
    'name': customer_id,           # 客户
    'line_id': bid_lot_id,         # 标单ID
    'bid_price': 850000,           # 整个标单的总价 (HK$850,000)
    'customer_currency_id': hkd_id
})

# 实际案例：
# 拍卖标单: iPhone 15 Pro 批次 (100台)
# - 客户A投标: HK$850,000 (整批价格)
# - 客户B投标: HK$860,000 (整批价格)
```

**场景B：详单投标模式** (`bid_type='items'`)
```python
# 客户对标单中的每个明细分别报价
for line in bid_lot.bid_order_lot_line_ids:
    customer_item_quotation = self.env['customer.items.quotation'].create({
        'name': customer_id,           # 客户
        'lot_line_id': line.id,        # 明细行ID
        'bid_price': 8500,             # 单个明细的价格 (HK$8,500/台)
        'customer_currency_id': hkd_id
    })

# 实际案例：
# 拍卖标单: iPhone 15 Pro 批次
# ├── 明细1: iPhone 15 Pro 256GB 黑色 (50台)
# │   ├── 客户A报价: HK$8,500/台
# │   └── 客户B报价: HK$8,600/台
# └── 明细2: iPhone 15 Pro 512GB 白色 (50台)
#     ├── 客户A报价: HK$9,500/台
#     └── 客户B报价: HK$9,400/台
```

##### **代码中的关联关系**

```python
# 在 bid.order.lot.line 中
class BidOrderLotLine(models.Model):
    _name = 'bid.order.lot.line'

    customer_item_quotation_ids = fields.One2many(
        'customer.items.quotation', 'lot_line_id')
    customer_item_quotation_id = fields.Many2one(
        'customer.items.quotation', compute='_get_customer_item_quotation')

# 在 bid.order.lot 中
class BidOrderLot(models.Model):
    _name = 'bid.order.lot'

    customer_bid = fields.Many2one('customer.bid.line')
    customer_bid_ids = fields.One2many('customer.bid.line', 'line_id')
```

##### **使用场景总结**

| 特征 | customer.items.quotation | customer.bid.line |
|------|-------------------------|-------------------|
| **报价方式** | 分明细报价 | 整批报价 |
| **灵活性** | 高 - 可针对不同明细设置不同价格 | 低 - 只能设置整批价格 |
| **复杂度** | 高 - 需要管理多个明细报价 | 低 - 只需管理一个总价 |
| **适用场景** | 商品规格差异大，需要精细定价 | 商品规格相似，批量定价 |
| **数据量** | 大 - 每个明细一条记录 | 小 - 每个标单一条记录 |
| **计算方式** | 明细价格 × 数量 = 明细总价 | 直接设置标单总价 |

### 2.3 拍卖单模型 (bid.order)

#### 文件位置：`quotation_bid/models/bid_order.py`

```python
class BidOrder(models.Model):
    _name = 'bid.order'
    _description = 'Auction'
    
    # 核心字段
    name = fields.Char(string='Group', required=True, copy=False, readonly=True, index=True)
    standard_id = fields.Many2one('bid.standard', string='Standard', required=True)
    supplier_id = fields.Many2one('res.partner', string='Vendor', required=True)
    
    # 时间控制
    supplier_time_remaining = fields.Datetime(string='Vendor RFQ Deadline', required=True)
    customer_time_remaining = fields.Datetime(string='Auction Endtime', copy=False)
    
    # 关联关系
    origin_id = fields.Many2many('vendor.request.quotation', string='Vendor RFQ', copy=False)
    bid_order_ids = fields.One2many('bid.order.lot', 'order_id', string='Lot')
    
    # 状态管理
    state = fields.Selection([
        ('draft', 'Not start'), 
        ('bidding', 'bidding'), 
        ('done', 'Closed'), 
        ('cancel', 'Cancel'),
        ('confirm', 'Confirm'), 
        ('sent', 'Sent Email'), 
        ('complete', 'Complete')
    ], string='Status', default='draft', store=True, tracking=True)
    
    # 投标类型
    bid_type = fields.Selection([
        ('items', 'bid by items'), 
        ('lot', "bid by lot")
    ], default='lot', string='Quote Type')
```

### 2.4 拍卖标单模型 (bid.order.lot)

#### 文件位置：`quotation_bid/models/bid_order_lot.py`

```python
class BidOrderLot(models.Model):
    _name = 'bid.order.lot'
    _description = 'Auction Lot'
    
    # 基本信息
    name = fields.Char(string='Lot Number', required=True, copy=False, readonly=True, index=True)
    order_id = fields.Many2one('bid.order', string='Auction', index=True)
    
    # 产品属性 (六个固定属性)
    grade = fields.Char(compute='_compute_attrs', store=True)
    version = fields.Char('Carrier', compute='_compute_attrs', store=True, index=1)
    capacity = fields.Char(compute='_compute_attrs', store=True)
    color = fields.Char(compute='_compute_attrs', store=True)
    fmip = fields.Char(compute='_compute_attrs', store=True)
    carrier_lock = fields.Char(compute='_compute_attrs', store=True)
    model_number = fields.Char(compute='_compute_attrs', store=True)
    vendor_grade = fields.Char(index=1)
    rfq_grade = fields.Char(string='RFQ Grade')
    
    # 关联关系
    bid_order_lot_line_ids = fields.One2many('bid.order.lot.line', 'bid_order_lot_id', string='Bid Order Line')
    customer_bid_lines = fields.One2many('customer.bid.line', 'line_id', string='Customer Bid Line')
    vendor_lot_ids = fields.One2many('vendor.lot', 'bid_order_lot_id')
    
    # 客户投标
    customer_bid = fields.Many2one('customer.bid.line', string='Customer', compute='_get_customer', store=True)
    
    # 状态管理
    state = fields.Selection([
        ('draft', 'Not Start'), 
        ('bidding', 'bidding'), 
        ('done', 'Closed'), 
        ('cancel', 'Cancel'),
        ('confirm', 'Confirm')
    ], string='Status', default='draft')
```

### 2.5 客户投标模型 (customer.bid.line)

#### 文件位置：`quotation_bid/models/customer_bid.py`

```python
class CustomerBidLine(models.Model):
    _name = 'customer.bid.line'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Bid Line'
    
    # 客户信息
    name = fields.Many2one('res.partner', string='Customer', required=True, index=True)
    
    # 投标信息
    line_id = fields.Many2one('bid.order.lot', string='Lot', required=True, index=True)
    bid_price = fields.Monetary(string='Quotation', currency_field='customer_currency_id')
    customer_currency_id = fields.Many2one('res.currency', string='Customer Currency')
    ubid_time = fields.Datetime(string='Quotation Time')
    
    # 状态管理
    state = fields.Selection([
        ('waiting', 'Waiting'), 
        ('won', 'Won'), 
        ('failed', 'Failed')
    ], string='Status', default='waiting')
    
    # 来源标识
    from_excel = fields.Boolean(default=False)
    active = fields.Boolean(default=True)
```

### 2.7 供应商询价单模型 (vendor.request.quotation)

#### 文件位置：`quotation_bid/models/vendor_request_quotation.py`

```python
class VendorRequestQuotation(models.Model):
    _name = 'vendor.request.quotation'
    _description = 'Vendor Request Quotation'

    # 基本信息
    name = fields.Char(string='RFQ Number', required=True, copy=False, readonly=True, index=True)
    supplier = fields.Many2one('res.partner', string='Vendor', required=True)

    # 关联关系
    vendor_lot_ids = fields.One2many('vendor.lot', 'rfq_id', string='Vendor RFQ Lot', copy=True)
    bid_order_id = fields.Many2one('bid.order', string='Auction', copy=False, index=True)

    # 状态管理
    bid_status = fields.Selection([
        ('draft', 'Draft'),
        ('pending_bid', 'Pending Bid'),
        ('bidded', 'Bidded'),
        ('sent', 'Sent')
    ], string='Bid Status', default='draft')

    # 货币和汇率
    currency_id = fields.Many2one('res.currency', string='Currency')
    exchange_rate = fields.Float(string='Exchange Rate', digits=(10, 4))

    # Tender Batch 批次管理
    tender_batch_id = fields.Many2one('documents.folder', string='Tender Batch')
    quotation_expire_date = fields.Datetime(string='Quotation expire date', copy=False)
```

### 2.6 Tender Batch 批次管理模型

#### **A. documents.folder (Tender Batch 主表)**

#### 文件位置：`documents_vendor/models/folder.py`

```python
class DocumentFolder(models.Model):
    _inherit = 'documents.folder'

    partner_id = fields.Many2one('res.partner', string="Vendor", domain="[('supplier_rank','>',0)]")
    doc_type = fields.Many2one('vendor.doc.type', string="Doc Type", ondelete="restrict")
    folder_seqn = fields.Char('Folder Seqn', index=True)
    tender_deadline = fields.Datetime(index=True, copy=False)

    @api.model
    def create(self, vals):
        seqn = self.env['ir.sequence'].with_context(galaxy_base_32=True).next_by_code('documents.folder.sn')
        vals.update({'folder_seqn': seqn})
        return super(DocumentFolder, self).create(vals)
```

#### **B. tender.batch.seqn (序号管理表)**

#### 文件位置：`documents_vendor/models/tender_batch_seqn.py`

```python
class TenderBatchSeqn(models.Model):
    _name = 'tender.batch.seqn'
    _description = 'Tender Batch Seqn only for techinical use'

    name = fields.Char('Name', index=True)
    batch_date = fields.Date('Batch Date', index=True)
    batch_date_local = fields.Char('Batch Date Local', index=True)
    seqn = fields.Integer('Seqn', default=1)

    @api.model
    def get_batch_seqn(self, batch_name='', batch_date=None):
        # 使用数据库锁确保序号唯一性
        self._cr.execute("LOCK TABLE tender_batch_seqn IN ACCESS EXCLUSIVE MODE NOWAIT")
        tzone = pytz.timezone("Asia/Shanghai")
        cur_day = batch_date.astimezone(tzone).strftime("%Y-%m-%d")
        rec = self.search([('name', '=', batch_name), ('batch_date_local', '=', cur_day)])
        if not rec:
            rec = self.create({
                'name': batch_name,
                'batch_date': batch_date,
                'batch_date_local': cur_day
            })
        else:
            rec.write({'seqn': rec.seqn+1})
        return rec.seqn
```

## 3. 表结构关系

### 3.1 核心表关系图

```mermaid
erDiagram
    bid_order ||--o{ bid_order_lot : "一对多"
    bid_order_lot ||--o{ bid_order_lot_line : "一对多"
    bid_order_lot ||--o{ customer_bid_line : "一对多"
    bid_order_lot ||--o{ vendor_lot : "一对多"

    vendor_request_quotation ||--o{ vendor_lot : "一对多"
    vendor_lot ||--o{ vendor_lot_line : "一对多"

    bid_order_lot_line ||--o{ customer_items_quotation : "一对多"

    res_partner ||--o{ customer_bid_line : "一对多"
    res_partner ||--o{ vendor_request_quotation : "一对多"

    bid_standard ||--o{ bid_order : "一对多"

    documents_folder ||--o{ vendor_request_quotation : "一对多"
    documents_folder ||--o{ documents_document : "一对多"
    tender_batch_seqn ||--o{ documents_folder : "序号生成"
    res_partner ||--o{ documents_folder : "供应商关联"
    
    bid_order {
        int id PK
        string name "拍卖单编号"
        int standard_id FK
        int supplier_id FK
        datetime supplier_time_remaining
        datetime customer_time_remaining
        string state
        string bid_type
    }
    
    bid_order_lot {
        int id PK
        string name "标单编号"
        int order_id FK
        string state
        int customer_bid FK
        string grade
        string version
        string capacity
        string color
    }
    
    customer_bid_line {
        int id PK
        int name FK "客户ID"
        int line_id FK "标单ID"
        float bid_price
        int customer_currency_id FK
        datetime ubid_time
        string state
        boolean from_excel
    }
    
    vendor_request_quotation {
        int id PK
        string name "询价单编号"
        int supplier FK
        int bid_order_id FK
        string bid_status
        int currency_id FK
        float exchange_rate
        int tender_batch_id FK "Tender Batch"
        datetime quotation_expire_date
    }

    documents_folder {
        int id PK
        string name "Tender Batch名称"
        int partner_id FK "供应商"
        datetime tender_deadline "截止时间"
        string folder_seqn "文件夹序号"
        int parent_folder_id FK
    }

    tender_batch_seqn {
        int id PK
        string name "序号名称"
        date batch_date "批次日期"
        string batch_date_local "本地日期"
        int seqn "序号"
    }
```

### 3.2 数据关系说明

#### **主要关系链**
1. **供应商询价** → **拍卖单** → **拍卖标单** → **客户投标**
2. **拍卖标单** → **标单行** → **客户详单报价**
3. **客户投标** → **中标结果** → **销售/采购订单**

#### **关键外键关系**
- `bid.order.lot.order_id` → `bid.order.id`
- `customer.bid.line.line_id` → `bid.order.lot.id`
- `customer.bid.line.name` → `res.partner.id`
- `vendor.lot.bid_order_lot_id` → `bid.order.lot.id`
- `vendor.lot.rfq_id` → `vendor.request.quotation.id`

## 4. 业务流程详解

### 4.1 完整业务流程图

```mermaid
sequenceDiagram
    participant S as 供应商
    participant V as 供应商询价单
    participant B as 拍卖单
    participant L as 拍卖标单
    participant C as 客户
    participant J as Java系统
    participant R as 中标结果
    
    Note over S,R: 1. Tender Batch创建阶段
    S->>S: 创建Tender Batch
    S->>V: 批量创建询价单
    V->>V: 关联到Tender Batch

    Note over S,R: 2. 询价阶段
    V->>V: 填写产品信息
    V->>B: 确认生成拍卖单
    B->>L: 创建拍卖标单
    
    Note over S,R: 2. 发布阶段
    L->>L: 设置截止时间
    L->>J: 发布到Java系统
    J->>C: 推送到移动端
    
    Note over S,R: 3. 投标阶段
    C->>J: 提交投标报价
    J->>L: 同步投标数据
    L->>L: 记录客户投标
    
    Note over S,R: 4. 开标阶段
    L->>L: 截止时间到达
    L->>R: 生成中标结果
    R->>C: 通知中标客户
    R->>S: 通知供应商
    
    Note over S,R: 5. 订单阶段
    R->>R: 确认中标
    R->>R: 生成销售订单
    R->>R: 生成采购订单
```

### 4.2 Tender Batch 管理流程

#### A. Tender Batch 创建和命名

```mermaid
flowchart TD
    A[创建Tender Batch] --> B[设置供应商]
    B --> C[设置截止时间]
    C --> D[生成批次编号]
    D --> E[创建文档文件夹]
    E --> F[关联询价单]
    F --> G[批量管理和跟踪]

    subgraph "命名规则"
        H[YYMMDD-序号]
        I[示例: 240115-1]
    end

    D --> H
    H --> I

    style A fill:#e3f2fd
    style D fill:#fff3e0
    style F fill:#e8f5e8
```

#### B. Tender Batch 命名机制

#### 文件位置：`documents_vendor/wizard/vendor_folder_upload.py`

```python
def create_vendor_doc_folder(self):
    tzone = pytz.timezone("Asia/Shanghai")
    tender_batch_day = self.tender_deadline.astimezone(tzone).strftime("%y%m%d")
    tender_batch_seqn = self.env['tender.batch.seqn'].get_batch_seqn('sequence.tender.batch', self.tender_deadline)
    self.documents_folder_text = tender_batch_day + '-' + str(tender_batch_seqn)
    # 命名格式：YYMMDD-序号，如：240115-1
```

#### C. Tender Batch 层级结构

```mermaid
graph TB
    A[Documents Root Folder] --> B[Vendor Documents Folder]
    B --> C[Supplier A Workspace]
    B --> D[Supplier B Workspace]
    C --> E[Tender Batch 240115-1]
    C --> F[Tender Batch 240115-2]
    D --> G[Tender Batch 240115-1]

    E --> H[RFQ Document 1]
    E --> I[RFQ Document 2]
    F --> J[RFQ Document 3]

    style B fill:#e3f2fd
    style E fill:#fff3e0
    style F fill:#fff3e0
    style G fill:#fff3e0
```

### 4.3 关键业务方法

#### A. 拍卖单发布

#### 文件位置：`quotation_bid/models/bid_order_lot.py`

```python
def publish_lot(self):
    """发布标单到Java系统"""
    # 验证发布条件
    check_error = self.validate_lot_before_publish()
    if check_error:
        raise models.UserError(check_error)
    
    # 更新状态
    self.write({'state': 'bidding'})
    auction_order = self[0].order_id
    if auction_order.state == 'draft':
        auction_order.state = 'bidding'
        auction_order.update_exchange_rate(exchange_rate=False, lots=self)
    
    # 发送MQ消息到Java系统
    if auction_order.is_app:
        bid_infor = self.env['rabbitmq.server'].search([('name', '=', 'BidInfoV2')])
        bid_infor.send(group_publish_data)
```

#### B. 客户投标处理

#### 文件位置：`rest_api_bid/services/bid_information.py`

```python
def customer_bid_info(self, search_param):
    """处理客户投标数据"""
    customer = self.env['res.partner'].search([('mobile', '=', search_param.phone)])
    line_data = search_param.line_data
    line_ids = [line.get('line_id') for line in line_data if line.get('line_id')]
    
    if customer and line_ids:
        # 归档历史数据
        had_bid = self.env['customer.bid.line'].search([
            ('name', '=', customer.id), 
            ('line_id', 'in', line_ids)
        ])
        had_bid.write({'active': False})
        
        # 创建新的投标记录
        for line in line_data:
            self.env['customer.bid.line'].create({
                'name': customer.id,
                'line_id': line.get('line_id'),
                'bid_price': line.get('bid_price'),
                'customer_currency_id': line.get('currency_id'),
                'ubid_time': datetime.now()
            })
```

#### C. 中标确认

#### 文件位置：`quotation_bid/models/bid_result.py`

```python
def confirm_bid_result(self, lot_ids=[]):
    """确认中标结果"""
    # 检查供应商标单状态
    error_rec_list = self.check__vendor_lots_status()
    if len(error_rec_list) > 0:
        return {'message': _('you can only complete waiting status lot'), 'success': False}

    # 更改供应商标单状态
    self.change_vendor_lots_status()

    # 发送消息到APP
    self._message_to_app()

    # 更新标单状态
    lot_ids = self.lot_line_ids.vendor_lot_id.bid_order_lot_id.ids
    super(BidResult, self).confirm_bid_result(lot_ids)
```

#### D. Tender Batch 在中标管理中的应用

#### 文件位置：`documents_quotation_bid/models/bid_won_result.py`

```python
def bid_won_result_auto_match(self, tender_batch_id, won_result_line):
    """中标单自动匹配出价单 - 基于Tender Batch"""
    if tender_batch_id:
        # 基于Tender Batch匹配询价单
        quotation_ids = self.env['vendor.request.quotation'].search([
            ('tender_batch_id', '=', tender_batch_id.id),
            ('bid_status', 'in', ('bidded', 'sent'))
        ]).ids
    else:
        # 全局匹配
        quotation_ids = self.env['vendor.request.quotation'].search([
            ('bid_status', 'in', ('bidded', 'sent'))
        ]).ids

    # 匹配供应商标单
    vendor_lot = self.env['vendor.lot'].search([
        ('rfq_id', 'in', quotation_ids),
        ('bid_id', '=', str(won_result_line.lot_id).strip()),
        ('bid_status', '=', 'to_be_opened'),
        ('customer_id', '!=', None)
    ])
```

## 5. 数据流转机制

### 5.1 数据流转架构图

```mermaid
flowchart TD
    subgraph "数据输入层"
        A[供应商询价单录入]
        B[Excel批量导入]
        C[API接口数据]
    end
    
    subgraph "数据处理层"
        D[询价单确认]
        E[拍卖单生成]
        F[标单发布]
        G[投标数据同步]
    end
    
    subgraph "数据存储层"
        H[(vendor.request.quotation)]
        I[(bid.order)]
        J[(bid.order.lot)]
        K[(customer.bid.line)]
    end
    
    subgraph "数据输出层"
        L[MQ消息队列]
        M[REST API]
        N[报表导出]
        O[邮件通知]
    end
    
    A --> D
    B --> D
    C --> G
    D --> E
    E --> F
    F --> G
    
    D --> H
    E --> I
    F --> J
    G --> K
    
    F --> L
    G --> M
    K --> N
    F --> O
    
    style D fill:#e3f2fd
    style E fill:#fff3e0
    style F fill:#e8f5e8
    style G fill:#fce4ec
```

### 5.2 关键数据流转点

#### **1. 询价单 → 拍卖单**
- **触发条件**：供应商询价单确认
- **数据转换**：vendor.lot → bid.order.lot
- **关键字段**：产品属性、数量、价格

#### **2. 拍卖单 → Java系统**
- **传输方式**：RabbitMQ消息队列
- **消息格式**：JSON格式的标单数据
- **同步内容**：标单信息、产品详情、截止时间

#### **3. Java系统 → 客户投标**
- **接收方式**：REST API接口
- **数据验证**：客户身份、标单有效性
- **存储位置**：customer.bid.line表

#### **4. 投标数据 → 中标结果**
- **处理逻辑**：价格排序、状态更新
- **结果生成**：bid.result记录
- **通知机制**：MQ消息、邮件通知

## 6. 重点代码位置

### 6.1 核心模型文件

| 功能模块 | 文件路径 | 主要内容 |
|---------|---------|---------|
| **拍卖单核心** | `quotation_bid/models/bid_order.py` | 拍卖单模型、业务逻辑 |
| **拍卖标单** | `quotation_bid/models/bid_order_lot.py` | 标单模型、发布逻辑 |
| **客户投标** | `quotation_bid/models/customer_bid.py` | 客户投标模型 |
| **供应商询价** | `quotation_bid/models/vendor_request_quotation.py` | 询价单模型、确认逻辑 |
| **中标结果** | `quotation_bid/models/bid_result.py` | 中标处理逻辑 |
| **Tender Batch管理** | `documents_vendor/models/folder.py` | Tender Batch文件夹模型 |
| **批次序号管理** | `documents_vendor/models/tender_batch_seqn.py` | 批次序号生成逻辑 |
| **文档集成** | `documents_vendor/models/document.py` | 文档与Tender Batch关联 |

### 6.2 业务流程文件

| 业务流程 | 文件路径 | 关键方法 |
|---------|---------|---------|
| **标单发布** | `quotation_bid/models/bid_order_lot.py` | `publish_lot()`, `publish_lot_with_duration()` |
| **投标确认** | `quotation_bid/models/bid_order.py` | `action_confirm_customer_bid()` |
| **中标处理** | `quotation_bid/models/bid_result.py` | `confirm_bid_result()` |
| **询价确认** | `quotation_bid/models/vendor_request_quotation.py` | `action_confirm()`, `action_bidded()` |
| **Tender Batch创建** | `documents_vendor/wizard/vendor_folder_upload.py` | `create_vendor_doc_folder()`, `create_vendor_doc_folder_simple()` |
| **批次序号生成** | `documents_vendor/models/tender_batch_seqn.py` | `get_batch_seqn()` |
| **中标自动匹配** | `documents_quotation_bid/models/bid_won_result.py` | `bid_won_result_auto_match()` |

### 6.3 API接口文件

| 接口类型 | 文件路径 | 主要功能 |
|---------|---------|---------|
| **客户投标API** | `rest_api_bid/services/bid_information.py` | 接收客户投标数据 |
| **供应商接收API** | `rest_api_receive/services/vendor_public_receive_services.py` | 接收供应商投标数据 |
| **Java RPC接口** | `java_bid_server/models/rpc/bid_rpc.py` | 与Java系统通信 |

### 6.4 视图配置文件

| 界面功能 | 文件路径 | 主要内容 |
|---------|---------|---------|
| **拍卖单视图** | `quotation_bid/views/bid_order_views.xml` | 拍卖单列表、表单视图 |
| **标单视图** | `quotation_bid/views/bid_order_lot_views.xml` | 标单管理界面 |
| **客户投标视图** | `quotation_bid/views/customer_bid_views.xml` | 客户投标管理 |
| **询价单视图** | `quotation_bid/views/vendor_request_quotation_views.xml` | 供应商询价界面 |

## 7. 系统集成接口

### 7.1 MQ消息队列集成

#### 文件位置：`quotation_bid/data/rabbitmq_queue.xml`

| 队列名称 | 类型 | 用途 | 路由键 |
|---------|------|------|--------|
| BidInfo | Publisher | 发送标单信息 | /ERP/SEND/BID |
| BidInfoV2 | Publisher | 发送标单信息V2 | /ERP/SEND/BID |
| BidPrice | Consumer | 接收投标报价 | /BID/SEND/ERP |
| BidPriceAck | Publisher | 发送报价应答 | /ERP/SEND/BID |
| BidBest | Publisher | 发送中标结果 | /ERP/SEND/BID |

### 7.2 REST API接口

#### 客户投标接口
```python
# 文件位置：rest_api_bid/services/bid_information.py
@restapi.method([('/customer_bid_info','POST')])
def customer_bid_info(self, search_param):
    """创建客户投标数据"""
    # 处理客户投标逻辑
```

#### 供应商数据接收接口
```python
# 文件位置：rest_api_receive/services/vendor_public_receive_services.py
@restapi.method([(["/post_bidded_data"], "POST")])
def post_bidded_data(self, search_param):
    """验证token并接收投标数据"""
    # 处理供应商投标数据
```

### 7.3 外部系统集成

#### **Java投标系统集成**
- **通信方式**：RabbitMQ + REST API
- **数据同步**：实时双向同步
- **主要功能**：标单发布、投标接收、状态同步

#### **移动端APP集成**
- **接口协议**：REST API
- **认证方式**：Token认证
- **主要功能**：客户投标、标单查看、结果通知

#### **供应商网站集成**
- **集成方式**：爬虫自动化
- **支持网站**：ITOCHU(Docomo)等
- **主要功能**：自动投标、结果获取

## 8. 架构图表

### 8.1 技术架构层次图

```mermaid
graph TB
    subgraph "表现层"
        A[Web界面]
        B[移动端APP]
        C[API接口]
    end
    
    subgraph "业务逻辑层"
        D[拍卖管理]
        E[投标处理]
        F[中标管理]
        G[信用控制]
    end
    
    subgraph "数据访问层"
        H[ORM模型]
        I[数据验证]
        J[事务管理]
    end
    
    subgraph "数据存储层"
        K[(PostgreSQL)]
        L[文件存储]
        M[缓存系统]
    end
    
    subgraph "集成层"
        N[RabbitMQ]
        O[REST API]
        P[邮件服务]
    end
    
    A --> D
    B --> E
    C --> F
    D --> H
    E --> I
    F --> J
    G --> H
    H --> K
    I --> L
    J --> M
    
    D --> N
    E --> O
    F --> P
    
    style D fill:#e3f2fd
    style E fill:#fff3e0
    style F fill:#e8f5e8
    style G fill:#fce4ec
```

### 8.2 数据流向总览图

```mermaid
flowchart LR
    subgraph "输入源"
        A[供应商录入]
        B[Excel导入]
        C[API数据]
    end
    
    subgraph "处理中心"
        D[询价单处理]
        E[拍卖单生成]
        F[标单发布]
        G[投标处理]
        H[中标确认]
    end
    
    subgraph "输出目标"
        I[Java系统]
        J[移动端]
        K[邮件通知]
        L[报表导出]
        M[订单生成]
    end
    
    A --> D
    B --> D
    C --> G
    D --> E
    E --> F
    F --> I
    F --> J
    G --> H
    H --> K
    H --> L
    H --> M
    
    I -.-> G
    J -.-> G
    
    style D fill:#e3f2fd
    style E fill:#fff3e0
    style F fill:#e8f5e8
    style G fill:#fce4ec
    style H fill:#f3e5f5
```

## 9. 详细业务场景

### 9.1 投标类型对比

| 投标类型 | 说明 | 适用场景 | 数据模型 |
|---------|------|---------|---------|
| **按标单投标 (lot)** | 客户对整个标单出价 | 批量采购、标准化产品 | customer.bid.line |
| **按详单投标 (items)** | 客户对每个产品详单出价 | 个性化需求、精细化报价 | customer.items.quotation |

### 9.2 状态流转详解

#### A. 拍卖单状态流转

```mermaid
stateDiagram-v2
    [*] --> draft : 创建拍卖单
    draft --> bidding : 发布标单
    bidding --> done : 截止时间到达
    bidding --> cancel : 手动取消
    done --> confirm : 确认中标
    confirm --> sent : 发送邮件
    confirm --> complete : 完成拍卖
    cancel --> draft : 重新开始

    note right of bidding : 客户可以投标
    note right of done : 停止接收投标
    note right of complete : 生成订单
```

#### B. 客户投标状态流转

```mermaid
stateDiagram-v2
    [*] --> waiting : 提交投标
    waiting --> won : 中标
    waiting --> failed : 未中标
    won --> [*] : 生成订单
    failed --> [*] : 投标结束

    note right of waiting : 等待开标
    note right of won : 最高价中标
    note right of failed : 价格不够或其他原因
```

### 9.3 关键业务规则

#### **投标规则**
1. **时间限制**：客户只能在截止时间前投标
2. **价格规则**：必须高于起拍价
3. **货币一致**：投标货币必须与标单货币一致
4. **信用控制**：投标金额不能超过客户信用额度

#### **中标规则**
1. **价格优先**：最高价格优先中标
2. **时间优先**：同价格情况下，早投标优先
3. **客户优先级**：VIP客户在同等条件下优先

### 9.4 异常处理机制

#### **投标异常处理**

#### 文件位置：`quotation_bid_customer_credit/models/bid_order.py`

```python
def _action_lock_lot(self, lots_list):
    """锁定额度异常处理"""
    try:
        GalaxyLotCreditAPI = LotCreditAPI()
        response_list = GalaxyLotCreditAPI.batch_lock_credit_v2(lots_list)
        success_lots, error_lots, server_error = GalaxyLotCreditAPI.format_response_list(response_list)

        if server_error:
            raise RetryableJobError(msg=f'{server_error} {error_lots}')

        # 处理成功的锁定
        self.change_lot_credit_state(lots=success_lots, state='lock')

        # 处理失败的锁定
        if error_lots:
            _logger.warning(f'lock lots credit error {error_lots}')
            # 增加临时额度重试逻辑

    except Exception as e:
        _logger.error(f'Lock credit failed: {e}')
        raise
```

#### **数据同步异常处理**

#### 文件位置：`quotation_bid/models/rabbitmq_server.py`

```python
def callback_queue_galaxy_bid_price(self, ch, method, properties, body):
    """投标数据同步异常处理"""
    def do_work(self, ch, method, properties, body):
        error_body = []
        success_body = []

        try:
            for bid in body:
                user_id = bid.get('user_id', '')
                lot_id = bid.get('lot_id', '')

                # 验证客户存在
                customer = self.env['res.partner'].search([('ref', '=', user_id)], limit=1)
                if not customer:
                    error_body.append({'user_id': user_id, 'error': 'Customer not found'})
                    continue

                # 验证标单存在
                line_id = self.env['bid.order.lot'].search([('name', '=', lot_id)], limit=1)
                if not line_id:
                    error_body.append({'lot_id': lot_id, 'error': 'Lot not found'})
                    continue

                # 创建投标记录
                # ... 业务逻辑
                success_body.append({'user_id': user_id, 'lot_id': lot_id, 'status': 'success'})

        except Exception as e:
            _logger.error(f'Process bid data error: {e}')
            error_body.append({'error': str(e)})

        # 发送应答消息
        bidPriceACK = self.env['rabbitmq.server'].search([('name', '=', 'BidPriceAck')])
        if bidPriceACK:
            bidPriceACK.send(error_body + success_body, is_raise=False)
```

## 10. 扩展模块详解

### 10.1 SKU标准化模块

#### 文件位置：`quotation_bid_sku_standard/models/bid_order_lot.py`

```python
class BidOrderLot(models.Model):
    _inherit = 'bid.order.lot'

    @api.model
    def generate_supplier_code(self, supplier_name):
        """生成供应商编码"""
        return ''.join(lazy_pinyin(supplier_name, style=Style.FIRST_LETTER))

    def export_lot_to_excel(self):
        """导出标单到Excel"""
        # 生成Excel文件逻辑
        # 包含产品信息、价格、客户投标等
```

#### **主要功能**
- **SKU标准化**：统一产品编码规则
- **历史价格分析**：`report.bid.order.lot.line`模型
- **数据导出**：Excel格式的标单导出
- **供应商编码**：自动生成供应商代码

### 10.2 客户信用管理模块

#### 文件位置：`quotation_bid_customer_credit/models/customer_lot_credit.py`

```python
class CustomerLotCredit(models.Model):
    _name = 'customer.lot.credit'
    _description = 'Customer Lot Credit Management'

    customer = fields.Many2one('res.partner', string='Customer', required=True, index=True)
    lot_id = fields.Many2one('bid.order.lot', string='Lot', required=True, index=True)
    user_id = fields.Char(string='Java User ID')
    price_amount = fields.Float(string='Locked Amount', digits=(10, 2))
    state = fields.Selection([('unlock', 'Unlock'), ('lock', 'Lock')], string='Status', default='unlock')

    def _compute_get_customer_bid_state(self):
        """计算客户投标状态"""
        for rec in self:
            if rec.lot_id.customer_bid and rec.lot_id.customer_bid.name.id == rec.customer.id:
                rec.customer_bid_state = 'bidding'
            else:
                rec.customer_bid_state = 'not_bidding'
```

#### **主要功能**
- **额度锁定**：投标时自动锁定客户额度
- **额度释放**：投标结束后释放额度
- **状态同步**：与Java系统实时同步额度状态
- **批量操作**：支持批量额度管理

### 10.3 供应商扩展模块

#### 文件位置：`quotation_bid_supplier/models/bid_order_lot.py`

```python
class BidOrderLot(models.Model):
    _inherit = 'bid.order.lot'

    ebid_supplier_id = fields.Many2one(related='order_id.ebid_supplier_id', store=True, index=True)
    supplier_ids = fields.Many2many(related='order_id.supplier_ids')
    vendor_alias = fields.Char(related='supplier_id.vendor_alias', string="Vendor Alias")

    def customer_follow(self):
        """客户关注标单"""
        user_id = self.env.user.id
        if user_id not in self.follow_user_ids.ids:
            self.follow_user_ids = [(4, user_id)]

    def customer_remove_follow(self):
        """取消关注标单"""
        user_id = self.env.user.id
        if user_id in self.follow_user_ids.ids:
            self.follow_user_ids = [(3, user_id)]
```

#### **主要功能**
- **供应商管理**：扩展供应商信息
- **关注功能**：用户可以关注感兴趣的标单
- **优先级管理**：设置标单优先级
- **供应商别名**：支持供应商别名显示

## 11. 性能优化和监控

### 11.1 关键性能指标

- **标单发布响应时间**：< 3秒
- **投标数据同步延迟**：< 1秒
- **并发投标处理能力**：1000+ TPS
- **数据库查询优化**：索引覆盖率 > 95%

### 11.2 监控要点

- **MQ消息队列状态**
- **API接口响应时间**
- **数据库连接池状态**
- **文件存储空间使用**

### 11.3 性能优化策略

#### **数据库优化**
```sql
-- 关键索引
CREATE INDEX idx_bid_order_lot_state ON bid_order_lot(state);
CREATE INDEX idx_customer_bid_line_customer ON customer_bid_line(name, line_id);
CREATE INDEX idx_vendor_lot_rfq ON vendor_lot(rfq_id, bid_order_lot_id);
```

#### **缓存策略**
- **Redis缓存**：热点标单数据
- **应用缓存**：汇率、配置信息
- **查询缓存**：复杂报表查询结果

#### **异步处理**
- **队列任务**：大批量数据处理
- **后台任务**：邮件发送、文件生成
- **定时任务**：数据清理、统计计算

## 12. 总结

Galaxy ERP的报价投标系统是一个复杂的多方协作平台，具有以下特点：

### 12.1 架构特色

- **模块化设计**：清晰的模块分工和职责划分
- **多端集成**：支持Web、移动端、API多种接入方式
- **实时同步**：通过MQ实现与Java系统的实时数据同步
- **灵活扩展**：支持多种投标类型和业务场景

### 12.2 技术优势

- **高并发处理**：支持大量客户同时投标
- **数据一致性**：完整的事务管理和数据验证
- **系统集成**：与多个外部系统无缝集成
- **可维护性**：清晰的代码结构和文档

### 12.3 业务价值

- **提高效率**：自动化的投标流程
- **降低成本**：减少人工干预和错误
- **增强体验**：多端一致的用户体验
- **数据洞察**：完整的投标数据分析

---

**文档版本**：v1.0  
**最后更新**：2024-01-15  
**维护者**：Galaxy ERP开发团队
