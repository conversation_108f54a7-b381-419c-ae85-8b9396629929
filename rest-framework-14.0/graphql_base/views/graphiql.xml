<?xml version="1.0" encoding="utf-8"?>
<!--
    Copyright 2018 ACSONE SA/NV
    License LGPL-3.0 or later (http://www.gnu.org/licenses/lgpl).
    The html code below is originally Copyright (c) Facebook, Inc.
    published under Apache license.
-->
<odoo>
    <template id="graphiql" name="GraphiQL">
        <t t-call="web.layout">
            <t t-set="head">
                <style>
                body {
                    height: 100%;
                    margin: 0;
                    width: 100%;
                    overflow: hidden;
                }
                #graphiql {
                    height: 100vh;
                }
                </style>

                <!--
                This GraphiQL example depends on Promise and fetch, which are available in
                modern browsers, but can be "polyfilled" for older browsers.
                GraphiQL itself depends on React DOM.
                If you do not want to rely on a CDN, you can host these files locally or
                include them directly in your favored resource bunder.
                -->
                <link href="//cdn.jsdelivr.net/npm/graphiql@0.12.0/graphiql.css" rel="stylesheet"/>
                <script src="//cdn.jsdelivr.net/npm/whatwg-fetch@2.0.3/fetch.min.js"></script>
                <script src="//cdn.jsdelivr.net/npm/react@16.2.0/umd/react.production.min.js"></script>
                <script src="//cdn.jsdelivr.net/npm/react-dom@16.2.0/umd/react-dom.production.min.js"></script>
                <script src="//cdn.jsdelivr.net/npm/graphiql@0.12.0/graphiql.min.js"></script>
            </t>
            <t t-set="head" t-value="head"/>
        </t>
        <body>
            <div id="graphiql">Loading...</div>
            <script>

            /**
            * This GraphiQL example illustrates how to use some of GraphiQL's props
            * in order to enable reading and updating the URL parameters, making
            * link sharing of queries a little bit easier.
            *
            * This is only one example of this kind of feature, GraphiQL exposes
            * various React params to enable interesting integrations.
            */

            // Parse the search string to get url parameters.
            var search = window.location.search;
            var parameters = {};
            search.substr(1).split('&amp;').forEach(function (entry) {
                var eq = entry.indexOf('=');
                if (eq >= 0) {
                parameters[decodeURIComponent(entry.slice(0, eq))] =
                    decodeURIComponent(entry.slice(eq + 1));
                }
            });

            // if variables was provided, try to format it.
            if (parameters.variables) {
                try {
                parameters.variables =
                    JSON.stringify(JSON.parse(parameters.variables), null, 2);
                } catch (e) {
                // Do nothing, we want to display the invalid JSON as a string, rather
                // than present an error.
                }
            }

            // When the query and variables string is edited, update the URL bar so
            // that it can be easily shared
            function onEditQuery(newQuery) {
                parameters.query = newQuery;
                updateURL();
            }

            function onEditVariables(newVariables) {
                parameters.variables = newVariables;
                updateURL();
            }

            function onEditOperationName(newOperationName) {
                parameters.operationName = newOperationName;
                updateURL();
            }

            function updateURL() {
                var newSearch = '?' + Object.keys(parameters).filter(function (key) {
                    return Boolean(parameters[key]);
                }).map(function (key) {
                return encodeURIComponent(key) + '=' +
                    encodeURIComponent(parameters[key]);
                }).join('&amp;');
                history.replaceState(null, null, newSearch);
            }

            // Defines a GraphQL fetcher using the fetch API. You're not required to
            // use fetch, and could instead implement graphQLFetcher however you like,
            // as long as it returns a Promise or Observable.
            function graphQLFetcher(graphQLParams) {
                // This example expects a GraphQL server at the path /graphql.
                // Change this to point wherever you host your GraphQL server.
                data = new FormData();
                data.append('query', graphQLParams['query']);
                if (graphQLParams['variables']) {
                    data.append('variables', JSON.stringify(graphQLParams['variables']));
                }
                if (graphQLParams['operationName']) {
                    data.append('operationName', graphQLParams['operationName']);
                }
                data.append('csrf_token', odoo.csrf_token);
                return fetch('', {
                    method: 'post',
                    headers: {
                        'Accept': 'application/json',
                    },
                    body: data,
                    credentials: 'include',
                }).then(function (response) {
                    return response.text();
                }).then(function (responseBody) {
                    try {
                        return JSON.parse(responseBody);
                    } catch (error) {
                        return responseBody;
                    }
                });
            }

            // Render &lt;GraphiQL /> into the body.
            // See the README in the top level of this module to learn more about
            // how you can customize GraphiQL by providing different values or
            // additional child elements.
            ReactDOM.render(
                React.createElement(GraphiQL, {
                    fetcher: graphQLFetcher,
                    query: parameters.query,
                    variables: parameters.variables,
                    operationName: parameters.operationName,
                    onEditQuery: onEditQuery,
                    onEditVariables: onEditVariables,
                    onEditOperationName: onEditOperationName
                }),
                document.getElementById('graphiql')
            );
            </script>
        </body>
    </template>
</odoo>
