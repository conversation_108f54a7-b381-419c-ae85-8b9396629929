odoo.define('quotation_bid_tour', function(require) {
    "use strict";

    var core = require('web.core');
    var tour = require('web_tour.tour');

    var _t = core._t;

    tour.register('quotation_bid_tour', {
        test: true,
        url: "/web",
    },[
        {
            trigger: '.o_app[data-menu-xmlid="approvals.approvals_menu_root"]',
            content: 'open approvals app',
            run: 'click',
        },
    ]);

});
