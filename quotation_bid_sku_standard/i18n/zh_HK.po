# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* quotation_bid_sku_standard
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0+e-20230306\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-10 05:07+0000\n"
"PO-Revision-Date: 2023-05-10 05:07+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: quotation_bid_sku_standard
#: model_terms:ir.ui.view,arch_db:quotation_bid_sku_standard.quotation_history_bid_order_lot_line_search
msgid "ASUS"
msgstr ""

#. module: quotation_bid_sku_standard
#: model_terms:ir.ui.view,arch_db:quotation_bid_sku_standard.quotation_history_bid_order_lot_line_search
msgid "Apple"
msgstr ""

#. module: quotation_bid_sku_standard
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_report_bid_order_lot_line__auction_id
msgid "Auction Name"
msgstr ""

#. module: quotation_bid_sku_standard
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_bid_order_lot_line__lot_line_subtotal
msgid "Bid"
msgstr "出價"

#. module: quotation_bid_sku_standard
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_res_partner__customer_bid_amount_recently
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_res_users__customer_bid_amount_recently
msgid "Bid Amount(3 Months To Today)"
msgstr ""

#. module: quotation_bid_sku_standard
#: model:ir.model,name:quotation_bid_sku_standard.model_report_bid_order_lot_line
msgid "Bid History Price Information"
msgstr ""

#. module: quotation_bid_sku_standard
#: model:ir.model,name:quotation_bid_sku_standard.model_bid_order_lot_line
msgid "Bid Information"
msgstr "標單信息"

#. module: quotation_bid_sku_standard
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_report_bid_order_lot_line__bid_lot_id
msgid "Bid Lot"
msgstr "標單"

#. module: quotation_bid_sku_standard
#: model_terms:ir.ui.view,arch_db:quotation_bid_sku_standard.quotation_history_bid_order_lot_line_form
#: model_terms:ir.ui.view,arch_db:quotation_bid_sku_standard.quotation_history_bid_order_lot_line_tree
msgid "Bid Lot Line ID"
msgstr "出價單詳單號"

#. module: quotation_bid_sku_standard
#: model:ir.model,name:quotation_bid_sku_standard.model_bid_order
msgid "Bid Order"
msgstr "標單"

#. module: quotation_bid_sku_standard
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_report_item_customer_quotation__bid_order_lot_line_name
msgid "Bid Order Lot Line Name"
msgstr ""

#. module: quotation_bid_sku_standard
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_report_bid_order_lot_line__bid_price
msgid "Bid Price"
msgstr "出價"

#. module: quotation_bid_sku_standard
#: model_terms:ir.ui.view,arch_db:quotation_bid_sku_standard.bid_order_lot_line_form
#: model_terms:ir.ui.view,arch_db:quotation_bid_sku_standard.bid_order_lot_line_tree
msgid "Bid Price(USD)"
msgstr "供應商出價(美元)"

#. module: quotation_bid_sku_standard
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_report_bid_order_lot_line__bid_type
msgid "Bid Type"
msgstr "標單類型"

#. module: quotation_bid_sku_standard
#: model_terms:ir.ui.view,arch_db:quotation_bid_sku_standard.quotation_history_bid_order_lot_line_search
msgid "Bid Win"
msgstr "已中標"

#. module: quotation_bid_sku_standard
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_report_bid_order_lot_line__internal_brand
#: model_terms:ir.ui.view,arch_db:quotation_bid_sku_standard.quotation_history_bid_order_lot_line_search
msgid "Brand"
msgstr "品牌"

#. module: quotation_bid_sku_standard
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_report_bid_order_lot_line__capacity
msgid "Capacity"
msgstr "容量"

#. module: quotation_bid_sku_standard
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_report_bid_order_lot_line__version
msgid "Carrier"
msgstr "運營商"

#. module: quotation_bid_sku_standard
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_report_bid_order_lot_line__carrier_lock
msgid "Carrier Lock"
msgstr "運營商鎖"

#. module: quotation_bid_sku_standard
#: model_terms:ir.ui.view,arch_db:quotation_bid_sku_standard.quotation_history_bid_order_lot_line_tree
msgid "Clicks"
msgstr ""

#. module: quotation_bid_sku_standard
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_report_bid_order_lot_line__color
msgid "Color"
msgstr ""

#. module: quotation_bid_sku_standard
#: model:ir.model,name:quotation_bid_sku_standard.model_res_partner
msgid "Contact"
msgstr "聯繫人"

#. module: quotation_bid_sku_standard
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_report_bid_order_lot_line__create_uid
msgid "Created by"
msgstr ""

#. module: quotation_bid_sku_standard
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_report_bid_order_lot_line__create_date
msgid "Created on"
msgstr ""

#. module: quotation_bid_sku_standard
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_report_item_customer_quotation__name
msgid "Customer"
msgstr ""

#. module: quotation_bid_sku_standard
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_report_item_customer_quotation__customer_currency_id
msgid "Customer Currency"
msgstr ""

#. module: quotation_bid_sku_standard
#: code:addons/quotation_bid_sku_standard/models/report_bid_order_lot_line.py:0
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_report_item_customer_quotation__bid_price
#, python-format
msgid "Customer Quotation"
msgstr "客戶報價"

#. module: quotation_bid_sku_standard
#: model_terms:ir.ui.view,arch_db:quotation_bid_sku_standard.quotation_history_bid_order_lot_line_tree
msgid "Customer Quotation(HKD)"
msgstr "客戶報價（港幣）"

#. module: quotation_bid_sku_standard
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_bid_order__display_name
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_bid_order_lot__display_name
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_bid_order_lot_line__display_name
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_generate_bid_information__display_name
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_quotation_history_price__display_name
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_report_bid_order_lot_line__display_name
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_report_item_customer_quotation__display_name
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_res_partner__display_name
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_supplier_origin_upload__display_name
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_vendor_lot_line__display_name
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_vendor_request_quotation__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: quotation_bid_sku_standard
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_generate_bid_information__ebid_supplier_ids
msgid "EBID Supplier"
msgstr "在線供應商"

#. module: quotation_bid_sku_standard
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_bid_order__supplier_code
msgid "EBID Vendor Code"
msgstr ""

#. module: quotation_bid_sku_standard
#: model:ir.actions.act_window,name:quotation_bid_sku_standard.action_ebid_supplier
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_generate_bid_information__ebid_supplier_id
#: model:ir.ui.menu,name:quotation_bid_sku_standard.menu_ebid_supplier
msgid "Ebid Supplier"
msgstr "在線供應商"

#. module: quotation_bid_sku_standard
#: model_terms:ir.ui.view,arch_db:quotation_bid_sku_standard.quotation_history_bid_order_lot_line_form
msgid "Ext Grade"
msgstr "供應商成色"

#. module: quotation_bid_sku_standard
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_report_bid_order_lot_line__fmip
msgid "FMIP"
msgstr ""

#. module: quotation_bid_sku_standard
#: model:ir.model,name:quotation_bid_sku_standard.model_generate_bid_information
msgid "Generate Bid Information"
msgstr ""

#. module: quotation_bid_sku_standard
#: model_terms:ir.ui.view,arch_db:quotation_bid_sku_standard.quotation_history_bid_order_lot_line_search
msgid "Google"
msgstr ""

#. module: quotation_bid_sku_standard
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_report_bid_order_lot_line__grade
msgid "Grade"
msgstr ""

#. module: quotation_bid_sku_standard
#: model_terms:ir.ui.view,arch_db:quotation_bid_sku_standard.bid_order_lot_line_search
msgid "Group by"
msgstr ""

#. module: quotation_bid_sku_standard
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_bid_order__id
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_bid_order_lot__id
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_bid_order_lot_line__id
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_generate_bid_information__id
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_quotation_history_price__id
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_report_bid_order_lot_line__id
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_report_item_customer_quotation__id
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_res_partner__id
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_supplier_origin_upload__id
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_vendor_lot_line__id
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_vendor_request_quotation__id
msgid "ID"
msgstr ""

#. module: quotation_bid_sku_standard
#: model_terms:ir.ui.view,arch_db:quotation_bid_sku_standard.report_item_customer_quotation_view_tree
msgid "Item Quotation"
msgstr ""

#. module: quotation_bid_sku_standard
#: model_terms:ir.ui.view,arch_db:quotation_bid_sku_standard.quotation_history_bid_order_lot_line_search
msgid "LG"
msgstr ""

#. module: quotation_bid_sku_standard
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_bid_order____last_update
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_bid_order_lot____last_update
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_bid_order_lot_line____last_update
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_generate_bid_information____last_update
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_quotation_history_price____last_update
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_report_bid_order_lot_line____last_update
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_report_item_customer_quotation____last_update
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_res_partner____last_update
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_supplier_origin_upload____last_update
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_vendor_lot_line____last_update
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_vendor_request_quotation____last_update
msgid "Last Modified on"
msgstr "最後修改於"

#. module: quotation_bid_sku_standard
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_report_bid_order_lot_line__write_uid
msgid "Last Updated by"
msgstr ""

#. module: quotation_bid_sku_standard
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_report_bid_order_lot_line__write_date
msgid "Last Updated on"
msgstr ""

#. module: quotation_bid_sku_standard
#: model:ir.model,name:quotation_bid_sku_standard.model_bid_order_lot
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_report_item_customer_quotation__bid_order_lot_id
#: model_terms:ir.ui.view,arch_db:quotation_bid_sku_standard.bid_order_lot_line_search
msgid "Lot"
msgstr ""

#. module: quotation_bid_sku_standard
#: model:ir.actions.act_window,name:quotation_bid_sku_standard.bid_lot_detail_act_window
#: model:ir.ui.menu,name:quotation_bid_sku_standard.menu_bid_lot_detail
msgid "Lot Detail"
msgstr "詳單"

#. module: quotation_bid_sku_standard
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_report_bid_order_lot_line__lot_or_item_customer_hk_quotation
msgid "Lot Offer(HKD)"
msgstr ""

#. module: quotation_bid_sku_standard
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_report_bid_order_lot_line__internal_model
msgid "Model"
msgstr ""

#. module: quotation_bid_sku_standard
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_report_bid_order_lot_line__model_number
msgid "Model Number"
msgstr ""

#. module: quotation_bid_sku_standard
#: model_terms:ir.ui.view,arch_db:quotation_bid_sku_standard.quotation_history_bid_order_lot_line_search
msgid "Motorola"
msgstr ""

#. module: quotation_bid_sku_standard
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_report_bid_order_lot_line__name
msgid "Number"
msgstr ""

#. module: quotation_bid_sku_standard
#: model:ir.model.fields,help:quotation_bid_sku_standard.field_bid_order__supplier_code
msgid "Old EBID Supplier Code"
msgstr ""

#. module: quotation_bid_sku_standard
#: model_terms:ir.ui.view,arch_db:quotation_bid_sku_standard.quotation_history_bid_order_lot_line_search
msgid "OnePlus"
msgstr ""

#. module: quotation_bid_sku_standard
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_report_bid_order_lot_line__origin_data_id
msgid "Origin Record Id"
msgstr ""

#. module: quotation_bid_sku_standard
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_report_bid_order_lot_line__processed_sign
msgid "Processed Sign"
msgstr ""

#. module: quotation_bid_sku_standard
#: model_terms:ir.ui.view,arch_db:quotation_bid_sku_standard.bid_order_lot_line_search
msgid "Product"
msgstr ""

#. module: quotation_bid_sku_standard
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_report_bid_order_lot_line__quantity
msgid "QTY"
msgstr "數量"

#. module: quotation_bid_sku_standard
#: model:ir.actions.act_window,name:quotation_bid_sku_standard.product_quotation_history_act_window
#: model:ir.ui.menu,name:quotation_bid_sku_standard.menu_quotation_history
msgid "Quotation History"
msgstr "報價歷史"

#. module: quotation_bid_sku_standard
#: model:ir.model,name:quotation_bid_sku_standard.model_quotation_history_price
msgid "Quotation History Price"
msgstr "歷史報價"

#. module: quotation_bid_sku_standard
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_report_bid_order_lot_line__rfq_ids
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_report_bid_order_lot_line__rfq_ids_char
msgid "Quotation Number"
msgstr "詢價單號"

#. module: quotation_bid_sku_standard
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_report_bid_order_lot_line__lot_quotation_time
msgid "Quote Time"
msgstr "報價時間"

#. module: quotation_bid_sku_standard
#: model:ir.ui.menu,name:quotation_bid_sku_standard.menu_bid_report
msgid "Report"
msgstr "報告"

#. module: quotation_bid_sku_standard
#: model:ir.model,name:quotation_bid_sku_standard.model_report_item_customer_quotation
msgid "Report Item Customer Quotation"
msgstr ""

#. module: quotation_bid_sku_standard
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_report_bid_order_lot_line__sku_full_name
#: model_terms:ir.ui.view,arch_db:quotation_bid_sku_standard.bid_order_lot_line_search
msgid "SKU"
msgstr ""

#. module: quotation_bid_sku_standard
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_supplier_origin_upload__standard_id
msgid "SKU Standard"
msgstr "產品標準"

#. module: quotation_bid_sku_standard
#: model_terms:ir.ui.view,arch_db:quotation_bid_sku_standard.quotation_history_bid_order_lot_line_search
msgid "Samsung"
msgstr ""

#. module: quotation_bid_sku_standard
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_bid_order__ebid_supplier_id
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_bid_order__standard_id
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_generate_bid_information__standard_id
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_quotation_history_price__standard_id
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_report_bid_order_lot_line__standard_id
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_vendor_request_quotation__standard_id
#: model_terms:ir.ui.view,arch_db:quotation_bid_sku_standard.quotation_history_bid_order_lot_line_search
msgid "Standard"
msgstr "產品標準"

#. module: quotation_bid_sku_standard
#: model:ir.actions.server,name:quotation_bid_sku_standard.ir_cron_sync_quotation_history_data_ir_actions_server
#: model:ir.cron,cron_name:quotation_bid_sku_standard.ir_cron_sync_quotation_history_data
#: model:ir.cron,name:quotation_bid_sku_standard.ir_cron_sync_quotation_history_data
msgid "Sync quotaiotn history data"
msgstr ""

#. module: quotation_bid_sku_standard
#: model:ir.model,name:quotation_bid_sku_standard.model_vendor_request_quotation
msgid "Tender"
msgstr ""

#. module: quotation_bid_sku_standard
#: model:ir.model,name:quotation_bid_sku_standard.model_vendor_lot_line
msgid "Tender Detail"
msgstr ""

#. module: quotation_bid_sku_standard
#: model:ir.model,name:quotation_bid_sku_standard.model_supplier_origin_upload
msgid "Upload Supplier Origin "
msgstr ""

#. module: quotation_bid_sku_standard
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_bid_order__supplier_ids
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_report_bid_order_lot_line__vendor_id
msgid "Vendor"
msgstr "供應商"

#. module: quotation_bid_sku_standard
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_bid_order__supplier_alias
msgid "Vendor Alias"
msgstr "供應商別名"

#. module: quotation_bid_sku_standard
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_report_bid_order_lot_line__vendor_currency
msgid "Vendor Currency"
msgstr "供應商幣別"

#. module: quotation_bid_sku_standard
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_report_bid_order_lot_line__vendor_grade
msgid "Vendor Grade"
msgstr "供應商成色"

#. module: quotation_bid_sku_standard
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_bid_order_lot_line__rfq_ids
#: model_terms:ir.ui.view,arch_db:quotation_bid_sku_standard.bid_order_lot_line_form
#: model_terms:ir.ui.view,arch_db:quotation_bid_sku_standard.bid_order_lot_line_tree
msgid "Vendor RFQ"
msgstr "供應商詢價單"

#. module: quotation_bid_sku_standard
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_report_bid_order_lot_line__lot_bid_state
msgid "Winning State"
msgstr ""

#. module: quotation_bid_sku_standard
#: code:addons/quotation_bid_sku_standard/models/report_bid_order_lot_line.py:0
#, python-format
msgid "creating or updating quotation history report"
msgstr ""

#. module: quotation_bid_sku_standard
#: code:addons/quotation_bid_sku_standard/models/report_bid_order_lot_line.py:0
#, python-format
msgid "creating quotation history report"
msgstr ""

#. module: quotation_bid_sku_standard
#: model:ir.model.fields,help:quotation_bid_sku_standard.field_report_bid_order_lot_line__lot_or_item_customer_hk_quotation
msgid "from lot quotation if bid type is lot"
msgstr ""

#. module: quotation_bid_sku_standard
#: model:ir.model.constraint,message:quotation_bid_sku_standard.constraint_report_bid_order_lot_line_quotation_history_report_origin_data_id_unique
msgid "quotation history_report origin data id should be unique"
msgstr ""

#. module: quotation_bid_sku_standard
#: code:addons/quotation_bid_sku_standard/models/report_bid_order_lot_line.py:0
#, python-format
msgid "updated bid won result of quotation history report"
msgstr ""

#. module: quotation_bid_sku_standard
#: model:ir.model.fields,field_description:quotation_bid_sku_standard.field_generate_bid_information__bid_order_id
msgid "Auction"
msgstr "拍賣單"
