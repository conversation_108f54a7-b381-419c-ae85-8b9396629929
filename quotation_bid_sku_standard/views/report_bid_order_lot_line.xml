<odoo>
    <data>
        <!--产品历史价格开始-->
        <record model="ir.ui.view" id="quotation_history_bid_order_lot_line_tree">
            <field name="name">report_bid_order_lot_line_tree</field>
            <field name="model">report.bid.order.lot.line</field>
            <field name="arch" type="xml">
                <tree create="0" delete="0" edit="0" default_order='internal_brand,internal_model,lot_quotation_time desc'>
                    <field name="sku_full_name" optional='hide'/>
                    <field name="internal_brand"/>
                    <field name="internal_model"/>
                    <field name="model_number" optional="hide"/>
                    <field name="capacity" optional="hide"/>
                    <field name="color" optional="hide"/>
                    <field name="version" optional="hide"/>
                    <field name="carrier_lock" optional="hide"/>
                    <field name="fmip" optional="hide"/>
                    <field name="vendor_grade"/>
                    <field name="grade" optional="hide"/>
                    <field name="quantity"/>
                    <field name="bid_customer_id" optional="show"/>
                    <field name="lot_or_item_customer_hk_quotation" string='Customer Quotation(HKD)' decoration-galaxy-win="lot_bid_state=='won'"/>
                    <button name="action_item_customer_quotation" type="object" icon="fa-link" class="oe_stat_button">
                            <field name="lot_or_item_customer_hk_quotation" widget="statinfo" string="Clicks"/>
                    </button>
                    <field name="lot_quotation_time"/>
                    <field name='lot_bid_state' invisible='1'/>
                    <field name='name' string="Bid Lot Line ID" optional="hide"/>
                    <field name="standard_id" optional="hide"/>
                    <field name="bid_type" optional="hide"/>
                    <field name="bid_price" optional="hide"/>
                    <field name="vendor_id" optional="show"/>
                    <field name="vendor_currency" optional="hide"/>
                    <field name="bid_lot_id" string='Bid Lot' optional="hide"/>
                    <field name="lot_bid_state"/>
                </tree>
            </field>
        </record>

        <record model="ir.ui.view" id="quotation_history_bid_order_lot_line_form">
            <field name="name">report_bid_order_lot_line_form</field>
            <field name="model">report.bid.order.lot.line</field>
            <field name="arch" type="xml">   
                <form create="0" delete="0" edit="0">
                    <sheet>
                        <group>
                            <group>
                                <field name="sku_full_name"/>
                                <field name="vendor_grade" string='Ext Grade'/>
                                <field name="internal_brand"/>
                                <field name="internal_model"/>
                                <field name="model_number"/>
                                <field name="version"/>
                                <field name="color"/>
                                <field name="auction_id" options="{'no_open':True}"/>
                                <field name="bid_lot_id" options="{'no_open':True}"/>
                                <field name="lot_bid_state"/>
                                <field name="rfq_ids_char"/>
                            </group>
                            <group>
                                <field name="capacity"/>
                                <field name="carrier_lock"/>
                                <field name="grade"/>
                                <field name="fmip"/>
                                <field name="quantity"/>
                                <div class='oe_online'>
                                    <field name="lot_or_item_customer_hk_quotation" decoration-galaxy-win="lot_bid_state=='won'"/>
                                    <button name="action_item_customer_quotation" type="object" icon="fa-link" class="oe_stat_button">
                                    </button>
                                </div>
                                <field name="lot_quotation_time"/>
                                <field name='name' string="Bid Lot Line ID"/>
                                <field name="standard_id" options="{'no_open':True}"/>
                            </group> 
                        </group>
                    </sheet>
                </form>
            </field>
        </record>
        <record model='ir.ui.view' id='quotation_history_bid_order_lot_line_search'>
            <field name='name'>report_bid_order_lot_line_search</field>
            <field name='model'>report.bid.order.lot.line</field>
            <field name='arch' type='xml'>
                <search>
                    <field name='sku_full_name'/>
                    <field name="internal_brand"/>
                    <field name="internal_model"/>
                    <field name="model_number"/>
                    <field name="vendor_grade"/>
                    <field name="bid_lot_id"/>
                    <filter name='lot_bid_state' string='Bid Win' domain="[('lot_bid_state','=','won')]"/>
                    <filter string="Standard" name="group_by_standard" context="{'group_by':'standard_id'}"/>
                    <filter string="Brand" name="group_by_brand" context="{'group_by':'internal_brand'}"/>
                    <filter name="Apple" string="Apple" domain="[('internal_brand', '=', 'Apple')]" />
                    <filter name="Samsung" string="Samsung" domain="[('internal_brand', '=', 'Samsung')]" />
                    <filter name="OnePlus" string="OnePlus" domain="[('internal_brand', '=', 'OnePlus')]" />
                    <filter name="Google" string="Google" domain="[('internal_brand', '=', 'Google')]" />
                    <filter name="LG" string="LG" domain="[('internal_brand', '=', 'LG')]" />
                    <filter name="Motorola" string="Motorola" domain="[('internal_brand', '=', 'Motorola')]" />
                    <filter name="ASUS" string="ASUS" domain="[('internal_brand', '=', 'Asus')]" />

                    <!-- <filter string="Offer Time" name="group_by_offer_time" context="{'group_by':'lot_quotation_time'}"/> -->
                    <!-- <filter string="Bid Lot No" name="group_by_bid_lot_no" context="{'group_by':'bid_lot_no'}"/>
                    <filter string="Bid Lot Detail No" name="group_by_bid_lot_detail_no" context="{'group_by':'name'}"/> -->
                    <searchpanel>
                        <field name="standard_id" select="multi" icon="fa-tag" enable_counters="1"/>
                    </searchpanel>
                </search>
            </field> 
        </record>
        <!--产品历史价格结束-->
    </data>
</odoo>
