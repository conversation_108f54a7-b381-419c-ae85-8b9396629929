# -*- coding: utf-8 -*-

from . import controllers
from . import models
from . import wizard

from odoo import api, SUPERUSER_ID



def _create_default_user(cr, registry):
    env = api.Environment(cr, SUPERUSER_ID, {})
    hkd = env['res.currency'].with_context(active_test=False).search([('name','=',"HKD")])
    if not hkd:
        hkd = env['res.currency'].create({'name':"HKD",'symbol':"$"})
    else:
        if not hkd.active:
            hkd.active = True

    pricelist = env['product.pricelist'].search([('currency_id','=',hkd.id)])
    if not pricelist:
        pricelist = env['product.pricelist'].create({'name':'HDK','currency_id':hkd.id})
    else:
        pricelist = pricelist[0]
    default_user = env.ref("quotation_bid.default_quotation_customer")
    if default_user:
        default_user.write({'property_product_pricelist':pricelist.id})