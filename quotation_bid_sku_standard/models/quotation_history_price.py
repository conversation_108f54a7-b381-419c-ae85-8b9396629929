from operator import index
from odoo import models, fields, api, _
import logging

_logger = logging.getLogger(__name__)


class QuotationHistoryPrice(models.Model):
    _inherit = 'quotation.history.price'

    standard_id = fields.Many2one('bid.standard', string='Standard', required=True, index=True)

    def change_vendor_sku(self, new_vendor_id):
        # 临时方法，只是为了升级partner sku standard
        # 需要把之前修改sku_id属于USMP SF的，修改为SSM
        for rec in self:
            item_number = rec.vendor_sku_id.item_number
            new_sku_id = self.env['galaxy.vendor.sku'].search([('item_number', '=', item_number),
                                                               ('vendor_id', '=', new_vendor_id)]).id
            if new_sku_id:
                rec.vendor_sku_id = new_sku_id
            else:
                _logger.info(f'not found Item Number:{item_number} for vendor id:{new_vendor_id}')