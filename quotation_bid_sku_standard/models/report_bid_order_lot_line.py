from ast import Store
import datetime
import time
import logging
from pypinyin import Style, lazy_pinyin
from odoo import models, fields, api, _
from odoo.tools import DEFAULT_SERVER_DATETIME_FORMAT, misc

_logger = logging.getLogger(__name__)


class BidOrderLine(models.Model):
    _name = 'report.bid.order.lot.line'
    _description = 'Bid History Price Information'
    # report字段开始
    origin_data_id = fields.Integer('Origin Record Id', index=True)
    sku_full_name = fields.Char(string='SKU')
    internal_brand = fields.Char(string="Brand")
    internal_model = fields.Char(string="Model")
    model_number = fields.Char('Model Number')
    capacity = fields.Char('Capacity')
    color = fields.Char(string='Color')
    version = fields.Char('Carrier')
    carrier_lock = fields.Char(string="Carrier Lock")
    fmip = fields.Char('FMIP')
    vendor_grade = fields.Char(string='Vendor Grade')
    grade = fields.Char(string='Grade')
    quantity = fields.Integer(string='QTY')
    lot_or_item_customer_hk_quotation = fields.Float(string='Lot Offer(HKD)', help='from lot quotation if bid type is lot')
    lot_quotation_time = fields.Datetime(string="Quote Time")
    lot_bid_state = fields.Char(string="Winning State")
    name = fields.Char(string='Number')
    standard_id = fields.Many2one('bid.standard', string="Standard", index=True)
    # 供应商出价
    bid_price = fields.Float(string="Bid Price", digits=(16, 2))
    bid_lot_id = fields.Many2one('bid.order.lot', string='Bid Lot', index=True)
    bid_customer_id = fields.Many2one('res.partner', string='Customer', related="bid_lot_id.customer_id")
    bid_type = fields.Char(string="Bid Type")
    vendor_id = fields.Many2one('res.partner', string="Vendor", index=True)
    vendor_currency = fields.Many2one('res.currency', string="Vendor Currency")
    auction_id = fields.Many2one('bid.order', string='Auction Name', index=True)
    # 2023-04-24 rfq_ids因为性能问题弃用，改用rfq_ids_char，之后适当时间可以删除这个字段
    rfq_ids = fields.Many2many('vendor.request.quotation', string="Quotation Number")
    rfq_ids_char = fields.Char(string="Quotation Number")
    processed_sign = fields.Boolean('Processed Sign', default=False)

    def action_item_customer_quotation(self):
        view_id = self.sudo().env.ref("quotation_bid_sku_standard.report_item_customer_quotation_view_tree").id
        self.env['report.item.customer.quotation'].init(self.name)
        return {
                'name': _('Customer Quotation'),
                'type': 'ir.actions.act_window',
                'view_mode': 'list',
                'res_model': 'report.item.customer.quotation',
                'view_id': view_id,
                'target': 'new'
        }

    # report字段结束
    _sql_constraints = [('quotation_history_report_origin_data_id_unique', 'unique(origin_data_id)',
                         'quotation history_report origin data id should be unique')]

    def _compute_get_bid_price(self, rec):
        # Lot报价，可以取标单出价
        bid_price = 0
        usd_hkd_exchange_rate = 0
        vendor_currency_exchange_rate = 0
        usd_hkd_exchange_rate = rec.bid_order_lot_id.order_id.exchange_rate
        if rec.bid_order_lot_id.order_id.bid_type == 'lot' and rec.bid_order_lot_id.order_id.supplier_id.property_purchase_currency_id.name == 'USD':
            # subtotal是美元价格，因为供应商出现其他货币的时候，出价的汇率是设置到了rfq那边
            bid_price = rec.bid_order_lot_id.subtotal
        else:
            # subtotal是美元出价，这边需要转换为供应商币种出价
            # 获取rfq汇率
            if len(rec.bid_order_lot_id.vendor_lot_ids) > 0:
                vendor_currency_exchange_rate = rec.bid_order_lot_id.vendor_lot_ids[0].rfq_id.exchange_rate
            # 对于Lot报价的只有详单数量是1的时候，才能认为lot价格==详单价格
            if rec.bid_order_lot_id.order_id.bid_type == 'lot' and len(rec.bid_order_lot_id.bid_order_lot_line_ids) == 1:
                bid_price = rec.bid_order_lot_id.subtotal * vendor_currency_exchange_rate
            elif rec.bid_order_lot_id.order_id.bid_type == 'items':
                bid_price = rec.subtotal * vendor_currency_exchange_rate
        return bid_price

    def _get_lot_or_item_hk_quotation(self, item):
        if len(item.bid_order_lot_id.bid_order_lot_line_ids) == 1 and item.bid_order_lot_id.order_id.bid_type == 'lot':
            return item.bid_order_lot_id.customer_hk_bid
        else:
            return item.customer_hk_quotation

    def report_load_data(self, minutes_delay=30):
        cur_id = self.search([], order='id desc', limit=1).origin_data_id
        # 报告数据延时时间
        self.env['bid.order.lot.line'].invalidate_cache()
        if not cur_id:
            # 获取报价完毕的信息
            # 第一次初始化的时候，加载全部已经报价的数据
            rec = self.env['bid.order.lot.line'].search([('bid_order_lot_id.order_id.state', 'in',
                                                          ('confirm', 'complete', 'done', 'cancel')),
                                                         ('bid_order_lot_id.order_id.active', '=', True),
                                                         '|',
                                                         ('bid_order_lot_id.customer_hk_bid', '>', 0),
                                                         ('customer_hk_quotation', '>', 0)
                                                         ],
                                                        order='id')
            rec += self.env['bid.order.lot.line'].search([('bid_order_lot_id.order_id.state', 'in', ('draft', 'bidding')),
                                                          ('bid_order_lot_id.order_id.active', '=', True)],
                                                         order='id')
        else:
            """ ('create_date', '<=', (datetime.datetime.now() - datetime.timedelta(minutes=minutes_delay)).strftime(DEFAULT_SERVER_DATETIME_FORMAT)) """
            rec = self.env['bid.order.lot.line'].search([('id', '>', cur_id), ('bid_order_lot_id.order_id.active', '=', True)],
                                                        order='id', limit=6000)
        report_rec_list = []
        for item in rec:
            report_rec_list.append({
                'origin_data_id': item.id,
                'sku_full_name': item.sku_full_name,
                'internal_brand': item.internal_sku.brand.name,
                'internal_model': item.internal_sku.model_name.name,
                'model_number': item.model_number,
                'capacity': item.capacity,
                'color': item.color,
                'version': item.version,
                'carrier_lock': item.carrier_lock,
                'fmip': item.fmip,
                'vendor_grade': item.vendor_grade,
                'grade': item.grade,
                'quantity': item.quantity,
                # 默认创建的时候设置报价为0，因为截止之前，不应该给销售看到客户报价数据
                # 'lot_or_item_customer_hk_quotation': self._get_lot_or_item_hk_quotation(item),
                'lot_or_item_customer_hk_quotation': 0,
                'lot_quotation_time': item.bid_order_lot_id.customer_bid.ubid_time,
                'lot_bid_state': item.bid_order_lot_id.customer_bid.state,
                'name': item.name,
                'standard_id': item.bid_order_lot_id.order_id.standard_id.id,
                # 'bid_price': self._compute_get_bid_price(item),
                # 默认创建的时候设置报价为0，因为截止之前，不应该给销售看到客户报价数据
                'bid_price': 0,
                'bid_lot_id': item.bid_order_lot_id.id,
                'bid_type': item.bid_order_lot_id.order_id.bid_type,
                'vendor_id': item.bid_order_lot_id.order_id.supplier_id.id,
                'vendor_currency': item.bid_order_lot_id.order_id.supplier_id.property_purchase_currency_id.id,
                'auction_id': item.bid_order_lot_id.order_id.id,
                'rfq_ids_char': ','.join(item.bid_order_lot_id.order_id.origin_id.mapped('name')),
            })
        self.create(report_rec_list)
        _logger.info(_('creating quotation history report'))
        self.report_update_quotation_data()

    def report_update_quotation_data(self, minutes_delay=30):
        # 到截止时间以后更新一下数据
        # 可以删除报价为0的数据
        self.env['bid.order.lot.line'].invalidate_cache()
        lot_ids = self.env['report.bid.order.lot.line'].search(
            [('processed_sign', '=', False),
             ('auction_id.customer_time_remaining', '<=',
              (datetime.datetime.now() - datetime.timedelta(minutes=minutes_delay)).strftime(DEFAULT_SERVER_DATETIME_FORMAT))],
            order='id').mapped('origin_data_id')
        rec = self.env['bid.order.lot.line'].search([('id', 'in', lot_ids)], order='id')
        create_report_rec_list = []
        unlink_report_rec_list = self.env['report.bid.order.lot.line']
        for item in rec:
            report_rec = {
                'origin_data_id': item.id,
                'sku_full_name': item.sku_full_name,
                'internal_brand': item.internal_sku.brand.name,
                'internal_model': item.internal_sku.model_name.name,
                'model_number': item.model_number,
                'capacity': item.capacity,
                'color': item.color,
                'version': item.version,
                'carrier_lock': item.carrier_lock,
                'fmip': item.fmip,
                'vendor_grade': item.vendor_grade,
                'grade': item.grade,
                'quantity': item.quantity,
                'lot_or_item_customer_hk_quotation': self._get_lot_or_item_hk_quotation(item),
                'lot_quotation_time': item.bid_order_lot_id.customer_bid.ubid_time,
                'lot_bid_state': item.bid_order_lot_id.customer_bid.state,
                'name': item.name,
                'standard_id': item.bid_order_lot_id.order_id.standard_id.id,
                'bid_price': self._compute_get_bid_price(item),
                'bid_lot_id': item.bid_order_lot_id.id,
                'bid_type': item.bid_order_lot_id.order_id.bid_type,
                'vendor_id': item.bid_order_lot_id.order_id.supplier_id.id,
                'vendor_currency': item.bid_order_lot_id.order_id.supplier_id.property_purchase_currency_id.id,
                'auction_id': item.bid_order_lot_id.order_id.id,
                'rfq_ids_char': ','.join(item.bid_order_lot_id.order_id.origin_id.mapped('name')),
                'processed_sign': True,
            }
            check_rec = self.search([('origin_data_id', '=', item.id)])
            if not check_rec and report_rec['lot_or_item_customer_hk_quotation'] > 0:
                create_report_rec_list.append(report_rec)
            else:
                # 如果报价为0，就删除
                if report_rec['lot_or_item_customer_hk_quotation'] == 0:
                    unlink_report_rec_list += check_rec
                else:
                    check_rec.write(report_rec)
        if unlink_report_rec_list.ids:
            self.env.cr.execute("""
                DELETE FROM report_bid_order_lot_line WHERE id IN %s
            """, (tuple(unlink_report_rec_list.ids),))
        if create_report_rec_list:
            self.create(create_report_rec_list)
        _logger.info(_('creating or updating quotation history report'))

    def report_update_bind_won_data(self, minutes_delay=30):
        Icp = self.env["ir.config_parameter"].sudo()
        last_cust_bid_win_id = Icp.get_param(
            'last_cust_bid_win_id',
            0,
        )
        self.env['customer.bid.line'].invalidate_cache()
        win_bids = self.env['customer.bid.line'].search(
            [('state', '=', 'won'), ('id', '>', last_cust_bid_win_id)],
            order='id')
        if win_bids:
            last_cust_bid_win_id = win_bids[-1].id
            Icp.set_param('last_cust_bid_win_id', last_cust_bid_win_id)
        lot_ids = win_bids.line_id.ids
        rec = self.env['bid.order.lot.line'].search([('bid_order_lot_id.id', 'in', lot_ids)], order='id')
        check_rec_map = tuple(self.search([('origin_data_id', 'in', rec.ids)]).mapped('origin_data_id'))
        update_ids = []
        for item in rec:
            check_rec = item.id in check_rec_map
            if check_rec:
                update_ids.append(item.id)
        if update_ids:
            self.env.cr.execute("""
                UPDATE report_bid_order_lot_line SET lot_bid_state = 'won' WHERE origin_data_id IN %s""",
                                (tuple(update_ids),))
        _logger.info(_('updated bid won result of quotation history report'))
