from odoo import models, fields, api
import time
import logging
from pypinyin import Style, lazy_pinyin

_logger = logging.getLogger(__name__)


class BidOrderLine(models.Model):
    _inherit = 'bid.order.lot.line'

    rfq_ids = fields.Many2many(related='bid_order_lot_id.order_id.origin_id')
    lot_line_subtotal = fields.Float(related='bid_order_lot_id.subtotal')

    def change_vendor_sku(self, new_vendor_id):
        # 临时方法，只是为了升级partner sku standard
        # 需要把之前修改sku_id属于USMP SF的，修改为SSM
        for rec in self:
            item_number = rec.sku_id.item_number
            new_sku_id = self.env['galaxy.vendor.sku'].search([('item_number', '=', item_number),
                                                               ('vendor_id', '=', new_vendor_id)]).id
            if new_sku_id:
                rec.sku_id = new_sku_id
            else:
                _logger.info(f'not found Item Number:{item_number} for vendor id:{new_vendor_id}')

    def _get_lot_id(self):
        for line in self:
            # 此方法未被使用，应该可以删除
            if line.order_id.supplier_code:
                line.lot_id = self._generate_lot(line.order_id.supplier_code, str(line.id))
            else:
                pass
                # 以下更新vendor code是否合理? 生成供应商各个汉字的首字母组成供应商编码
                # 并且此时EBID端并未更新这个supplier code
                supplier_code = self.generate_supplier_code(line.order_id.standard_id.name)
                line.order_id.supplier_id.ref = supplier_code

    def _get_group_id(self):
        for order in self:
            # 此方法未被使用，应该可以删除
            # 以下更新vendor code是否合理? 生成供应商各个汉字的首字母组成供应商编码
            # 并且此时EBID端并未更新这个supplier code
            if order.supplier_id:
                if order.supplier_code:
                    order.group_id = self._generate_group(order.supplier_code, str(order.id))
                else:
                    supplier_code = self.generate_supplier_code(order.supplier_id.name)
                    order.supplier_id.ref = supplier_code

    def check_sku(self):
        """
            检查"标单"数据是否已存在galaxy_vendor_sku表中，
            若存在,则建立关联关系,否则生成对应数据。
            检查规则：如果存在供应商sku编码，通过供应商sku编码匹配,否则通过供应商sku匹配
        """
        galaxy_vendor_sku = self.env['galaxy.vendor.sku'].search([('standard_id', '=', self[0].order_id.standard_id.id)])
        vendor_item_number = {g.item_number: g.id for g in galaxy_vendor_sku if g.item_number}
        galaxy_vendor_sku = {g.vendor_sku: g.id for g in galaxy_vendor_sku if g.vendor_sku}
        for line in self:
            if line.item_no and line.disposition:
                if line.item_no + line.disposition in vendor_item_number:
                    line.update({'sku_id': vendor_item_number[line.item_no + line.disposition]})
            elif line.item_no and not line.dispositon:
                if line.item_no in vendor_item_number:
                    line.update({'sku_id': vendor_item_number[line.item_no]})
            else:
                if line.vendor_sku in galaxy_vendor_sku:
                    line.update({'sku_id': galaxy_vendor_sku[line.vendor_sku]})
            if not line.sku_id:
                self.generate_sku(line)

    def generate_sku(self, line):
        """
        生成galaxy_vendor_sku数据
        """
        if line:
            standard_id = line.order_id.standard_id.id
            vendor_id = line.order_id.supplier_id.id
            # 此时的vendor_id只是合并询价单过程中的某一家供应商id,选择这个供应商的目的是为了生成默认的运费
            re = self.env['galaxy.vendor.sku'].create({
                'vendor_id': vendor_id,
                'standard_id': standard_id,
                'model': line.model,
                'vendor_sku': line.vendor_sku,
                'brand': line.make,
                'carrier': line.carrier,
                'item_number': self._get_item_no(line)
            })
            line.sku_id = re.id
