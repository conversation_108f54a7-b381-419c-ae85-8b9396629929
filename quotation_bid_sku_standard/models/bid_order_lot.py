from odoo import models, fields, api, _
import datetime
import time
import logging
from pytz import timezone
_logger = logging.getLogger(__name__)
import time
from pypinyin import Style, lazy_pinyin
import uuid
from odoo.addons.base_galaxy.lib.base10toN import base10_to_32
from odoo.addons.base_galaxy.models.galaxy_func import time_costing

import datetime


class BidOrderLot(models.Model):
    _inherit = 'bid.order.lot'

    @api.model
    def generate_supplier_code(self, supplier_name):
        """
        生成供应商各个汉字的首字母组成供应商编码
        """
        return ''.join(lazy_pinyin(supplier_name, style=Style.FIRST_LETTER))
