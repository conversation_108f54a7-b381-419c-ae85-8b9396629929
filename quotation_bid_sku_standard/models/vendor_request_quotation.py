# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.osv import expression
import xlrd
import time
import uuid
import logging
import base64
import pandas as pd
from openpyxl import load_workbook
from io import BytesIO
from odoo.addons.base_galaxy.lib.base10toN import base10_to_32
import csv23
from csv23 import write_csv
from openpyxl.styles import Border, Side

_logger = logging.getLogger(__name__)


class VendorRequestQuotation(models.Model):
    _inherit = 'vendor.request.quotation'
    # 移动到了quotaion_bid,修改请到quotation_bid
    standard_id = fields.Many2one('bid.standard', string='Standard', required=True)
