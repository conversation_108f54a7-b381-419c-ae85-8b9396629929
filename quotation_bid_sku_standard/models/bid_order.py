from odoo import models, fields, api, _
import datetime
import time
import logging
import time
import uuid
import xlrd
import base64
from odoo.addons.base_galaxy.lib.base10toN import base10_to_32
from openpyxl import load_workbook
from io import BytesIO
from pytz import timezone

_logger = logging.getLogger(__name__)

'''
1:需要更新bid.standard 的 vendor_ref_code字段为res.partner.ref字段
2:需要更新历史报价,增加standard_id字段
'''
class BidOrder(models.Model):
    """
    标单用与关联询价单
    """
    _inherit = 'bid.order'

    standard_id = fields.Many2one('bid.standard', string='Standard', required=True)
    # 此处让用户选择supplier id的目的是为了在合并询价单的时候(比如多个供应商)选择某个供应商的运费当作标单组的默认运费
    # supplier_id = fields.Many2one('res.partner', string='Vendor', required=False)
    ebid_supplier_id = fields.Many2one('ebid.supplier',
                                       string="Ebid Supplier",
                                       )
    supplier_code = fields.Char(related='ebid_supplier_id.vendor_ref_code')
    supplier_ids = fields.Many2many('res.partner', string="Vendor", compute='_compute_supplier_ids')
    supplier_alias = fields.Char(string="Vendor Alias", compute='_compute_get_supplier_alias')

    @api.depends('supplier_ids')
    def _compute_get_supplier_alias(self):
        for rec in self:
            rec.supplier_alias = ','.join(['' if item is False else item for item in rec.supplier_ids.mapped('vendor_alias')])

    @api.depends('origin_id')
    def _compute_supplier_ids(self):
        for rec in self:
            rec.supplier_ids = tuple(rec.origin_id.supplier.ids)

    def _history_price(self, customer_bids):
        group = self.env['sync.vendor.sku.group'].search([
                                                        ('vendors', 'parent_of', self.supplier_id.ids)],
                                                        limit=1)
        vendor_ids = group.vendors.ids if group.vendors else self.supplier_id.ids
        # vendor_ids继续保存的目的是为了在出现问题的时候roll back
        data = []
        for c in customer_bids:
            if len(c.bid_order_lot_line_ids) == 1:
                vendor_sku_id = c.bid_order_lot_line_ids.sku_id
                if c.customer_bid.customer_currency_id.name == "HKD":
                    unit_price = c.customer_bid.bid_price
                elif c.customer_bid.foreign_currency_id.name == "HKD":
                    unit_price = c.customer_bid.foreign_bid_price
                else:
                    unit_price = 0
                    # 如果不存在HKD,则不处理
                if unit_price:
                    data.append({
                        'lot_id': c.id,
                        'standard_id': c.order_id.standard_id.id,
                        'vendor_ids': tuple(vendor_ids),
                        'item_number': vendor_sku_id.item_number,
                        'unit_price': unit_price,
                        'customer_bid': c.customer_bid.id,
                        'history_create_date': datetime.datetime.now(),
                        'state': 'waiting'
                    })
                else:
                    pass
            else:
                pass
                # 当详单数量大于1时，不做处理
        if data:
            self.env['quotation.history.price'].create(data)

    def upload_bid_result(self):
        active_ids = self._context.get('active_ids')
        orders = self.search([('id', 'in', active_ids)])
        not_confirm_orders = [order for order in orders if order.status == 'cancel' or order.status == False]
        if not_confirm_orders:
            raise models.UserError('投标单需要先确认后，才可上传中标信息！')

        standard_id = set(orders.supplier_id.ids)
        if len(standard_id) > 1:
            raise models.UserError('需要选择相同的standard投标单！')
       
        return self._get_bid_result_window(standard_id.pop(), active_ids)

    def form_upload_bid_result(self):
        if self.status == 'confirm':
            return self._get_bid_result_window(self.standard_id.id, [self.id])
        raise models.UserError('投标单还未确认,确认后才可上传中标信息。')
    
    def _get_bid_result_window(self, standard_id, orders):
        view_id = self.sudo().env.ref('quotation_bid.supplier_bid_result_upload_form').id
        return {
            'name': 'Open Bid',
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'res_model': 'supplier.bid.result.upload',
            'view_id': view_id,
            'context': {
                'default_standard_id': standard_id,
                'default_order_ids': tuple(orders)
            },
            'target': 'new'
        }

    def action_sku(self):
        action = self.sudo().env.ref('partner_sku_standard.action_window_partner_sku_validate').sudo().read()[0]
        lines = self.bid_order_ids.bid_order_lot_line_ids.filtered(
            lambda line: line.match_state == 'unmatched' or not line.match_state)
        sku_id = [line.sku_id.id for line in lines]
        action['context'] = {}
        action['domain'] = [('id', 'in', sku_id)]
        action['limit'] = len(sku_id)
        return action
