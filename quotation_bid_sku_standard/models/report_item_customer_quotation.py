from odoo import models, fields, tools


class ReportItemCustomerQuotation(models.Model):
    _name = 'report.item.customer.quotation'
    _description = 'Report Item Customer Quotation'
    _auto = False

    name = fields.Many2one('res.partner', string='Customer')
    customer_currency_id = fields.Many2one('res.currency', string='Customer Currency')
    bid_price = fields.Monetary(string='Customer Quotation', currency_field='customer_currency_id')
    bid_order_lot_id = fields.Many2one('bid.order.lot', string='Lot')
    bid_order_lot_line_name = fields.Char()


    def init(self, name=''):
        tools.drop_view_if_exists(self._cr, 'report_item_customer_quotation')
        sql = """
            create or replace view report_item_customer_quotation  as (
            select 
                row_number() over() as id,
                ciq.name,
                ciq.customer_currency_id,
                ciq.bid_price,
                boll.bid_order_lot_id as bid_order_lot_id,
                boll.name as bid_order_lot_line_name
            from customer_items_quotation ciq
            join bid_order_lot_line boll on boll.id = ciq.lot_line_id
            where boll.name=%s
            union all
            select 
                row_number() over() as id,
                cbl.name,
                cbl.customer_currency_id,
                cbl.bid_price,
                bol.id as bid_order_lot_id,
                boll.name as bid_order_lot_line_name
            from customer_bid_line cbl 
            join bid_order_lot bol on bol.id = cbl.line_id
            join bid_order_lot_line boll on boll.bid_order_lot_id = bol.id 
            join bid_order bo on bo.id = bol.order_id
            where bo.bid_type='lot' and boll.name=%s)
        """ %(repr(name), repr(name))

        self._cr.execute(sql)


    





