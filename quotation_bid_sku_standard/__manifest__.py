# -*- coding: utf-8 -*-
{
    'name':
    "Galaxy Bidding support Standard SKU",
    'summary':
    """
        Galaxy Bidding support Standard SKU
       """,
    'description':
    """
        Galaxy Bidding support Standard SKU
    """,
    'author':
    "<PERSON>",
    'website':
    "",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/14.0/odoo/addons/base/data/ir_module_category_data.xml
    # for the full list
    'category':
    'Galaxy_ERP/Galaxy_ERP',
    'version':
    '0.1',

    # any module necessary for this one to work correctly
    'depends': [
        'base', 'quotation_bid', 'partner_sku_standard', 'base_bid_management', 'base_galaxy'
    ],

    # always loaded
    'data': [
        'security/ir.model.access.csv',
        'views/vendor_request_quotation.xml',
        'views/bid_order_views.xml',
        'views/bid_order_lot_line.xml',
        'views/menu.xml',
        'views/report_bid_order_lot_line.xml',
        'data/cron.xml',
        'wizard/supplier_upload_views.xml',
        'wizard/generate_bid_information_views.xml',
        'views/report_item_customer_quotation_views.xml',
    ],
    'qweb': [
    
    ],
    'application': False,
}
