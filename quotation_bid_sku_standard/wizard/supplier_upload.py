#!/usr/bin/env python
# coding=utf-8
# author: zhuang

import io
import re
import time
import base64
import threading
import xlrd
import copy
import pandas as pd
import numpy as np
import logging
from os import execl, execle

from odoo import fields, models, api, _

_logger = logging.getLogger(__name__)


class SupplierOriginUpload(models.TransientModel):
    _inherit = 'supplier.origin.upload'

    # 增加standard支持
    standard_id = fields.Many2one('bid.standard', string='SKU Standard')

    @api.onchange('supplier_template')
    def _onchange_supplier_template(self):
        if self.supplier_template.standard_id.id:
            self.standard_id = self.supplier_template.standard_id.id

    def parse_each_excel_with_template(self):
        error_log = []
        next = {
            'name': 'Vendor RFQ',
            'type': 'ir.actions.act_window',
            'res_model': 'vendor.request.quotation',
            'views': [[False, "list"], [False, "form"]],
            'context': {
                'tree_view_ref': 'quotation_bid.vendor_request_quotation_tree',
                'form_view_ref': 'quotation_bid.vendor_request_quotation_form'
            },
            'target': 'current',
        }
        return_data = {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Vendor RFQ Upload Result',
                'next': next,
            }
        }
        for e in self.excel:
            if e.datas:
                whole_vals = {
                    'excel': e.datas,
                    'file_name': e.name,
                    'note': self.note,
                    'manual_create': False,
                    'supplier': self.supplier_id.id,
                    'supplier_time_remaining': self.supplier_time_remaining,
                    'supplier_template': self.supplier_template.id,
                    'standard_id': self.standard_id.id,
                    'bid_type': self.supplier_template.bid_type,
                }
                vendor_lot_data = self.parse_each_excel_4_quotation_bid(datas=e.datas, file_name=e.name)
                if vendor_lot_data:
                    whole_vals['vendor_lot_ids'] = vendor_lot_data
                    self._create_quotation_bid(whole_vals)
                else:
                    error_log.append({'error': 'Empty Data', 'name': e.name})
        if error_log:
            message = f"""
                    Upload Failed: {len(error_log)}/{len(self.excel)},
                    Error Files: {','.join([log.get('name') for log in error_log])}"""
            return_data['params'].update({
                'message': message,
                'type': 'danger',
                'sticky': True,
            })
        else:
            message = f"Upload Success: {len(self.excel)}/{len(self.excel)}"
            return_data['params'].update({
                'message': message,
                'type': 'success',
            })
        return return_data

    def _check_galaxy_vendor_sku_records(self, template, new_row, origin_line_dict):
        # check galaxy_vendor_sku,if match,update origin_line_dict
        rec = self.env['galaxy.vendor.sku'].search([('standard_id', '=', self.standard_id.id),
                                                    ('item_number', '=', origin_line_dict['item_no']), ('state', '=', 'match')],
                                                   limit=1)
        if rec:
            origin_line_dict['make'] = rec.supplier_brand.name
            origin_line_dict['model'] = rec.supplier_model.name
            attribute_ids_tmp = []
            for line in rec.attr_line:
                vals = {
                    'attr': line.attr.id,
                    'origin_data': line.origin_attr_value,
                    'alias': line.supplier_attr_value.id,
                    'inner_attr_value': line.inner_attr_value.id,
                }
                attribute_ids_tmp.append((0, 0, vals))
            origin_line_dict['attribute_ids'] = attribute_ids_tmp
        return origin_line_dict

    def _extract_each_row_brand_info(self, template, new_row, origin_line_dict):
        # TODO 逻辑不严密
        if not template.brand_id or not template.brand_rule:
            if template.attr_match_type == 'bm':
                raise models.ValidationError('brand message in supplier template is not setting correct,please check!')
        ori_data = self.splice_string_4_template_many2many_field(new_row, template.brand_id)
        if template.brand_rule == 'match':
            res_obj = self.env['supplier.brand.correspond.rule'].search([('origin_data', '=', ori_data),
                                                                         ('standard_id', '=', self.standard_id.id),
                                                                         ('status', '=', 'match')])
            if res_obj:
                origin_line_dict['origin_data_brand'] = res_obj.origin_data
                origin_line_dict['make'] = res_obj.supplier_brand.name
            else:
                if template.brand_default:
                    origin_line_dict['make'] = template.brand_default.name
                else:
                    origin_line_dict['make'] = ''
                origin_line_dict['origin_data_brand'] = ori_data
        elif template.brand_rule == 'all':
            origin_line_dict['make'] = ori_data
        return origin_line_dict

    def _extract_each_row_device_info(self, template, new_row, origin_line_dict):
        # TODO 逻辑不严密
        if not template.device_rule or not template.device:
            if template.attr_match_type == 'bm':
                raise models.ValidationError('model message in supplier template is not setting correct,please check!')
        ori_data = self.splice_string_4_template_many2many_field(new_row, template.device)
        if template.device_rule == 'match':
            res_obj = self.env['supplier.model.correspond.rule'].search([('origin_data', '=', ori_data),
                                                                         ('standard_id', '=', self.standard_id.id),
                                                                         ('status', '=', 'match')])
            if res_obj:
                origin_line_dict['origin_data_model'] = res_obj.origin_data
                origin_line_dict['model'] = res_obj.supplier_model.name
            else:
                if template.device_default:
                    origin_line_dict['model'] = template.device_default.name
                else:
                    origin_line_dict['model'] = ''
                origin_line_dict['origin_data_model'] = ori_data
        elif template.device_rule == 'all':
            origin_line_dict['model'] = ori_data
        return origin_line_dict

    def parse_doc_excel_with_template(self):
        rec_id = False
        for e in self.excel:
            if e.datas:
                whole_vals = {
                    'excel': e.datas,
                    'file_name': e.name,
                    'note': self.note,
                    'manual_create': False,
                    'supplier': self.supplier_id.id,
                    'supplier_time_remaining': self.supplier_time_remaining,
                    'supplier_template': self.supplier_template.id,
                    'standard_id': self.standard_id.id,
                }
                vendor_lot_data = self.parse_each_excel_4_quotation_bid(datas=e.datas, file_name=e.name)
                if vendor_lot_data:
                    whole_vals['vendor_lot_ids'] = vendor_lot_data
                    rec_id = self._create_quotation_bid(whole_vals).id
        return rec_id
