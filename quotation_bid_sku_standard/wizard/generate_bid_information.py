# from odoo import models,fields,api,_
from pyexpat import model
from odoo import models, fields, api
from odoo import _
import logging
import math
import time
from odoo.addons.base_galaxy.lib.base10toN import base10_to_32

_logger = logging.getLogger(__name__)


class GenerateBidInformation(models.TransientModel):
    _inherit = 'generate.bid.information'

    # 增加standard支持,已经移动到quotation_bid模块，修改请到quotation_bid
    standard_id = fields.Many2one('bid.standard', string='Standard')
    ebid_supplier_ids = fields.Many2many(related='supplier_id.ebid_supplier_ids')
    ebid_supplier_id = fields.Many2one('ebid.supplier',
                                       string="Ebid Supplier",
                                       domain="[('id','in',ebid_supplier_ids)]")
    bid_order_id = fields.Many2one('bid.order', string='Auction', domain=[('state', 'in', ('draft', 'bidding', 'done'))])
