# Copyright 2019 ACSONE SA/NV
# License LGPL-3.0 or later (http://www.gnu.org/licenses/lgpl).
# -*- coding: utf-8 -*-
# 1 : imports of python lib
from marshmallow import fields
# 2 : imports of odoo
# 3 : imports from odoo modules
from odoo.addons.datamodel.core import Datamodel


class vendorReceiveInfo(Datamodel):
    _name = "vendor.receive.info"

    token = fields.String(required=True, allow_none=False)
    vendor_id = fields.String(required=True, allow_none=False)
    vendor_data = fields.Str(required=True, allow_none=False)
