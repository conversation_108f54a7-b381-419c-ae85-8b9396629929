#!/usr/bin/env python
# coding=utf-8
# author: zhuangweijia

import logging
import time
import random
import json
import demjson
from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
# from ...base_galaxy.models.public_func import display_notification
from odoo.addons.base_galaxy.models.public_func import display_notification
from odoo.addons.base_galaxy.models.gol import get_value,set_value,del_value

_logger = logging.getLogger(__name__)

class galaxy_vendor_sku(models.Model):
    _inherit = "galaxy.vendor.sku"
    _order = 'write_date desc'
    
    document_id = fields.Many2many('documents.document', string='Document')
