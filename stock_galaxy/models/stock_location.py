# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo import _, api, fields, models
from odoo.exceptions import UserError
from odoo.osv import expression


class Location(models.Model):
    _inherit = "stock.location"
    _description = "Inventory Locations"
    
    @api.model
    def default_get(self, fields):
        res = super(Location, self).default_get(fields)
        if 'barcode' in fields and 'barcode' not in res and res.get('complete_name'):
            res['barcode'] = res['complete_name']
        return res

    barcode = fields.Char('Barcode', copy=False,compute='_compute_barcode',store=True)
    

    @api.depends('name', 'location_id.complete_name')
    def _compute_barcode(self):
        '''
            按照规则根据Location display name得到barcode
            取上一层name+'-'+'当前location name'+'(第二层locationname)'
            例如WH/Stock/SH A1/2 barcode是SH A1-2(stock)
        '''
        for location in self:
            display_name_list = location.display_name.split('/')
            display_name_list_len = len(display_name_list)
            if len(display_name_list)>=3:
                if location.name:
                    location.barcode = display_name_list[display_name_list_len-2]+'-'+location.name+'('+display_name_list[1]+')'
            else:
                location.barcode = location.display_name
                
    def print_barcode(self):
        pass