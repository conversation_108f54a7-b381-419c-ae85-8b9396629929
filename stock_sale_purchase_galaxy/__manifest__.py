# -*- coding: utf-8 -*-
{
    'name': "stock_sale_purchase_galaxy",

    'summary': """
            add stock picking information on sale order
        """,

    'description': """
        add stock picking information on sale order
    """,

    'author': "<PERSON><PERSON>",
    'website': "",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/14.0/odoo/addons/base/data/ir_module_category_data.xml
    # for the full list
    'category': 'Galaxy_ERP/Galaxy_ERP',
    'version': '0.1',

    # any module necessary for this one to work correctly
    'depends': ['stock','sale','purchase'],

    # always loaded
    'data': [
        # 'security/ir.model.access.csv',
        'views/hide_system_menu.xml',
        'views/purchase_order_views.xml',
    ],
    # only loaded in demonstration mode
    'demo': [
        'demo/demo.xml',
    ],
    'application': False,
    'auto_install': False,
    'installable': False,  # This prevents the module from being installed
}
