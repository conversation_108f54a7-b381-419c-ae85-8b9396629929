<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record model="ir.ui.menu" id="sale.product_menu_catalog">
            <field name="name" >Production</field>
            <field name="groups_id" eval="(ref('base.group_no_one'),)"/>
        </record>
        <record model="ir.ui.menu" id="stock.product_product_menu">
            <field name="name" >Production variants</field>
            <field name="groups_id" eval="(ref('base.group_no_one'),)"/>
        </record>
        <record model="ir.ui.menu" id="stock.menu_product_variant_config_stock">
            <field name="name" >Production</field>
            <field name="groups_id" eval="(ref('base.group_no_one'),)"/>
        </record>
        <record model="ir.ui.menu" id="purchase.menu_purchase_products">
            <field name="name" >Production</field>
            <field name="groups_id" eval="(ref('base.group_no_one'),)"/>
        </record>
        
        <record model="ir.ui.menu" id="stock.menu_product_in_config_stock">
            <field name="groups_id" eval="(ref('base.group_no_one'),)"/>
        </record>
        
        <record model="ir.ui.menu" id="purchase.menu_product_in_config_purchase">
            <field name="groups_id" eval="(ref('base.group_no_one'),)"/>
        </record>
        
        <record model="ir.ui.menu" id="sale.prod_config_main">
            <field name="groups_id" eval="(ref('base.group_no_one'),)"/>
        </record>
    
        
    </data>
</odoo>