# -*- coding: utf-8 -*-
from odoo import api, fields, models
import io,logging,traceback
from odoo.exceptions import UserError, ValidationError
from datetime import datetime, time
from dateutil.relativedelta import relativedelta
from itertools import groupby
from pytz import timezone, UTC
from werkzeug.urls import url_encode
import logging
_logger = logging.getLogger(__name__)
from odoo import api, fields, models, SUPERUSER_ID,_
from odoo.osv import expression
from odoo.tools import DEFAULT_SERVER_DATETIME_FORMAT
from odoo.tools.float_utils import float_is_zero
from odoo.exceptions import AccessError, UserError, ValidationError
from odoo.tools.misc import formatLang, get_lang
from odoo.tools import float_is_zero, float_compare

class extent_purchase_order(models.Model):
    '''
       一个PO对应一个SO
    '''
    _inherit = 'purchase.order'
    package_mode = fields.Selection(selection=[
            ('package', 'Package'),
            ('lot', 'LOT'),
        ],string='Receiving mode',)
    vendor_lot = fields.Char('Lot')
    #overwrite
    def button_confirm(self):
        if self.package_mode in ('',False,None):
            raise UserError('Please select the receiving mode and confirm the order')
            
        return super(extent_purchase_order,self).button_confirm()
        
        
    #覆盖原生的创建调拨单的方法，需要一个标单号创建一个调拨单
    #overwrite
    def _create_picking(self):
        StockPicking = self.env['stock.picking']
        bid_order_line_id = -1
        for order in self:
            for order_line in order.order_line.sorted(key="bid_order_lot_id"):
                if any(product.type in ['product', 'consu'] for product in order_line.product_id):
                    order = order.with_company(order.company_id)
                    new_bid_order_line_id = order_line.bid_order_lot_id.id
                    #只有标单号变更或者是第一行订单行的时候才创建新的入库单
                    _logger.debug(f"new_bid_order_line_id{new_bid_order_line_id}")  
                    _logger.debug(f"bid_order_line_id{bid_order_line_id}") 
                    
                    if bid_order_line_id != new_bid_order_line_id:
                        pickings = order.picking_ids.filtered(lambda x: x.state not in ('done', 'cancel'))
                        bid_order_line_id = new_bid_order_line_id
                        
                        res = order._prepare_picking()
                        picking = StockPicking.with_user(SUPERUSER_ID).create(res)
                    print('picking name',picking.name,picking.state)
                    #设置   ，标单号
                    picking.bid_line_number = order_line.bid_order_lot_id.name or ''
                    picking.bid_line_number_vendor = order_line.bid_order_lot_id.bid_id or ''
                    picking.package_mode = str(dict(self.fields_get(allfields=['package_mode'])['package_mode']['selection'])[self.package_mode])
                    moves = order_line._create_stock_moves(picking)
                    moves = moves.filtered(lambda x: x.state not in ('done', 'cancel'))._action_confirm()
                    seq = 0
                    for move in sorted(moves, key=lambda move: move.date):
                        seq += 5
                        move.sequence = seq
                    #moves._action_assign()
                    picking.message_post_with_view('mail.message_origin_link',
                        values={'self': picking, 'origin': order},
                        subtype_id=self.env.ref('mail.mt_note').id)
                    picking.state='draft'
                    picking.move_lines.write({'state':'draft'})
        return True
    
    #overwrite
    def _prepare_picking(self):
        if not self.group_id:
            self.group_id = self.group_id.create({
                'name': self.name,
                'partner_id': self.partner_id.id
            })
        if not self.partner_id.property_stock_supplier.id:
            raise UserError(_("You must set a Vendor Location for this partner %s", self.partner_id.name))
        
        #根据收货模式不同，设置不同的目的仓库
        if self.package_mode == 'package':
            destination_location = self._get_destination_location()
        else:
            destination_location = int(self.env['ir.config_parameter'].sudo().get_param('pov_warehouse_id') or 0)
            
        if destination_location ==0:
            raise UserError(_("Please set lot receiving warehouse in inventory module"))
            
        return {
            'picking_type_id': self.picking_type_id.id,
            'partner_id': self.partner_id.id,
            'user_id': False,
            'date': self.date_order,
            'origin': self.name,
            'location_dest_id': destination_location,
            'location_id': self.partner_id.property_stock_supplier.id,
            'company_id': self.company_id.id,
            'state':'draft',
        }
        
class extent_purchase_order_line(models.Model):
    _inherit = 'purchase.order.line'
    
    def _create_or_update_picking(self):
        for line in self:
            if line.product_id and line.product_id.type in ('product', 'consu'):
                # Prevent decreasing below received quantity
                if float_compare(line.product_qty, line.qty_received, line.product_uom.rounding) < 0:
                    raise UserError(_('You cannot decrease the ordered quantity below the received quantity.\n'
                                      'Create a return first.'))

                if float_compare(line.product_qty, line.qty_invoiced, line.product_uom.rounding) == -1:
                    # If the quantity is now below the invoiced quantity, create an activity on the vendor bill
                    # inviting the user to create a refund.
                    line.invoice_lines[0].move_id.activity_schedule(
                        'mail.mail_activity_data_warning',
                        note=_('The quantities on your purchase order indicate less than billed. You should ask for a refund.'))

                # If the user increased quantity of existing line or created a new line
                pickings = line.order_id.picking_ids.filtered(lambda x: x.state not in ('done', 'cancel') and x.location_dest_id.usage in ('internal', 'transit', 'customer'))
                picking = pickings and pickings[0] or False
                if not picking:
                    res = line.order_id._prepare_picking()
                    picking = self.env['stock.picking'].create(res)

                moves = line._create_stock_moves(picking)
                print('order_line','picking name',picking.name,picking.state)
                moves._action_confirm()._action_assign()