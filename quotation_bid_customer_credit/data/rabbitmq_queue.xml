<odoo>
    <data>
        <record id='publisher_syncCustomerCredit' model='rabbitmq.server'>
            <field name='name'>PublishersyncCustomerCredit</field>
            <field name='exchange_type'>topic</field>
            <field name='queue'>queue.galaxy.credit.info.ack</field>
            <field name='exchange'>exch.galaxy.credit.info</field>
            <field name='routing_key'>/ODOO/ACK/BID</field>
            <field name='style'>publisher</field>
        </record>

        <record id='consumer_syncCustomerCredit' model='rabbitmq.server'>
            <field name='name'>ConsumersyncCustomerCredit</field>
            <field name='exchange_type'>topic</field>
            <field name='queue'>queue.galaxy.credit.info</field>
            <field name='exchange'>exch.galaxy.credit.info</field>
            <field name='routing_key'>/BID/SEND/ODOO</field>
            <field name='style'>consumer</field>
            <field name='code'>sync_customer_credit</field>
        </record>

        <record id='sync_customer_lot_credit_consumer' model='rabbitmq.server'>
            <field name='name'>syncCustomerLotCredit</field>
            <field name='exchange_type'>topic</field>
            <field name='queue'>queue.galaxy.lock.credit</field>
            <field name='exchange'>exch.galaxy.lock.credit</field>
            <field name='routing_key'>/ERP/SEND/LOCKCREDIT</field>
            <field name='style'>consumer</field>
            <field name='code'>sync_customer_lock_credit</field>
        </record>

    </data>
</odoo>