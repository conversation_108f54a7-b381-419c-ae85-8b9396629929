<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!--释放超过rfq 截止时间的标单额度-->
        <record id="ir_cron_release_customer_credit" model="ir.cron">
            <field name="name">Release Quotation Bid Customer Credit</field>
            <field name="model_id" ref="quotation_bid_customer_credit.model_customer_lot_credit"/>
            <field name="state">code</field>
            <field name='active'>True</field>
            <field name="code">model.action_release_lot_credit_schedule()</field>
            <field name="interval_number">1</field>
            <field name="interval_type">minutes</field>
            <field name="numbercall">-1</field>
        </record>

        <!--释放超过超过截止时间的临时额度-->
        <record id="ir_cron_release_ebid_customer_temporary_credit" model="ir.cron">
            <field name="name">Release temporary Customer Credit</field>
            <field name="model_id" ref="quotation_bid_customer_credit.model_galaxy_ebid_user_temp_credit"/>
            <field name="state">code</field>
            <field name='active'>True</field>
            <field name="code">model.action_schedule_check_temp_credit()</field>
            <field name="interval_number">1</field>
            <field name="interval_type">minutes</field>
            <field name="numbercall">-1</field>
        </record>

        <record id="ir_cron_compare_customer_credit_with_ebid" model="ir.cron">
            <field name="name">Compare Customer Credit with Ebid</field>
            <field name="model_id" ref="base.model_res_partner"/>
            <field name="state">code</field>
            <field name='active'>True</field>
            <field name="code">model.action_compare_customer_credit_with_ebid_schedule()</field>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="numbercall">-1</field>
            <field name="doall">True</field>
            <field name="nextcall" eval="(DateTime.now().replace(hour=2, minute=0, second=0) +
                                            timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')"/>
        </record>

        <record id="ir_cron_compare_customer_credit_with_payment" model="ir.cron">
            <field name="name">Compare Customer Credit with Payment</field>
            <field name="model_id" ref="base.model_res_partner"/>
            <field name="state">code</field>
            <field name='active'>True</field>
            <field name="code">model.action_compare_customer_credit_with_payment_schedule()</field>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="numbercall">-1</field>
            <field name="doall">True</field>
            <field name="nextcall" eval="(DateTime.now().replace(hour=2, minute=0, second=0) +
                                            timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')"/>
        </record>
    </data>
</odoo>
