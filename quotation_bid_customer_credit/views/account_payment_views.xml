<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- VIEWS -->
        <record id="view_account_payment_form" model="ir.ui.view">
            <field name="name">view_account_payment_form</field>
            <field name="model">account.payment</field>
            <field name="priority">16</field>
            <field name="inherit_id" ref="account.view_account_payment_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='ref']" position="after">
                    <field name='bid_deposit_id' readonly='1' options="{'no_open': True}" invisible='1'/>
                    <field name='attachment_ids' widget='many2many_binary' readonly='1' invisible="1"/>
                    <!--attrs="{'invisible': [('bid_deposit_id', '=', False)]}"-->
                    <field name="bid_deposit_type" invisible="1"/>
                </xpath>  
                <xpath expr="//button[@name='action_draft']" position="replace">
                        <button name="action_draft" string="Reset To Draft" type="object" class="btn btn-secondary" attrs="{'invisible': ['|', ('bid_deposit_id', '!=', False), ('state', 'not in', ('posted', 'cancel'))]}" groups="account.group_account_invoice"/>
                </xpath>
                <xpath expr="//div[@name='button_box']" position="inside">
                    <button name="action_open_bid_deposit" type="object"  class="oe_stat_button" string='Deposit Order' 
                        icon="fa-dollar" attrs="{'invisible': [('bid_deposit_id', '=', False)]}"/>                    
                </xpath>

                <xpath expr="//field[@name='payment_type']" position="attributes">
                    <attribute name="attrs">
                        {'readonly': ['|',('state' ,'!=', 'draft'), ('bid_deposit_type', 'in', ('outbound','inbound'))]}
                    </attribute>
                    <attribute name="force_save">
                        1
                    </attribute>
                </xpath>

                <xpath expr="//field[@name='partner_id']" position="attributes">
                    <attribute name="attrs">
                        {'readonly': ['|', '|' ,('state' ,'!=', 'draft'), ('bid_deposit_type', 'in', ('outbound','inbound')), ('is_internal_transfer', '=', True)]}
                    </attribute>
                    <attribute name="force_save">
                        1
                    </attribute>
                </xpath>

                <xpath expr="//field[@name='amount']" position="attributes">
                    <attribute name="attrs">
                        {'readonly': ['|', ('state' ,'!=', 'draft'), ('bid_deposit_type', '=', 'outbound')]}
                    </attribute>
                    <attribute name="force_save">
                        1
                    </attribute>
                </xpath>

                <xpath expr="//field[@name='currency_id']" position="attributes">
                    <attribute name="attrs">
                        {'readonly': ['|', ('state' ,'!=', 'draft'), ('bid_deposit_type', 'in', ('outbound','inbound'))]}
                    </attribute>
                    <attribute name="force_save">
                        1
                    </attribute>
                </xpath>

                <xpath expr="//field[@name='partner_type']" position="replace">
                        <field name="partner_type" widget="selection" force_save='1'
                             attrs="{'readonly': 1, 'invisible': [('is_internal_transfer', '=', True)]}"/>
                </xpath>
            </field>
        </record>
        <record id="view_account_payment_tree" model="ir.ui.view">
            <field name="name">view_account_payment_tree</field>
            <field name="model">account.payment</field>
            <field name="priority">16</field>
            <field name="inherit_id" ref="account.view_account_payment_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//tree" position="attributes">
                    <attribute name='import'>0</attribute>
                </xpath>
                 <xpath expr="//field[@name='currency_id']" position='replace'>
                </xpath>     
                <xpath expr="//field[@name='state']" position='before'>
                    <field name="currency_id" groups="base.group_multi_currency"/>
                </xpath>
                <xpath expr="//field[@name='name']" position='after'>
                    <field name="ref"/>
                    <field name="bid_deposit_id"  invisible="not context.get('bid_customer_credit',False)"/>
                    <field name="bid_deposit_note" invisible="not context.get('bid_customer_credit',False)"/>
                </xpath>                 

            </field>
        </record>
    </data>
</odoo>
