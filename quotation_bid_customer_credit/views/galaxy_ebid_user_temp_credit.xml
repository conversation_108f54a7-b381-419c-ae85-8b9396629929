<odoo>

  <record model="ir.actions.act_window" id="galaxy_ebid_user_temp_credit_act_window">
        <field name="name">Ebid User Temporary Credit</field>
        <field name="res_model">galaxy.ebid.user.temp.credit</field>
        <field name="view_mode">tree,form</field>
    </record>

    <record model='ir.ui.view' id='galaxy_ebid_user_temp_credit_search'>
        <field name='name'>galaxy_ebid_user_temp_credit_search</field>
        <field name='model'>galaxy.ebid.user.temp.credit</field>
        <field name='arch' type='xml'>
            <search>
                <field name="odoo_customer_id"/>
                <filter name="draft_ebid_user_temp_credit" string="Draft" domain="[('state','=','draft')]"/>
                <filter name="confirm_ebid_user_temp_credit" string="Confirm" domain="[('state','=','confirm')]"/>
                <filter name="done_ebid_user_temp_credit" string="Done" domain="[('state','=','done')]"/>
                <filter name="cancel_ebid_user_temp_credit" string="Cancel" domain="[('state','=','cancel')]"/>
            </search>
        </field>
    </record>

    <record model="ir.ui.view" id="galaxy_ebid_user_temp_credit_tree">
        <field name="name">galaxy_ebid_user_temp_credit_tree</field>
        <field name="model">galaxy.ebid.user.temp.credit</field>
        <field name="arch" type="xml">
            <tree create="1" delete="1">
                <field name='odoo_customer_id' />
                <field name="temp_credit"  />
                <field name="currency_id"/>
                <field name="temp_credit_expire_date"/>
                <field name="note" widget="text"/>
                <field name="state"/>
            </tree>
        </field>
    </record>

    <record model="ir.ui.view" id="galaxy_ebid_user_temp_credit_form">
        <field name="name">galaxy_ebid_user_temp_credit_form</field>
        <field name="model">galaxy.ebid.user.temp.credit</field>
        <field name="arch" type="xml">
            <form create="1" delete="1"> 
            <header>
                <button name='action_confirm' type='object' string='Confirm' 
                    class='btn btn-primary'
                    attrs="{'invisible':[('state','in',('confirm','cancel', 'done'))]}"/>
                <button name='action_cancel' type='object' 
                    string='Cancel' class='btn btn-secondary'
                    attrs="{'invisible':[('state','in',('cancel', 'draft', 'done'))]}"/>
                <field name='state' widget='statusbar'/>
            </header>   
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="odoo_customer_id" readonly="1"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name='ebid_user_id' context="{'display_bid_user_id':1}" attrs="{'readonly':[('state', 'in', ('confirm', 'cancel', 'done'))]}" options="{'no_create':True, 'no_create_edit':True}"/>
                            <field name="temp_credit" attrs="{'readonly':[('state', 'in', ('confirm', 'cancel', 'done'))]}"/>
                            <field name="currency_id"/>
                            <field name="temp_credit_expire_date"  force_save="1" readonly="1"/>
                            <field name='note' widget="text"/>
                        </group>  
                    </group>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>

        </field>

    </record>
</odoo>
