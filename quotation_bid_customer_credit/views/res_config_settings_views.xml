<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="res_config_settings_view_bid_end_time_delay" model="ir.ui.view">
        <field name="name">res_config_settings_view_bid_end_time_delay</field>
        <field name="model">res.config.settings</field>
        <field name="priority" eval="25"/>
        <field name="inherit_id" ref="base.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[hasclass('app_settings_block') and @data-key='quotation_bid']" position="inside">
                <h2>Bid End Time Delay</h2>      
                <div class="row mt16 o_settings_container" id="bid_end_time_delay_setting_container">
                    <div class="col-12 col-lg-6 o_setting_box" id="galaxy_bid_end_time_delay">
                        <div class="o_setting_right_pane">
                            <div class="content-group">
                                <div class="mt16 row">
                                    <label for="galaxy_bid_end_time_delay" string="Delay(Minutes)" class="col-3 col-lg-3 o_light_label"/>
                                    <field name="galaxy_bid_end_time_delay" class="oe_inline" placeholder='Input Bid end time delay(minutes)'/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </xpath>

            <xpath expr="//div[hasclass('app_settings_block') and @data-key='quotation_bid']" position="inside">
                <h2>Temporary Credit Expiration(Hours)</h2>      
                <div class="row mt16 o_settings_container" id="ebid_temporary_credit_setting_container">
                    <div class="col-12 col-lg-6 o_setting_box" id="ebid_temporary_credit">
                        <div class="o_setting_right_pane">
                            <div class="content-group">
                                <div class="mt16 row">
                                    <label for="temporary_credit_expiration_time_in_hours" string="Hours" class="col-3 col-lg-3 o_light_label"/>
                                    <field name="temporary_credit_expiration_time_in_hours" class="oe_inline" placeholder='Input temporary credit expiration(hours)'/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </xpath>

             <xpath expr="//div[hasclass('app_settings_block') and @data-key='quotation_bid']" position="inside">
                <h2>Bid Deposit Journal</h2>      
                <div class="row mt16 o_settings_container" id="bid_deposit_bank_journal_setting_container">
                    <div class="col-12 col-lg-6 o_setting_box" id="bank_journal_id">
                        <div class="o_setting_right_pane">
                            <div class="content-group">
                                <div class="mt16 row">
                                    <label for="bank_journal_id" string="Bank Journal" class="col-3 col-lg-3 o_light_label"/>
                                    <field name="bank_journal_id" class="oe_inline" placeholder='Bid Deposit Journal' options="{'no_create': True}"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


                <div class="row mt16 o_settings_container" id="bid_deposit_cash_journal_setting_container">
                    <div class="col-12 col-lg-6 o_setting_box" id="chsh_journal_id">
                        <div class="o_setting_right_pane">
                            <div class="content-group">
                                <div class="mt16 row">
                                    <label for="cash_journal_id" string="Cash Journal" class="col-3 col-lg-3 o_light_label"/>
                                    <field name="cash_journal_id" class="oe_inline" placeholder='Bid Deposit Journal' options="{'no_create': True}"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </xpath>
        </field>
    </record>  
</odoo>
