<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="approval_request_view_form" model="ir.ui.view">
        <field name="name">approval.request.view.form.inherit</field>
        <field name="model">approval.request</field>
        <field name="inherit_id" ref="approvals.approval_request_view_form"/>
        <field name="arch" type="xml">
            <!-- <xpath expr="//button[@name='action_cancel']" position="replace">
                <field name='bid_deposit_id' invisible='1'/>
                    <button name="action_cancel" string="Cancel" type="object" attrs="{'invisible':['|','|',('bid_deposit_id','!=',False),('request_status','in',['new','cancel']),'&amp;',('user_status','==',False),('has_access_to_request','==',False)]}"/>
            </xpath>
            <xpath expr="//button[@name='action_draft']" position="replace">
                <button name="action_draft" string="Back To Draft" type="object" attrs="{'invisible':['|','|', ('bid_deposit_id', '!=', False),('request_status','!=','cancel'),'&amp;',('user_status','==',False),('has_access_to_request','==',False)]}"/>
            </xpath> -->
            <xpath expr="//field[@name='request_owner_id']" position="replace">
                    <field name='bid_deposit_id' invisible='1'/>
                    <field name="request_owner_id" groups="approvals.group_approval_user" domain="[('share', '=', False)]" attrs="{'readonly':['|', ('bid_deposit_id', '!=', False), ('request_status','not in',('new', 'pending'))]}"/>
            </xpath>

            <xpath expr="//field[@name='partner_id']" position="replace">
                    <field name="partner_id" attrs="{'invisible':[('has_partner','==','no')], 'required': [('has_partner','==','required')], 'readonly': ['|', ('bid_deposit_id', '!=', False), ('request_status','not in',('new', 'pending'))]}"/>
            </xpath>   
            <xpath expr="//field[@name='amount']" position="replace">
                    <field name="amount" attrs="{'invisible':[('has_amount','==','no')], 'required': [('has_amount','==','required')], 'readonly':['|', ('bid_deposit_id', '!=', False), ('request_status','not in',('new', 'pending'))]}"/>
            </xpath>   

            <xpath expr="//field[@name='approver_ids']" position="attributes">
                <attribute name="attrs">{'readonly': [('bid_deposit_id', '!=', False)]}</attribute>
                
            </xpath>
            <xpath expr="//field[@name='is_manager_approver']" position="after">
                <div class="oe_button_box" name="button_box">
                    <button name="action_open_bid_deposit" type="object" class="oe_stat_button" string="Deposit Order" icon="fa-dollar" attrs="{'invisible': [('bid_deposit_id', '=', False)]}"/>    
                </div>
            </xpath>
        
        </field>
    </record>

</odoo>
