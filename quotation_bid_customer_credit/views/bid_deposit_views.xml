<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="bid_deposit_inbound_act_window" model="ir.actions.act_window">
        <field name="name">Receive Deposit</field>
        <field name="res_model">bid.deposit</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('type', '=', 'inbound')]</field>
        <field name="context">{'default_type': 'inbound'}</field>
        <field name="help" type="html">
            <p>create your customer Recevie deposit</p>
        </field>
    </record>

    <record id="bid_deposit_outbound_act_window" model="ir.actions.act_window">
        <field name="name">Refund Deposit</field>
        <field name="res_model">bid.deposit</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('type', '=', 'outbound')]</field>
        <field name="context">{'default_type': 'outbound'}</field>
        <field name="help" type="html">
            <p>create your customer refund deposit</p>
        </field>
    </record>


    <record id="bid_deposit_view_tree" model="ir.ui.view">
        <field name="name">bid_deposit_view_tree</field>
        <field name="model">bid.deposit</field>
        <field name="arch" type="xml">
            <tree string="Bid Deposit Tree" default_order="id desc">
                <field name='number'/>
                <field name='customer_id'/>
                <field name='phone'/>
                <field name='currency_id'/>
                <field name='amount'/>
                <field name='create_date'/>
                <field name='journal' optional='hide'/>
                <field name='user_id' optional='hide'/>
                <field name='note' optional='hide'/>
                <field name='approval' optional='hide'/>
                <field name='state'/>
            </tree>
        </field>
    </record>


    <record id="bid_deposit_view_form" model="ir.ui.view">
        <field name="name">bid_deposit_view_form</field>
        <field name="model">bid.deposit</field>
        <field name="arch" type="xml">
            <form string="Bid Deposit Form" create='0'>
                 <header>
                    <!-- <button name='action_draft' type='object' string="draft"  attrs="{'invisible': [('state', '!=', 'cancel')]}"/> -->
                    <button name='action_cancel' type='object' string="Cancel"  attrs="{'invisible': [('state', 'in', ('cancel','draft'))]}"/>
                    <button name='action_complete' string='Confirm' type='object' class='oe_highlight' 
                                attrs="{'invisible': ['|',('state', '!=', 'draft'),('type', '=', 'outbound')]}"/>
                    <button name='action_approval' string='Request Approvaling' type='object' attrs="{'invisible': ['|',('type', '=', 'inbound'),('state', 'in', ('cancel', 'approvaling', 'complete'))]}"/>
                    <field name='state' widget='statusbar' statusbar_visible='draft,complete' />
                </header>
                <sheet>
                    <div class='oe_title'>
                        <h1>
                            <field name='number' readonly='1'/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="type" widget="radio" readonly='1'/>
                            <field name='customer_id' context="{'display_bid_user_id':1}" options="{'no_create': True}" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                            <field name='phone'/>
                            <label for="amount"/>
                            <div name="amount_div" class="o_row">
                                    <field name="amount" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                    <field name='currency_id' readonly='1' force_save='1' options="{'no_open': True}"/>
                            </div>
                            <field name='refundable_deposit' attrs="{'invisible':[('type', '=', 'inbound')]}" force_save='1' readonly='1' widget='integer'/>
                            <field name='create_date'/>
                        </group>    
                        <group>
                            <field name='journal' attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                            <!-- <field name='approval' attrs="{'readonly': [('state', '!=', 'draft')]}"/> -->
                            <field name='user_id' options="{'no_create':True, 'no_open': True}" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                            <field name='bid_user_id'/>
                            <field name='note' />
                            <field name='request_status'/>
                            <field name='reference'/>
                            <!-- <field name='approval_request_id'/>
                            <field name='account_payment_id'/> -->
                        </group> 
                        <group>
                                <field name='attachment_ids' widget='many2many_binary' attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                        </group>   
                    </group>
                </sheet>
                <div class="o_attachment_preview"/>
                 <div class="oe_chatter">
                    <field name="message_follower_ids" groups="base.group_user"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/> 
                </div>
            </form>
        </field>
    </record>
     

</odoo>
