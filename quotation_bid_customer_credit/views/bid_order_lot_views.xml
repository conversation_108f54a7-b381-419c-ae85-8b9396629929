<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <!-- <record id="waitting_pre_lock_credit_inherit" model="ir.ui.view">
        <field name="name">waitting_pre_lock_credit_inherit</field>
        <field name="model">bid.order.lot</field>
        <field name="inherit_id" ref="quotation_bid.bid_order_lot_tree_for_discount_shipping"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='quantity']" position="after">
                <field name='current_waiting_pre_lock_price' invisible='1'/>
                <field name='customer_id' invisible='1'/>
            </xpath>      
            <xpath expr="//field[@name='customer_bid']" position="attributes">
                 <attribute name="options">
                    {'no_open':true,'string':'Quotation', 'in_red':true}

                </attribute>
                <attribute name='context'>
                 {'bid':True, 'form_view_ref':'quotation_bid.customer_bid_line_bid_form',
                    'default_line_id':id, 'current_waiting_pre_lock_price': current_waiting_pre_lock_price,'customer_id': customer_id}        
                </attribute>
            </xpath> 
        </field>
    </record> -->

</odoo>
