<odoo>
    <record model="ir.actions.act_window" id="quotation_credit_act_window">
        <field name="name">Lot Credit</field>
        <field name="res_model">customer.lot.credit</field>
        <field name="domain">[('release_type','=','manual')]</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'search_default_lot_bid_lock':1, 'quotation_credit_admin':0}</field>
    </record>
    <record model="ir.actions.act_window" id="quotation_credit_act_window_admin">
        <field name="name">Lot Credit(Admin)</field>
        <field name="res_model">customer.lot.credit</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'search_default_lot_bid_lock':1, 'quotation_credit_admin':1}</field>
    </record>
    <record model='ir.ui.view' id='customer_lot_credit_search'>
        <field name='name'>bid_result_lot_search</field>
        <field name='model'>customer.lot.credit</field>
        <field name='arch' type='xml'>
            <search>
                <field name="group_id"/>
                <field name="lot_id"/>
                <field name='customer' string="name"/>
                <field name='customer_tel' string="Tel"/>
                <field name='lot_batch'/>
                <filter name='lot_bid_lock' string='Lock' domain="[('state','=','lock')]"/>
                <filter name='lot_bid_unlock' string='UnLock' domain="[('state','=','unlock')]"/>
                <separator/>
                <filter name='lot_bid_won' string='Won' domain="[('bid_status','=','Won')]"/>
                <filter name='lot_bid_lost' string='Lost' domain="[('bid_status','=','Lost')]"/>
                <filter name='lot_bid_bidding' string='Bidding' domain="[('bid_status','=','Bidding')]"/>
                <separator/>
                <filter string="Archived" name="Inactive_lot_credit" domain="[('active','=',False)]" />
                <searchpanel>
                    <field name="vendor_id" select="multi" icon="fa-tag" enable_counters="1"/>
                </searchpanel>
            </search>
        </field>
    </record>
    <record model="ir.ui.view" id="customer_lot_credit_tree">
        <field name="name">customer_lot_credit_tree</field>
        <field name="model">customer.lot.credit</field>
        <field name="arch" type="xml">
            <tree name="customer_lot_credit_tree" create='0' delete='0' edit='0' default_order="id desc">
                <header>
                    <button name="action_relase_lot" class="btn-primary" type="object" string="Release"/>
                    <button name="action_lock_lot" class="btn-secondary" type="object" string="Lock"/>
                </header>
                <field name="lot_batch" optional="hide"/>
                <field name="group_id"/>
                <field name="lot_id"/>
                <field name="quantity"/>
                <field name='bided_qty'/>
                <field name="price_amount"/>
                <field name='customer'/>
                <field name="customer_tel" optional="hide"/>
                <field name="brand" optional="hide"/>
                <field name="model" optional="hide"/>
                <field name="version" optional="hide"/>
                <field name="bid_status" decoration-danger = "bid_status ==&quot;Lost&quot;" decoration-success="bid_status==&quot;Won&quot;" widget="badge" optional="hide"/>
                <field name="state"/>
                <field name="create_date" optional="hide"/>
            </tree>
        </field>
    </record>

    <record model="ir.ui.view" id="customer_lot_credit_form">
        <field name="name">customer_lot_credit_form</field>
        <field name="model">customer.lot.credit</field>
        <field name="arch" type="xml">
            <form create="0" edit="0" delete="0"> 
                <header>
                    <field name='state' widget='statusbar'/>
                </header>   
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="lot_id"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="quantity"/>
                            <field name='bided_qty'/>
                            <field name="price_amount"/>
                            <field name='customer'/>
                            <field name="customer_tel"/>
                            <field name="brand"/>
                        </group>  
                        <group>
                            <field name="lot_batch"/>
                            <field name='customer'/>
                            <field name="model"/>
                            <field name="version"/>
                            <field name="create_date"/>
                        </group>           
                    </group>
                </sheet>
            </form>
        </field>
    </record>
</odoo>
