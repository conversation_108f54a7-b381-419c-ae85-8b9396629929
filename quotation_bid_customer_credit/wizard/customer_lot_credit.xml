<odoo>

  <record model="ir.actions.act_window" id="wizard_quotation_credit_header_act_window">
        <field name="name">Quotation Credit</field>
        <field name="res_model">wizard.customer.lot.credit.header</field>
        <field name="view_mode">form</field>
    </record>

    <record model="ir.ui.view" id="wizard_customer_lot_credit_header_form">
        <field name="name">customer_lot_credit_form</field>
        <field name="model">wizard.customer.lot.credit.header</field>
        <field name="arch" type="xml">
            <form edit="0" create="0" delete="0"> 
                <footer>
                    <button name="action_relase_lot" class="btn-primary" invisible="context.get('action_lock',True)" type="object" string="Confirm"/>
                    <button name="action_lock_lot" class="btn-primary" invisible="not context.get('action_lock',False)" type="object" string="Confirm"/>
                    <button string="Cancel" class="btn btn-secondary" special="cancel"/>
                </footer>   
                <sheet>
                    <group>
                        <group>
                            <field name="lots_qty"/>
                        </group>  
                        <group>
                            <field name='lots_amount'/>
                        </group>           
                    </group>
                    <notebook>
                        <page name="line_id" string="Lots">
                            <field name="customer_lot_credit_ids" >
                                <tree edit="0" create="0" delete="0">
                                    <field name="lot_id"/>
                                    <field name="price_amount"/>
                                    <field name='customer'/>
                                    <field name="state"/>
                                 </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>
</odoo>
