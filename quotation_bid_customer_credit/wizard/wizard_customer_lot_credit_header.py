import logging
import datetime
import pytz
from odoo import models, fields, _, api, tools
from ...java_bid_server.models.api_customer_lot_credit import LotCreditAPI
from odoo.addons.base_galaxy.models.public_func import display_notification, display_sticky_notification

_logger = logging.getLogger(__name__)


class WizardCustomerLotCreditHeader(models.TransientModel):
    _name = 'wizard.customer.lot.credit.header'
    _description = 'Wizard Customer Lot Credit line'

    customer_lot_credit_ids = fields.One2many('wizard.customer.lot.credit.line', 'customer_lot_credit_header_id', string="Lots")
    lots_qty = fields.Integer(string="Total Lots", compute="_compute_get_lots_qty")
    lots_amount = fields.Float(string="Total Amount", compute="_compute_get_lots_amount")

    @api.depends('customer_lot_credit_ids')
    def _compute_get_lots_qty(self):
        for rec in self:
            rec.lots_qty = len(self.customer_lot_credit_ids)

    @api.depends('customer_lot_credit_ids')
    def _compute_get_lots_amount(self):
        for rec in self:
            rec.lots_amount = sum(self.customer_lot_credit_ids.mapped('price_amount'))

    def _check_release_valid(self):
        time_delay = int(self.env['ir.config_parameter'].sudo().get_param('galaxy_bid_end_time_delay')
                         ) if self.env['ir.config_parameter'].sudo().get_param('galaxy_bid_end_time_delay') else 0
        for rec in self.customer_lot_credit_ids:
            if rec.aucnet_state not in ('confirm', 'complete'):
                raise models.UserError('You can only unlock confirmed or completed lots credit!')
            rfq_end_time = max(rec.lot_id.order_id.origin_id.mapped('supplier_time_remaining'))
            rfq_end_time = rfq_end_time + datetime.timedelta(minutes=time_delay)
            if datetime.datetime.now() < rfq_end_time:
                raise models.UserError(
                    f'You can only release lot {rec.lot_id.name} after {rfq_end_time.astimezone(pytz.timezone(self._context.get("tz"))).strftime("%Y-%m-%d %H:%M:%S")}'
                )

    # 手动释放Lot额度
    def action_relase_lot(self):
        if not self.env.is_admin():
            self._check_release_valid()
        lots_list = []
        for rec in self.customer_lot_credit_ids:
            # 标单报价是0直接释放
            if rec.price_amount != 0:
                lots_list.append({
                    "lotId": rec.lot_id.name,
                    "userId": rec.user_id,
                    "standardId": rec.vendor_code,
                    "groupId": rec.group_id
                })
            else:
                self.env['customer.lot.credit'].search([('lot_id', '=', rec.lot_id.name),
                                                        ('user_id', '=', rec.user_id)]).write({'state': 'unlock'})
        view_id = self.env.ref('quotation_bid_customer_credit.customer_lot_credit_tree').id
        action = self.env.ref('quotation_bid_customer_credit.quotation_credit_act_window').sudo().read()[0]
        if self._context.get('quotation_credit_admin'):
            action = self.env.ref('quotation_bid_customer_credit.quotation_credit_act_window_admin').sudo().read()[0]
        else:
            action = self.env.ref('quotation_bid_customer_credit.quotation_credit_act_window').sudo().read()[0]
        del action['res_id']
        del action['id']
        action.update({'target': 'main'})
        action.update({'view_type': 'tree'})
        action.update({'views': [[view_id, "list"], [False, "form"]]})
        if len(lots_list) == 0:
            return display_notification(_('Release Lot Successfully'), _('All lots completed successfully'), action, 'success')

        GalaxyLotCreditAPI = LotCreditAPI()
        response_list = GalaxyLotCreditAPI.batch_free_credit_v2(lots_list)
        success_lots, error_lots, server_error = GalaxyLotCreditAPI.format_response_list(response_list)
        if server_error:
            raise models.UserError(_('please try again'))
        for success_lot in success_lots:
            user_id = success_lot.get('userId', '')
            lot_id = success_lot.get('lotId', '')
            self.env['customer.lot.credit'].search([('lot_id', '=', lot_id),
                                                    ('user_id', '=', user_id)]).write({'state': 'unlock'})
        if error_lots:
            str_error_lots = [str(error_lot) for error_lot in error_lots]
        if len(error_lots) == 0:
            return display_notification(_('Release Lot Successfully'), _('All lots completed successfully'), action,
                                        'success')
        else:
            return display_sticky_notification(_('Release Lot Error'), f"Error lots:{''.join(str_error_lots)}", action, 'error')

    def action_lock_lot(self):
        lots_list = []
        for rec in self.customer_lot_credit_ids:
            if rec.price_amount != 0:
                lots_list.append({
                    "lotId": rec.lot_id.name,
                    "userId": rec.user_id,
                    "standardId": rec.vendor_code,
                    "groupId": rec.group_id,
                    "priceAmount": round(rec.price_amount, 2)
                })
            else:
                self.env['customer.lot.credit'].search([('lot_id', '=', rec.lot_id.name),
                                                        ('user_id', '=', rec.user_id)]).write({'state': 'lock'})
        view_id = self.env.ref('quotation_bid_customer_credit.customer_lot_credit_tree').id
        action = self.env.ref('quotation_bid_customer_credit.quotation_credit_act_window').sudo().read()[0]
        if self._context.get('quotation_credit_admin'):
            action = self.env.ref('quotation_bid_customer_credit.quotation_credit_act_window_admin').sudo().read()[0]
        else:
            action = self.env.ref('quotation_bid_customer_credit.quotation_credit_act_window').sudo().read()[0]
        del action['res_id']
        del action['id']
        action.update({'target': 'main'})
        action.update({'view_type': 'tree'})
        action.update({'views': [[view_id, "list"], [False, "form"]]})
        if len(lots_list) == 0:
            return True
        GalaxyLotCreditAPI = LotCreditAPI()
        response_list = GalaxyLotCreditAPI.batch_lock_credit_v2(lots_list)
        success_lots, error_lots, server_error = GalaxyLotCreditAPI.format_response_list(response_list)
        if server_error:
            raise models.UserError(_('please try again'))
        for success_lot in success_lots:
            user_id = success_lot.get('userId', '')
            lot_id = success_lot.get('lotId', '')
            self.env['customer.lot.credit'].search([('lot_id', '=', lot_id),
                                                    ('user_id', '=', user_id)]).write({'state': 'lock'})
        if error_lots:
            str_error_lots = [str(error_lot) for error_lot in error_lots]
        if len(error_lots) == 0:
            return display_notification(_('Lock Lot Successfully'), _('All lots completed successfully'), action, 'success')
        else:
            return display_sticky_notification(_('Lock Lot Error'), f"Error lots:{''.join(str_error_lots)}", action, 'error')
