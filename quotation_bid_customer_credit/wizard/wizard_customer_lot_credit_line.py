from odoo import models, fields, _
from odoo.addons.base_galaxy.models.public_func import display_notification, display_sticky_notification


class WizardCustomerLotCreditLine(models.TransientModel):
    _name = 'wizard.customer.lot.credit.line'
    _description = 'Wizard Customer Lot Credit line'

    customer_lot_credit_header_id = fields.Many2one('wizard.customer.lot.credit.header', string="Customer Lock")
    vendor_code = fields.Char(required=True, string="Ebid Vendor Code")
    group_id = fields.Char(required=True, string="Auction")
    customer = fields.Many2one('res.partner', required=True, index=True)
    user_id = fields.Char(string="Ebid User Id")
    lot_id = fields.Many2one('bid.order.lot', required=True, index=True)
    price_amount = fields.Float(digits=(16, 2), string="Amount")
    state = fields.Selection([('lock', 'LOCK'), ('unlock', 'UNLOCK')], default='lock', index=True)
    aucnet_state = fields.Selection(related='lot_id.order_id.state')
