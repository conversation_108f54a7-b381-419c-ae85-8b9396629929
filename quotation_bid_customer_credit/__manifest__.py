# -*- coding: utf-8 -*-


{
    'name':
    "quotation_bid_customer_credit",
    'summary':
    """
            release or lock customer credit with lot
       """,
    'description':
    """
        prelock or lock or unlock customer credit with lot
    """,
    'author':
    "<PERSON><PERSON>",
    'website':
    "http://www.yourcompany.com",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/14.0/odoo/addons/base/data/ir_module_category_data.xml
    # for the full list
    'category':
    'Galaxy_ERP/Galaxy_ERP',
    'version':
    '0.1',

    # any module necessary for this one to work correctly
    'depends': ['base', 'quotation_bid', 'account_customer_credit', 'account', 'account_accountant', 'approvals', 'queue_job', 'galaxy_business_approvals'],

    # always loaded
    'data': [
        'security/ir.model.access.csv',
        'views/customer_lot_credit.xml',
        'views/bid_order_lot_views.xml',
        'views/res_partner_views.xml',
        'views/galaxy_ebid_user_temp_credit.xml',
        'wizard/customer_lot_credit.xml',
        'data/res_config_settings_views.xml',
        'data/cron.xml',
        'data/queue_job_channel.xml',
        'data/rabbitmq_queue.xml',
        'views/res_config_settings_views.xml',
        'data/sequence.xml',
        'views/bid_deposit_views.xml',
        'views/account_payment_views.xml',
        'views/menu_views.xml',
        'data/approval_category_views.xml',
        'data/report_paperformat_data.xml',
        'views/approval_request_views.xml',
        'views/bid_deposit_templates.xml',
        'views/bid_deposit_report.xml',
        'data/channel.xml',
        "views/bid_order_views.xml",
    ],
   
}
