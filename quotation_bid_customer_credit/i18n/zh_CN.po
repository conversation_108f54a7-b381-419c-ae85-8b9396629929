# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* quotation_bid_customer_credit
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0+e-20210217\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-09-05 13:09+0000\n"
"PO-Revision-Date: 2022-09-05 13:09+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: quotation_bid_customer_credit
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.report_bid_deposit_document
msgid "1、僅接受公司帳戶轉入，不接受個人帳戶"
msgstr ""

#. module: quotation_bid_customer_credit
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.report_bid_deposit_document
msgid "2、轉帳前請先確認轉帳公司合法性。如使用新公司帳戶轉入，需在轉帳前提共商業登記證及電訊牌照進行認證"
msgstr ""

#. module: quotation_bid_customer_credit
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.report_bid_deposit_document
msgid "<strong>Swift Code: </strong>DHBKHKHHXXX"
msgstr ""

#. module: quotation_bid_customer_credit
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.report_bid_deposit_document
msgid "<strong>付款單位： </strong>"
msgstr ""

#. module: quotation_bid_customer_credit
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.report_bid_deposit_document
msgid "<strong>付款方式： </strong>現金支付或銀行轉帳"
msgstr ""

#. module: quotation_bid_customer_credit
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.report_bid_deposit_document
msgid "<strong>付款用途： </strong>投標押金"
msgstr ""

#. module: quotation_bid_customer_credit
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.report_bid_deposit_document
msgid "<strong>付款說明</strong>"
msgstr ""

#. module: quotation_bid_customer_credit
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.report_bid_deposit_document
msgid "<strong>付款通知單</strong>"
msgstr ""

#. module: quotation_bid_customer_credit
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.report_bid_deposit_document
msgid "<strong>付款金額： </strong>"
msgstr ""

#. module: quotation_bid_customer_credit
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.report_bid_deposit_document
msgid "<strong>分行編號：</strong>478"
msgstr ""

#. module: quotation_bid_customer_credit
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.report_bid_deposit_document
msgid "<strong>地址： </strong>香港觀塘區鴻圖道1號26樓2612-22 銀河電訊"
msgstr ""

#. module: quotation_bid_customer_credit
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.report_bid_deposit_document
msgid "<strong>工作時間： </strong>週一 - 週五 11:00am - 18:30pm，法定節假日除外"
msgstr ""

#. module: quotation_bid_customer_credit
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.report_bid_deposit_document
msgid "<strong>帳號： </strong>"
msgstr ""

#. module: quotation_bid_customer_credit
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.report_bid_deposit_document
msgid "<strong>收款帳號：</strong>"
msgstr ""

#. module: quotation_bid_customer_credit
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.report_bid_deposit_document
msgid "<strong>日期： </strong>"
msgstr ""

#. module: quotation_bid_customer_credit
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.report_bid_deposit_document
msgid "<strong>星展银行帐號： </strong>"
msgstr ""

#. module: quotation_bid_customer_credit
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.report_bid_deposit_document
msgid "<strong>結算幣種： </strong>"
msgstr ""

#. module: quotation_bid_customer_credit
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.report_bid_deposit_document
msgid "<strong>聯繫電話： </strong>+852***"
msgstr ""

#. module: quotation_bid_customer_credit
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.report_bid_deposit_document
msgid "<strong>轉帳說明</strong>"
msgstr ""

#. module: quotation_bid_customer_credit
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.report_bid_deposit_document
msgid "<strong>銀行編號： </strong>016"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_deposit__account_payment_id
msgid "Account Payment"
msgstr "付款账户"

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_deposit__message_needaction
msgid "Action Needed"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_customer_lot_credit__active
msgid "Active"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_deposit__activity_ids
msgid "Activities"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_deposit__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_deposit__activity_state
msgid "Activity State"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_deposit__activity_type_icon
msgid "Activity Type Icon"
msgstr ""

#. module: quotation_bid_customer_credit
#: code:addons/quotation_bid_customer_credit/wizard/wizard_customer_lot_credit_header.py:0
#: code:addons/quotation_bid_customer_credit/wizard/wizard_customer_lot_credit_header.py:0
#: code:addons/quotation_bid_customer_credit/wizard/wizard_customer_lot_credit_header.py:0
#, python-format
msgid "All lots completed successfully"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_deposit__amount
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_customer_lot_credit__price_amount
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_wizard_customer_lot_credit_line__price_amount
msgid "Amount"
msgstr "金额"

#. module: quotation_bid_customer_credit
#: code:addons/quotation_bid_customer_credit/models/bid_deposit.py:0
#, python-format
msgid "Amount should greater than zero"
msgstr "金额应大于零"

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_deposit__approval
msgid "Approval"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model,name:quotation_bid_customer_credit.model_approval_request
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_deposit__approval_request_id
msgid "Approval Request"
msgstr "审批申请"

#. module: quotation_bid_customer_credit
#: code:addons/quotation_bid_customer_credit/models/bid_deposit.py:0
#, python-format
msgid "Approval request not exsit"
msgstr "审批单不存在"

#. module: quotation_bid_customer_credit
#: model:ir.model.fields.selection,name:quotation_bid_customer_credit.selection__bid_deposit__approval__approvaled
msgid "Approvaled"
msgstr "已批准"

#. module: quotation_bid_customer_credit
#: model:ir.model.fields.selection,name:quotation_bid_customer_credit.selection__bid_deposit__state__approvaling
msgid "Approvaling"
msgstr "审批中"

#. module: quotation_bid_customer_credit
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.customer_lot_credit_search
msgid "Archived"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_account_payment__attachment_ids
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_deposit__attachment_ids
msgid "Attachment"
msgstr "附件"

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_deposit__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_wizard_customer_lot_credit_line__group_id
msgid "Auction"
msgstr "投标单组"

#. module: quotation_bid_customer_credit
#: model:ir.model.fields.selection,name:quotation_bid_customer_credit.selection__customer_lot_credit__release_type__auto
msgid "Auto"
msgstr ""

#. module: quotation_bid_customer_credit
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.approval_request_view_form
msgid "Back To Draft"
msgstr "回到草稿"

#. module: quotation_bid_customer_credit
#: model:ir.model.fields.selection,name:quotation_bid_customer_credit.selection__bid_deposit__journal__bank
msgid "Bank"
msgstr "银行"

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_res_config_settings__bank_journal_id
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.res_config_settings_view_bid_end_time_delay
msgid "Bank Journal"
msgstr "银行日记账"

#. module: quotation_bid_customer_credit
#: model:ir.model,name:quotation_bid_customer_credit.model_bid_deposit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_account_payment__bid_deposit_id
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_approval_request__bid_deposit_id
msgid "Bid Deposit"
msgstr "投标押金"

#. module: quotation_bid_customer_credit
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.bid_deposit_view_form
msgid "Bid Deposit Form"
msgstr "付款方式"

#. module: quotation_bid_customer_credit
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.res_config_settings_view_bid_end_time_delay
msgid "Bid Deposit Journal"
msgstr "投標押金日記賬"

#. module: quotation_bid_customer_credit
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.bid_deposit_view_tree
msgid "Bid Deposit Tree"
msgstr ""

#. module: quotation_bid_customer_credit
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.res_config_settings_view_bid_end_time_delay
msgid "Bid End Time Delay"
msgstr "投标截止延迟时间(分钟)"

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_res_config_settings__galaxy_bid_end_time_delay
msgid "Bid End Time Delay(Minutes)"
msgstr "投标截止延迟时间(分钟)"

#. module: quotation_bid_customer_credit
#: model:ir.model,name:quotation_bid_customer_credit.model_customer_bid_line
msgid "Bid Line"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model,name:quotation_bid_customer_credit.model_bid_order
msgid "Bid Order"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model,name:quotation_bid_customer_credit.model_bid_result
msgid "Bid Result"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_customer_lot_credit__bid_status
msgid "Bid Status"
msgstr ""

#. module: quotation_bid_customer_credit
#: code:addons/quotation_bid_customer_credit/models/approval_request.py:0
#, python-format
msgid "Bid deposit has been canceled"
msgstr "投标单押金已取消"

#. module: quotation_bid_customer_credit
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.customer_lot_credit_search
msgid "Bidding"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_customer_lot_credit__brand
msgid "Brand"
msgstr "品牌"

#. module: quotation_bid_customer_credit
#: model:ir.model.fields.selection,name:quotation_bid_customer_credit.selection__bid_deposit__state__cancel
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.approval_request_view_form
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.bid_deposit_view_form
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.wizard_customer_lot_credit_header_form
msgid "Cancel"
msgstr "取消"

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_customer_lot_credit__version
msgid "Carrier"
msgstr "运营商"

#. module: quotation_bid_customer_credit
#: model:ir.model.fields.selection,name:quotation_bid_customer_credit.selection__bid_deposit__journal__cash
msgid "Cash"
msgstr "现金"

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_res_config_settings__cash_journal_id
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.res_config_settings_view_bid_end_time_delay
msgid "Cash Journal"
msgstr "现金日记账"

#. module: quotation_bid_customer_credit
#: model:ir.model.fields.selection,name:quotation_bid_customer_credit.selection__bid_deposit__state__complete
msgid "Complete"
msgstr "完成"

#. module: quotation_bid_customer_credit
#: model:ir.model,name:quotation_bid_customer_credit.model_res_config_settings
msgid "Config Settings"
msgstr "配置设置"

#. module: quotation_bid_customer_credit
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.bid_deposit_view_form
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.wizard_customer_lot_credit_header_form
msgid "Confirm"
msgstr "确认"

#. module: quotation_bid_customer_credit
#: model:ir.model,name:quotation_bid_customer_credit.model_res_partner
msgid "Contact"
msgstr "联系人"

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_deposit__create_uid
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_customer_lot_credit__create_uid
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_wizard_customer_lot_credit_header__create_uid
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_wizard_customer_lot_credit_line__create_uid
msgid "Created by"
msgstr "创建人"

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_deposit__create_date
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_customer_lot_credit__create_date
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_wizard_customer_lot_credit_header__create_date
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_wizard_customer_lot_credit_line__create_date
msgid "Created on"
msgstr "创建时间"

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_deposit__currency_id
msgid "Currency"
msgstr "货币"

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_deposit__customer_id
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_customer_lot_credit__customer
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_wizard_customer_lot_credit_line__customer
msgid "Customer"
msgstr "客戶"

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_wizard_customer_lot_credit_line__customer_lot_credit_header_id
msgid "Customer Lock"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model,name:quotation_bid_customer_credit.model_customer_lot_credit
msgid "Customer Lot Credit"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_deposit__user_id
msgid "Customer Service"
msgstr "客服"

#. module: quotation_bid_customer_credit
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.res_config_settings_view_bid_end_time_delay
msgid "Delay(Minutes)"
msgstr "延迟时间(分钟)"

#. module: quotation_bid_customer_credit
#: code:addons/quotation_bid_customer_credit/models/account_payment.py:0
#: model:approval.category,name:quotation_bid_customer_credit.approval_category_deposit
#: model:ir.ui.menu,name:quotation_bid_customer_credit.deposit_menu
#, python-format
msgid "Deposit"
msgstr "押金"

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_account_payment__display_name
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_approval_request__display_name
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_deposit__display_name
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_order__display_name
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_order_lot__display_name
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_result__display_name
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_customer_bid_line__display_name
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_customer_lot_credit__display_name
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_rabbitmq_server__display_name
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_res_config_settings__display_name
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_res_partner__display_name
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_wizard_customer_lot_credit_header__display_name
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_wizard_customer_lot_credit_line__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: quotation_bid_customer_credit
#: model:ir.model.fields.selection,name:quotation_bid_customer_credit.selection__bid_deposit__state__draft
msgid "Draft"
msgstr "草稿"

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_wizard_customer_lot_credit_line__user_id
msgid "Ebid User Id"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_customer_lot_credit__user_id
msgid "Ebid UserID"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_wizard_customer_lot_credit_line__vendor_code
msgid "Ebid Vendor Code"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_deposit__message_follower_ids
msgid "Followers"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_deposit__message_channel_ids
msgid "Followers (Channels)"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_deposit__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,help:quotation_bid_customer_credit.field_bid_deposit__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_deposit__journal
msgid "Form of payment"
msgstr "付款方式"

#. module: quotation_bid_customer_credit
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.report_bid_deposit_document
msgid "GALAXY TELECOM (HK) LTD"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_customer_lot_credit__group_id
msgid "Group"
msgstr "投标单编号"

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_account_payment__id
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_approval_request__id
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_deposit__id
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_order__id
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_order_lot__id
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_result__id
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_customer_bid_line__id
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_customer_lot_credit__id
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_rabbitmq_server__id
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_res_config_settings__id
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_res_partner__id
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_wizard_customer_lot_credit_header__id
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_wizard_customer_lot_credit_line__id
msgid "ID"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_deposit__activity_exception_icon
msgid "Icon"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,help:quotation_bid_customer_credit.field_bid_deposit__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,help:quotation_bid_customer_credit.field_bid_deposit__message_needaction
#: model:ir.model.fields,help:quotation_bid_customer_credit.field_bid_deposit__message_unread
msgid "If checked, new messages require your attention."
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,help:quotation_bid_customer_credit.field_bid_deposit__message_has_error
#: model:ir.model.fields,help:quotation_bid_customer_credit.field_bid_deposit__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,help:quotation_bid_customer_credit.field_customer_lot_credit__active
msgid ""
"If unchecked, it will allow you to hide the folder instead of removing it."
msgstr ""

#. module: quotation_bid_customer_credit
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.res_config_settings_view_bid_end_time_delay
msgid "Input Bid end time delay(minutes)"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_deposit__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields.selection,name:quotation_bid_customer_credit.selection__customer_lot_credit__state__lock
#: model:ir.model.fields.selection,name:quotation_bid_customer_credit.selection__wizard_customer_lot_credit_line__state__lock
msgid "LOCK"
msgstr "锁定"

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_account_payment____last_update
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_approval_request____last_update
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_deposit____last_update
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_order____last_update
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_order_lot____last_update
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_result____last_update
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_customer_bid_line____last_update
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_customer_lot_credit____last_update
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_rabbitmq_server____last_update
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_res_config_settings____last_update
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_res_partner____last_update
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_wizard_customer_lot_credit_header____last_update
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_wizard_customer_lot_credit_line____last_update
msgid "Last Modified on"
msgstr "最后修改日"

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_deposit__write_uid
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_customer_lot_credit__write_uid
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_wizard_customer_lot_credit_header__write_uid
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_wizard_customer_lot_credit_line__write_uid
msgid "Last Updated by"
msgstr "最后修改人"

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_deposit__write_date
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_customer_lot_credit__write_date
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_wizard_customer_lot_credit_header__write_date
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_wizard_customer_lot_credit_line__write_date
msgid "Last Updated on"
msgstr ""

#. module: quotation_bid_customer_credit
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.customer_lot_credit_search
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.customer_lot_credit_tree
msgid "Lock"
msgstr "锁定"

#. module: quotation_bid_customer_credit
#: code:addons/quotation_bid_customer_credit/wizard/wizard_customer_lot_credit_header.py:0
#: code:addons/quotation_bid_customer_credit/wizard/wizard_customer_lot_credit_header.py:0
#: code:addons/quotation_bid_customer_credit/wizard/wizard_customer_lot_credit_header.py:0
#, python-format
msgid "Lock Lot Error"
msgstr "锁定Lot错误"

#. module: quotation_bid_customer_credit
#: code:addons/quotation_bid_customer_credit/wizard/wizard_customer_lot_credit_header.py:0
#, python-format
msgid "Lock Lot Successfully"
msgstr "锁定Lot成功"

#. module: quotation_bid_customer_credit
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.customer_lot_credit_search
msgid "Lost"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model,name:quotation_bid_customer_credit.model_bid_order_lot
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_customer_lot_credit__lot_id
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_wizard_customer_lot_credit_line__lot_id
msgid "Lot"
msgstr "批次"

#. module: quotation_bid_customer_credit
#: model:ir.actions.act_window,name:quotation_bid_customer_credit.quotation_credit_act_window
#: model:ir.ui.menu,name:quotation_bid_customer_credit.galaxy_vendor_lot_credit_menu
msgid "Lot Credit"
msgstr "Lot额度管理"

#. module: quotation_bid_customer_credit
#: model:ir.actions.act_window,name:quotation_bid_customer_credit.quotation_credit_act_window_admin
#: model:ir.ui.menu,name:quotation_bid_customer_credit.galaxy_vendor_lot_credit_menu_admin
msgid "Lot Credit(Admin)"
msgstr "Lot额度管理(Admin)"

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_wizard_customer_lot_credit_header__customer_lot_credit_ids
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.wizard_customer_lot_credit_header_form
msgid "Lots"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_deposit__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields.selection,name:quotation_bid_customer_credit.selection__customer_lot_credit__release_type__manual
msgid "Manual"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_deposit__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_deposit__message_ids
msgid "Messages"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_customer_lot_credit__model
msgid "Model"
msgstr ""

#. module: quotation_bid_customer_credit
#: code:addons/quotation_bid_customer_credit/models/bid_deposit.py:0
#: code:addons/quotation_bid_customer_credit/models/bid_deposit.py:0
#: code:addons/quotation_bid_customer_credit/models/bid_deposit.py:0
#, python-format
msgid "New"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_deposit__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_deposit__activity_summary
msgid "Next Activity Summary"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_deposit__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_deposit__note
msgid "Note"
msgstr "备注"

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_deposit__number
msgid "Number"
msgstr "编号"

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_deposit__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_deposit__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,help:quotation_bid_customer_credit.field_bid_deposit__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,help:quotation_bid_customer_credit.field_bid_deposit__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,help:quotation_bid_customer_credit.field_bid_deposit__message_unread_counter
msgid "Number of unread messages"
msgstr ""

#. module: quotation_bid_customer_credit
#: code:addons/quotation_bid_customer_credit/models/bid_deposit.py:0
#, python-format
msgid "Only draft order can delete"
msgstr "只有草稿状态的押金单可以被删除"

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_account_payment__bid_deposit_type
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_deposit__type
msgid "Order Type"
msgstr "押金类型"

#. module: quotation_bid_customer_credit
#: model:ir.model,name:quotation_bid_customer_credit.model_account_payment
msgid "Payments"
msgstr "支付"

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_customer_lot_credit__customer_tel
msgid "Phone"
msgstr ""

#. module: quotation_bid_customer_credit
#: code:addons/quotation_bid_customer_credit/models/bid_deposit.py:0
#, python-format
msgid "Please upload transfer receipt"
msgstr "请上传付款单据"

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_customer_lot_credit__quantity
msgid "QTY"
msgstr "數量"

#. module: quotation_bid_customer_credit
#: model:ir.actions.act_window,name:quotation_bid_customer_credit.wizard_quotation_credit_header_act_window
msgid "Quotation Credit"
msgstr "额度管理"

#. module: quotation_bid_customer_credit
#: model:ir.model,name:quotation_bid_customer_credit.model_rabbitmq_server
msgid "Rabbitmq Server"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields.selection,name:quotation_bid_customer_credit.selection__bid_deposit__type__inbound
msgid "Receive"
msgstr "收押金"

#. module: quotation_bid_customer_credit
#: model:ir.actions.act_window,name:quotation_bid_customer_credit.bid_deposit_inbound_act_window
#: model:ir.actions.report,name:quotation_bid_customer_credit.action_report_receive_deposit
#: model:ir.ui.menu,name:quotation_bid_customer_credit.receive_deposit_menu
msgid "Receive Deposit"
msgstr "收押金单"

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_deposit__reference
msgid "Reference"
msgstr "审批备注"

#. module: quotation_bid_customer_credit
#: model:ir.actions.act_window,name:quotation_bid_customer_credit.bid_deposit_outbound_act_window
#: model:ir.ui.menu,name:quotation_bid_customer_credit.refund_deposit_menu
msgid "Refund Deposit"
msgstr "退押金单"

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_deposit__refundable_deposit
msgid "Refundable Deposit"
msgstr "可退押金"

#. module: quotation_bid_customer_credit
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.customer_lot_credit_tree
msgid "Release"
msgstr "释放"

#. module: quotation_bid_customer_credit
#: code:addons/quotation_bid_customer_credit/models/bid_order.py:0
#: code:addons/quotation_bid_customer_credit/models/bid_order.py:0
#: code:addons/quotation_bid_customer_credit/models/bid_order.py:0
#: code:addons/quotation_bid_customer_credit/wizard/wizard_customer_lot_credit_header.py:0
#: code:addons/quotation_bid_customer_credit/wizard/wizard_customer_lot_credit_header.py:0
#: code:addons/quotation_bid_customer_credit/wizard/wizard_customer_lot_credit_header.py:0
#, python-format
msgid "Release Lot Error"
msgstr "释放lot错误"

#. module: quotation_bid_customer_credit
#: code:addons/quotation_bid_customer_credit/wizard/wizard_customer_lot_credit_header.py:0
#: code:addons/quotation_bid_customer_credit/wizard/wizard_customer_lot_credit_header.py:0
#, python-format
msgid "Release Lot Successfully"
msgstr "释放lot成功"

#. module: quotation_bid_customer_credit
#: model:ir.actions.server,name:quotation_bid_customer_credit.ir_cron_release_customer_credit_ir_actions_server
#: model:ir.cron,cron_name:quotation_bid_customer_credit.ir_cron_release_customer_credit
#: model:ir.cron,name:quotation_bid_customer_credit.ir_cron_release_customer_credit
msgid "Release Quotation Bid Customer Credit"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_customer_lot_credit__release_type
msgid "Release Type"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields.selection,name:quotation_bid_customer_credit.selection__bid_deposit__approval__request
msgid "Request"
msgstr ""

#. module: quotation_bid_customer_credit
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.bid_deposit_view_form
msgid "Request Approvaling"
msgstr "请求审批"

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_deposit__request_status
msgid "Request Status"
msgstr "审批单状态"

#. module: quotation_bid_customer_credit
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.view_account_payment_form
msgid "Reset To Draft"
msgstr "设为草稿"

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_deposit__activity_user_id
msgid "Responsible User"
msgstr "负责审批人"

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_deposit__message_has_sms_error
msgid "SMS Delivery error"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields.selection,name:quotation_bid_customer_credit.selection__bid_deposit__type__outbound
msgid "Sent"
msgstr "退押金"

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_deposit__state
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_customer_lot_credit__state
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_wizard_customer_lot_credit_line__state
msgid "State"
msgstr "状态"

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_customer_lot_credit__aucnet_state
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_wizard_customer_lot_credit_line__aucnet_state
msgid "Status"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,help:quotation_bid_customer_credit.field_bid_deposit__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: quotation_bid_customer_credit
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.customer_lot_credit_search
msgid "Tel"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_customer_lot_credit__lot_batch
msgid "Tender Batch"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_wizard_customer_lot_credit_header__lots_amount
msgid "Total Amount"
msgstr "总金额"

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_wizard_customer_lot_credit_header__lots_qty
msgid "Total Lots"
msgstr "lot數量"

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,help:quotation_bid_customer_credit.field_bid_deposit__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields.selection,name:quotation_bid_customer_credit.selection__customer_lot_credit__state__unlock
#: model:ir.model.fields.selection,name:quotation_bid_customer_credit.selection__wizard_customer_lot_credit_line__state__unlock
msgid "UNLOCK"
msgstr "释放"

#. module: quotation_bid_customer_credit
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.customer_lot_credit_search
msgid "UnLock"
msgstr "释放"

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_deposit__message_unread
msgid "Unread Messages"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_deposit__message_unread_counter
msgid "Unread Messages Counter"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_customer_lot_credit__vendor_id
msgid "Vendor"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_customer_lot_credit__vendor_code
msgid "Vendor Code"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,field_description:quotation_bid_customer_credit.field_bid_deposit__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,help:quotation_bid_customer_credit.field_bid_deposit__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model,name:quotation_bid_customer_credit.model_wizard_customer_lot_credit_header
#: model:ir.model,name:quotation_bid_customer_credit.model_wizard_customer_lot_credit_line
msgid "Wizard Customer Lot Credit line"
msgstr ""

#. module: quotation_bid_customer_credit
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.customer_lot_credit_search
msgid "Won"
msgstr ""

#. module: quotation_bid_customer_credit
#: code:addons/quotation_bid_customer_credit/models/bid_deposit.py:0
#, python-format
msgid "amount should be integer"
msgstr ""

#. module: quotation_bid_customer_credit
#: model:ir.model.fields,help:quotation_bid_customer_credit.field_customer_lot_credit__release_type
msgid "auto type is invisible to user"
msgstr ""

#. module: quotation_bid_customer_credit
#: code:addons/quotation_bid_customer_credit/models/bid_deposit.py:0
#, python-format
msgid "bid deposit has been completed"
msgstr "押金单已完成"

#. module: quotation_bid_customer_credit
#: code:addons/quotation_bid_customer_credit/models/bid_deposit.py:0
#, python-format
msgid "can not approvaling"
msgstr "无法申请审批"

#. module: quotation_bid_customer_credit
#: code:addons/quotation_bid_customer_credit/models/approval_request.py:0
#, python-format
msgid "can not back to draft because of bid deposit"
msgstr "因为押金已支付无法退回到草稿状态"

#. module: quotation_bid_customer_credit
#: code:addons/quotation_bid_customer_credit/models/bid_order.py:0
#, python-format
msgid ""
"can not redo because of at least one lot has been unlock or supplier time "
"remainning is closed"
msgstr ""

#. module: quotation_bid_customer_credit
#: model_terms:ir.actions.act_window,help:quotation_bid_customer_credit.bid_deposit_inbound_act_window
msgid "create your customer Recevie deposit"
msgstr "创建客户的收押金单"

#. module: quotation_bid_customer_credit
#: model_terms:ir.actions.act_window,help:quotation_bid_customer_credit.bid_deposit_outbound_act_window
msgid "create your customer refund deposit"
msgstr "创建客户的退押金单"

#. module: quotation_bid_customer_credit
#: model:ir.model.constraint,message:quotation_bid_customer_credit.constraint_customer_lot_credit_customer_lot_id_uniq
msgid "customer and lot_id must be unique"
msgstr "客户和标单应唯一"

#. module: quotation_bid_customer_credit
#: code:addons/quotation_bid_customer_credit/models/bid_deposit.py:0
#, python-format
msgid "customer can reback deposit %s"
msgstr "客户可退回押金为%s"

#. module: quotation_bid_customer_credit
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.view_account_payment_form
msgid "deposit order"
msgstr "押金单"

#. module: quotation_bid_customer_credit
#: code:addons/quotation_bid_customer_credit/models/bid_deposit.py:0
#, python-format
msgid "deposit payment has been posted"
msgstr "财务押金单已过账"

#. module: quotation_bid_customer_credit
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.customer_lot_credit_search
msgid "name"
msgstr ""

#. module: quotation_bid_customer_credit
#: code:addons/quotation_bid_customer_credit/models/bid_deposit.py:0
#, python-format
msgid "not config destination account in setting page"
msgstr "没有在配置页面配置目标账户"

#. module: quotation_bid_customer_credit
#: code:addons/quotation_bid_customer_credit/models/bid_deposit.py:0
#, python-format
msgid "not config journal in setting page"
msgstr "没有在配置页面配置日记账"

#. module: quotation_bid_customer_credit
#: code:addons/quotation_bid_customer_credit/models/bid_deposit.py:0
#, python-format
msgid "only cancel state could be draft"
msgstr "只有取消状态才可变为草稿"

#. module: quotation_bid_customer_credit
#: code:addons/quotation_bid_customer_credit/models/approval_request.py:0
#, python-format
msgid "payment deposit has been posted, can't withdraw"
msgstr "财务押金已过账，无法撤回"

#. module: quotation_bid_customer_credit
#: model_terms:ir.ui.view,arch_db:quotation_bid_customer_credit.report_bid_deposit_document
msgid "銀河電訊（香港）有限公司"
msgstr ""
