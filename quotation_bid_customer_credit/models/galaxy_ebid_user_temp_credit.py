import logging
from datetime import timedelta
from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError

_logger = logging.getLogger(__name__)

class GalaxyEbidUserTempCredit(models.Model):
    _name = 'galaxy.ebid.user.temp.credit'
    _description = 'Galaxy Ebid User Temp Credit'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _rec_name = 'odoo_customer_id'

    ebid_user_id = fields.Many2one('galaxy.ebid.user', string='Ebid User', required=True, domain="[('state', '=', 'audited')]")
    state = fields.Selection([('draft', 'Draft'), ('confirm', 'Confirm'), ('done', 'Done'), ('cancel', 'Cancel')],
                             default='draft',
                             tracking=True)
    odoo_customer_id = fields.Many2one('res.partner', related='ebid_user_id.odoo_customer', string='Customer', readonly=True)
    currency_id = fields.Many2one('res.currency', related='ebid_user_id.odoo_customer.customer_currency_id', string='Currency', readonly=True)
    temp_credit = fields.Float(string='Temporary credit', required=True, digits=(10, 2), tracking=True)
    temp_credit_expire_date = fields.Datetime(string='Credit expire date', tracking=True)
    note = fields.Text()



    @api.constrains('temp_credit')
    def _constrains_temp_credit(self):
        for partner in self.odoo_customer_id:
            if self.temp_credit:
                if partner.property_product_pricelist.currency_id.name == "HKD":
                    if len(str(int(self.temp_credit))) > 12:
                        raise models.UserError(_('temporary credit should less than 999999999999'))
                elif partner.property_product_pricelist.currency_id.name == "USD":
                    if len(str(int(self.temp_credit))) > 10:
                        raise models.UserError(_('temporary credit should less than 9999999999'))
                else:
                    raise models.UserError(_('customer should set currency (HKD or USD) firstly'))

    def unlink(self):
        for rec in self:
            if rec.state not in  ('draft', 'cancel'):
                raise UserError(_("You can only delete draft or cancel state temporary credit."))
        return super(GalaxyEbidUserTempCredit, self).unlink()

    def action_confirm(self):
        temporary_credit_expire_hours = int(self.env['ir.config_parameter'].sudo().get_param('ebid_user_temporary_credit_expiration_time_in_hours') or 0)
        if temporary_credit_expire_hours <= 0:
            raise UserError(_("Temporary credit expiration time must be greater than 0."))
        if self.temp_credit <= 0:
            raise UserError(_("Temporary credit must be greater than 0."))
        if self.state != 'draft':
            raise UserError(_("You can only confirm draft temporary credit."))
        original_temp_credit = self.odoo_customer_id.customer_bid_credit_temporary
        _logger.info("customer id %s original_temp_credit: %s", self.odoo_customer_id, original_temp_credit)
        new_temp_credit = original_temp_credit + self.temp_credit
        self.odoo_customer_id.write({'customer_bid_credit_temporary': new_temp_credit})
        _logger.info("customer id %s new_temp_credit: %s", self.odoo_customer_id, new_temp_credit)
        self.temp_credit_expire_date = fields.Datetime.now() + timedelta(hours=temporary_credit_expire_hours)
        self.state = 'confirm'

    def change_customer_temp_credit(self, customer=None, amount=0):
        """
        临时方法，优化后删除
        """
        if not customer or not amount:
            return
        original_temp_credit = customer.customer_bid_credit_temporary
        _logger.info("customer id %s original_temp_credit: %s", customer, original_temp_credit)
        new_temp_credit = original_temp_credit + amount
        customer.write({'customer_bid_credit_temporary': new_temp_credit})
        _logger.info("customer id %s new_temp_credit: %s", customer, new_temp_credit)

    def action_cancel(self):
        if self.state != 'confirm':
            raise UserError(_("You can only cancel confirm temporary credit."))
        original_temp_credit = self.odoo_customer_id.customer_bid_credit_temporary
        _logger.info("customer id %s original_temp_credit: %s", self.odoo_customer_id, original_temp_credit)
        new_temp_credit = original_temp_credit - self.temp_credit
        if new_temp_credit < 0:
            raise UserError(_("Temporary credit must be greater than 0."))
        self.odoo_customer_id.write({'customer_bid_credit_temporary': new_temp_credit})
        _logger.info("customer id %s new_temp_credit: %s", self.odoo_customer_id, new_temp_credit)
        self.state = 'cancel'
    
    def action_done(self):
        if self.state != 'confirm':
            raise UserError(_("You can only done confirm temporary credit."))
        original_temp_credit = self.odoo_customer_id.customer_bid_credit_temporary
        _logger.info("customer id %s original_temp_credit: %s", self.odoo_customer_id, original_temp_credit)
        new_temp_credit = original_temp_credit - self.temp_credit
        if new_temp_credit < 0:
            raise UserError(_("Temporary credit must be greater than 0."))
        self.odoo_customer_id.write({'customer_bid_credit_temporary': new_temp_credit})
        _logger.info("customer id %s new_temp_credit: %s", self.odoo_customer_id, new_temp_credit)
        self.state = 'done'

    def action_schedule_check_temp_credit(self):
        check_rec = self.search([('state', '=', 'confirm'), ('temp_credit_expire_date', '<=', fields.Datetime.now())])
        for rec in check_rec:
            rec.action_done()
