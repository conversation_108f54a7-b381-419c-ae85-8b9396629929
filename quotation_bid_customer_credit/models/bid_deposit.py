from odoo import models, fields, api, _, SUPERUSER_ID
import logging
import math
_logger = logging.getLogger(__name__)


class BidDeposit(models.Model):
    _name = 'bid.deposit'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = "Bid Deposit"
    _rec_name = 'number'

    number = fields.Char(string='Number',
                         required=True, default=lambda self: _('New'))
    customer_id = fields.Many2one(
        'res.partner', required=True, string='Customer', domain=[('customer_rank', '>', 0)])
    phone = fields.Char(related="customer_id.phone")
    bid_user_id = fields.Char(related="customer_id.bid_user_id")
    amount = fields.Monetary(currency_field='currency_id')
    currency_id = fields.Many2one('res.currency', required=True)
    note = fields.Text()
    type = fields.Selection(
        [('inbound', 'Receive'), ('outbound', 'Sent')], required=True, string='Order Type')
    journal = fields.Selection([('bank', 'Bank'), ('cash', 'Cash')],
                               required=True, default='bank', string='Form of payment')
    state = fields.Selection([('draft', 'Draft'),
                              ('approvaling', 'Approvaling'),
                              ('complete', 'Complete'),
                              ('cancel', 'Cancel')], default='draft')
    user_id = fields.Many2one(
        'res.users', string='Customer Service', default=lambda self: self.env.uid, index=True)
    attachment_ids = fields.Many2many('ir.attachment')
    approval = fields.Selection(
        [('approvaled', 'Approvaled'), ('request', 'Request')])
    request_status = fields.Selection(
        related='approval_request_id.request_status')
    reference = fields.Char(related='approval_request_id.reference')
    account_payment_id = fields.Many2one('account.payment')
    approval_request_id = fields.Many2one('approval.request')
    refundable_deposit = fields.Float(
        digits=(10, 2), compute='_compute_refundable_deposit')

    def unlink(self):
        deposit_state_lst = self.mapped('state')
        deposit_state_lst = list(set(deposit_state_lst))
        if len(deposit_state_lst) == 1 and deposit_state_lst[-1] == 'draft':
            super(BidDeposit, self).unlink()
        else:
            raise models.UserError(_('Only draft order can delete'))

    def _compute_refundable_deposit(self):
        for bd in self:
            bd.refundable_deposit = bd._get_refundable_deposit()

    @api.onchange('attachment_ids')
    def set_attachment_res_id(self):
        for item in self.attachment_ids:
            item.res_model = 'bid.deposit'
            item.res_id = self.id

    def action_approval(self):
        if self.type == 'outbound':
            refundable_deposit = self._get_refundable_deposit()
            if self.amount > refundable_deposit:
                raise models.UserError(
                    _('customer can reback deposit %s') % (refundable_deposit))
            approval_request_id = self._create_approval()
            _logger.info(
                f'before update deposit {self.customer_id.customer_deposit}')
            _logger.info(
                f'customer {self.customer_id.name} {self.customer_id.id} reduce {self.amount} deposit')
            self.sudo().customer_id.customer_deposit -= self.amount
            _logger.info(
                f'after update deposit {self.customer_id.customer_deposit}')
            self.state = 'approvaling'
            self.approval_request_id = approval_request_id
            _logger.info('exec action_approval finished')
        else:
            raise models.UserError(_('can not approvaling'))

    def _create_approval(self):
        category_id = self.sudo().env.ref(
            'quotation_bid_customer_credit.approval_category_deposit').id
        category = self.sudo().env['approval.category'].search(
            [('id', '=', category_id)])
        if not category:
            raise models.UserError(_('Approval request not exsit'))
        if category.automated_sequence:
            name = category.sequence_id.next_by_id()
        else:
            name = category.name
        request = self.sudo().env['approval.request'].create({
            'name': name,
            'category_id': category_id,
            'request_owner_id': self.env.uid,
            'amount': self.amount,
            'bid_deposit_id': self.id,
            'partner_id': self.customer_id.id,
            'approver_ids': [(0, 0, {'user_id': approver.id, 'status': 'new'}) for approver in category.user_ids]})
        request.action_confirm()
        return request.id

    def action_cancel(self):
        self = self.with_user(SUPERUSER_ID)
        if self.account_payment_id.state == 'posted':
            raise models.UserError(_("deposit payment has been posted"))
        if self.type == 'outbound':
            # 调用action_cancel,上下文必传
            self.account_payment_id.with_context(
                {'from_bid_deposit_to_force_cancel': True}).action_cancel()
            self.state = 'cancel'
            request_status = self.approval_request_id.request_status
            if request_status == 'pending':
                self.approval_request_id.sudo().mapped(
                    'approver_ids').write({'status': 'cancel'})
            if request_status == 'pending' or request_status == 'approved':
                _logger.info('bid deposit action_cancel increase the deposit')
                _logger.info(
                    f'before increase deposit {self.customer_id.customer_deposit}')
                _logger.info(
                    f'customer {self.customer_id.name} {self.customer_id.id} increases {self.amount} deposit')
                self.customer_id.customer_deposit += self.amount
                _logger.info(
                    f'after increase deposit {self.customer_id.customer_deposit}')
        else:
            self.account_payment_id.with_context(
                {'from_bid_deposit_to_force_cancel': True}).action_cancel()
            self.state = 'cancel'

    def action_draft(self):
        if self.state == 'cancel':
            self.state = 'draft'
        else:
            raise models.UserError(_('only cancel state could be draft'))

    def action_complete(self):
        if self.state == 'complete':
            raise models.UserError(_("bid deposit has been completed"))
        if self.journal == 'bank':
            if not self.attachment_ids and self.type == 'inbound':
                raise models.UserError(_("Please upload transfer receipt"))
            journal_id = int(
                self.env['ir.config_parameter'].sudo().get_param('bank_journal_id'))
        else:
            journal_id = int(
                self.env['ir.config_parameter'].sudo().get_param('cash_journal_id'))
        destination_account_id = int(
            self.env['ir.config_parameter'].sudo().get_param('bid_deposit_account_id'))
        if not journal_id:
            raise models.UserError(_("not config journal in setting page"))
        if not destination_account_id:
            raise models.UserError(
                _('not config destination account in setting page'))
        data = {'amount': self.amount,
                'currency_id': self.currency_id.id,
                'destination_account_id': destination_account_id,
                'journal_id': journal_id,
                'partner_id': self.customer_id.id,
                'payment_type': self.type,
                'partner_type': 'customer',
                'is_deposit': True,
                'bid_deposit_id': self.id}
        payment = self.env['account.payment'].sudo().create(data)
        self.state = 'complete'
        self.account_payment_id = payment.id
        for item in self.attachment_ids:
            payment_att = item.copy()
            payment_att.sudo().res_model = 'account.payment'
            payment_att.sudo().res_id = payment.id

    @api.onchange('customer_id')
    def _onchange_customer_id(self):
        self.currency_id = self.customer_id.property_product_pricelist.currency_id
        self.refundable_deposit = self._get_refundable_deposit()

    def _get_refundable_deposit(self):
        if self.customer_id.deposit_rate >= 0:
            if self.customer_id.deposit_rate > 0:
                refundable_deposit = (self.customer_id.customer_bid_credit -
                                      self.customer_id.frozen_quota - self.customer_id.customer_bid_credit_temporary) / self.customer_id.deposit_rate
            else:
                refundable_deposit = (self.customer_id.customer_deposit -
                                      self.customer_id.frozen_quota - self.customer_id.customer_bid_credit_temporary)/1
            if refundable_deposit > 0:
                return refundable_deposit
        return 0

    @api.constrains('amount')
    def _constrains_amoount(self):
        for bd in self:
            if bd.customer_id.property_product_pricelist.currency_id.name == "HKD":
                digits = format(math.modf(bd.amount)[0], '.2f')
                if float(digits) > 0:
                    raise models.UserError(_('amount should be integer'))
            if bd.amount <= 0:
                raise models.UserError(_('Amount should greater than zero'))

    @api.model
    def create(self, vals):
        if vals.get('number', _('New')) == _('New'):
            sequence = self.env['ir.sequence'].next_by_code('bid.deposit')
            vals['number'] = sequence
        rst = super(BidDeposit, self).create(vals)
        for bid_deposit in rst:
            for item in bid_deposit.attachment_ids:
                item.res_model = 'bid.deposit'
                item.res_id = bid_deposit.id
        return rst

    def write(self, vals):
        # 移除附件跟记录的关联
        for bid_deposit in self:
            for item in bid_deposit.attachment_ids:
                item.res_model = ''
                item.res_id = 0
        rst = super(BidDeposit, self).write(vals)
        # 增加附件跟记录的关联
        for bid_deposit in self:
            for item in bid_deposit.attachment_ids:
                item.res_model = 'bid.deposit'
                item.res_id = bid_deposit.id
        return rst
