from odoo.addons.queue_job.exception import RetryableJobError
from odoo import models, _
from odoo.addons.java_bid_server.models.api_customer_lot_credit import LotCreditAPI
from odoo.addons.base_galaxy.models.public_func import display_sticky_notification
from odoo.addons.base_galaxy.models.galaxy_func import time_costing
import datetime
import logging
import uuid
_logger = logging.getLogger(__name__)


class BidOrder(models.Model):
    _inherit = 'bid.order'

    @time_costing
    def _update_lot_credit_bid_status(self):
        lot_ids = self.bid_order_ids.ids
        clcs = self.env['customer.lot.credit'].search(
            [('lot_id', 'in', lot_ids)])
        channel = self.env.ref(
            'quotation_bid_customer_credit.channel_auction').sudo()
        clcs.with_delay(priority=1, description=f"{self.name}修改额度管理记录的状态",
                        channel=channel.complete_name)._compute_get_customer_bid_state()

    @time_costing
    def _reverse_update_lot_credit_bid_status(self):
        lot_ids = self.bid_order_ids.ids
        self.env['customer.lot.credit'].search(
            [('lot_id', 'in', lot_ids)])._reverse_compute_get_customer_bid_state()

    def confirm_to_done(self):
        super(BidOrder, self).confirm_to_done()
        self._customer_lot_credit_rollback()
        self._update_lot_credit_bid_status()

    def _customer_lot_credit_rollback(self):
        clc = self.env['customer.lot.credit'].search(
            [('group_id', '=', self.name)])
        min_supplier_time_remaining = min(
            self.origin_id.mapped('supplier_time_remaining'))
        galaxy_bid_end_time_delay = int(self.env['ir.config_parameter'].sudo(
        ).get_param('galaxy_bid_end_time_delay') or 0)
        min_supplier_time_remaining = min_supplier_time_remaining + \
            datetime.timedelta(minutes=galaxy_bid_end_time_delay)
        unlock = any([True for cl in clc if cl.state == 'unlock'])
        _logger.info(
            f'min_supplier_time_remaining {min_supplier_time_remaining}')
        _logger.info(datetime.datetime.now())
        _logger.info(f"unlock {unlock}")
        if unlock or datetime.datetime.now() >= min_supplier_time_remaining:
            raise models.UserError(
                _("can not redo because of at least one lot has been unlock or supplier time remainning is closed"))

    def set_lot_lost_and_release_credit(self):
        self.ensure_one()
        lots = self.bid_order_ids.filtered(lambda lot: not lot.customer_win and
                                           not lot.vendor_lot_ids.filtered(lambda vendor_lot: vendor_lot.is_countered))
        if lots:
            lots.bid_status = 'lost'
            lots.vendor_lot_ids.write({'bid_status': 'lose_the_bid'})
            self.env['customer.lot.credit'].search(
                [('lot_id', 'in', lots.ids)]).write({'bid_status': 'Lost'})
            self._send_message_to_customer(lots)
            self.action_relase_lot(lots)
            # notfictation
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('open_bid'),
                    'message': _("Success"),
                    'sticky': False,
                    'className': 'bg-info',
                }
            }
        else:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('open_bid'),
                    'message': _("don't need to open bid"),
                    'sticky': False,
                    'className': 'bg-info',
                }
            }

    @time_costing
    def message_to_app(self):
        result = super(BidOrder, self).message_to_app()
        self._update_lot_credit_bid_status()
        not_win_lots = self.bid_order_ids.filtered(
            lambda lot: not lot.customer_win and lot.customer_bid)
        if not_win_lots:
            self.action_relase_lot(not_win_lots)
        return result

    @time_costing
    def reverse_message_to_app(self):
        result = super(BidOrder, self).reverse_message_to_app()
        self._reverse_update_lot_credit_bid_status()
        lock_result = self.action_lock_lot()
        if lock_result:
            return lock_result
        return result

    @time_costing
    def action_lock_lot(self):
        not_win_lots = self.bid_order_ids.filtered(
            lambda l: not l.customer_win and l.customer_bid)
        lot_ids = not_win_lots.ids or []
        customer_ids = not_win_lots.customer_bid.name.ids or []
        customer_lot_credit = self.env['customer.lot.credit'].search(
            [('state', '=', 'unlock'), ('lot_id', 'in', lot_ids), ('customer', 'in', customer_ids)])
        lots_list = []
        for rec in customer_lot_credit:
            # 标单报价是0直接释放
            if rec.price_amount > 0:
                lots_list.append({
                    "lotId": rec.lot_id.name,
                    "userId": rec.user_id,
                    "standardId": rec.vendor_code,
                    "groupId": rec.group_id,
                    'priceAmount': round(rec.price_amount, 2)
                })

        if lots_list:
            channel = self.env.ref(
                'quotation_bid_customer_credit.channel_auction').sudo()
            self.with_delay(priority=2, description=f"{self.name}锁定额度", channel=channel.complete_name)._action_lock_lot(
                lots_list=lots_list)

    @time_costing
    def action_relase_lot(self, lots):
        not_win_lots = lots
        lot_ids = not_win_lots.ids or []
        customer_ids = not_win_lots.customer_bid.name.ids or []
        customer_lot_credit = self.env['customer.lot.credit'].search([('state', '=', 'lock'),
                                                                      ('lot_id',
                                                                       'in', lot_ids),
                                                                      ('customer', 'in', customer_ids)])
        lots_list = []
        for rec in customer_lot_credit:
            # 标单报价是0直接释放
            if rec.price_amount != 0:
                lots_list.append({
                    "lotId": rec.lot_id.name,
                    "userId": rec.user_id,
                    "standardId": rec.vendor_code,
                    "groupId": rec.group_id
                })
            else:
                self.env['customer.lot.credit'].search([('lot_id', '=', rec.lot_id.name),
                                                        ('user_id', '=', rec.user_id)]).write({'state': 'unlock'})

        # 释放额度放到队列异步执行
        if lots_list:
            channel = self.env.ref(
                'quotation_bid_customer_credit.channel_auction').sudo()
            self.with_delay(priority=0, description=f"{self.name}释放额度",
                            channel=channel.complete_name)._action_relase_lot(lots_list)

    def _action_relase_lot(self, lots_list):
        _logger.info(f'release lots credit {lots_list}')
        GalaxyLotCreditAPI = LotCreditAPI()
        response_list = GalaxyLotCreditAPI.batch_free_credit_v2(lots_list)
        success_lots, error_lots, server_error = GalaxyLotCreditAPI.format_response_list(
            response_list)
        for success_lot in success_lots:
            user_id = success_lot.get('userId', '')
            lot_id = success_lot.get('lotId', '')
            self.env['customer.lot.credit'].search([('lot_id', '=', lot_id),
                                                    ('user_id', '=', user_id)]).write({'state': 'unlock'})
        if server_error or error_lots:
            # 遇到错误重新入队执行
            raise RetryableJobError(msg=f'{server_error} {error_lots}')

    def change_lot_credit_state(self, lots=[], state=''):
        if not lots or not state:
            return
        for success_lot in lots:
            user_id = success_lot.get('userId', '')
            lot_id = success_lot.get('lotId', '')
            self.env['customer.lot.credit'].search([('lot_id', '=', lot_id),
                                                    ('user_id', '=', user_id)]).write({'state': state})

    def change_temp_credit_by_lot(self, fix_lots_list=[], method=''):
        if not fix_lots_list or not method:
            return
        for fix_lot in fix_lots_list:
            userId = fix_lot.get('userId', '')
            customer = self.env['res.partner'].search([('ref', '=', userId)])
            amount = fix_lot.get(
                'priceAmount', 0) if method == 'add' else -fix_lot.get('priceAmount', 0)
            # 修改客户的临时额度有可能会失败--比如用户不存在
            try:
                self.env['galaxy.ebid.user.temp.credit'].change_customer_temp_credit(customer=customer, amount=amount)
            except Exception as e:
                _logger.error(f'change_temp_credit_by_lot error {e} {fix_lot}')

    def _action_lock_lot(self, lots_list):
        """
        锁定额度
        需要特别注意的问题点：提交给Ebid Java服务批量锁定，有的成功，有的可能因为额度不够等异常失败
        对于成功的，需要修改额度管理记录的状态为lock
        如果因为额度不够，锁定失败的，需要设置一个临时额度，确保锁定成功，然后撤销临时额度，这样用户的可用额度会减少
        """
        _logger.info(f'lock lots credit {lots_list}')
        GalaxyLotCreditAPI = LotCreditAPI()
        response_list = GalaxyLotCreditAPI.batch_lock_credit_v2(lots_list)
        success_lots, error_lots, server_error = GalaxyLotCreditAPI.format_response_list(
            response_list)
        if server_error:
            raise RetryableJobError(msg=f'{server_error} {error_lots}')
        self.change_lot_credit_state(lots=success_lots, state='lock')
        if error_lots:
            # 遇到额度不足的错误，增加临时额度，重新锁定
            # 增加临时额度，重新锁定的已经不再需要,选择时间移除--因为现在后台发起的锁定,允许额度为负数
            _logger.warning(f'lock lots credit error {error_lots}')
            fix_lots_list = []
            for error_lot in error_lots:
                lotId = error_lot.get('lotId', '')
                fix_lots_list.append(
                    next((item for item in lots_list if item['lotId'] == lotId), None))
            if fix_lots_list:
                # 根据Lot增加临时额度
                self.change_temp_credit_by_lot(fix_lots_list=fix_lots_list, method='add')
                # 按Lot锁定额度
                GalaxyLotCreditAPI = LotCreditAPI()
                response_list = GalaxyLotCreditAPI.batch_lock_credit_v2(
                    fix_lots_list)
                success_lots, error_lots, server_error = GalaxyLotCreditAPI.format_response_list(
                    response_list)
                # 根据Lot移除临时额度
                self.change_temp_credit_by_lot(fix_lots_list=fix_lots_list, method='sub')
                if server_error:
                    raise RetryableJobError(msg=f'{server_error} {error_lots}')
                self.change_lot_credit_state(lots=success_lots, state='lock')
                # 如果修复额度后还有错误，只是记录日志，不再处理
                _logger.error(f'lock lots credit error after fix customer temporary credit {error_lots}')
