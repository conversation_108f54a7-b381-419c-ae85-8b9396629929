from odoo import models, fields, api, _
from odoo.exceptions import UserError


class bidResult(models.Model):
    _inherit = 'bid.result'
    
    def _update_lot_credit_bid_status(self, lot_ids=[]):
        self.env['customer.lot.credit'].search([('lot_id', 'in', lot_ids)])._compute_get_customer_bid_state()
        
    def confirm_bid_result(self, lot_ids=[]):
        super(bidResult,self).confirm_bid_result(lot_ids)
        self._update_lot_credit_bid_status(lot_ids)

    def draft_bid_result(self, lot_ids=[]):
        super(bidResult,self).draft_bid_result(lot_ids)
        self._update_lot_credit_bid_status(lot_ids)