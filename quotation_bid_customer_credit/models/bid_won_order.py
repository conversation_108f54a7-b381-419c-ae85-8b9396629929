from odoo import models, fields, api, _
from odoo.exceptions import UserError


class bidWonOrder(models.Model):
    _inherit = 'bid.won.order'

    def _update_lot_credit_bid_status(self, lot_ids=[]):
        self.env['customer.lot.credit'].search([('lot_id', 'in', lot_ids)])._compute_get_customer_bid_state()

    def confirm_bid_won_order(self, lot_ids=[], check_sign=True):
        super(bidWonOrder, self).confirm_bid_won_order(lot_ids, check_sign=True)
        # self._update_lot_credit_bid_status(lot_ids)
        self.env['customer.lot.credit'].search([('lot_id', 'in', lot_ids)])._compute_get_customer_bid_state_4_wo()

    def cancel_bid_won_order(self, lot_ids=[], check_sign=False):
        super(bidWonOrder, self).cancel_bid_won_order(lot_ids, check_sign=False)
        self.env['customer.lot.credit'].search([('lot_id', 'in', lot_ids)])._reverse_compute_get_customer_bid_state_4_wo()


    """
    def draft_bid_result(self, lot_ids=[]):
        super(bidResult,self).draft_bid_result(lot_ids)
        self._update_lot_credit_bid_status(lot_ids)
    """
