from odoo import models, fields, api, _


class ResConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    galaxy_bid_end_time_delay = fields.Integer(string='Bid End Time Delay(Minutes)', config_parameter='galaxy_bid_end_time_delay')
    temporary_credit_expiration_time_in_hours = fields.Integer(config_parameter='ebid_user_temporary_credit_expiration_time_in_hours')
    bank_journal_id = fields.Many2one('account.journal', domain="[('type', 'in', ('bank', ))]", config_parameter='bank_journal_id')
    cash_journal_id = fields.Many2one('account.journal', domain="[('type', 'in', ('cash',))]", config_parameter='cash_journal_id')
    """ @api.constrains('galaxy_bid_end_time_delay')
    def _constrains_discount(self):
        for d in self:
            if d.galaxy_bid_end_time_delay > 100 or d.discount <= 0:
                raise models.UserError(_('Bid time delay should be between 0-100!')) """

    def get_values(self):
        res = super(ResConfigSettings, self).get_values()
        galaxy_bid_end_time_delay = int(self.env['ir.config_parameter'].sudo().get_param('galaxy_bid_end_time_delay') or 0)
        temporary_credit_expiration_time_in_hours = int(self.env['ir.config_parameter'].sudo().get_param('ebid_user_temporary_credit_expiration_time_in_hours') or 0)
        bank_journal_id = int(self.env['ir.config_parameter'].sudo().get_param('bank_journal_id'))
        cash_journal_id = int(self.env['ir.config_parameter'].sudo().get_param('cash_journal_id'))
        res.update(
            galaxy_bid_end_time_delay=galaxy_bid_end_time_delay,
            bank_journal_id = bank_journal_id,
            cash_journal_id = cash_journal_id,
            temporary_credit_expiration_time_in_hours = temporary_credit_expiration_time_in_hours,
        )
        return res
