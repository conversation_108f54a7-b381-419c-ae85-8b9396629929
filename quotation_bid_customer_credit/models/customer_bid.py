import logging
_logger = logging.getLogger(__name__)
from odoo import models



class CustomerBidLine(models.Model):
    _inherit = 'customer.bid.line'

    # def name_get(self):
    #     if self._context.get('bid'): 
    #         return [(line.id,'%s  (HK$%s/US$%s)(%s/%s)*%s' 
    #             %(line.name.name,line.getHKD(),format(line.getUSD(),',.2f'), 
    #                 line.get_total_waiting_pre_lock_with_currency(), line.get_available_quota_with_currency(), 
    #                 line.compare_available_quota_with_total_waiting_pre_lock())) for line in self]
    #     return [(line.id,'%s' %(line.name.name)) for line in self]

    # def compare_available_quota_with_total_waiting_pre_lock(self):
    #     available_quota = self.get_available_quota()
    #     total = self.get_total_waiting_pre_lock()
    #     if total > available_quota:
    #         return '1'
    #     else:
    #         return '0'

    # def get_available_quota(self):
    #     available_quota = self.name.available_quota
    #     return available_quota

    # def get_total_waiting_pre_lock(self):
    #     # 总待锁定额度 = current_waiting_pre_lock_price 当前选择的客户（可能未保存）） + 预锁定额度
    #     current_waiting_pre_lock_price = self._context.get('current_waiting_pre_lock_price', 0)
    #     customer_id = self._context.get('customer_id')
    #     total = self.get_pre_lock_credit() + self.waiting_for_pre_lock()
    #     if self.name.id == customer_id:
    #         total += current_waiting_pre_lock_price
    #     return total

    # def get_available_quota_with_currency(self):
    #     available_quota = self.get_available_quota()
    #     if self.customer_currency_id.name == "HKD":
    #         return ''.join(['HK$', str(format(available_quota,',.0f'))])
    #     elif self.customer_currency_id.name == "USD":
    #         return ''.join(['HK$', str(format(available_quota,',.2f'))])
    #     return str(format(available_quota,',.2f'))

    # def get_total_waiting_pre_lock_with_currency(self):
    #     total = self.get_total_waiting_pre_lock()
    #     if self.customer_currency_id.name == "HKD":
    #         return ''.join(['HK$', str(format(total,',.0f'))])
    #     elif self.customer_currency_id.name == "USD":
    #         return ''.join(['HK$', str(format(total,',.2f'))])
    #     return str(format(total,',.2f'))

    # def get_pre_lock_credit(self):
    #     pre_lock_credit = self.env['customer.lot.credit'].search([('customer', '=',self.name.id ), 
    #                                                             ('state', '=', 'pre_lock')])
    #     return sum([pre.price_amount for pre in pre_lock_credit])
    

    # def waiting_for_pre_lock(self):
    #     # 不包含当前lot的等待锁定额度
    #     lots = self.env['bid.order'].search([('id', '=', self.line_id.order_id.id)]).bid_order_ids
    #     bid_type = self.line_id.order_id.bid_type
    #     if bid_type == 'items':
    #         w_pre_lock = 0
    #         for lot in lots:
    #             if lot.id != self.line_id.id and lot.customer_bid.name==self.name:
    #                 w_pre_lock += sum([item.customer_item_quotation_id.bid_price*item.quantity for item in lot.bid_order_lot_line_ids])
    #         return w_pre_lock  
    #     else:
    #         return sum([lot.customer_bid.bid_price*lot.quantity for lot in lots if lot.id !=self.line_id.id and lot.customer_bid.name==self.name])


    