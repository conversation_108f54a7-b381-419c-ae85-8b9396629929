import datetime
import uuid
import logging
from odoo import models, fields, _, tools, api
from itertools import groupby
from odoo.addons.java_bid_server.models.api_customer_lot_credit import LotCreditAPI
from odoo.addons.base_galaxy.models.public_func import display_notification, display_sticky_notification

_logger = logging.getLogger(__name__)


class CustomerLotCredit(models.Model):
    _name = 'customer.lot.credit'
    _description = 'Customer Lot Credit'
    _rec_name = 'lot_id'

    vendor_code = fields.Char(required=True)
    group_id = fields.Char(required=True)
    customer = fields.Many2one('res.partner', required=True, index=True)
    customer_tel = fields.Char(related='customer.phone')
    lot_batch = fields.Char('Tender Batch')
    user_id = fields.Char('Ebid UserID', index=True)
    lot_id = fields.Many2one('bid.order.lot', required=True, index=True)
    vendor_id = fields.Many2one(related='lot_id.order_id.supplier_id', store=True, index=True)
    brand = fields.Char(related="lot_id.make")
    model = fields.Char(related="lot_id.model")
    version = fields.Char(related="lot_id.version")
    quantity = fields.Integer(related="lot_id.quantity")
    bided_qty = fields.Integer()
    price_amount = fields.Float(digits=(16, 2), string="Amount")
    state = fields.Selection([('lock', 'LOCK'), ('unlock', 'UNLOCK')], default='lock', index=True)
    aucnet_state = fields.Selection(related='lot_id.order_id.state')
    bid_status = fields.Char(string="Bid Status", index=True, default='Bidding')
    release_type = fields.Selection([('auto', 'Auto'), ('manual', 'Manual')],
                                    default='auto',
                                    help="auto type is invisible to user")
    active = fields.Boolean('Active',
                            default=True,
                            help="If unchecked, it will allow you to hide the folder instead of removing it.")

    _sql_constraints = [('customer_lot_id_uniq', 'unique(customer,lot_id)', 'customer and lot_id must be unique')]

    @api.model
    def set_customer_lot_credit_state_unlock(self, data=[]):
        _logger.info('修改额度管理的状态为unlock;%s', data)
        user_id_lst = []
        lot_id_lst = []
        for credit in data:
            user_id_lst.append(credit.get('user_id'))
            lot_id_lst.append(credit.get('lot_id'))
        lot_id_lst = self.env['bid.order.lot'].search([('name', 'in', lot_id_lst)]).ids
        self.env['customer.lot.credit'].search([('user_id', 'in', user_id_lst), ('lot_id', 'in', lot_id_lst)]).state = 'unlock'
        return 0

    """ @api.depends('lot_id')
    def _compute_get_lot_batch(self):
        for rec in self:
            rec.lot_batch = ''
            document_ids = rec.lot_id.order_id.origin_id.mapped('document_id')
            if len(document_ids) > 0:
                rec.lot_batch = document_ids[0].folder_id.name """

    def _compute_get_customer_bid_state(self):
        # 开标的时候检查是否中标，修改一些状态信息
        # 过滤from excel=True的记录，是因为目前的额度管理里面，没有从excel导入的记录
        bid_state_convert = {'bid': 'Bidding', 'won': 'Won', 'failed': 'Lost'}
        cbls = self.env['customer.bid.line'].search([('from_excel', '=', False), ('line_id', 'in', self.lot_id.ids), ('name', 'in', self.customer.ids)])
        for rec in self:
            # rec.bid_status = 'Bidding'
            for cbl in cbls:
                if cbl.line_id == rec.lot_id and cbl.name == rec.customer:
                    rec.bid_status = bid_state_convert.get(cbl.state, 'Bidding')
                    release_type = ('manual' if rec.bid_status == 'Won' else 'auto')
                    rec.release_type = release_type
                    break

    def _reverse_compute_get_customer_bid_state(self):
        # 反向修改时，只需要将bid_status 从lost修改成Bidding；
        # _compute_get_customer_bid_state包含了两部分功能： 更新bid_status 和 release_type
        clcs = self.filtered(lambda rec: rec.bid_status == 'Lost')
        for rec in clcs:
            rec.bid_status = 'Bidding'

    def _compute_get_customer_bid_state_4_wo(self):
        _logger.info('---- in _compute_get_customer_bid_state:')
        # 开标的时候更加是否中标，修改一些状态信息
        bid_state_convert = {'bid': 'Bidding', 'won': 'Won', 'failed': 'Lost'}
        for rec in self:
            rec.bid_status = 'Bidding'
            customer_bid_state = rec.lot_id.customer_bid_lines.filtered(
                lambda m: m.line_id == rec.lot_id and m.name == rec.customer).mapped('state')
            if len(customer_bid_state) > 0:
                _logger.info(f"---- rec: {rec}")
                rec.bid_status = bid_state_convert.get(customer_bid_state[0], 'Bidding')
                release_type = ('manual' if rec.bid_status == 'Won' else 'auto')
                rec.release_type = release_type

    def _reverse_compute_get_customer_bid_state_4_wo(self):
        _logger.info(f'---- in _reverse_compute_get_customer_bid_state_4_wo: {self}')
        clcs = self.filtered(lambda rec: rec.bid_status == 'Won')
        for rec in clcs:
            _logger.info(f"------ rec: {rec}")
            rec.bid_status = 'Bidding'
            rec.release_type = 'auto'

    def _change_won_lot_release_state(self):
        for rec in self:
            if rec.bid_status == 'Won':
                rec.release_type = 'manual'

    def load_initialize_won_lots(self):
        # 仅仅是初始化信用额度使用
        # ('lot_bid_state', '=', 'won')
        won_losts = self.env['report.bid.order.lot.line'].search([])
        cur_rec = self.search([])
        for bid_lot_id, lots in groupby(won_losts, lambda m: m.bid_lot_id):
            amount = 0
            lots = list(lots)
            for item in lots:
                amount += item.lot_or_item_customer_hk_quotation * item.quantity
            if amount > 0:
                if len(cur_rec.filtered(lambda a: a.lot_id == bid_lot_id and a.customer == bid_lot_id.customer_id)) == 0:
                    self.create({
                        'vendor_code': bid_lot_id.order_id.supplier_id.ref or '',
                        'group_id': bid_lot_id.order_id.name,
                        'customer': bid_lot_id.customer_id.id,
                        'user_id': bid_lot_id.customer_id.ref,
                        'lot_id': bid_lot_id.id,
                        'price_amount': amount,
                        'state': 'unlock',
                    })

    def create_lot_credit_wizard(self, action_type=None):
        if not action_type:
            return
        wizard_header_rec = {}
        lots_list = []
        ids = self._context.get('active_ids')
        if len(ids) == 0:
            return
        quota_record_limit = int(tools.config.get('galaxy_quota_limit', 500))
        if len(ids) > quota_record_limit:
            raise models.UserError(_(f'The selected lots can not more than {quota_record_limit}'))
        recs = self.browse(ids)
        for rec in recs:
            lots_list.append((0, 0, {
                "vendor_code": rec.vendor_code,
                "group_id": rec.group_id,
                "customer": rec.customer.id,
                "user_id": rec.user_id,
                "lot_id": rec.lot_id.id,
                "price_amount": rec.price_amount,
                "state": rec.state,
            }))
        res_id = 0
        wizard_header_rec['customer_lot_credit_ids'] = lots_list
        res_id = self.env['wizard.customer.lot.credit.header'].create(wizard_header_rec).id
        return res_id

    def action_relase_lot(self):
        res_id = self.create_lot_credit_wizard('release')
        view_id = self.env.ref('quotation_bid_customer_credit.wizard_customer_lot_credit_header_form').id
        return {
            'name': 'Release Quotation Credit',
            'type': 'ir.actions.act_window',
            'res_model': 'wizard.customer.lot.credit.header',
            'view_type': 'form',
            'view_mode': 'form',
            'view_id': view_id,
            'res_id': res_id,
            'target': 'new',
            'context': {
                'action_lock': False
            }
        }

    def action_lock_lot(self):
        res_id = self.create_lot_credit_wizard('lock')
        view_id = self.env.ref('quotation_bid_customer_credit.wizard_customer_lot_credit_header_form').id
        return {
            'name': 'Lock Quotation Credit',
            'type': 'ir.actions.act_window',
            'res_model': 'wizard.customer.lot.credit.header',
            'view_type': 'form',
            'view_mode': 'form',
            'view_id': view_id,
            'res_id': res_id,
            'target': 'new',
            'context': {
                'action_lock': True
            },
        }

    def public_action_lock_lot(self, lots_list=[]):
        """
        公共方法，用于手动锁定标单额度
        """
        GalaxyLotCreditAPI = LotCreditAPI()
        response_list = GalaxyLotCreditAPI.batch_lock_credit_v2(lots_list)
        success_lots, error_lots, server_error = GalaxyLotCreditAPI.format_response_list(response_list)
        if server_error:
            raise models.UserError(_('please try again'))
        for success_lot in success_lots:
            user_id = success_lot.get('userId', '')
            lot_id = success_lot.get('lotId', '')
            self.search([('lot_id', '=', lot_id), ('user_id', '=', user_id)]).write({'state': 'lock'})
        return success_lots, error_lots

    # 修改此方法 只释放额度，同步到java
    def action_release_lot_credit_schedule(self):
        lots_list = []
        quota_record_limit = int(tools.config.get('galaxy_quota_limit', 500))
        customer_lot_credit = self.search([('state', '=', 'lock'), ('aucnet_state', 'in', ('confirm', 'complete', 'sent'))],
                                          order='lot_id')
        time_delay = int(self.env['ir.config_parameter'].sudo().get_param('galaxy_bid_end_time_delay')
                         ) if self.env['ir.config_parameter'].sudo().get_param('galaxy_bid_end_time_delay') else 0
        _logger.info('开始定时释放标单额度')
        # time delay单位是分钟
        super_customer = self.env.ref('quotation_bid.default_quotation_customer')
        for lot_id, lots in groupby(customer_lot_credit, lambda m: m.lot_id):
            # 如果有多个rfq截止时间，取最晚的
            lots = list(lots)
            rfq_end_time = max(lot_id.order_id.origin_id.mapped('supplier_time_remaining'))
            rfq_end_time = rfq_end_time + datetime.timedelta(minutes=time_delay)
            if datetime.datetime.now() >= rfq_end_time:
                # 检查一下释放这个标单投标用户被选中--选择分3种情况，一种是不选，一个是选中的是正常客户，一个是用Galaxy代替了正常客户
                # 如果选中的是正常客户，当前lot的其他客户都释放，被选中的客户不释放额度，需要用户手动释放，如果选中的是Galaxy或者没有选择任何客户，释放当前lot的所有客户额度
                countered_lots = lot_id.vendor_lot_ids.filtered(lambda lot: lot.is_countered)
                if countered_lots:
                    continue
                if not lot_id.customer_bid.name or lot_id.customer_bid.name == super_customer:
                    for rec in lots:
                        if rec.price_amount != 0:
                            # 标单报价是0直接释放
                            lots_list.append({
                                "lotId": rec.lot_id.name,
                                "userId": rec.user_id,
                                "standardId": rec.vendor_code,
                                "groupId": rec.group_id,
                            })
                        else:
                            rec.state = 'unlock'
                else:
                    for rec in lots:
                        if rec.lot_id.customer_bid.name != rec.customer:
                            if rec.price_amount != 0:
                                # 标单报价是0直接释放
                                lots_list.append({
                                    "lotId": rec.lot_id.name,
                                    "userId": rec.user_id,
                                    "standardId": rec.vendor_code,
                                    "groupId": rec.group_id,
                                })
                            else:
                                rec.state = 'unlock'
                # _logger.info(f'待释放标单:{lot_id.name}')
                # _logger.info(f'释放明细:{lots_list}')

            if len(lots_list) >= quota_record_limit:
                _logger.warning(f'同步释放额度记录条数应小于{quota_record_limit}的限制')
                break
        if len(lots_list) == 0:
            _logger.info('没有需要释放标单额度的记录')
            return
        else:
            _logger.info(f'本次需释放的标单数量是{len(lots_list)}个')
        GalaxyLotCreditAPI = LotCreditAPI()
        msg = GalaxyLotCreditAPI.batch_free_credit(lots_list, quota_record_limit)
        if not isinstance(msg, dict):
            _logger.info('batch lock Lot retrun from Java is not a dict')
            _logger.info(msg)
        else:
            error_lot = []
            if msg.get('code', 403) != 200:
                raise models.UserError(f'Error call API, error message{msg.get("message", "")}')
            for item in msg['data']:
                if isinstance(item, dict):
                    handleCode = item.get('handleCode', -1)
                    user_id = item.get('userId', '') or ''
                    lot_id = item.get('lotId', '') or ''
                    handle_desc = item.get('handleDesc', '') or ''
                    if handleCode != 0:
                        error_lot.append(str(user_id) + ' ' + str(lot_id) + ' ' + str(handle_desc) + '\r\n')
                    else:
                        self.env['customer.lot.credit'].search([('lot_id', '=', item.get('lotId')),
                                                                ('user_id', '=', item.get('userId'))
                                                                ]).write({'state': 'unlock'})
                else:
                    _logger.error(f'format error: {str(msg)}')
            if len(error_lot) == 0:
                _logger.info('所有标单额度释放成功')
                # _logger.info(lots_list)
            else:
                _logger.info('部分标单释放失败')
                _logger.info(error_lot)
