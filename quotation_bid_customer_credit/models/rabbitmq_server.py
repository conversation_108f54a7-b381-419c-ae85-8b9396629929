import logging
import json
import uuid
from datetime import datetime
import traceback
import pika
import threading
from odoo import api, registry
from odoo.tools import config
from odoo import fields, models, api, _
import time
from threading import Timer

_logger = logging.getLogger(__name__)
from odoo import models
import functools
from odoo.tools import (config, existing_tables, ignore, lazy_classproperty, lazy_property, sql, OrderedSet)
from odoo.api import Environment


class RabbitmqServer(models.Model):
    _inherit = 'rabbitmq.server'

    # 消费客户锁定数据
    def sync_customer_credit(self, channel, method, properties, body):

        def do_work(self, channel, method, properties, body):
            _logger.info('接收到更改用户锁定额度数据')
            # _logger.info("properties")
            # _logger.info(properties)
            # _logger.info("body")
            # _logger.info(body)
            try:
                my_data = json.loads(body)
                # _logger.info(my_data)
                consumer_message_id = my_data.get('message_id', '')
                data = my_data['data']
                response_data = []
                for rec in data:
                    user_id = rec.get('user_id', False)
                    if user_id:
                        partner_rec = self.env['res.partner'].with_context(active_test=False).search([('ref', '=', user_id)],
                                                                                                     limit=1)
                        if partner_rec:
                            frozen_quota = rec.get('freezing', False)
                            partner_rec.write({'frozen_quota': frozen_quota})
                            response_data.append({'user_id': user_id, 'item_code': 0})
                            _logger.info(f'修改用户:{user_id}, 新额度{frozen_quota}')
                        else:
                            response_data.append({'user_id': user_id, 'item_code': 1})
                # 应答
                response = {
                    "message_id": str(uuid.uuid1()),
                    "code": 0,
                    "message": "",
                    "ack_req_id": consumer_message_id,
                    "data": response_data
                }
                PublishersyncCustomerCredit = self.env['rabbitmq.server'].search([('name', '=', 'PublishersyncCustomerCredit')])
                if PublishersyncCustomerCredit:
                    # _logger.info('发送消费额度应答消息')
                    # _logger.info(response)
                    PublishersyncCustomerCredit.send(response, is_raise=False)
                _logger.info('同步客户锁定额度数据结束')
            except Exception as e:
                _logger.error('sync_customer_credit 消费用户锁定额度数据出错')
                _logger.error(e)
                _logger.error(traceback.format_exc())
                raise
        try:
            _logger.info('同步客户锁定额度数据开始')
            self.work_thread_v2(do_work, channel, method, properties, body)
        except Exception as error:
            _logger.error(error)
            _logger.error('同步额度数据格式有误')
            raise

    def sync_customer_lock_credit(self, channel, method, properties, body):

        def do_work(self, channel, method, properties, body):
            _logger.info('sync customer lot lock credit')
            _logger.info(body)
            success = True
            try:
                body = json.loads(body)
            except Exception as error:
                success = False
                _logger.error(error)
            if success:
                self.del_customer_lock_credit(body)
                _logger.info('sync customer lot lock credit finish')

        self.work_thread_v2(do_work, channel, method, properties, body)

    def del_customer_lock_credit(self, body):
        data, error_body = self.format_customer_lock_credit(body)
        try:
            self.create_customer_lock_credit(data)
        except Exception as error:
            _logger.error('sync customer lot lock credit failed')
            _logger.error(error)
            raise
        if error_body:
            _logger.error(f"wrong customer lock credit {error_body}")

    def format_customer_lock_credit(self, customer_lock_credit):
        """
            格式化客户的锁定额度
            :param customer_lock_credit {}
            :return data {}, error_customer_lock_credit {}
        """
        data = {}
        error_customer_lock_credit = {}

        lot_id = customer_lock_credit.get('lot_id')
        user_id = customer_lock_credit.get('user_id')
        locking_price = customer_lock_credit.get('locking_price')
        standard_id = customer_lock_credit.get('standard_id')
        group_id = customer_lock_credit.get('group_id')
        bided_qty = customer_lock_credit.get('bided_qty', 0)
        if isinstance(locking_price, (int, float)) and lot_id and user_id and standard_id and group_id:
            partner_id = self.env['res.partner'].search([('ref', '=', customer_lock_credit.get('user_id'))], limit=1).id
            bid_order_lot_id = self.env['bid.order.lot'].search([('name', '=', customer_lock_credit.get('lot_id'))], limit=1)
            if bid_order_lot_id and partner_id:
                data = {
                    'vendor_code': standard_id,
                    'group_id': group_id,
                    'customer': partner_id,
                    'user_id': user_id,
                    'lot_id': bid_order_lot_id.id,
                    'price_amount': locking_price,
                    'state': 'lock',
                    'bided_qty': bided_qty or bid_order_lot_id.quantity,
                }
            else:
                _logger.error(f'user_id {user_id} not exsit or lot_id {lot_id} not exsit')
                error_customer_lock_credit = customer_lock_credit
        else:
            error_customer_lock_credit = customer_lock_credit
            _logger.error(f'wrong customer lock credit {customer_lock_credit}')

        return data, error_customer_lock_credit

    def create_customer_lock_credit(self, data):
        """
            创建或者更新客户锁定额度
            :param: data {}
        """
        lot_id = data.get('lot_id')
        customer = data.get('customer')
        clc = self.env['customer.lot.credit'].with_context(active_test=False).search([('lot_id', '=', lot_id),
                                                                                      ('customer', '=', customer)])
        if clc:
            clc.write(data)
        else:
            self.env['customer.lot.credit'].create(data)
        self.env.cr.commit()
