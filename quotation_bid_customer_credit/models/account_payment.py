# -*- coding: utf-8 -*-
import logging
import traceback

_logger = logging.getLogger(__name__)
from odoo import api, fields, models, _
from odoo.addons.java_bid_server.models.api_customer_lot_credit import LotCreditAPI


class AccountPayment(models.Model):
    _inherit = 'account.payment'

    bid_deposit_id = fields.Many2one('bid.deposit')
    attachment_ids = fields.Many2many(related='bid_deposit_id.attachment_ids')
    bid_deposit_type = fields.Selection(related='bid_deposit_id.type')
    bid_deposit_note = fields.Text(related='bid_deposit_id.note', string="Deposite Note")
    has_posted = fields.Boolean(default=False)
    is_deposit = fields.Boolean(string="Is Deposit", default=False, help="Technical field, used to identity deposit payment")

    def action_open_bid_deposit(self):
        return {
            'type': 'ir.actions.act_window',
            'name': _('Deposit'),
            'res_model': 'bid.deposit',
            'view_mode': 'form',
            'res_id': self.bid_deposit_id.id,
            'target': 'current'
        }

    def _get_deposit_account(self):
        deposit_account_code = ''
        if self.env['ir.config_parameter'].sudo().get_param('bid_deposit_account_id'):
            destination_account_id = int(self.env['ir.config_parameter'].sudo().get_param('bid_deposit_account_id') or 0)
            deposit_account_code = self.env['account.account'].browse(destination_account_id).code
        return deposit_account_code

    def check_deposit_valid(self, partner_id, amount):
        self.ensure_one()
        GalaxyLotCreditAPI = LotCreditAPI()
        try:
            msg, code = GalaxyLotCreditAPI.query_credit_by_cust(self.partner_id.ref)
        except Exception as e:
            _logger.info('check_deposit_valid error')
            _logger.error(traceback.format_exc())
            _logger.error(e)
            raise models.UserError('Error occured during Check deposit valid')
        available_quota = 0
        if code != 0:
            raise models.UserError(_(f'Error occured,{msg}'))
        else:
            ret_code = msg.get('code', 1)
            ret_msg = msg.get('message', '')
            if ret_code != 0:
                raise models.UserError(_(f'Error occured,{ret_msg}'))
            else:
                creditAvailableHk = msg['data'].get('creditAvailableHk', 0)
                creditAvailableUsd = msg['data'].get('creditAvailableUsd', 0)
                _logger.info('creditAvailableHk')
                _logger.info(creditAvailableHk)
                _logger.info('creditAvailableUsd')
                _logger.info(creditAvailableUsd)
                _logger.info('partner_id.property_product_pricelist.currency_id.name')
                _logger.info(partner_id.property_product_pricelist.currency_id.name)
                if self.partner_id.property_product_pricelist.currency_id.name == "HKD":
                    available_quota = float(creditAvailableHk)
                if self.partner_id.property_product_pricelist.currency_id.name == "USD":
                    available_quota = float(creditAvailableUsd)
                _logger.info('available_quota')
                _logger.info(available_quota)
        # 确保当前的res partner记录的值是最新的
        self.env['res.partner'].invalidate_cache(ids=partner_id.ids)
        # available_quota = partner_id.available_quota
        # prelock_quota = partner_id.pre_frozen_quota
        return_availiable_amount = 0
        _logger.info('partner_id.deposit_rate')
        _logger.info(partner_id.deposit_rate)
        if partner_id.deposit_rate > 0:
            return_availiable_amount = (available_quota) / partner_id.deposit_rate
            quota_balance = return_availiable_amount - amount
        else:
            raise models.UserError(_('deposit rate should greater than zero'))
        if quota_balance < 0:
            raise models.UserError(_('Return Deposit must less than  %s' % (return_availiable_amount, )))
        
    def _compute_customer_credit_create(self):
        _logger.info('押金记录创建，重新计算客户总押金')
        destination_account = self._get_deposit_account()
        for rec in self:
            if rec.bid_deposit_type == 'outbound':
                # 对于退押金的payment不用重新计算客户总押金，因为之前的流程已经退过押金
                _logger.info('押金记录创建，退押金的payment无需重新计算客户总押金')
                continue
            _logger.info(f'Customer Id:{rec.partner_id.id}, 客户名称:{rec.partner_id.name}')
            _logger.info(f'付款类型:{rec.payment_type}, 金额:{rec.amount}')
            if rec.payment_type == 'outbound':
                rec.check_deposit_valid(rec.partner_id, rec.amount)
            delta_amount = rec.amount if rec.payment_type == 'inbound' else -rec.amount
            if rec.destination_account_id.code == destination_account:
                rec.partner_id._compute_get_customer_deposit(delta_amount)

    def _compute_customer_credit_unlink(self):
        _logger.info('押金记录删除，重新计算客户总押金')
        destination_account = self._get_deposit_account()
        for rec in self:
            if rec.state in ('cancel', 'draft'):
                _logger.info('押金记录是取消或草稿状态，无需重新计算客户总押金')
                continue
            if rec.bid_deposit_type == 'outbound':
                # 对于退押金的payment不用重新计算客户总押金，因为之前的流程已经退过押金
                _logger.info('押金记录删除,退押金的payment，无需重新计算客户总押金')
                continue
            _logger.info(f'Customer Id:{rec.partner_id.id}, 客户名称:{rec.partner_id.name}')
            _logger.info(f'付款类型:{rec.payment_type}, 金额:{rec.amount}')
            delta_amount = -rec.amount if rec.payment_type == 'inbound' else rec.amount
            if rec.destination_account_id.code == destination_account:
                if delta_amount < 0:
                    rec.check_deposit_valid(rec.partner_id, abs(delta_amount))
                rec.partner_id._compute_get_customer_deposit(delta_amount)

    def _compute_customer_credit_draft(self):
        _logger.info('押金记录从已过账状态重置为草稿，重新计算客户总押金')
        destination_account = self._get_deposit_account()
        for rec in self:
            if rec.bid_deposit_type == 'outbound':
                # 对于退押金的payment不用重新计算客户总押金，因为之前的流程已经退过押金
                _logger.info('押金修改为draft状态,退押金的payment，无需重新计算客户总押金')
                continue
            _logger.info(f'Customer Id:{rec.partner_id.id}, 客户名称:{rec.partner_id.name}')
            _logger.info(f'付款类型:{rec.payment_type}, 金额:{rec.amount}')
            delta_amount = rec.amount if rec.payment_type == 'outbound' else -rec.amount
            if rec.destination_account_id.code == destination_account:
                if delta_amount < 0:
                    rec.check_deposit_valid(rec.partner_id, abs(delta_amount))
                rec.partner_id._compute_get_customer_deposit(delta_amount)

    def _compute_customer_credit_confirm(self):
        _logger.info('确认押金,状态变更为posted，重新计算客户总押金')
        destination_account = self._get_deposit_account()
        for rec in self:
            if rec.bid_deposit_type == 'outbound':
                # 对于退押金的payment不用重新计算客户总押金，因为之前的流程已经退过押金
                _logger.info('postpayment,退押金的payment，无需重新计算客户总押金')
                continue
            _logger.info(f'Customer Id:{rec.partner_id.id}, 客户名称:{rec.partner_id.name}')
            _logger.info(f'付款类型:{rec.payment_type}, 金额:{rec.amount}')
            delta_amount = rec.amount if rec.payment_type == 'inbound' else -rec.amount
            if rec.destination_account_id.code == destination_account:
                if delta_amount < 0:
                    rec.check_deposit_valid(rec.partner_id, abs(delta_amount))
                rec.partner_id._compute_get_customer_deposit(delta_amount)

    @api.model
    def create(self, vals):
        rec = super(AccountPayment, self).create(vals)
        if rec.state == 'posted':
            rec._compute_customer_credit_create()
        return rec

    """ # overwrite
    def action_cancel(self):
        # 目前发现只能从草稿状态才能修改为取消状态,confirm状态只能修改为草稿状态
        # 草稿状态变更为取消状态的时候,不需要重新计算
        ''' draft -> cancelled '''
        super(AccountPayment, self).action_cancel()
        self._compute_customer_credit_cancel() """

    # overwrite
    def action_draft(self):
        ''' canceled or post -> draft '''
        self.ensure_one()
        # 取消状态重置为草稿的时候,不需要重新计算
        # 已过账状态状态修改为草稿的时候,需要重新计算
        origin_state = self.state
        super(AccountPayment, self).action_draft()
        if origin_state == 'posted' and self.state == 'draft':
            self._compute_customer_credit_draft()

    # overwrite
    def action_post(self):
        # 这边限制批量的原因是如果多个记录一起post，前面成功的信用额度会用API同步到Java，如果最后一条记录失败，odoo会回滚，但是Java API不会
        original_state = False
        deposit_sign = False
        is_deposit_list = self.mapped('is_deposit')
        if self.partner_id.customer_rank > 0:
            if self.currency_id != self.partner_id.property_product_pricelist.currency_id:
                raise models.UserError(_('Currency must be same as customer currency'))
        elif self.partner_id.supplier_rank > 0:
            if self.currency_id != self.partner_id.property_purchase_currency_id:
                raise models.UserError(_('Currency must be same as supplier currency'))
        if any(is_deposit_list) and len(is_deposit_list) > 1:
            raise models.UserError('You can post only one deposit record one time!')
        if all(is_deposit_list) and len(is_deposit_list) == 1:
            original_state = self.state
            deposit_sign = True
        super(AccountPayment, self).action_post()
        if deposit_sign and not self.has_posted:
            # 防止重复提交
            if self.state == 'posted' and original_state != 'posted':
                self.has_posted = True
                self._compute_customer_credit_confirm()
        
        

    # overwrite
    def action_cancel(self):
        super(AccountPayment, self).action_cancel()
        for rec in self:
            if rec.bid_deposit_type == 'outbound' and not self._context.get('from_bid_deposit_to_force_cancel'):
                raise models.UserError(_('You can not cancel refund depost!'))

    """ def write(self, vals):
        # 为什么按钮的action_post方法修改state的没有进入writ方法？
        old_partner = self.partner_id
        old_amount = self.amount
        old_payment_type = self.payment_type
        old_payment_state = self.state
        rec = super(AccountPayment, self).write(vals)
        if len(vals) == 0:
            return rec
        new_partner = self.partner_id
        new_amount = self.amount
        new_payment_type = self.payment_type
        new_payment_state = self.state
        condition = [old_partner.id == new_partner.id, old_amount == new_amount, old_payment_type == new_payment_type]
        if all(condition):
            return rec
        self._compute_customer_credit_write(
            old_partner,
            old_amount,
            old_payment_type,
            new_partner,
            new_amount,
            new_payment_type,
        )
        return rec """

    def unlink(self):
        # unlink 记录可能是多个
        for rec in self:
            if rec.bid_deposit_type == 'outbound' and not self._context.get('from_bid_deposit_force_to_unlink'):
                raise models.UserError(_('You can not delete refund depost!'))
            if rec.state != 'draft':
                raise models.UserError(_('You can only delete draft depost!'))
        self._compute_customer_credit_unlink()
        rec = super(AccountPayment, self).unlink()
        return rec

    @api.onchange('partner_id')
    def get_currency(self):
        if self.partner_id.customer_currency_id:
            self.currency_id = self.partner_id.customer_currency_id

    @api.model
    def default_get(self, fields_list):
        '''
             创建记录的时候，如果是在设置客户信用额度页面，设置一些字段的默认值
        '''
        res = super(AccountPayment, self).default_get(fields_list)
        if self.is_deposit:
            destination_account_id = None
            if self.env['ir.config_parameter'].sudo().get_param('bid_deposit_account_id'):
                destination_account_id = int(self.env['ir.config_parameter'].sudo().get_param('bid_deposit_account_id'))
            res.update({'destination_account_id': destination_account_id})

        if self._context.get('customer_so_retainer'):
            destination_account_id = None
            if self.env['ir.config_parameter'].sudo().get_param('bid_retainer_account_id'):
                destination_account_id = int(self.env['ir.config_parameter'].sudo().get_param('bid_retainer_account_id'))
            res.update({'destination_account_id': destination_account_id})
        return res

    @api.depends('journal_id', 'partner_id', 'partner_type', 'is_internal_transfer')
    def _compute_destination_account_id(self):
        super(AccountPayment, self)._compute_destination_account_id()
        if self.is_deposit:
            self.destination_account_id = False
            for pay in self:
                if self.env['ir.config_parameter'].sudo().get_param('bid_deposit_account_id'):
                    pay.destination_account_id = int(self.env['ir.config_parameter'].sudo().get_param('bid_deposit_account_id'))
        """ if self._context.get('customer_so_retainer'):
            self.destination_account_id = False
            for pay in self:
                if self.env['ir.config_parameter'].sudo().get_param('bid_retainer_account_id'):
                    pay.destination_account_id = int(
                        self.env['ir.config_parameter'].sudo().get_param('bid_retainer_account_id'))
        """
