import logging
import traceback

from odoo import api, models, _
from odoo.tools import float_compare
from odoo.exceptions import UserError

from odoo.addons.java_bid_server.models.credit_api import CreditAPI

_logger = logging.getLogger(__name__)


class ResPartner(models.Model):
    _inherit = 'res.partner'

    @api.model
    def message_post_channel_customer_credit_warning(self, message):
        admin_user = self.env.ref('base.user_admin')
        channel_id = self.env.ref('quotation_bid_customer_credit.channel_customer_credit_warning')
        notification_ids = [((0, 0, {
            'res_partner_id': admin_user.partner_id.id,
            'notification_type': 'inbox'}))]
        root_user = self.env.ref('base.user_root')

        channel_id.message_post(author_id=root_user.id,
                                body=message,
                                message_type='notification',
                                subtype_xmlid="mail.mt_comment",
                                notification_ids=notification_ids,
                                partner_ids=[admin_user.id],
                                notify_by_email=False,
                                )

    @staticmethod
    def get_customer_credit_from_ebid(ref):
        """
        获取Java Ebid后台系统客户信用额度数据
        返回成功实例
        {'code': 0, 'message': 'successSUCCESS0', 'data': {'depositeHk': '50000.00',
        'creditAmountHk': '3000000.00', 'freezingHk': '467860.00', 'creditAvailableHk': '2532140.00',
        'depositeUsd': '0.00', 'creditAmountUsd': '0.00', 'freezingUsd': '0.00', 'creditAvailableUsd': '0.00'}}

        """
        creditAPI = CreditAPI()
        res = creditAPI.get_customer_credit(ref)
        return res

    def get_posted_payment_partner(self):
        payments = self.env['account.payment'].search([('is_deposit', '=', True), ('state', 'in', ['posted'])])
        partners = payments.mapped('partner_id').filtered(lambda r: r.ref)
        return partners

    def compare_customer_credit_with_ebid(self):
        """
        查询客户ebid信用额度，并和当前记录信用额度比较
        """
        e_messages = []
        for rec in self:
            try:
                msg = self.get_customer_credit_from_ebid(rec.ref)
            except Exception as e:
                _logger.info('get_customer_credit_from_ebid error')
                _logger.error(traceback.format_exc())
                _logger.error(e)
                raise UserError('Error occurred during get_customer_credit_from_ebid。 '
                                'partner.id: {rec.id} name: {rec.name} ref: {rec.ref}')

            ret_code = msg.get('code', 1)
            ret_msg = msg.get('message', '')
            ret_data = msg.get('data', {})
            if ret_code != 0:
                raise UserError(_(f'Error occurred,{ret_msg}'))

            available_quota = 0
            deposit = 0
            creditAmount = 0
            freezingAmount = 0

            creditAvailableHk = ret_data.get('creditAvailableHk', 0)
            creditAvailableUsd = ret_data.get('creditAvailableUsd', 0)
            depositUsd = ret_data.get('depositeUsd', 0)
            depositHk = ret_data.get('depositeHk', 0)
            creditAmountUsd = ret_data.get('creditAmountUsd', 0)
            creditAmountHk = ret_data.get('creditAmountHk', 0)
            freezingAmountHk = ret_data.get('freezingHk', 0)
            freezingAmountUsd = ret_data.get('freezingUsd', 0)

            if rec.customer_currency_id.name == "HKD":
                available_quota = float(creditAvailableHk or 0)
                deposit = float(depositHk or 0)
                creditAmount = float(creditAmountHk or 0)
                freezingAmount = float(freezingAmountHk or 0)
            elif rec.customer_currency_id.name == "USD":
                available_quota = float(creditAvailableUsd or 0)
                deposit = float(depositUsd or 0)
                creditAmount = float(creditAmountUsd or 0)
                freezingAmount = float(freezingAmountUsd or 0)
            else:
                raise UserError(
                    _(f'partner {rec.id} {rec.ref} currency error; except HKD or USD, got {rec.customer_currency_id.name}'))

            # 确保当前的res partner记录的值是最新的
            self.env['res.partner'].invalidate_cache(ids=rec.ids)
            if float_compare(rec.customer_deposit, deposit, precision_rounding=2) != 0:
                e_messages.append('Odoo deposit is not equal to Bid service deposit ')
                e_messages.append(
                    f'{rec.name} {rec.ref} odoo deposit:{rec.customer_deposit} Bid service deposit:{deposit}')

            if float_compare(rec.frozen_quota, freezingAmount, precision_rounding=2) != 0:
                e_messages.append('Odoo frozen quota is not equal to Bid service deposit')
                e_messages.append(
                    f'{rec.name} {rec.ref} odoo frozen quota:{rec.frozen_quota} Bid service deposit:{freezingAmount}')

            if float_compare(rec.available_quota, available_quota, precision_rounding=2) != 0:
                e_messages.append('Odoo available quota is not equal to Bid service available quota')
                e_messages.append(
                    f'{rec.name} {rec.ref} odoo available_quota:{rec.available_quota} Bid service available_ quota:{available_quota}')

            if float_compare(rec.customer_bid_credit, creditAmount, precision_rounding=2) != 0:
                e_messages.append('Odoo customer bid credit is not equal to Bid service customer bid credit')
                e_messages.append(
                    f'{rec.name} {rec.ref} odoo customer bid credit:{rec.customer_bid_credit} Bid service deposit:{creditAmount}')
        if e_messages:
            _logger.error('\n'.join(e_messages))
            self.message_post_channel_customer_credit_warning('<br/>'.join(e_messages))
        else:
            msg = 'Compare customer credit with ebid finished, no error.'
            _logger.info(msg)
            self.message_post_channel_customer_credit_warning(msg)

    def action_compare_customer_credit_with_ebid_schedule(self):
        # 仅查询有已过账投标押金记录的客户
        partners = self.get_posted_payment_partner()
        partners.compare_customer_credit_with_ebid()

    @api.model
    def summary_calc_customer_deposit(self, partner_ids=None):
        """
        汇总计算投标押金记录的客户的金额
        收款 计算 已过账的押金
        退款 计算 草稿+已过账的押金
        :param list partner_ids: partner ids列表，如果提供了，则只计算指定等，否则全部
        :return: {partner_id: customer_deposit}
        """
        domain = [('is_deposit', '=', True), ('state', 'in', ['draft', 'posted'])]
        if partner_ids:
            domain.append(('partner_id', 'in', partner_ids))
        payments = self.env['account.payment'].search(domain)

        res = {}
        for payment in payments:
            if payment.partner_id.id not in res:
                res[payment.partner_id.id] = 0.0

            if payment.payment_type == 'inbound' and payment.state == 'posted':
                res[payment.partner_id.id] += payment.amount
            elif payment.payment_type == 'outbound' and payment.state in ['draft', 'posted']:
                res[payment.partner_id.id] -= payment.amount

        return res

    def action_compare_customer_credit_with_payment_schedule(self):
        """
        比较 汇总计算已过账投标押金记录的客户金额 与 partner记录中的投标押金金额 是否一致
        :return:
        """
        # 仅查询有已过账投标押金记录的客户
        payments = self.env['account.payment'].search([('is_deposit', '=', True), ('state', 'in', ['draft', 'posted'])])
        partners = payments.mapped('partner_id').filtered(lambda r: r.ref)
        summary_deposit_res = self.summary_calc_customer_deposit(partners.ids)

        e_messages = []

        for partner in partners:
            summary_deposit = summary_deposit_res.get(partner.id, 0.0)
            if float_compare(summary_deposit, partner.customer_deposit, precision_rounding=2) != 0:
                msg = f'{partner.name} {partner.ref} deposit : {partner.customer_deposit} ' \
                      f'is not equal to payment summary total deposit : {summary_deposit}.'
                e_messages.append(msg)
        if e_messages:
            _logger.error('\n'.join(e_messages))
            self.message_post_channel_customer_credit_warning('<br/>'.join(e_messages))
        else:
            msg = 'Compare customer credit with payment finished, no error.'
            _logger.info(msg)
            self.message_post_channel_customer_credit_warning(msg)
