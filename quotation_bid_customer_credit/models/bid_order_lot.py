import logging
_logger = logging.getLogger(__name__)

from odoo import models, fields, api


class BidOrderLot(models.Model):
    _inherit = 'bid.order.lot'

    def update_couter_customer_credit_lot(self, lot_name, user_id, countered_quantity, customer_countered_price, bid_status='Bidding'):
        """
        更新客户锁定额度
        """
        _logger.info(f'update_couter_customer_credit_lot: {lot_name}, {user_id}, {countered_quantity}, {customer_countered_price}')
        customer_lot_credit_rec = self.env['customer.lot.credit'].search([('lot_id', '=', lot_name), ('user_id', '=', user_id)])
        if customer_lot_credit_rec:
            customer_lot_credit_rec.write({
                'bided_qty': countered_quantity,
                'bid_status': bid_status,
                'price_amount': customer_countered_price*countered_quantity})
