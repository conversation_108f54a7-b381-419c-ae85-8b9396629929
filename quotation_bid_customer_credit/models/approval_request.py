import logging
from odoo import models, fields, _
_logger = logging.getLogger(__name__)


# pylint: disable=C0301,R1720,R1710
class ApprovalRequest(models.Model):
    _inherit = 'approval.request'

    bid_deposit_id = fields.Many2one('bid.deposit')

    def action_draft(self):
        """action_draft"""
        if self.bid_deposit_id:
            raise models.UserError(
                _('can not back to draft because of bid deposit'))
        return super(ApprovalRequest, self).action_draft()

    def action_approve(self, approver=None):
        """action_approve"""
        res = super(ApprovalRequest, self).action_approve(approver=approver)
        status_lst = self.mapped('approver_ids.status')
        minimal_approver = self.approval_minimum if len(
            status_lst) >= self.approval_minimum else len(status_lst)
        if self.bid_deposit_id and self.bid_deposit_id.state != 'complete' and status_lst.count('approved') >= minimal_approver:
            self.bid_deposit_id.action_complete()
        return res

    def action_refuse(self, approver=None):
        """action_refuse"""
        res = super(ApprovalRequest, self).action_refuse(approver=approver)
        # 只有在其他审批人没有拒绝下，才可以增加押金，押金只能增加一次
        refused = [True for approver in self.approver_ids if approver.user_id.id !=
                   self.env.uid and approver.status == 'refused']
        if self.bid_deposit_id and not any(refused):
            _logger.info('action_refuse increase the deposit')
            _logger.info(
                'before increase deposit %s', self.bid_deposit_id.customer_id.customer_deposit)
            _logger.info(
                'customer %s %s increases %s deposit', self.bid_deposit_id.customer_id.name, self.bid_deposit_id.customer_id.id, self.amount)
            self.sudo().bid_deposit_id.customer_id.customer_deposit += self.bid_deposit_id.amount
            _logger.info(
                'after increase deposit %s', self.bid_deposit_id.customer_id.customer_deposit)
        return res

    def action_open_bid_deposit(self):
        """action_open_bid_deposit"""
        return {
            'type': 'ir.actions.act_window',
            'name': _('Deposit'),
            'res_model': 'bid.deposit',
            'view_mode': 'form',
            'res_id': self.bid_deposit_id.id,
            'target': 'current'
        }

    def action_withdraw(self, approver=None):
        """action_withdraw"""
        if not self.bid_deposit_id:
            res = super(ApprovalRequest, self).action_withdraw(approver=approver)
            return res
        else:
            if self.bid_deposit_id.account_payment_id.state == 'posted':
                raise models.UserError(
                    _("payment deposit has been posted, can't withdraw"))
            else:
                if self.bid_deposit_id.state == 'cancel':
                    raise models.UserError(_('Bid deposit has been canceled'))
                else:
                    # approved or refused === pending
                    old_request_status = self.request_status
                    super(ApprovalRequest, self).action_withdraw(
                        approver=approver)
                    new_request_status = self.request_status
                    if old_request_status == 'approved' and new_request_status == 'pending':
                        self.bid_deposit_id.account_payment_id.sudo().with_context(
                            **{'from_bid_deposit_force_to_unlink': True}).unlink()
                        self.bid_deposit_id.state = 'approvaling'
                    elif old_request_status == 'refused' and new_request_status == 'pending':
                        refused = [True for approver in self.approver_ids if approver.user_id.id !=
                                   self.env.uid and approver.status == 'refused']
                        if self.bid_deposit_id and not any(refused):
                            _logger.info('action_withdraw reduce the deposit')
                            _logger.info(
                                'before reduce deposit %s', self.bid_deposit_id.customer_id.customer_deposit)
                            _logger.info(
                                'customer %s %s reduces %s deposit', self.bid_deposit_id.customer_id.name, self.bid_deposit_id.customer_id.id, self.amount)
                            self.sudo().bid_deposit_id.customer_id.customer_deposit -= self.bid_deposit_id.amount
                            _logger.info(
                                'after reduce deposit %s', self.bid_deposit_id.customer_id.customer_deposit)
