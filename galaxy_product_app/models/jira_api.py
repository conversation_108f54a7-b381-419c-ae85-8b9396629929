import logging
_logger = logging.getLogger(__name__)
from jira import JIRA
from odoo import tools


class JiraAPI():

    def __init__(self) -> None:
        self.jira_token = tools.config.get('jira_token') 
        self.headers = JIRA.DEFAULT_OPTIONS["headers"].copy()
        self.headers["Authorization"] = f"Bearer {self.jira_token}"
        self.jira_url = tools.config.get('jira_url') 
        self.my_jira = self.jira_connection()
        self.JIRA_PRIORITY = {
                                '1':'Lowest', 
                                '2': 'Low', 
                                '3': 'Medium', 
                                '4': 'High', 
                                '5': 'Highest'}

    def jira_connection(self):
        my_jira = None
        try:  
            my_jira = JIRA(self.jira_url, options={'headers': self.headers})
        except Exception as error:
            _logger.error(f'connect jira failed {error}')  
        return my_jira

    def create_jira_issue(self, project, summary, description, issuetype, priority):
        if isinstance(issuetype, int):
            issuetype = {'id': issuetype} 
        else:
            issuetype = {'name': issuetype}
        issue_dict = {
                'project': {'key': project},
                'summary': summary,
                'description': description,
                'issuetype': issuetype,
                'priority': {'name': priority}
                }
        try:
            return self.my_jira.create_issue(fields=issue_dict)
        except Exception as error:
            _logger.error(error)
            return None

    def check_project_exsit(self, project):
        try:
            projects = self.my_jira.projects()
            keys = [p.key for p in projects]
            if project in keys:
                return True
            return False
        except Exception as error:
            _logger.error(error)
            return False
       

    def get_issue_types(self):
        if self.my_jira:
            try:
                issues = self.my_jira.issue_types()
                return [{'name':i.name, 'id': i.id} for i in issues]
            except Exception as error:
                _logger.error(error)
        return []

    def get_project_component(self, project):
        if self.my_jira:
            try:
                components = self.my_jira.project_components(project)
                return [{'name':c.name, 'id': c.id} for c in components]
            except Exception as error:
                _logger.error(error)
        return []

