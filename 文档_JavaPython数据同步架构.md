# Galaxy ERP - RabbitMQ与Java服务数据同步架构文档

## 概述

本文档详细描述了Galaxy ERP项目中通过RabbitMQ与Java投标服务进行数据同步的完整架构、实现方式和配置方法。

## 目录

- [1. 架构概览](#1-架构概览)
- [2. 核心组件](#2-核心组件)
- [3. 主要同步业务场景](#3-主要同步业务场景)
- [4. 消息队列配置](#4-消息队列配置)
- [5. 数据同步流程](#5-数据同步流程)
- [6. 配置参数](#6-配置参数)
- [7. 消息格式示例](#7-消息格式示例)
- [8. 部署和监控](#8-部署和监控)

## 1. 架构概览

### 1.1 整体架构图

```mermaid
graph TB
    subgraph "Odoo ERP System"
        A[galaxy_monkey_patching] --> B[RabbitMQ Thread]
        B --> C[rabbitmq_galaxy]
        C --> D[Consumer Threads]
        C --> E[Publisher Methods]
        
        F[Business Modules] --> E
        D --> G[Business Logic]
        
        subgraph "Business Modules"
            F1[base_bid_management]
            F2[quotation_bid]
            F3[account_customer_credit]
            F4[quotation_bid_customer_credit]
        end
    end
    
    subgraph "RabbitMQ Broker"
        H[Exchanges]
        I[Queues]
        J[Routing Keys]
    end
    
    subgraph "Java Bid System"
        K[Bid Service]
        L[Customer Service]
        M[Credit Service]
    end
    
    E --> H
    H --> I
    I --> K
    K --> I
    I --> D
    
    style A fill:#f9f,stroke:#333,stroke-width:2px
    style C fill:#bbf,stroke:#333,stroke-width:2px
    style H fill:#bfb,stroke:#333,stroke-width:2px
```

### 1.2 数据流向图

```mermaid
sequenceDiagram
    participant O as Odoo ERP
    participant R as RabbitMQ
    participant J as Java Service
    
    Note over O,J: 客户信息同步
    J->>R: 发送客户数据
    R->>O: 消费客户数据
    O->>O: 创建/更新客户记录
    O->>R: 发送应答消息
    R->>J: 确认处理完成
    
    Note over O,J: 投标报价同步
    J->>R: 发送投标报价
    R->>O: 消费报价数据
    O->>O: 创建报价记录
    O->>R: 发送处理结果
    R->>J: 返回处理状态
    
    Note over O,J: 标单状态同步
    O->>O: 标单状态变更
    O->>R: 发送状态消息
    R->>J: 通知状态变更
    J->>J: 更新投标系统
    
    Note over O,J: 信用额度同步
    O->>R: 请求客户额度
    R->>J: 转发请求
    J->>R: 返回额度数据
    R->>O: 消费额度数据
    O->>O: 更新客户信用
```

## 2. 核心组件

### 2.1 基础设施层

#### 文件位置：`rabbitmq_galaxy/models/rabbitmq_server.py`

```python
class RabbitmqServer(models.Model):
    _name = 'rabbitmq.server'
    _description = 'Rabbitmq Server'
    
    def send(self, body, is_raise=True):
        """发送数据到mq队列"""
        body = json.dumps(body)
        connection = self.create_mq_connection()
        # 发布消息到RabbitMQ
        channel.basic_publish(exchange=self.exchange,
                              routing_key=self.routing_key,
                              body=body)
    
    def create_mq_connection(self, connection_name=''):
        """创建mq连接"""
        credentials = pika.PlainCredentials(rabbitmq_user, rabbitmq_password)
        mq_connection = pika.BlockingConnection(
            pika.ConnectionParameters(host=rabbitmq_host,
                                      credentials=credentials,
                                      virtual_host=rabbitmq_virtual_host))
```

### 2.2 启动机制

#### 文件位置：`galaxy_monkey_patching/__init__.py`

```python
def threaded_start(server, *args, **kwargs):
    res = orig_threaded_start(server, *args, **kwargs)
    _start_runner_thread("threaded server")      # 启动队列任务线程
    _start_rabbitmq_thread("threaded server")    # 启动RabbitMQ线程
    _start_fetchmail_thread("threaded server")   # 启动邮件IDLE线程
    return res

def _start_rabbitmq_thread(server_type):
    global rabbitmq_thread
    if not config["stop_after_init"]:
        db_name = config.options.get('db_name')
        rabbitmq_consumer = config.get('rabbitmq_consumer')
        if db_name and rabbitmq_consumer:
            rabbitmq_thread = RabbitMQThread()
            rabbitmq_thread.start()
```

#### 文件位置：`galaxy_monkey_patching/rabbitmq/thread_rabbitmq.py`

```python
class RabbitMQThread(Thread):
    def run(self):
        time.sleep(START_DELAY)
        db_name = config.options.get('db_name')
        from odoo.addons import rabbitmq_galaxy
        self.consumer_thread_list = rabbitmq_galaxy.models.rabbitmq_server.RabbitmqServer._start_consumer(db_name)
```

## 3. 主要同步业务场景

### 3.1 客户信息同步 (Java → Odoo)

#### 文件位置：`base_bid_management/models/rabbitmq_server.py`

```python
def sync_ebid_customer(self, channel, method, properties, body):
    """同步投标客户信息"""
    def do_work(self, channel, method, properties, body):
        # 处理Java发送的客户数据
        # 创建或更新Odoo中的客户记录
        PublishersyncCustomer = self.env['rabbitmq.server'].search([('name', '=', 'Publisher Online Bid User sync')])
        if PublishersyncCustomer:
            PublishersyncCustomer.send(response, is_raise=False)

    self.work_thread_v2(do_work, channel, method, properties, body)
```

#### 队列配置：`base_bid_management/data/rabbitmq_queue.xml`

```xml
<record id='consumer_online_bid_user_sync' model='rabbitmq.server'>
    <field name='name'>Customer online Bid User Sync</field>
    <field name='queue'>queue.galaxy.sync.user</field>
    <field name='exchange'>exch.galaxy.sync.user</field>
    <field name='routing_key'>/BID/SEND/ODOO</field>
    <field name='style'>consumer</field>
    <field name='code'>sync_ebid_customer</field>
</record>
```

#### 3.1.1 同步规则详解

**重要说明**：并非所有Java投标用户都会自动同步到`res.partner`表！

##### **同步策略**：
- **新用户**：只创建到`galaxy.ebid.user`表，**不会自动创建**`res.partner`记录
- **已存在用户**：通过手机号或ref字段找到对应的`res.partner`时，会更新该记录

##### **核心处理逻辑**：
#### 文件位置：`base_bid_management/models/customer_data.py`

```python
def load_json_to_res_partner(self, data_jsons):
    """创建客户或更新客户,创建ebid 用户或更新ebid用户"""
    for data_json in data_jsons:
        customer_id = data_json.get('id', None)
        customer_phone = data_json.get('tel', None)

        # 1. 先通过手机号查找现有客户
        partner_id = res_partner_obj.search([('phone', '=', customer_phone)], limit=1)

        # 2. 如果没找到，再通过ref字段查找
        if not partner_id:
            partner_id = res_partner_obj.search([('ref', '=', customer_id)], limit=1)

        # 3. 处理galaxy.ebid.user表（必定执行）
        if not partner_id:
            # 新用户：只创建galaxy.ebid.user记录
            # 2023-04-25 修改逻辑，不自动创建新客户
            if not ebid_user:
                self.env['galaxy.ebid.user'].create(ebid_user_dict)
            else:
                ebid_user.write(ebid_user_dict)
        elif len(partner_id) == 1:
            # 已存在客户：更新res.partner + 处理galaxy.ebid.user
            partner_id.write(result_val)  # 更新客户信息
            if ebid_user:
                ebid_user.write(ebid_user_dict)
            else:
                ebid_user_dict['odoo_customer'] = partner_id.id
                self.env['galaxy.ebid.user'].create(ebid_user_dict)
```

#### 3.1.2 涉及的数据表

| 表名 | 操作类型 | 说明 |
|------|---------|------|
| `galaxy.ebid.user` | 必定操作 | 存储所有Java投标用户信息，作为中间审核表 |
| `res.partner` | 条件操作 | 只有找到已存在客户时才更新，新用户不自动创建 |
| `res.users` | 不操作 | 已废除自动创建用户功能 |

#### 3.1.3 标志字段

- **`ref` 字段**：存储Java投标系统的用户ID（如：`user2025042200001`）
- **`phone` 字段**：手机号，作为备用查找标志
- **`status` 字段**：`active`/`inactive`（基于Java的`del_flag`）
- **`active` 字段**：布尔值，控制记录是否激活

#### 3.1.4 手动审核流程

##### **Galaxy Ebid User模型**：
#### 文件位置：`base_bid_management/models/galaxy_ebid_user.py`

```python
class GalaxyEbidUser(models.Model):
    _name = 'galaxy.ebid.user'
    _description = 'Galaxy Ebid User'

    ebid_user_id = fields.Char('User ID', readonly=True)
    odoo_customer = fields.Many2one('res.partner')
    state = fields.Selection([('draft', 'Draft'), ('audited', 'Audited'), ('cancel', 'Cancel')])

    def action_create_odoo_customer(self):
        """创建odoo客户"""
        result_val = self.get_customer_info()
        new_customer = partner_sudo.create(result_val)
        self.odoo_customer = new_customer.id

    def action_confirm(self):
        """审核用户"""
        self.odoo_customer.sudo().write(result_val)
        self.state = 'audited'
        # 发送MQ消息通知Java系统审核完成
        ebid_user_audit.send([{
            'req_id': str(uuid.uuid1()),
            'audit': True,
            'odoo_user_id': self.odoo_customer.bid_user_id,
            'user_id': self.ebid_user_id
        }])
```

##### **审核步骤**：
1. **查看待审核用户**：Galaxy Ebid User菜单中状态为`draft`的记录
2. **创建客户**：点击"Create Odoo Customer"按钮创建`res.partner`记录
3. **审核确认**：点击"Confirm"按钮，状态变为`audited`
4. **自动通知**：发送MQ消息通知Java系统绑定成功

### 3.2 投标报价同步 (Java → Odoo)

#### 文件位置：`quotation_bid/models/rabbitmq_server.py`

```python
def callback_queue_galaxy_bid_price(self, ch, method, properties, body):
    """接收Java发送的投标报价数据"""
    def do_work(self, ch, method, properties, body):
        error_body = []
        success_body = []
        for bid in body:
            user_id = bid.get('user_id', '')
            lot_id = bid.get('lot_id', '')
            # 验证客户和标单
            customer = self.env['res.partner'].search([('ref', '=', user_id)], limit=1)
            line_id = self.env['bid.order.lot'].search([('name', '=', lot_id)], limit=1)
            # 创建报价记录
        
        # 发送应答
        bidPriceACK = self.env['rabbitmq.server'].search([('name', '=', 'BidPriceAck')])
        if bidPriceACK:
            bidPriceACK.send(error_body + success_body, is_raise=False)
```

#### 队列配置：`quotation_bid/data/rabbitmq_queue.xml`

```xml
<record id='consumer_BidPrice' model='rabbitmq.server'>
    <field name='name'>BidPrice</field>
    <field name='queue'>queue.galaxy.bid.price</field>
    <field name='exchange'>exch.galaxy.bid.price</field>
    <field name='routing_key'>/BID/SEND/ERP</field>
    <field name='style'>consumer</field>
    <field name='code'>callback_queue_galaxy_bid_price</field>
</record>
```

### 3.3 标单状态同步 (Odoo → Java)

#### 文件位置：`quotation_bid/models/bid_order.py`

```python
@api.depends('state')
def _send_mq_message(self):
    """发送标单状态变更消息到Java"""
    for s in self:
        if not s.send_mq_message and s.state == 'done' and s.customer_time_remaining:
            bidInfo = self.env['rabbitmq.server'].search([('name', '=', 'BidInfo')])
            if bidInfo:
                group_change_end_date = {
                    'req_id': str(uuid.uuid1()), 
                    'operate_type': 3,
                    'data': [{'group_id': s.name, 'status_type': 1}]
                }
                bidInfo.send(group_change_end_date)
                s.send_mq_message = True
```

### 3.4 客户信用额度同步 (双向)

#### 请求额度 (Odoo → Java)
#### 文件位置：`account_customer_credit/models/rabbitmq_server.py`

```python
def get_customer_credit_from_java(self, user_id_list=[]):
    """主动请求获取客户锁定额度"""
    RequestCustomerCredit = self.env['rabbitmq.server'].search([('name', '=', 'PublisherRequestCustomerCredit')])
    if RequestCustomerCredit:
        data = {'message_id': str(uuid.uuid1()), 'user_id_list': user_id_list}
        RequestCustomerCredit.send(data)
```

#### 接收额度 (Java → Odoo)
#### 文件位置：`quotation_bid_customer_credit/models/rabbitmq_server.py`

```python
def sync_customer_credit(self, channel, method, properties, body):
    """消费客户锁定数据"""
    def do_work(self, channel, method, properties, body):
        # 处理Java返回的信用额度数据
        # 更新客户信用记录
        PublishersyncCustomerCredit = self.env['rabbitmq.server'].search([('name', '=', 'PublishersyncCustomerCredit')])
        if PublishersyncCustomerCredit:
            PublishersyncCustomerCredit.send(response, is_raise=False)
    
    self.work_thread_v2(do_work, channel, method, properties, body)
```

## 4. 消息队列配置

### 4.1 投标相关队列配置

#### 文件位置：`quotation_bid/data/rabbitmq_queue.xml`

| 队列名称 | 类型 | 用途 | 路由键 |
|---------|------|------|--------|
| BidInfo | Publisher | 发送标单信息 | /ERP/SEND/BID |
| BidPrice | Consumer | 接收投标报价 | /BID/SEND/ERP |
| BidPriceAck | Publisher | 发送报价应答 | /ERP/SEND/BID |
| RequestPrice | Publisher | 请求报价数据 | /ERP/REQUEST/PRICE |

### 4.2 客户信息队列配置

#### 文件位置：`base_bid_management/data/rabbitmq_queue.xml`

| 队列名称 | 类型 | 用途 | 路由键 |
|---------|------|------|--------|
| Customer online Bid User Sync | Consumer | 接收客户信息 | /BID/SEND/ODOO |
| Publisher Online Bid User sync | Publisher | 发送客户应答 | /ODOO/ACK/BID |

### 4.3 信用额度队列配置

#### 文件位置：`account_customer_credit/data/rabbitmq_queue.xml`

| 队列名称 | 类型 | 用途 | 路由键 |
|---------|------|------|--------|
| PublisherRequestCustomerCredit | Publisher | 请求客户额度 | /ODOO/SEND/BID |
| PublishersyncCustomerCredit | Publisher | 发送额度应答 | /ODOO/ACK/BID |

## 5. 数据同步流程

### 5.1 客户信息同步流程

```mermaid
flowchart TD
    A[Java投标用户注册/更新] --> B[发送MQ消息到Odoo]
    B --> C[Odoo接收消息]
    C --> D{通过手机号查找客户}
    D -->|找到| E[更新res.partner]
    D -->|未找到| F{通过ref查找客户}
    F -->|找到| E
    F -->|未找到| G[仅处理galaxy.ebid.user]

    E --> H{galaxy.ebid.user存在?}
    G --> I{galaxy.ebid.user存在?}

    H -->|存在| J[更新galaxy.ebid.user]
    H -->|不存在| K[创建galaxy.ebid.user并关联客户]
    I -->|存在| L[更新galaxy.ebid.user]
    I -->|不存在| M[创建galaxy.ebid.user]

    J --> N[发送应答消息]
    K --> N
    L --> N
    M --> N

    N --> O[Java接收应答]

    subgraph "手动审核流程"
        P[管理员查看draft状态用户] --> Q[创建Odoo客户]
        Q --> R[审核确认]
        R --> S[发送审核完成MQ消息]
        S --> T[Java系统更新用户状态]
    end

    M -.-> P
    L -.-> P

    style G fill:#ffeb3b,stroke:#f57c00,stroke-width:2px
    style M fill:#ffeb3b,stroke:#f57c00,stroke-width:2px
    style L fill:#ffeb3b,stroke:#f57c00,stroke-width:2px
    style P fill:#e1f5fe,stroke:#0277bd,stroke-width:2px
```

### 5.2 投标报价同步流程

```mermaid
flowchart TD
    A[Java投标系统] --> B[发送报价数据到MQ]
    B --> C[Odoo消费者接收消息]
    C --> D{验证客户和标单}
    D -->|有效| E[创建bid.order.quotation记录]
    D -->|无效| F[记录到错误列表]
    E --> G[添加到成功列表]
    F --> H[合并错误和成功列表]
    G --> H
    H --> I[发送处理结果应答]
    I --> J[Java接收处理状态]
```

### 5.3 标单状态同步流程

```mermaid
flowchart TD
    A[Odoo标单状态变更] --> B{检查状态是否为done}
    B -->|是| C{检查是否已发送消息}
    B -->|否| D[结束]
    C -->|否| E[构造状态消息]
    C -->|是| D
    E --> F[发送到BidInfo队列]
    F --> G[标记已发送消息]
    G --> H[Java投标系统接收]
    H --> I[更新投标系统状态]
```

## 6. 配置参数

### 6.1 Odoo配置文件

#### 文件位置：`odoo.conf`

```ini
[options]
# RabbitMQ连接配置
galaxy_rabbitmq_host = localhost
galaxy_rabbitmq_user = guest
galaxy_rabbitmq_password = guest
galaxy_rabbitmq_virtual_host = /

# 启用RabbitMQ消费者
rabbitmq_consumer = True

# 数据库配置
db_name = your_database_name

# Java服务配置
sync_domain = http://***************:8899
```

### 6.2 模块依赖配置

#### 文件位置：`galaxy_monkey_patching/__manifest__.py`

```python
'depends': ['base', 'queue_job', 'rabbitmq_galaxy', 'fetchmail_idle']
```

## 7. 消息格式示例

### 7.1 客户信息同步消息

#### 7.1.1 Java发送的客户数据消息

```json
[
    {
        "req_id": "1c47c1540a164a959a2a2e5707d0fbb7",
        "id": "user2025042200001",
        "tel": "8574006127",
        "pass": "$2a$08$gJMXsqZ2fv5zhaVG2J3/JeVJ6powdH1PMrEnXu4qj9lsMY5PgPYZW",
        "communicate_person": "Shirley",
        "company_name": "FY international llc",
        "company_address": null,
        "wx": null,
        "currency": 2,
        "del_flag": 1,
        "region": "+1",
        "deposite": 1000.00,
        "multiple": 5,
        "credit": 5000.00,
        "freezing": 1000.00,
        "available": 4000.00,
        "deposite_usd": 1000.00,
        "credit_usd": 5000.00,
        "freezing_usd": 1000.00,
        "available_usd": 4000.00,
        "approve_status": 0,
        "create_date": 1745273064000,
        "last_login_time": 1745276947000
    }
]
```

#### 7.1.2 Odoo发送的应答消息

```json
[
    {
        "code": 0,
        "message": "",
        "user_id": "user2025042200001",
        "ack_req_id": "1c47c1540a164a959a2a2e5707d0fbb7"
    }
]
```

#### 7.1.3 审核完成通知消息

```json
[
    {
        "req_id": "550e8400-e29b-41d4-a716-446655440000",
        "audit": true,
        "odoo_user_id": "CUS001",
        "user_id": "user2025042200001"
    }
]
```

### 7.2 投标报价消息

```json
{
    "user_id": "12345",
    "lot_id": "LOT001",
    "price": 100.00,
    "quantity": 10,
    "currency": "USD",
    "bid_time": "2024-01-15T10:30:00Z"
}
```

### 7.3 标单状态消息

```json
{
    "req_id": "550e8400-e29b-41d4-a716-446655440000",
    "operate_type": 3,
    "data": [
        {
            "group_id": "BID001",
            "status_type": 1
        }
    ]
}
```

### 7.4 信用额度请求消息

```json
{
    "message_id": "550e8400-e29b-41d4-a716-446655440001",
    "user_id_list": ["12345", "12346", "12347"]
}
```

### 7.5 信用额度响应消息

```json
{
    "message_id": "550e8400-e29b-41d4-a716-446655440001",
    "data": [
        {
            "user_id": "12345",
            "currency": "USD",
            "total_credit": 10000.00,
            "locked_credit": 2000.00,
            "available_credit": 8000.00
        }
    ]
}
```

## 8. 部署和监控

### 8.1 启动流程

1. **模块自动加载**：Odoo启动时自动加载`galaxy_monkey_patching`模块
2. **猴子补丁生效**：修改服务器启动行为
3. **RabbitMQ线程启动**：延迟5秒后启动消费者线程
4. **消费者注册**：根据配置启动对应的消费者

### 8.2 监控和日志

- **连接监控**：定期检查RabbitMQ连接状态
- **消息日志**：详细记录每个消息的处理过程
- **错误处理**：完整的异常处理和重试机制
- **性能监控**：消息处理时间和成功率统计

### 8.3 故障恢复

- **自动重连**：连接断开时自动重新连接
- **消息持久化**：确保消息不丢失
- **事务安全**：使用数据库事务保证数据一致性
- **优雅停止**：服务器关闭时正确停止所有线程

## 9. 关键文件清单

### 9.1 核心模块文件

| 文件路径 | 作用 |
|---------|------|
| `galaxy_monkey_patching/__init__.py` | 猴子补丁入口，服务器启动修改 |
| `galaxy_monkey_patching/rabbitmq/thread_rabbitmq.py` | RabbitMQ线程实现 |
| `rabbitmq_galaxy/models/rabbitmq_server.py` | RabbitMQ基础服务类 |

### 9.2 业务模块文件

| 文件路径 | 作用 |
|---------|------|
| `base_bid_management/models/rabbitmq_server.py` | 客户信息同步处理 |
| `quotation_bid/models/rabbitmq_server.py` | 投标报价同步处理 |
| `account_customer_credit/models/rabbitmq_server.py` | 信用额度请求处理 |
| `quotation_bid_customer_credit/models/rabbitmq_server.py` | 信用额度同步处理 |

### 9.3 配置文件

| 文件路径 | 作用 |
|---------|------|
| `base_bid_management/data/rabbitmq_queue.xml` | 客户信息队列配置 |
| `quotation_bid/data/rabbitmq_queue.xml` | 投标相关队列配置 |
| `account_customer_credit/data/rabbitmq_queue.xml` | 信用额度队列配置 |
| `odoo.conf` | 系统配置参数 |

---

**文档版本**：v1.0  
**最后更新**：2024-01-15  
**维护者**：Galaxy ERP开发团队
