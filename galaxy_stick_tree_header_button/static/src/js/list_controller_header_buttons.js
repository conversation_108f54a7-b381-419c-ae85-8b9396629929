odoo.define(function (require) {
    'use strict';
    var ListController = require('web.ListController');
    const viewUtils = require('web.viewUtils');

    ListController.include({
        _renderHeaderButtons() {
            if (this.$headerButtons) {
                this.$headerButtons.remove();
                this.$headerButtons = null;
            }
            if (!this.headerButtons.length) {
                return;
            }
            const hasSelectdRecords = this.selectedRecords.length
            const btnClasses = 'btn-primary btn-secondary btn-link btn-success btn-info btn-warning btn-danger'.split(' ');
            let $elms = $();
            this.headerButtons.forEach(node => {
                const context = node.attrs.context ? JSON.parse(node.attrs.context.replace(/'/g, '"')) : {}
                if (context.show_button || hasSelectdRecords) {
                    const $btn = viewUtils.renderButtonFromNode(node);
                    $btn.addClass('btn');
                    if (!btnClasses.some(cls => $btn.hasClass(cls))) {
                        $btn.addClass('btn-secondary');
                    }
                    $btn.on("click", this._onHeaderButtonClicked.bind(this, node));
                    $elms = $elms.add($btn);
                }
            });
            this.$headerButtons = $elms;
            if (this.$buttons.children('.o_list_export_xlsx').length > 0) {
                // 存在 class 为 o_list_exportxlsx 的元素
                this.$headerButtons.insertBefore(this.$buttons.children().filter('.o_list_export_xlsx'));
            } else {
                this.$headerButtons.appendTo(this.$buttons);
            }
        },
    })

});