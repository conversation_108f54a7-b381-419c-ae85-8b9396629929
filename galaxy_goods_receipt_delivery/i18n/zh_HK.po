# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* galaxy_goods_receipt_delivery
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0+e-20230306\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-04-21 09:40+0000\n"
"PO-Revision-Date: 2025-04-21 09:40+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: galaxy_goods_receipt_delivery
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_receipt_stock_delivery_order_out_wizard_form
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_receipt_stock_delivery_order_wizard_form
msgid ""
"<strong>Confirmed Box Count</strong>\n"
"                        <br/>"
msgstr ""
"<strong>已確認箱數</strong>\n"
"                        <br/>"

#. module: galaxy_goods_receipt_delivery
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_receipt_stock_pick_order_wizard_form
msgid ""
"<strong>Picked Box Count</strong>\n"
"                        <br/>"
msgstr ""
"<strong>已揀貨箱數</strong>\n"
"                        <br/>"

#. module: galaxy_goods_receipt_delivery
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_receipt_stock_delivery_order_out_wizard_form
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_receipt_stock_delivery_order_wizard_form
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_receipt_stock_pick_order_wizard_form
msgid ""
"<strong>Total Box Count</strong>\n"
"                        <br/>"
msgstr ""
"<strong>總箱數</strong>\n"
"                        <br/>"

#. module: galaxy_goods_receipt_delivery
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_receipt_stock_delivery_order_out_wizard_form
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_receipt_stock_delivery_order_wizard_form
msgid ""
"<strong>Wait Confirm Box Count</strong>\n"
"                        <br/>"
msgstr ""
"<strong>待確認箱數</strong>\n"
"                        <br/>"

#. module: galaxy_goods_receipt_delivery
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_receipt_stock_pick_order_wizard_form
msgid ""
"<strong>Wait Pick Box Count</strong>\n"
"                        <br/>"
msgstr ""
"<strong>待揀貨箱數</strong>\n"
"                        <br/>"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields.selection,name:galaxy_goods_receipt_delivery.selection__galaxy_stock_delivery_order__state__audit
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_stock_delivery_order_form
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_stock_delivery_order_kanban
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_stock_delivery_order_search
msgid "Audit"
msgstr "審核"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_line_wizard__box_id
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_out_line_wizard__box_id
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_pick_order_line_wizard__box_id
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_delivery_order_line__box_id
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_pick_order_line__box_id
msgid "Box"
msgstr "箱"

#. module: galaxy_goods_receipt_delivery
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_receipt_stock_delivery_order_out_wizard_form
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_receipt_stock_delivery_order_wizard_form
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_receipt_stock_pick_order_wizard_form
msgid "Box No"
msgstr "箱號"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_out_wizard__box_no_error_message
msgid "Box No Error Message"
msgstr "箱號錯誤訊息"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_out_wizard__box_no_info_message
msgid "Box No Info Message"
msgstr "箱號資訊訊息"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_out_wizard__box_no_input
msgid "Box No Input"
msgstr "箱號輸入"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_line_wizard__carton
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_out_line_wizard__carton
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_out_wizard__box_no
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_wizard__carton
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_wizard__carton_input
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_pick_order_line_wizard__carton
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_pick_order_wizard__carton
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_pick_order_wizard__carton_input
msgid "Box Number"
msgstr "箱號"

#. module: galaxy_goods_receipt_delivery
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_stock_delivery_order_form
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_stock_pick_order_form
msgid "Boxes"
msgstr "箱子"

#. module: galaxy_goods_receipt_delivery
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_stock_delivery_order_kanban
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_stock_pick_order_kanban
msgid "Boxes:"
msgstr "箱子："

#. module: galaxy_goods_receipt_delivery
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_receipt_stock_delivery_order_audit_wizard_form
msgid "Cancel"
msgstr "取消"

#. module: galaxy_goods_receipt_delivery
#: code:addons/galaxy_goods_receipt_delivery/models/galaxy_stock_delivery_order.py:0
#, python-format
msgid "Cannot complete delivery order. Some boxes are not delivered yet."
msgstr "無法完成出貨單。部分箱子尚未出貨。"

#. module: galaxy_goods_receipt_delivery
#: code:addons/galaxy_goods_receipt_delivery/models/galaxy_stock_pick_order.py:0
#, python-format
msgid "Cannot complete picking order. Some boxes are not picked yet."
msgstr "無法完成揀貨單。部分箱子尚未揀貨。"

#. module: galaxy_goods_receipt_delivery
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_receipt_stock_delivery_order_wizard_form
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_receipt_stock_pick_order_wizard_form
msgid "Close"
msgstr "關閉"

#. module: galaxy_goods_receipt_delivery
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_receipt_stock_delivery_order_wizard_form
msgid "Close And Continue"
msgstr ""

#. module: galaxy_goods_receipt_delivery
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_receipt_stock_delivery_order_out_wizard_form
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_stock_delivery_order_form
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_stock_delivery_order_kanban
msgid "Confirm"
msgstr ""

#. module: galaxy_goods_receipt_delivery
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_receipt_stock_delivery_order_out_wizard_form
msgid "Confirm And Continue"
msgstr ""

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields.selection,name:galaxy_goods_receipt_delivery.selection__galaxy_stock_delivery_order_line__state__confirmed
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_receipt_stock_delivery_order_out_wizard_form
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_receipt_stock_delivery_order_wizard_form
msgid "Confirmed"
msgstr ""

#. module: galaxy_goods_receipt_delivery
#: model_terms:ir.actions.act_window,help:galaxy_goods_receipt_delivery.action_galaxy_stock_delivery_order
msgid "Create your first delivery order"
msgstr ""

#. module: galaxy_goods_receipt_delivery
#: model_terms:ir.actions.act_window,help:galaxy_goods_receipt_delivery.action_galaxy_stock_pick_order
msgid "Create your first pick order"
msgstr ""

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_audit_wizard__create_uid
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_line_wizard__create_uid
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_out_line_wizard__create_uid
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_out_wizard__create_uid
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_wizard__create_uid
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_pick_order_line_wizard__create_uid
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_pick_order_wizard__create_uid
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_delivery_order__create_uid
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_delivery_order_line__create_uid
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_pick_order__create_uid
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_pick_order_line__create_uid
msgid "Created by"
msgstr ""

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_audit_wizard__create_date
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_line_wizard__create_date
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_out_line_wizard__create_date
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_out_wizard__create_date
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_wizard__create_date
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_pick_order_line_wizard__create_date
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_pick_order_wizard__create_date
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_delivery_order__create_date
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_delivery_order_line__create_date
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_pick_order__create_date
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_pick_order_line__create_date
msgid "Created on"
msgstr ""

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_delivery_order__customer_id
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_pick_order__customer_id
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_stock_delivery_order_search
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_stock_pick_order_search
msgid "Customer"
msgstr "客戶"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_out_wizard__delivered_qty
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_wizard__delivered_qty
msgid "Delivered Quantity"
msgstr "已出貨數量"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields.selection,name:galaxy_goods_receipt_delivery.selection__galaxy_stock_delivery_order__state__delivery
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_stock_delivery_order_search
msgid "Delivery"
msgstr "出貨"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_delivery_order_line__delivery_date
msgid "Delivery Date"
msgstr "出貨日期"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_out_wizard__delivery_error_message
msgid "Delivery Error Message"
msgstr "出貨錯誤訊息"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_out_wizard__delivery_info_message
msgid "Delivery Info Message"
msgstr "出貨資訊訊息"

#. module: galaxy_goods_receipt_delivery
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_receipt_stock_delivery_order_audit_wizard_form
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_receipt_stock_delivery_order_out_wizard_form
msgid "Delivery No"
msgstr "出貨單號"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_audit_wizard__delivery_no_input
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_out_wizard__delivery_no_input
msgid "Delivery No Input"
msgstr "出貨單號輸入"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_out_wizard__delivery_order_id
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_wizard__delivery_order_id
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_delivery_order_line__delivery_order_id
#: model:ir.ui.menu,name:galaxy_goods_receipt_delivery.galaxy_stock_delivery_order
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_stock_delivery_order_form
msgid "Delivery Order"
msgstr "出貨單"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_line_wizard__delivery_order_line_id
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_out_line_wizard__delivery_order_line_id
msgid "Delivery Order Line"
msgstr "出貨單明細"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_out_wizard__line_ids
msgid "Delivery Order Lines"
msgstr "出貨單明細"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_audit_wizard__delivery_no
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_out_wizard__delivery_no
msgid "Delivery Order Number"
msgstr "出貨單號"

#. module: galaxy_goods_receipt_delivery
#: model:ir.actions.act_window,name:galaxy_goods_receipt_delivery.action_galaxy_stock_delivery_order
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_stock_delivery_order_search
msgid "Delivery Orders"
msgstr "出貨單"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_delivery_order_line__user_id
msgid "Delivery User"
msgstr "出貨人員"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_audit_wizard__display_name
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_line_wizard__display_name
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_out_line_wizard__display_name
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_out_wizard__display_name
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_wizard__display_name
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_pick_order_line_wizard__display_name
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_pick_order_wizard__display_name
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_delivery_order__display_name
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_delivery_order_line__display_name
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_pick_order__display_name
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_pick_order_line__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields.selection,name:galaxy_goods_receipt_delivery.selection__galaxy_stock_delivery_order__state__done
#: model:ir.model.fields.selection,name:galaxy_goods_receipt_delivery.selection__galaxy_stock_pick_order__state__done
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_stock_delivery_order_search
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_stock_pick_order_search
msgid "Done"
msgstr "完成"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields.selection,name:galaxy_goods_receipt_delivery.selection__galaxy_stock_delivery_order__state__draft
#: model:ir.model.fields.selection,name:galaxy_goods_receipt_delivery.selection__galaxy_stock_pick_order__state__draft
msgid "Draft"
msgstr "草稿"

#. module: galaxy_goods_receipt_delivery
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_stock_delivery_order_kanban
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_stock_pick_order_kanban
msgid "Dropdown menu"
msgstr "下拉選單"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_delivery_order__finish_date
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_pick_order__finish_date
msgid "Finish Date"
msgstr "完成日期"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_delivery_order__finish_user_id
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_pick_order__finish_user_id
msgid "Finish User"
msgstr "完成人員"

#. module: galaxy_goods_receipt_delivery
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_stock_pick_order_form
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_stock_pick_order_kanban
msgid "Finished"
msgstr "已完成"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_line_wizard__glot_number
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_out_line_wizard__glot_number
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_pick_order_line_wizard__glot_number
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_delivery_order_line__glot_number
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_pick_order_line__glot_number
msgid "GLot"
msgstr "批次"

#. module: galaxy_goods_receipt_delivery
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_stock_delivery_order_search
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_stock_pick_order_search
msgid "Group By"
msgstr "分組依據"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_audit_wizard__id
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_line_wizard__id
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_out_line_wizard__id
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_out_wizard__id
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_wizard__id
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_pick_order_line_wizard__id
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_pick_order_wizard__id
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_delivery_order__id
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_delivery_order_line__id
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_pick_order__id
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_pick_order_line__id
msgid "ID"
msgstr "ID"

#. module: galaxy_goods_receipt_delivery
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_stock_delivery_order_search
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_stock_pick_order_search
msgid "In Progress"
msgstr "處理中"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_audit_wizard____last_update
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_line_wizard____last_update
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_out_line_wizard____last_update
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_out_wizard____last_update
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_wizard____last_update
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_pick_order_line_wizard____last_update
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_pick_order_wizard____last_update
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_delivery_order____last_update
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_delivery_order_line____last_update
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_pick_order____last_update
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_pick_order_line____last_update
msgid "Last Modified on"
msgstr ""

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_audit_wizard__write_uid
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_line_wizard__write_uid
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_out_line_wizard__write_uid
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_out_wizard__write_uid
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_wizard__write_uid
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_pick_order_line_wizard__write_uid
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_pick_order_wizard__write_uid
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_delivery_order__write_uid
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_delivery_order_line__write_uid
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_pick_order__write_uid
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_pick_order_line__write_uid
msgid "Last Updated by"
msgstr ""

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_audit_wizard__write_date
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_line_wizard__write_date
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_out_line_wizard__write_date
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_out_wizard__write_date
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_wizard__write_date
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_pick_order_line_wizard__write_date
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_pick_order_wizard__write_date
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_delivery_order__write_date
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_delivery_order_line__write_date
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_pick_order__write_date
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_pick_order_line__write_date
msgid "Last Updated on"
msgstr ""

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_delivery_order__line_ids
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_pick_order__line_ids
msgid "Lines"
msgstr ""

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_line_wizard__location_id
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_out_line_wizard__location_id
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_pick_order_line_wizard__location_id
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_delivery_order_line__location_id
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_pick_order_line__location_id
msgid "Location"
msgstr "位置"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_wizard__error_message
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_wizard__message
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_pick_order_wizard__error_message
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_pick_order_wizard__message
msgid "Message"
msgstr "訊息"

#. module: galaxy_goods_receipt_delivery
#: code:addons/galaxy_goods_receipt_delivery/wizard/stock_delivery_order_wizard.py:0
#: model:ir.model,name:galaxy_goods_receipt_delivery.model_galaxy_stock_delivery_order
#, python-format
msgid "Stock Delivery Order"
msgstr "庫存出貨單"

#. module: galaxy_goods_receipt_delivery
#: model:ir.actions.act_window,name:galaxy_goods_receipt_delivery.action_galaxy_receipt_stock_delivery_order_audit_wizard
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_receipt_stock_delivery_order_audit_wizard_form
msgid "Stock Delivery Order Audit"
msgstr "庫存出貨單審核"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model,name:galaxy_goods_receipt_delivery.model_galaxy_receipt_stock_delivery_order_audit_wizard
msgid "Stock Delivery Order Audit Wizard"
msgstr "庫存出貨單審核精靈"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model,name:galaxy_goods_receipt_delivery.model_galaxy_stock_delivery_order_line
msgid "Stock Delivery Order Line"
msgstr "庫存出貨單明細"

#. module: galaxy_goods_receipt_delivery
#: model:ir.actions.act_window,name:galaxy_goods_receipt_delivery.action_galaxy_receipt_stock_delivery_order_line_wizard
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_receipt_stock_delivery_order_line_wizard_tree
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_receipt_stock_delivery_order_out_wizard_form
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_receipt_stock_delivery_order_wizard_form
msgid "Stock Delivery Order Lines"
msgstr "庫存出貨單明細"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model,name:galaxy_goods_receipt_delivery.model_galaxy_receipt_stock_delivery_order_out_wizard
msgid "Stock Delivery Order Out Wizard"
msgstr "庫存出貨單出貨精靈"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model,name:galaxy_goods_receipt_delivery.model_galaxy_receipt_stock_delivery_order_out_line_wizard
msgid "Stock Delivery Order Out Wizard Lines"
msgstr "庫存出貨單出貨精靈明細"

#. module: galaxy_goods_receipt_delivery
#: model:ir.actions.act_window,name:galaxy_goods_receipt_delivery.action_galaxy_receipt_stock_delivery_order_wizard
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_receipt_stock_delivery_order_out_wizard_form
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_receipt_stock_delivery_order_wizard_form
msgid "Stock Delivery Order Wizard"
msgstr "庫存出貨單精靈"

#. module: galaxy_goods_receipt_delivery
#: model:ir.ui.menu,name:galaxy_goods_receipt_delivery.galaxy_stock_pick_order
msgid "Stock Pick"
msgstr "揀貨單"

#. module: galaxy_goods_receipt_delivery
#: code:addons/galaxy_goods_receipt_delivery/wizard/stock_pick_order_wizard.py:0
#: model:ir.model,name:galaxy_goods_receipt_delivery.model_galaxy_stock_pick_order
#, python-format
msgid "Stock Pick Order"
msgstr "揀貨單"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model,name:galaxy_goods_receipt_delivery.model_galaxy_stock_pick_order_line
msgid "Stock Pick Order Line"
msgstr "揀貨單明細"

#. module: galaxy_goods_receipt_delivery
#: model:ir.actions.act_window,name:galaxy_goods_receipt_delivery.action_galaxy_receipt_stock_pick_order_line_wizard
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_receipt_stock_pick_order_line_wizard_tree
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_receipt_stock_pick_order_wizard_form
msgid "Stock Pick Order Lines"
msgstr "揀貨單明細"

#. module: galaxy_goods_receipt_delivery
#: model:ir.actions.act_window,name:galaxy_goods_receipt_delivery.action_galaxy_receipt_stock_pick_order_wizard
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_receipt_stock_pick_order_wizard_form
msgid "Stock Pick Order Wizard"
msgstr "揀貨單精靈"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model,name:galaxy_goods_receipt_delivery.model_galaxy_receipt_stock_delivery_order_line_wizard
#: model:ir.model,name:galaxy_goods_receipt_delivery.model_galaxy_receipt_stock_pick_order_line_wizard
msgid "Stock Transfer Out Wizard Lines"
msgstr "庫存出貨精靈明細"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields,help:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_out_wizard__box_no_error_message
msgid "The error message of the box no"
msgstr "箱號錯誤訊息"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields,help:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_out_wizard__delivery_error_message
msgid "The error message of the delivery"
msgstr "出貨錯誤訊息"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields,help:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_out_wizard__box_no_info_message
msgid "The info message of the box no"
msgstr "箱號資訊訊息"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields,help:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_out_wizard__delivery_info_message
msgid "The info message of the delivery"
msgstr "出貨資訊訊息"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields,help:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_out_wizard__box_no_input
msgid "The input of the box no"
msgstr "箱號輸入"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields,help:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_audit_wizard__delivery_no_input
#: model:ir.model.fields,help:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_out_wizard__delivery_no_input
msgid "The input of the delivery no"
msgstr "出貨單號輸入"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields,help:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_audit_wizard__pick_no_input
msgid "The input of the picking no"
msgstr "揀貨單號輸入"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields,help:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_out_wizard__box_no
msgid "The number of the box"
msgstr "箱號"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields,help:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_audit_wizard__delivery_no
#: model:ir.model.fields,help:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_out_wizard__delivery_no
msgid "The number of the delivery order"
msgstr "出貨單號"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields,help:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_audit_wizard__pick_no
msgid "The number of the picking order"
msgstr "揀貨單號"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_delivery_order__total_box_count
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_pick_order__total_box_count
msgid "Total Box Count"
msgstr "總箱數"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_out_wizard__total_qty
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_wizard__total_qty
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_pick_order_wizard__total_qty
msgid "Total Qty"
msgstr "總數量"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_wizard__line_ids
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_pick_order_wizard__line_ids
msgid "Transfer Lines"
msgstr "出貨明細"

#. module: galaxy_goods_receipt_delivery
#: code:addons/galaxy_goods_receipt_delivery/wizard/stock_delivery_order_out_wizard.py:0
#: code:addons/galaxy_goods_receipt_delivery/wizard/stock_delivery_order_wizard.py:0
#, python-format
msgid "Undelivered Boxes"
msgstr "未出貨箱子"

#. module: galaxy_goods_receipt_delivery
#: code:addons/galaxy_goods_receipt_delivery/wizard/stock_pick_order_wizard.py:0
#, python-format
msgid "Unpicked Boxes"
msgstr "未揀貨箱子"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields.selection,name:galaxy_goods_receipt_delivery.selection__galaxy_stock_delivery_order_line__state__wait_confirm
msgid "Wait Confirm"
msgstr "待確認"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_out_wizard__wait_deliver_qty
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_wizard__wait_deliver_qty
msgid "Wait Deliver Quantity"
msgstr "待出貨數量"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields.selection,name:galaxy_goods_receipt_delivery.selection__galaxy_stock_pick_order_line__state__wait_pick
msgid "Wait Pick"
msgstr "待揀貨"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_pick_order_wizard__wait_pick_qty
msgid "Wait Pick Quantity"
msgstr "待揀貨數量"

#. module: galaxy_goods_receipt_delivery
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_stock_delivery_order_kanban
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_stock_pick_order_kanban
msgid "Wait:"
msgstr "等待："

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields.selection,name:galaxy_goods_receipt_delivery.selection__galaxy_stock_delivery_order__state__waiting
msgid "Waiting"
msgstr "等待中"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_delivery_order__waiting_time
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_stock_pick_order__waiting_time
msgid "Waiting Time"
msgstr "等待時間"

#. module: galaxy_goods_receipt_delivery
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_line_wizard__wizard_id
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_delivery_order_out_line_wizard__wizard_id
#: model:ir.model.fields,field_description:galaxy_goods_receipt_delivery.field_galaxy_receipt_stock_pick_order_line_wizard__wizard_id
msgid "Wizard Reference"
msgstr "精靈參考"

#. module: galaxy_goods_receipt_delivery
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_stock_delivery_order_kanban
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_delivery.view_galaxy_stock_pick_order_kanban
msgid "min"
msgstr "分鐘"

#. module: galaxy_goods_receipt_delivery
#: code:addons/galaxy_goods_receipt_delivery/wizard/stock_delivery_order_out_wizard.py:0
#, python-format
msgid "出貨單 %s 狀態必須為待出貨或已出貨"
msgstr "出貨單 %s 狀態必須為待出貨或已出貨"

#. module: galaxy_goods_receipt_delivery
#: code:addons/galaxy_goods_receipt_delivery/wizard/stock_delivery_order_out_wizard.py:0
#, python-format
msgid "出貨單號不能為空"
msgstr "出貨單號不能為空"

#. module: galaxy_goods_receipt_delivery
#: code:addons/galaxy_goods_receipt_delivery/wizard/stock_delivery_order_out_wizard.py:0
#, python-format
msgid "已找到出貨單 %s"
msgstr "已找到出貨單 %s"

#. module: galaxy_goods_receipt_delivery
#: code:addons/galaxy_goods_receipt_delivery/wizard/stock_delivery_order_out_wizard.py:0
#, python-format
msgid "已找到箱號 %s"
msgstr "已找到箱號 %s"

#. module: galaxy_goods_receipt_delivery
#: code:addons/galaxy_goods_receipt_delivery/wizard/stock_delivery_order_out_wizard.py:0
#, python-format
msgid "找不到出貨單 %s"
msgstr "找不到出貨單 %s"

#. module: galaxy_goods_receipt_delivery
#: code:addons/galaxy_goods_receipt_delivery/wizard/stock_delivery_order_out_wizard.py:0
#: code:addons/galaxy_goods_receipt_delivery/wizard/stock_delivery_order_wizard.py:0
#: code:addons/galaxy_goods_receipt_delivery/wizard/stock_pick_order_wizard.py:0
#, python-format
msgid "箱號 %s 不存在"
msgstr "箱號 %s 不存在"

#. module: galaxy_goods_receipt_delivery
#: code:addons/galaxy_goods_receipt_delivery/wizard/stock_delivery_order_out_wizard.py:0
#, python-format
msgid "箱號 %s 已出貨"
msgstr "箱號 %s 已出貨"

#. module: galaxy_goods_receipt_delivery
#: code:addons/galaxy_goods_receipt_delivery/wizard/stock_pick_order_wizard.py:0
#, python-format
msgid "箱號 %s 狀態必須為待揀貨才能進行揀貨"
msgstr "箱號 %s 狀態必須為待揀貨才能進行揀貨"

#. module: galaxy_goods_receipt_delivery
#: code:addons/galaxy_goods_receipt_delivery/wizard/stock_delivery_order_out_wizard.py:0
#: code:addons/galaxy_goods_receipt_delivery/wizard/stock_delivery_order_wizard.py:0
#, python-format
msgid "箱號 %s 狀態必須為待確認才能進行出貨"
msgstr "箱號 %s 狀態必須為待確認才能進行出貨"

#. module: galaxy_goods_receipt_delivery
#: code:addons/galaxy_goods_receipt_delivery/wizard/stock_delivery_order_out_wizard.py:0
#, python-format
msgid "箱號不能為空"
msgstr "箱號不能為空"
