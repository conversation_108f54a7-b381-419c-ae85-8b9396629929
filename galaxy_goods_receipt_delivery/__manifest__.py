{
    'name': 'Goods Receipt and Delivery',
    'version': '1.0',
    'category': 'Galaxy_ERP/Galaxy_ERP',
    'summary': 'Manage Goods Receipt and Delivery',
    'description': """
        This module helps manage goods receipt and delivery processes.
        Features:
        - Create and manage goods receipt and delivery records
        - Track the status of goods movement
        - Generate reports
    """,
    'author': 'Galaxy',
    'website': 'https://www.galaxy.com',
    'depends': ['base', 'galaxy_goods_receipt'],
    'data': [
        'security/ir.model.access.csv',
        'data/ir_sequence.xml',
        'views/pick_order.xml',
        'views/delivery_order.xml',
        'wizard/stock_delivery_order_wizard.xml',
        'wizard/stock_pick_order_wizard.xml',
        'wizard/stock_delivery_order_audit_wizard_views.xml',
        'wizard/stock_delivery_order_out_wizard_views.xml',
        'views/menu.xml',
        'static/src/xml/assets.xml',
    ],
    'qweb': [
        'static/src/xml/delivery_tree.xml',
    ],
    'installable': True,
    'application': False,
    'auto_install': False,
    'license': 'LGPL-3',
} 