<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Tree View -->
    <record id="view_galaxy_stock_pick_order_tree" model="ir.ui.view">
        <field name="name">galaxy.stock.pick.order.tree</field>
        <field name="model">galaxy.stock.pick.order</field>
        <field name="arch" type="xml">
            <tree string="Pick Orders"  default_order="waiting_time desc">
                <field name="name"/>
                <field name="customer_id"/>
                <field name="total_box_count"/>
                <field name="picked_box_count"/>
                <field name="pending_box_count"/>
                <field name="waiting_time" widget="badge_integer" decoration-muted="waiting_time &lt;= 10" decoration-info="waiting_time &gt; 10 and waiting_time &lt;= 20" decoration-warning="waiting_time &gt; 20 and waiting_time &lt;= 30" decoration-danger="waiting_time &gt; 30"/>
                <field name="state" widget="badge" decoration-muted="state=='draft'" decoration-warning="state=='ready'" decoration-info="state=='picking'" decoration-success="state=='done'"/>
                <field name="create_date"/>
                <field name="create_uid" optional="hide"/>
                <field name="write_date" optional="hide"/>
                <field name="write_uid" optional="hide"/>
            </tree>
        </field>
    </record>

    <!-- Search View -->
    <record id="view_galaxy_stock_pick_order_search" model="ir.ui.view">
        <field name="name">galaxy.stock.pick.order.search</field>
        <field name="model">galaxy.stock.pick.order</field>
        <field name="arch" type="xml">
            <search string="Pick Orders">
                <field name="name" string="Search All"
                       filter_domain="['|', '|',
                                     ('name', 'ilike', self),
                                     ('customer_id.name', 'ilike', self),
                                     ('customer_id.bid_user_id', 'ilike', self)]"/>
                
                <filter string="In Progress" name="in_progress" domain="[('state', 'in', ['ready', 'picking'])]"/>
                <filter string="Ready" name="ready" domain="[('state', '=', 'ready')]"/>
                <filter string="Picking" name="picking" domain="[('state', '=', 'picking')]"/>
                <filter string="Done" name="done" domain="[('state', '=', 'done')]"/>
                
                <group expand="0" string="Group By">
                    <filter string="Status" name="group_by_state" context="{'group_by': 'state'}"/>
                    <filter string="Customer" name="group_by_partner" context="{'group_by': 'customer_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_galaxy_stock_pick_order_form" model="ir.ui.view">
        <field name="name">galaxy.stock.pick.order.form</field>
        <field name="model">galaxy.stock.pick.order</field>
        <field name="arch" type="xml">
            <form string="Pick Order">
                <header>
                    <button name="action_confirm" string="Confirm" type="object" class="btn-primary" attrs="{'invisible': [('state', '!=', 'draft')]}" confirm="Sure to confirm?"/>
                    <button name="action_start_picking" string="Start Picking" type="object" class="btn-primary" attrs="{'invisible': [('state', 'not in', ['ready'])]}" confirm="Sure to start picking?"/>
                    <button name="action_open_pick_wizard" string="Picking" type="object" class="btn-primary" attrs="{'invisible': [('state', '!=', 'picking')]}"/>
                    <button name="action_finished" string="Done" type="object" class="btn-secondary" attrs="{'invisible': [('state', '!=', 'picking')]}" confirm="Sure to finished?"/>
                    <!-- <button name="action_done" string="Done" type="object" class="btn-primary" special="cancel"/> -->
                    <field name="state" widget="statusbar" statusbar_visible="draft,ready,picking,done"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_open_all_boxes"
                            type="object"
                            class="oe_stat_button"
                            attrs="{'invisible': [('total_box_count', '=', 0)]}"
                            string="Boxes"
                            icon="fa-pencil-square-o">
                            <field name="total_box_count" widget="statinfo"/>
                        </button>

                        <button class="oe_stat_button" name="action_view_delivery_order" type="object" icon="fa-bars" attrs="{'invisible': [('delivery_order_id', '=', False)]}">
                            <field string="Delivery Order" name="delivery_order_id" widget="statinfo"/>
                        </button>
                    </div>
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="customer_id" options="{'no_create': True}" attrs="{'readonly': [('state', '!=', 'draft')]}" />
                            <field name="total_box_count"/>
                            <field name="picked_box_count"/>
                        </group>
                        <group>
                            <field name="pending_box_count"/>
                            <field name="waiting_time" readonly="1" force_save="1"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Boxes">
                            <field name="line_ids" attrs="{'readonly': [('state', '!=', 'draft')]}">
                                <tree editable="bottom">
                                    <field name="box_id" domain="[('status', 'in', ['store']), ('customer_id', '=', customer_id)]" options="{'no_create': True}"/>
                                    <field name="location_id"/>
                                    <field name="picking_unit_qty"/>
                                    <field name="glot_number"/>
                                    <field name="customer_id" optional="hide"/>
                                    <field name="state" readonly="1" force_save="1" widget="badge" decoration-success="state == 'picked'" decoration-warning="state == 'wait_pick'"/>
                                    <field name="pick_user_id" optional="hide"/>
                                    <field name="pick_up_date" optional="hide"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Action Window (Updated) -->
    <record id="action_galaxy_stock_pick_order" model="ir.actions.act_window">
        <field name="name">Pick Orders</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">galaxy.stock.pick.order</field>
        <field name="view_mode">tree,form,kanban</field>
        <field name="search_view_id" ref="view_galaxy_stock_pick_order_search"/>
        <field name="context">{'search_default_in_progress': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first pick order
            </p>
        </field>
    </record>

    <!-- Tree View for Pick Order Lines -->
    <record id="view_galaxy_stock_pick_order_line_tree" model="ir.ui.view">
        <field name="name">galaxy.stock.pick.order.line.tree</field>
        <field name="model">galaxy.stock.pick.order.line</field>
        <field name="arch" type="xml">
            <tree>
                <field name="pick_order_id"/>
                <field name="location_id"/>
                <field name="box_id"/>
                <field name="picking_unit_qty"/>
                <field name="glot_number"/>
                <field name="state"/>
                <field name="pick_user_id" optional="hide"/>
                <field name="pick_up_date" optional="hide"/>
            </tree>
        </field>
    </record>

    <!-- Kanban View -->
    <record id="view_galaxy_stock_pick_order_kanban" model="ir.ui.view">
        <field name="name">galaxy.stock.pick.order.kanban</field>
        <field name="model">galaxy.stock.pick.order</field>
        <field name="arch" type="xml">
            <kanban create="false" class="o_modules_kanban" default_group_by="state" default_order="waiting_time desc" group_create="false" group_edit="false" group_delete="false" sample="1" records_draggable="0">
                <field name="name"/>
                <field name="customer_id"/>
                <field name="state"/>
                <field name="total_box_count"/>
                <field name="picked_box_count"/>
                <field name="pending_box_count"/>
                <field name="waiting_time"/>
                <field name="create_date"/>
                <field name="create_uid"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_global_click container">
                            <div class="o_dropdown_kanban dropdown" t-if="!selection_mode">
                                <a class="dropdown-toggle o-no-caret btn" role="button" data-toggle="dropdown" href="#" aria-label="Dropdown menu" title="Dropdown menu">
                                    <span class="fa fa-ellipsis-v"/>
                                </a>
                                <div class="dropdown-menu" role="menu">
                                    <a name="action_start_picking" type="object" attrs="{'invisible':[('state','not in',['draft','ready'])]}" role="menuitem" class="dropdown-item">Start Picking</a>
                                    <a name="action_open_pick_wizard" type="object" attrs="{'invisible':[('state','!=','picking')]}" role="menuitem" class="dropdown-item">Open Pick Wizard</a>
                                    <a name="action_finished" type="object" attrs="{'invisible':[('state','!=','picking')]}" role="menuitem" class="dropdown-item">Finished</a>
                                </div>
                            </div>
                            <div class="oe_kanban_content">
                                <t t-if="record.state.raw_value == 'draft'">
                                    <span class="badge badge-pill float-right badge-muted mt4 mr16"><t t-esc="record.state.value"/></span>
                                </t>
                                <t t-if="record.state.raw_value == 'ready'">
                                    <span class="badge badge-pill float-right badge-warning mt4 mr16"><t t-esc="record.state.value"/></span>
                                </t>
                                <t t-if="record.state.raw_value == 'picking'">
                                    <span class="badge badge-pill float-right badge-info mt4 mr16"><t t-esc="record.state.value"/></span>
                                </t>
                                <t t-if="record.state.raw_value == 'done'">
                                    <span class="badge badge-pill float-right badge-success mt4 mr16"><t t-esc="record.state.value"/></span>
                                </t>
                                <div>
                                    <strong class="o_kanban_record_title"><field name="name"/></strong>
                                </div>
                                <div class="text-muted o_kanban_record_subtitle">
                                    <field name="customer_id"/>
                                </div>
                                <div class="o_kanban_record_bottom">
                                    <div class="oe_kanban_bottom_left">
                                        <span>Boxes: <t t-esc="record.picked_box_count.value"/>/<t t-esc="record.total_box_count.value"/></span>
                                    </div>
                                    <div class="oe_kanban_bottom_right">
                                        <span>Wait: <t t-esc="record.waiting_time.value"/> min</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>
</odoo>
