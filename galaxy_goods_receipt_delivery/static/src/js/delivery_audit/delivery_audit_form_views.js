odoo.define('galaxy_goods_receipt_delivery.delivery_audit_form_views', function (require) {
    "use strict";

    var core = require('web.core');
    var FormController = require('web.FormController');
    var FormView = require('web.FormView');
    var viewRegistry = require('web.view_registry');

    var _t = core._t;

    var DeliveryAuditFormController = FormController.extend({
        init: function () {
            this._super.apply(this, arguments);
            this.audioMap = {
                error: new Audio('/galaxy_goods_receipt/static/src/audio/error.mp3'),
                success: new Audio('/galaxy_goods_receipt/static/src/audio/success.mp3')
            };
        },

        _getAudio: function (type) {
            return type === 'scan_success' ? this.audioMap.success : this.audioMap.error;
        },

        custom_events: _.extend({}, FormController.prototype.custom_events, {
            delivery_no_input: '_handleDeliveryNoInputEvent',
            pick_no_input: '_handlePickNoInputEvent',
        }),

        _handleDeliveryNoInputEvent: function (event) {
            var self = this;
            let target = event.data.target;
            let dataPointID = event.data.dataPointID;
            let record = this.model.get(dataPointID);

            if (record.data.error_message) {
                this._getAudio('error').play();
                $('input[name="delivery_no"]').val('').focus();
            } else {
                this._getAudio('scan_success').play();
                $('input[name="pick_no"]').val('').focus();
            }
        },

        _handlePickNoInputEvent: function (event) {
            var self = this;
            let target = event.data.target;
            let dataPointID = event.data.dataPointID;
            let record = this.model.get(dataPointID);

            if (record.data.error_message) {
                this._getAudio('error').play();
                $('input[name="pick_no"]').val('').focus();
            } else {
                this._getAudio('scan_success').play();
                $('input[name="pick_no"]').val('').focus();
            }
        },
    });

    var DeliveryAuditFormView = FormView.extend({
        config: _.extend({}, FormView.prototype.config, {
            Controller: DeliveryAuditFormController,
        }),
    });

    viewRegistry.add('galaxy_receipt_delivery_audit_form', DeliveryAuditFormView);

    return DeliveryAuditFormView;
}); 