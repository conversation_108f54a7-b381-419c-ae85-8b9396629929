odoo.define('galaxy_goods_receipt_delivery.delivery_carton_field', function (require) {
    "use strict";

    var FieldChar = require('web.basic_fields').FieldChar;
    var registry = require('web.field_registry');
    var core = require('web.core');
    var _t = core._t;

    var DeliveryCartonField = FieldChar.extend({
        events: _.extend({}, FieldChar.prototype.events, {
            'keydown': '_onKeydown',
        }),

        init: function () {
            this._super.apply(this, arguments);
            this.cartonPattern = new RegExp('^[A-Za-z]\\d{5,6}$');
            this.formatErrorAudio = new Audio('/galaxy_goods_receipt/static/src/audio/error.mp3');
        },

        _onInput: function (event) {
            this.$el.next('span.carton-error').remove();

            let input = event.target.value;
            let currentLength = input.length;

            // 添加 abc123 的输入模式检查
            if (currentLength === 1 && input === 'a') return;
            if (currentLength === 2 && input === 'ab') return;
            if (currentLength === 3 && input === 'abc') return;
            if (currentLength === 4 && input === 'abc1') return;
            if (currentLength === 5 && input === 'abc12') return;
            if (currentLength === 6 && input === 'abc123') return;

            // 箱号格式验证
            if (currentLength >= 1 && !/[A-Za-z]/.test(input[0])) {
                event.target.value = '';
                return;
            }
            if (currentLength >= 2) {
                let remainingChars = input.slice(1);
                if (!/^\d*$/.test(remainingChars)) {
                    event.target.value = input[0];
                    return;
                }
            }
        },

        _onKeydown: function (event) {
            let input = event.target.value.trim().toUpperCase();
            if (!input) {
                return;
            }

            if (event.keyCode === 13) { // Check if Enter key was pressed
                var self = this;

                // 添加 abc123 的特殊处理
                if (input.toLowerCase() === 'abc123') {
                    self.trigger_up('galaxy_close_and_finished', {
                        target: event.target,
                        dataPointID: self.record.id,
                    });
                    return;
                }

                if (!this.cartonPattern.test(input)) {
                    this.$el.next('span.carton-error').remove();
                    this.$el.after(`<span class="carton-error" style="color:red;font-size: 20px;">${_t('格式錯誤')}</span>`);
                    this.$el.val('');
                    this.formatErrorAudio.play();
                    return;
                }

                self.trigger_up('field_changed', {
                    dataPointID: self.record.id,
                    changes: {'carton_input': input},
                    onSuccess: function () {
                        self.trigger_up('input_carton', {
                            target: event.target,
                            dataPointID: self.record.id,
                        });
                    }
                });
            } else {
                this._super.apply(this, arguments);
            }
        },
    });

    registry.add('delivery_carton_field', DeliveryCartonField);

    return DeliveryCartonField;
});
    