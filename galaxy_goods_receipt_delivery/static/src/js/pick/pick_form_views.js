odoo.define('galaxy_goods_receipt_delivery.pick_form_views', function (require) {
    "use strict";

    var core = require('web.core');
    var FormController = require('web.FormController');
    var FormView = require('web.FormView');
    var viewRegistry = require('web.view_registry');

    var _t = core._t;

    var PickLocationFieldFormController = FormController.extend({
        init: function () {
            this._super.apply(this, arguments);
            this.audioMap = {
                error: new Audio('/galaxy_goods_receipt/static/src/audio/error.mp3'),
                success: new Audio('/galaxy_goods_receipt/static/src/audio/success.mp3')
            };
        },

        _getAudio: function (type) {
            return type === 'scan_success' ? this.audioMap.success : this.audioMap.error;
        },

        custom_events: _.extend({}, FormController.prototype.custom_events, {
            input_carton: '_handleInputCartonEvent',
            galaxy_close_and_finished: '_handleCloseAndFinishedEvent',
        }),

        _handleCloseAndFinishedEvent: function (event) {
            let self = this;
            let dataPointID = event.data.dataPointID;
            let record = self.model.get(dataPointID);

            let method = 'action_close_and_finished';


            // 调用模型方法
            self._rpc({
                model: record.model,
                method: method,
                args: [record.res_id]
            }).then(function (result) {
                // 使用返回的action
                if (result) {
                    self.do_action(result).then(function () {
                        // 如果存在next_action则执行
                        if (result.params && result.params.next_action) {
                            self.do_action(result.params.next_action);
                        }
                    });
                }
            });
        },

        _handleInputCartonEvent: function (event) {
            var self = this;
            let target = event.data.target;
            let dataPointID = event.data.dataPointID;
            let record = this.model.get(dataPointID);

            if (record.data.error_message) {
                this._getAudio('error').play();
                $('input[name="carton"]').val('').focus();
            } else {
                this._getAudio('scan_success').play();
                $('input[name="carton"]').val('').focus();
            }
        },

    });

    var PickLocationFieldFormView = FormView.extend({
        config: _.extend({}, FormView.prototype.config, {
            Controller: PickLocationFieldFormController,
        }),
    });

    viewRegistry.add('galaxy_receipt_pick_form', PickLocationFieldFormView);

    return PickLocationFieldFormView;
}); 