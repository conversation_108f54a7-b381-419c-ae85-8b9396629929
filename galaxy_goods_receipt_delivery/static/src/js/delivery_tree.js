odoo.define('galaxy_goods_receipt_delivery.stock_delivery_order_tree', function (require) {
    'use strict';
    
    var ListController = require('web.ListController');
    var ListView = require('web.ListView');
    var viewRegistry = require('web.view_registry');

    var StockDeliveryOrderListController = ListController.extend({
        buttons_template: 'StockDeliveryOrderListView.buttons',
        events: _.extend({}, ListController.prototype.events, {
            'click .o_button_delivery_order_audit': '_openDeliveryOrderAuditForm',
            'click .o_button_delivery_order_out': '_openDeliveryOrderOutForm',
        }),

        _openDeliveryOrderAuditForm: function (event) {
            event.stopPropagation();
            this.do_action({
                type: 'ir.actions.act_window',
                res_model: 'galaxy.receipt.stock.delivery.order.audit.wizard',
                views: [[false, 'form']],
                target: 'new',
                context: {'from_delivery_order': 1},
            })
        },

        _openDeliveryOrderOutForm: function (event) {
            event.stopPropagation();
            this.do_action({
                type: 'ir.actions.act_window',
                res_model: 'galaxy.receipt.stock.delivery.order.out.wizard',
                views: [[false, 'form']],
                target: 'new',
                context: {'from_delivery_order': 1},
            })
        }
    });

    var StockDeliveryOrderListView = ListView.extend({
        config: _.extend({}, ListView.prototype.config, {
            Controller: StockDeliveryOrderListController,
        }),
    });

    viewRegistry.add('stock_delivery_order_tree', StockDeliveryOrderListView);
});
