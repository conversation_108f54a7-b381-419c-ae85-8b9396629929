odoo.define('galaxy_goods_receipt_delivery.delivery_out_box_field', function (require) {
    "use strict";

    var FieldChar = require('web.basic_fields').FieldChar;
    var registry = require('web.field_registry');
    var core = require('web.core');
    var _t = core._t;

    var DeliveryOutBoxField = FieldChar.extend({
        events: _.extend({}, FieldChar.prototype.events, {
            'keydown': '_onKeydown',
        }),

        init: function () {
            this._super.apply(this, arguments);
            // 箱号正则校验
            this.cartonPattern = new RegExp('^[A-Za-z]\\d{5,6}$');
            this.formatErrorAudio = new Audio('/galaxy_goods_receipt/static/src/audio/error.mp3');
        },

        _onInput: function (event) {
            this.$el.next('span.carton-error').remove();
        },

        _onKeydown: function (event) {
            let input = event.target.value.trim().toUpperCase();
            if (!input) {
                return;
            }

            if (event.keyCode === 13) { // Check if Enter key was pressed
                var self = this;

                if (!this.cartonPattern.test(input)) {
                    this.$el.next('span.carton-error').remove();
                    this.$el.after(`<span class="carton-error" style="color:red;font-size: 20px;">${_t('格式錯誤')}</span>`);
                    this.$el.val('');
                    this.formatErrorAudio.play();
                    return;
                }

                self.trigger_up('field_changed', {
                    dataPointID: self.record.id,
                    changes: {'box_no_input': input},
                    onSuccess: function () {
                        self.trigger_up('box_no_input', {
                            target: event.target,
                            dataPointID: self.record.id,
                        });
                    }
                });
            } else {
                this._super.apply(this, arguments);
            }
        },
    });

    registry.add('delivery_out_box_no_field', DeliveryOutBoxField);

    return DeliveryOutBoxField;
});
    