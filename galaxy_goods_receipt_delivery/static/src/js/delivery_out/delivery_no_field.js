odoo.define('galaxy_goods_receipt_delivery.delivery_out_delivery_field', function (require) {
    "use strict";

    var FieldChar = require('web.basic_fields').FieldChar;
    var registry = require('web.field_registry');
    var core = require('web.core');
    var _t = core._t;

    var DeliveryOutDeliveryField = FieldChar.extend({
        events: _.extend({}, FieldChar.prototype.events, {
            'keydown': '_onKeydown',
        }),

        _onKeydown: function (event) {
            let input = event.target.value.trim();
            if (!input) {
                return;
            }

            if (event.keyCode === 13) { // Check if Enter key was pressed
                var self = this;

                self.trigger_up('field_changed', {
                    dataPointID: self.record.id,
                    changes: {'delivery_no_input': input},
                    onSuccess: function () {
                        self.trigger_up('delivery_no_input', {
                            target: event.target,
                            dataPointID: self.record.id,
                        });
                    }
                });
            } else {
                this._super.apply(this, arguments);
            }
        },
    });

    registry.add('delivery_out_delivery_no_field', DeliveryOutDeliveryField);

    return DeliveryOutDeliveryField;
});
    