# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError
from datetime import datetime


class GalaxyStockPickOrder(models.Model):
    _name = 'galaxy.stock.pick.order'
    _description = 'Stock Pick Order'
    _rec_name = 'name'
    _order = 'create_date desc'
    
    @api.model
    def _read_group_state(self, stages, domain, order):
        state_list = dict(self._fields['state'].selection).keys()
        return state_list

    name = fields.Char(
        string='Order Number',
        required=True,
        readonly=True,
        copy=False,
        default=_('New'),
        index=True
    )

    customer_id = fields.Many2one(
        'res.partner',
        string='Customer',
        required=True
    )

    total_box_count = fields.Integer(
        string='Total Box Count',
        compute='_compute_box_counts',
        store=True
    )

    picked_box_count = fields.Integer(
        string='Picked Box Count',
        compute='_compute_box_counts',
        store=True
    )

    pending_box_count = fields.Integer(
        string='Pending Box Count',
        compute='_compute_box_counts',
        store=True
    )

    waiting_time = fields.Integer(
        string='Waiting Time',
        compute='_compute_waiting_time',
    )

    ready_date = fields.Datetime(string='Ready Date')
    finish_date = fields.Datetime(string='Finish Date')
    finish_user_id = fields.Many2one('res.users', string='Finish User')

    state = fields.Selection([
        ('draft', 'Draft'),
        ('ready', 'Ready'),
        ('picking', 'Picking'),
        ('done', 'Done')
    ], string='Status', default='draft', tracking=True, group_expand='_read_group_state')

    # box_ids = fields.Many2many(
    #     'galaxy.box',
    #     string='Boxes'
    # )

    line_ids = fields.One2many(
        'galaxy.stock.pick.order.line',
        'pick_order_id',
        string='Lines'
    )
    
    delivery_order_id = fields.Many2one('galaxy.stock.delivery.order', string='Delivery Order', index=True)
    
    @api.depends('ready_date', 'finish_date', 'state')
    def _compute_waiting_time(self):
        for record in self:
            if not record.ready_date:
                record.waiting_time = 0
            else:
                if record.state != 'done':
                    record.waiting_time = (fields.Datetime.now() - record.ready_date).total_seconds() // 60
                else:
                    record.waiting_time = (record.finish_date - record.ready_date).total_seconds() // 60

    @api.depends('line_ids', 'line_ids.state')
    def _compute_box_counts(self):
        for record in self:
            record.total_box_count = len(record.line_ids)
            record.picked_box_count = len(record.line_ids.filtered(lambda x: x.state == 'picked'))
            record.pending_box_count = record.total_box_count - record.picked_box_count

    @api.model
    def create(self, vals):
        seqn = self.env['ir.sequence'].with_context(galaxy_base_32=True).next_by_code('galaxy.stock.pick.order')
        vals.update({'name': seqn})
        return super(GalaxyStockPickOrder, self).create(vals)
    
    def unlink(self):
        # Check if any record is not in draft state
        non_draft_orders = self.filtered(lambda r: r.state != 'draft')
        if non_draft_orders:
            raise UserError(_('Only draft pick orders can be deleted. Please cancel or complete the orders first.'))
        return super(GalaxyStockPickOrder, self).unlink()
    
    def action_confirm(self):
        self.write({
            'state': 'ready',
            'ready_date': fields.Datetime.now(),
        })
        
    def action_start_picking(self):
        self.state = 'picking'

    def action_done(self):
        self.state = 'done'
        
    def action_open_all_boxes(self):
        action = self.env.ref('galaxy_goods_receipt.galaxy_box_action_window').read()[0]
        action['domain'] = [('id', 'in', self.line_ids.box_id.ids)]
        return action

    def action_open_pick_wizard(self):
        picking_path = self.env['picking.path'].search([])
        picking_path_sequence = {}
        for path in picking_path:
            picking_path_sequence[path.location_id.id] = path.no
        lines = self.line_ids
        # Sort lines based on picking_path_sequence
        lines = sorted(lines, key=lambda x: picking_path_sequence.get(x.box_id.location_id.id, 99999))
        total = len(lines)
        picked_qty = 0
        wait_pick_qty = 0
        line_vals = []
        for line in lines:
            vals = {
                'pick_order_line_id': line.id,
                'box_id': line.box_id.id,
                'location_id': line.box_id.location_id.id,
                'carton': line.box_id.name,
                'qty': line.box_id.picking_unit_qty,
                'glot_number': line.box_id.glot_number,
            }
            if line.state == 'picked':
                picked_qty += 1
                vals['state'] = 'picked'
            else:
                wait_pick_qty += 1
                vals['state'] = 'wait_pick'
            line_vals.append((0, 0, vals))
            
        wizard = self.env['galaxy.receipt.stock.pick.order.wizard'].create({
            'pick_order_id': self.id,
            'total_qty': total,
            'picked_qty': picked_qty,
            'wait_pick_qty': wait_pick_qty,
            'line_ids': line_vals,
        })
        return {
            'type': 'ir.actions.act_window',
            'name': 'Stock Pick Order Wizard',
            'res_model': 'galaxy.receipt.stock.pick.order.wizard',
            'view_mode': 'form',
            'res_id': wizard.id,
            'target': 'new',
            'context': {'create': False, 'delete': False},
        }
        
    def action_finished(self):
        # Check if all boxes are in picked state
        unpicked_lines = self.line_ids.filtered(lambda x: x.state != 'picked')
        if unpicked_lines:
            raise UserError(_("Cannot complete picking order. Some boxes are not picked yet."))
        
        finish_date = fields.Datetime.now()
            
        self.write({
            'state': 'done',
            'finish_date': finish_date,
            'finish_user_id': self.env.user.id,
            'waiting_time': (finish_date - self.create_date).total_seconds() // 60,
        })
        
        # Update box locations to picking area
        picking_location = self.env['stock.location'].search([
            ('usage', '=', 'internal'),
            ('name', '=', '拣货区')
        ], limit=1)
        
        if not picking_location:
            raise UserError(_("Picking location '拣货区' not found."))
            
        for line in self.line_ids:
            line.box_id.write({
                'location_id': picking_location.id,
                'status': 'picked',
            })

        # Create delivery order
        delivery_order_vals = {
            'customer_id': self.customer_id.id,
            'state': 'ready',
            'ready_date': fields.Datetime.now(),
            'pick_order_id': self.id,
        }
        
        # Create delivery order lines
        line_vals = []
        for line in self.line_ids:
            line_vals.append((0, 0, {
                'box_id': line.box_id.id,
            }))
        
        delivery_order_vals['line_ids'] = line_vals
        
        # Create the delivery order
        delivery_order = self.env['galaxy.stock.delivery.order'].create(delivery_order_vals)
        self.delivery_order_id = delivery_order.id
        return {
            'type': 'ir.actions.act_window',
            'name': 'Delivery Order',
            'res_model': 'galaxy.stock.delivery.order',
            'view_mode': 'form',
            'res_id': delivery_order.id,
        }
        
    def action_view_delivery_order(self):
        return {
            'type': 'ir.actions.act_window',
            'name': 'Delivery Order',
            'res_model': 'galaxy.stock.delivery.order',
            'view_mode': 'form',
            'res_id': self.delivery_order_id.id,
        }


class GalaxyStockPickOrderLine(models.Model):
    _name = 'galaxy.stock.pick.order.line'
    _description = 'Stock Pick Order Line'

    pick_order_id = fields.Many2one('galaxy.stock.pick.order', index=True)
    customer_id = fields.Many2one('res.partner', related='pick_order_id.customer_id')
    box_id = fields.Many2one('galaxy.box', required=True)

    state = fields.Selection([
        ('wait_pick', 'Wait Pick'),
        ('picked', 'Picked'),
    ], string='State', default='wait_pick')

    location_id = fields.Many2one(related='box_id.location_id')
    picking_unit_qty = fields.Integer(related='box_id.picking_unit_qty')
    glot_number = fields.Char(related='box_id.glot_number')

    pick_user_id = fields.Many2one('res.users', string='Pick User')
    pick_up_date = fields.Datetime(string='Pick Up Date')
