# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError
from datetime import datetime


class GalaxyStockDeliveryOrder(models.Model):
    _name = 'galaxy.stock.delivery.order'
    _description = 'Stock Delivery Order'
    _rec_name = 'name'
    _order = 'create_date desc'

    @api.model
    def _read_group_state(self, stages, domain, order):
        state_list = dict(self._fields['state'].selection).keys()
        return state_list

    name = fields.Char(
        string='Order Number',
        required=True,
        readonly=True,
        copy=False,
        default=_('New'),
        index=True
    )

    customer_id = fields.Many2one(
        'res.partner',
        string='Customer',
        required=True
    )

    total_box_count = fields.Integer(
        string='Total Box Count',
        compute='_compute_box_counts',
        store=True
    )

    picked_box_count = fields.Integer(
        string='Picked Box Count',
        compute='_compute_box_counts',
        store=True
    )

    pending_box_count = fields.Integer(
        string='Pending Box Count',
        compute='_compute_box_counts',
        store=True
    )

    waiting_time = fields.Integer(
        string='Waiting Time',
        compute='_compute_waiting_time',
    )

    finish_date = fields.Datetime(string='Finish Date')
    ready_date = fields.Datetime(string='Ready Date')
    finish_user_id = fields.Many2one('res.users', string='Finish User')

    state = fields.Selection([
        ('draft', 'Draft'),
        ('waiting', 'Waiting'),
        ('ready', 'Ready'),
        ('delivery', 'Delivery'),
        ('audit', 'Audit'),
        ('done', 'Done')
    ], string='Status', default='draft', tracking=True, group_expand='_read_group_state')

    # box_ids = fields.Many2many(
    #     'galaxy.box',
    #     string='Boxes'
    # )

    line_ids = fields.One2many(
        'galaxy.stock.delivery.order.line',
        'delivery_order_id',
        string='Lines'
    )

    picking_count = fields.Integer(compute='_compute_pickings')
    pick_order_id = fields.Many2one('galaxy.stock.pick.order', string='Pick Order', index=True)

    @api.depends('line_ids', 'line_ids.state')
    def _compute_box_counts(self):
        for record in self:
            total_count = len(record.line_ids)
            picked_count = 0
            for line in record.line_ids:
                if line.state == 'confirmed':
                    picked_count += 1

            record.total_box_count = total_count
            record.picked_box_count = picked_count
            record.pending_box_count = total_count - picked_count

    @api.depends('ready_date', 'finish_date', 'state')
    def _compute_waiting_time(self):
        for record in self:
            if not record.ready_date:
                record.waiting_time = 0
            else:
                if record.state != 'done':
                    record.waiting_time = (fields.Datetime.now() - record.ready_date).total_seconds() // 60
                else:
                    record.waiting_time = (record.finish_date - record.ready_date).total_seconds() // 60

    def _compute_pickings(self):
        for rec in self:
            pickings = self.env['stock.picking'].search([('origin', '=', rec.name)])
            rec.picking_count = len(pickings)

    @api.model
    def create(self, vals):
        seqn = self.env['ir.sequence'].with_context(galaxy_base_32=True).next_by_code('galaxy.stock.delivery.order')
        vals.update({'name': seqn})
        return super(GalaxyStockDeliveryOrder, self).create(vals)

    def unlink(self):
        # Check if any record is not in draft state
        non_draft_orders = self.filtered(lambda r: r.state != 'draft')
        if non_draft_orders:
            raise UserError(_('Only draft delivery orders can be deleted. Please cancel or complete the orders first.'))
        return super(GalaxyStockDeliveryOrder, self).unlink()

    def action_ready(self):
        self.write({
            'state': 'ready',
            'ready_date': fields.Datetime.now(),
        })

    def action_start_delivery(self):
        self.state = 'delivery'

    def action_done(self):
        self.state = 'done'

    def action_open_all_boxes(self):
        action = self.env.ref('galaxy_goods_receipt.galaxy_box_action_window').read()[0]
        action['domain'] = [('id', 'in', self.line_ids.box_id.ids)]
        return action

    def action_open_delivery_wizard(self):
        lines = self.line_ids
        total = len(lines)
        delivered_qty = 0
        wait_deliver_qty = 0
        line_vals = []
        for line in lines:
            vals = {
                'delivery_order_line_id': line.id,
                'box_id': line.box_id.id,
                'location_id': line.box_id.location_id.id,
                'carton': line.box_id.name,
                'qty': line.box_id.picking_unit_qty,
                'glot_number': line.box_id.glot_number,
            }
            if line.state == 'confirmed':
                delivered_qty += 1
                vals['state'] = 'confirmed'
            else:
                wait_deliver_qty += 1
                vals['state'] = 'wait_confirm'
            line_vals.append((0, 0, vals))
        wizard = self.env['galaxy.receipt.stock.delivery.order.wizard'].create({
            'delivery_order_id': self.id,
            'total_qty': total,
            'delivered_qty': delivered_qty,
            'wait_deliver_qty': wait_deliver_qty,
            'line_ids': line_vals,
        })
        return {
            'type': 'ir.actions.act_window',
            'name': 'Stock Delivery Order Wizard',
            'res_model': 'galaxy.receipt.stock.delivery.order.wizard',
            'views': [(False, 'form')],
            'res_id': wizard.id,
            'target': 'new',
            'context': {'create': False, 'delete': False},
        }

    def action_confirm(self):
        # Check if all boxes are in confirmed state
        unconfirmed_lines = self.line_ids.filtered(lambda x: x.state != 'confirmed')
        if unconfirmed_lines:
            raise UserError(_("Cannot complete delivery order. Some boxes are not delivered yet."))

        finish_date = fields.Datetime.now()

        ready_date = self.ready_date or self.create_date

        self.write({
            'state': 'audit',
            'finish_date': finish_date,
            'finish_user_id': self.env.user.id,
            'waiting_time': (finish_date - ready_date).total_seconds() // 60,
        })

        # Update box locations to picking area
        picking_location = self.env['stock.location'].search([
            ('usage', '=', 'internal'),
            ('name', '=', '出货区')
        ], limit=1)

        if not picking_location:
            raise UserError(_("Picking location '出货区' not found."))

        for line in self.line_ids:
            line.box_id.write({
                'location_id': picking_location.id,
            })

    def action_audit(self):
        """
        审核出货单
        1. 更新单据状态为完成
        2. 更新箱状态为已出货
        3. 更新箱位置为虚拟客户位置
        4. 更新箱出货时间
        """
        # 查找虚拟客户位置
        virtual_customer_location = self.sudo().env.ref('stock.stock_location_customers')

        # 更新箱信息
        for line in self.line_ids:
            line.box_id.write({
                'status': 'delivered',
                'location_id': virtual_customer_location.id,
                'delivery_date': fields.Datetime.now(),
            })

        # 更新单据状态
        self.state = 'done'
        self.action_create_stock_picking()

    def action_create_stock_picking(self):
        """
        创建出货单
        基于 delivery order 创建 Odoo 原生发货单
        """
        self.ensure_one()

        # 检查状态
        if self.state != 'done':
            raise UserError(_('Only done status can create stock picking'))

        # 检查是否已存在发货单
        out_picking_type = self.sudo().env.ref('stock.picking_type_out')
        existing_picking = self.env['stock.picking'].search([
            ('origin', '=', self.name),
            ('picking_type_id', '=', out_picking_type.id)
        ])
        if existing_picking:
            raise UserError(_('Stock picking already exists for this delivery order'))

        # 获取所有相关的 box
        boxes = self.line_ids.mapped('box_id')
        if not boxes:
            raise UserError(_('No boxes found in delivery order'))

        # 获取所有相关的 package
        packages = boxes.mapped('package_id')
        if not packages:
            raise UserError(_('No packages found in boxes'))

        # 获取源库位（使用第一个 package 的库位）
        source_location = packages[0].location_id
        if not source_location:
            raise UserError(_('Source location not found for boxes'))

        # 获取目标库位（客户库位）
        dest_location = self.env.ref('stock.stock_location_customers')

        # 创建发货单
        picking = self.env['stock.picking'].create({
            'partner_id': self.customer_id.id,
            'picking_type_id': out_picking_type.id,
            'location_id': self.sudo().env.ref('stock.stock_location_stock').id,
            'location_dest_id': dest_location.id,
            'scheduled_date': fields.Datetime.now(),
            'origin': self.name,
            'state': 'draft'
        })

        # 创建 package_level_ids_details
        picking.write({
            'package_level_ids_details': [(0, 0, {
                'package_id': package.id,
                'company_id': self.env.company.id,
                'location_id': source_location.id,
                'location_dest_id': dest_location.id,
            }) for package in packages]
        })

        # 确认发货单
        picking.action_confirm()
        picking.action_assign()

        # 更新 move lines 的 picking_type_id
        picking.move_lines.write({
            'picking_type_id': picking.picking_type_id.id,
        })

        # 设置完成数量
        picking.package_level_ids_details.move_line_ids.write({'qty_done': 1})

        # 验证发货单
        picking.button_validate()

        return {
            'type': 'ir.actions.act_window',
            'name': _('Stock Picking'),
            'res_model': 'stock.picking',
            'view_mode': 'form',
            'res_id': picking.id,
        }

    def action_view_pickings(self):
        pickings = self.env['stock.picking'].search([('origin', '=', self.name)])
        return {
            'name': _('Picking'),
            'type': 'ir.actions.act_window',
            'res_model': 'stock.picking',
            'view_mode': 'tree,form',
            'domain': [('id', 'in', pickings.ids)],
        }
        
    def action_view_pick_order(self):
        return {
            'name': _('Pick Order'),
            'type': 'ir.actions.act_window',
            'res_model': 'galaxy.stock.pick.order',
            'view_mode': 'form',
            'res_id': self.pick_order_id.id,
        }


class GalaxyStockDeliveryOrderLine(models.Model):
    _name = 'galaxy.stock.delivery.order.line'
    _description = 'Stock Delivery Order Line'

    delivery_order_id = fields.Many2one('galaxy.stock.delivery.order', index=True)
    customer_id = fields.Many2one('res.partner', related='delivery_order_id.customer_id')
    box_id = fields.Many2one('galaxy.box', required=True)

    state = fields.Selection([
        ('wait_confirm', 'Wait Confirm'),
        ('confirmed', 'Confirmed'),
    ], string='State', default='wait_confirm')

    location_id = fields.Many2one(related='box_id.location_id')
    picking_unit_qty = fields.Integer(related='box_id.picking_unit_qty')
    glot_number = fields.Char(related='box_id.glot_number')

    user_id = fields.Many2one('res.users', string='Delivery User')
    delivery_date = fields.Datetime(string='Delivery Date')
