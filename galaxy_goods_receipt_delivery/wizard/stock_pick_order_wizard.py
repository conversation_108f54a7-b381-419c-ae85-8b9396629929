from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
from collections import defaultdict


class StockPickOrderWizard(models.TransientModel):
    _name = 'galaxy.receipt.stock.pick.order.wizard'
    _description = 'Quick Stock Pick Order Wizard'

    pick_order_id = fields.Many2one('galaxy.stock.pick.order')

    carton = fields.Char(
        string='Box Number'
    )

    carton_input = fields.Char(
        string='Box Number',
    )

    picked_qty = fields.Integer(
        string='Picked Quantity',
    )
    wait_pick_qty = fields.Integer(
        string='Wait Pick Quantity',
    )
    total_qty = fields.Integer()

    # Detail lines
    line_ids = fields.One2many(
        'galaxy.receipt.stock.pick.order.line.wizard',
        'wizard_id',
        string='Transfer Lines'
    )

    message = fields.Text(string='Message')
    error_message = fields.Text(string='Message')

    @api.onchange('carton_input')
    def _onchange_carton_input(self):
        init_vals = {
            'carton': self.carton_input.strip() if self.carton_input else '',
            'carton_input': '',
            'error_message': '',
        }
        if not self.carton_input:
            return

        carton = init_vals['carton']

        line = self.pick_order_id.line_ids.filtered(lambda x: x.box_id.name == carton)
        box = line.box_id

        if not line:
            init_vals['error_message'] = _("箱號 %s 不存在" % carton)
            self.write(init_vals)
            return

        if line.state not in ['wait_pick']:
            init_vals['error_message'] = _("箱號 %s 狀態必須為待揀貨才能進行揀貨" % carton)
            self.write(init_vals)
            return
        
        wizard_line = self.line_ids.filtered(lambda x: x.box_id.id == box.id)
        wizard_line.write({
            'state': 'picked',
        })

        # init_vals['line_ids'] = [(0, 0, {
        #     'carton': box.name,
        #     'box_id': box.id,
        #     'qty': box.picking_unit_qty,
        #     'glot_number': box.glot_number,
        #     'pick_order_line_id': line.id,
        # })]
        init_vals['picked_qty'] = self.picked_qty + 1
        init_vals['wait_pick_qty'] = self.wait_pick_qty - 1
        self.write(init_vals)

        if self.pick_order_id.state == 'ready':
            self.pick_order_id.state = 'picking'

        line.write({
            'state': 'picked',
            'pick_user_id': self.env.user.id,
            'pick_up_date': fields.Datetime.now(),
        })
        box.status = 'picking'

    @api.depends('line_ids')
    def compute_count(self):
        for wizard in self:
            lines = wizard.pick_order_id.line_ids
            wizard.total_qty = len(lines)
            picked = 0
            wait_pick = 0
            for line in lines:
                if line.state == 'picked':
                    picked += 1
                elif line.state == 'wait_pick':
                    wait_pick += 1
            wizard.picked_qty = picked
            wizard.wait_pick_qty = wait_pick

    def action_show_all_unpick_box(self):
        lines = self.env['galaxy.stock.pick.order.line'].search([
            ('pick_order_id', '=', self.pick_order_id.id),
            ('state', '=', 'wait_pick'),
        ])
        return {
            'name': _('Unpicked Boxes'),
            'type': 'ir.actions.act_window',
            'res_model': 'galaxy.stock.pick.order.line',
            'view_mode': 'tree',
            'target': 'new',
            'views': [(False, 'tree')],
            'domain': [('id', 'in', lines.ids)],
        }

    def action_close_and_finished(self):
        self.pick_order_id.action_finished()
        return {
            'name': _('Stock Pick Order'),
            'type': 'ir.actions.act_window',
            'views': [(False, 'form')],
            'res_model': 'galaxy.stock.pick.order',
            'view_mode': 'form',
            'res_id': self.pick_order_id.id,
            'target': 'current',
        }


class StockPickOrderLineWizard(models.TransientModel):
    _name = 'galaxy.receipt.stock.pick.order.line.wizard'
    _description = 'Stock Transfer Out Wizard Lines'

    wizard_id = fields.Many2one(
        'galaxy.receipt.stock.pick.order.wizard',
        string='Wizard Reference',
        index=True,
    )
    pick_order_line_id = fields.Many2one('galaxy.stock.pick.order.line')
    box_id = fields.Many2one('galaxy.box')
    location_id = fields.Many2one('stock.location')
    carton = fields.Char(
        string='Box Number',
    )
    qty = fields.Float(
        string='Quantity',
    )
    glot_number = fields.Char(
        string='GLot'
    )
    
    state = fields.Selection([
        ('wait_pick', 'Wait Pick'),
        ('picked', 'Picked'),
    ], string='State', default='wait_pick')

    def unlink_record_from_wizard(self):
        wizard = self.wizard_id
        self.pick_order_line_id.write({
            'state': 'wait_pick',
        })
        self.box_id.status = 'store'
        self.unlink()
        wizard.compute_count()

        return {
            'success': True,
            'message': ''
        }
