<!-- picking_pick_putaway_wizard_form.xml -->
<odoo>
    <record id="view_galaxy_receipt_stock_pick_order_wizard_form" model="ir.ui.view">
        <field name="name">galaxy.receipt.stock.pick.order.wizard.form</field>
        <field name="model">galaxy.receipt.stock.pick.order.wizard</field>
        <field name="arch" type="xml">
            <form string="Stock Pick Order Wizard" js_class="galaxy_receipt_pick_form">
                <div class="row my-1 mx-3 bg-white">
                    <div class="col-4 border p-3 text-center">
                        <strong>Picked Box Count</strong>
                        <br/>
                        <div class='display-4 font-weight-light'>
                            <field name='picked_qty' nolabel="1" readonly='1' force_save='1'/>
                        </div>
                    </div>
                    <div class="col-4 border p-3 text-center">
                        <strong>Wait Pick Box Count</strong>
                        <br/>
                        <!--                        <div class='display-4 font-weight-light'>-->
                        <!--                            <field name="wait_pick_qty" nolabel="1" readonly='1' force_save='1'/>-->
                        <!--                        </div>-->
                        <button name="action_show_all_unpick_box" class="btn btn-link" type="object">
                            <field name="wait_pick_qty" widget="statinfo" nolabel="1" readonly='1' force_save='1'
                                   style="font-size: 3rem;"/>
                        </button>
                    </div>
                    <div class="col-4 border p-3 text-center">
                        <strong>Total Box Count</strong>
                        <br/>
                        <div class='display-4 font-weight-light'>
                            <field name='total_qty' nolabel="1" readonly='1' force_save='1'/>
                        </div>
                    </div>
                </div>
                <sheet>
                    <group>
                        <field name="pick_order_id" invisible="1"/>
                        <field name="carton_input" invisible="1" force_save="1"/>
                        <label for='carton' string='Box No'/>
                        <div class='o_row'>
                            <field name='carton' widget="pick_carton_field" force_save="1"/>
                            <span>
                                <field name='error_message' class="text-danger"
                                       readonly='1'
                                       attrs="{'invisible': [('error_message', '=', '')]}"/>
                                <field name='message' readonly='1' class="text-success"
                                       attrs="{'invisible': [('message', '=', '')]}"/>
                            </span>
                        </div>
                    </group>
                    <notebook>
                        <page string="Lines">
                            <field name="line_ids" 
                                   default_order="write_date desc"
                                   options="{'no_open': True, 'no_create': True}" nolabel="1"
                                   readonly="1"
                                   force_save="1">
                                <tree string="Stock Pick Order Lines" editable="bottom" delete="1" create="0" edit="0"
                                      js_class='direct_unlink_line_without_save'
                                      options='{"unlink_method": "unlink_record_from_wizard"}'>
                                    <field name="box_id" invisible="1"/>
                                    <field name="write_date" invisible="1"/>
                                    <field name="carton"/>
                                    <field name="location_id"/>
                                    <field name="qty"/>
                                    <field name="glot_number"/>
                                    <field name="pick_order_line_id" invisible="1"/>
                                    <field name="state" widget="badge" decoration-success="state == 'picked'" decoration-warning="state == 'wait_pick'"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <footer>
                    <button string="Close" class="btn-secondary" special="cancel"/>
                </footer>

            </form>
        </field>
    </record>


    <record id="action_galaxy_receipt_stock_pick_order_wizard" model="ir.actions.act_window">
        <field name="name">Stock Pick Order Wizard</field>
        <field name="res_model">galaxy.receipt.stock.pick.order.wizard</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="view_galaxy_receipt_stock_pick_order_wizard_form"/>
        <field name="target">new</field>
    </record>

    <!-- Tree view for galaxy.receipt.stock.pick.order.line.wizard -->
    <record id="view_galaxy_receipt_stock_pick_order_line_wizard_tree" model="ir.ui.view">
        <field name="name">galaxy.receipt.stock.pick.order.line.wizard.tree</field>
        <field name="model">galaxy.receipt.stock.pick.order.line.wizard</field>
        <field name="arch" type="xml">
            <tree string="Stock Pick Order Lines" editable="bottom" delete="0" create="0" edit="0">
                <field name="box_id" invisible="1"/>
                <field name="carton"/>
                <field name="qty"/>
                <field name="glot_number"/>
            </tree>
        </field>
    </record>

    <!-- Action for galaxy.receipt.stock.pick.order.line.wizard -->
    <record id="action_galaxy_receipt_stock_pick_order_line_wizard" model="ir.actions.act_window">
        <field name="name">Stock Pick Order Lines</field>
        <field name="res_model">galaxy.receipt.stock.pick.order.line.wizard</field>
        <field name="view_mode">tree</field>
        <field name="view_id" ref="view_galaxy_receipt_stock_pick_order_line_wizard_tree"/>
        <field name="target">current</field>
    </record>
</odoo>
