# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError


class GalaxyReceiptStockDeliveryOrderOutWizard(models.TransientModel):
    _name = 'galaxy.receipt.stock.delivery.order.out.wizard'
    _description = 'Stock Delivery Order Out Wizard'

    # 出货单号
    delivery_no = fields.Char(
        string='Delivery Order Number',
        help='The number of the delivery order'
    )
    delivery_no_input = fields.Char(
        string='Delivery No Input',
        help='The input of the delivery no'
    )

    delivery_order_id = fields.Many2one('galaxy.stock.delivery.order', string='Delivery Order')
    delivery_error_message = fields.Char(
        string='Delivery Error Message',
        help='The error message of the delivery'
    )
    delivery_info_message = fields.Char(
        string='Delivery Info Message',
        help='The info message of the delivery'
    )

    # 提货单号
    box_no = fields.Char(
        string='Box Number',
        help='The number of the box'
    )
    box_no_input = fields.Char(
        string='Box No Input',
        help='The input of the box no'
    )

    box_no_error_message = fields.Char(
        string='Box No Error Message',
        help='The error message of the box no'
    )
    box_no_info_message = fields.Char(
        string='Box No Info Message',
        help='The info message of the box no'
    )
    # 出货单行
    line_ids = fields.One2many(
        'galaxy.receipt.stock.delivery.order.out.line.wizard',
        'wizard_id',
        string='Delivery Order Lines'
    )

    delivered_qty = fields.Integer(
        string='Delivered Quantity',
    )
    wait_deliver_qty = fields.Integer(
        string='Wait Deliver Quantity',
    )
    total_qty = fields.Integer()

    @api.onchange('delivery_no_input')
    def onchange_delivery_no_input(self):
        delivery_no = self.delivery_no_input
        initial_values = {
            'delivery_no_input': '',
            'delivery_order_id': None,
            'delivery_error_message': '',
            'delivery_info_message': '',
            'line_ids': [(5, 0, 0)],
        }
        if delivery_no is False:
            return
        if not delivery_no:
            initial_values['delivery_error_message'] = _('出貨單號不能為空')
            self.write(initial_values)
            return
        delivery_order = self.env['galaxy.stock.delivery.order'].search([('name', '=', delivery_no)], limit=1)
        if not delivery_order:
            initial_values['delivery_error_message'] = _('找不到出貨單 %s', delivery_no)
            self.write(initial_values)
            return
        elif delivery_order.state not in ['ready', 'delivery']:
            initial_values['delivery_error_message'] = _('出貨單 %s 狀態必須為待出貨或已出貨', delivery_no)
            self.write(initial_values)
            return
        initial_values['delivery_order_id'] = delivery_order
        initial_values['delivery_info_message'] = _('已找到出貨單 %s', delivery_no)
        for line in delivery_order.line_ids:
            if line.state != 'confirmed':
                continue
            initial_values['line_ids'].append((0, 0, {
                'location_id': line.box_id.location_id.id,
                'carton': line.box_id.name,
                'box_id': line.box_id.id,
                'qty': line.box_id.picking_unit_qty,
                'glot_number': line.box_id.glot_number,
                'delivery_order_line_id': line.id,
            }))
        self.write(initial_values)
        self.compute_qty()
        if self.delivery_order_id.state == 'ready':
            self.delivery_order_id.state = 'delivery'
        return

    @api.onchange('box_no_input')
    def onchange_box_no_input(self):
        carton = self.box_no_input
        initial_values = {
            'box_no_input': '',
            'box_no_error_message': '',
            'box_no_info_message': '',
        }
        if carton is False:
            return
        if not carton:
            initial_values['box_no_error_message'] = _('箱號不能為空')
            self.write(initial_values)
            return

        line = self.delivery_order_id.line_ids.filtered(lambda x: x.box_id.name == carton)
        box = line.box_id

        if not line:
            initial_values['box_no_error_message'] = _("箱號 %s 不存在" % carton)
            self.write(initial_values)
            return

        if line.state == 'confirmed':
            initial_values['box_no_error_message'] = _("箱號 %s 已出貨" % carton)
            self.write(initial_values)
            return

        if line.state not in ['wait_confirm']:
            initial_values['box_no_error_message'] = _("箱號 %s 狀態必須為待確認才能進行出貨" % carton)
            self.write(initial_values)
            return

        initial_values['line_ids'] = [(0, 0, {
            'location_id': box.location_id.id,
            'carton': box.name,
            'box_id': box.id,
            'qty': box.picking_unit_qty,
            'glot_number': box.glot_number,
            'delivery_order_line_id': line.id,
        })]
        initial_values['delivered_qty'] = self.delivered_qty + 1
        initial_values['wait_deliver_qty'] = self.wait_deliver_qty - 1
        initial_values['box_no_info_message'] = _('已找到箱號 %s', carton)
        self.write(initial_values)

        if self.delivery_order_id.state == 'ready':
            self.delivery_order_id.state = 'delivery'

        line.write({
            'state': 'confirmed',
            'user_id': self.env.user.id,
            'delivery_date': fields.Datetime.now(),
        })
        box.write({'status': 'delivering'})
        return

    def compute_qty(self):
        self.total_qty = self.delivery_order_id.total_box_count
        self.wait_deliver_qty = self.delivery_order_id.pending_box_count
        self.delivered_qty = self.delivery_order_id.picked_box_count

    def action_show_all_undelivered_box(self):
        lines = self.env['galaxy.stock.delivery.order.line'].search([
            ('delivery_order_id', '=', self.delivery_order_id.id),
            ('state', '=', 'wait_confirm'),
        ])
        return {
            'name': _('Undelivered Boxes'),
            'type': 'ir.actions.act_window',
            'res_model': 'galaxy.stock.delivery.order.line',
            'view_mode': 'tree',
            'target': 'new',
            'views': [(False, 'tree')],
            'domain': [('id', 'in', lines.ids)],
        }

    def action_confirm(self):
        if self.delivery_order_id:
            self.delivery_order_id.action_confirm()

    def action_confirm_and_continue(self):
        self.action_confirm()
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'galaxy.receipt.stock.delivery.order.out.wizard',
            'views': [[False, 'form']],
            'target': 'new',
            'context': {'from_delivery_order': 1},
        }


class StockDeliveryOrderLineOutWizard(models.TransientModel):
    _name = 'galaxy.receipt.stock.delivery.order.out.line.wizard'
    _description = 'Stock Delivery Order Out Wizard Lines'

    wizard_id = fields.Many2one(
        'galaxy.receipt.stock.delivery.order.out.wizard',
        string='Wizard Reference',
        index=True,
    )
    delivery_order_line_id = fields.Many2one('galaxy.stock.delivery.order.line', index=True)
    box_id = fields.Many2one('galaxy.box')
    location_id = fields.Many2one('stock.location')
    carton = fields.Char(
        string='Box Number',
    )
    qty = fields.Float(
        string='Quantity',
    )
    glot_number = fields.Char(
        string='GLot'
    )
