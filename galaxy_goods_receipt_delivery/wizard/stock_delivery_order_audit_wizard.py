# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError


class GalaxyReceiptStockDeliveryOrderAuditWizard(models.TransientModel):
    _name = 'galaxy.receipt.stock.delivery.order.audit.wizard'
    _description = 'Stock Delivery Order Audit Wizard'

    # 出货单号
    delivery_no = fields.Char(
        string='Delivery Order Number',
        help='The number of the delivery order'
    )
    delivery_no_input = fields.Char(
        string='Delivery No Input',
        help='The input of the delivery no'
    )

    # 提货单号
    pick_no = fields.Char(
        string='Picking Order Number',
        help='The number of the picking order'
    )
    pick_no_input = fields.Char(
        string='Picking No Input',
        help='The input of the picking no'
    )

    @api.onchange('delivery_no_input')
    def onchange_delivery_no_input(self):
        if self.delivery_no_input:
            self.delivery_no = self.delivery_no_input

    @api.onchange('pick_no_input')
    def onchange_pick_no_input(self):
        if self.pick_no_input:
            self.pick_no = self.pick_no_input
