from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
from collections import defaultdict


class StockDeliveryOrderWizard(models.TransientModel):
    _name = 'galaxy.receipt.stock.delivery.order.wizard'
    _description = 'Quick Stock Delivery Order Wizard'

    delivery_order_id = fields.Many2one('galaxy.stock.delivery.order')

    carton = fields.Char(
        string='Box Number'
    )

    carton_input = fields.Char(
        string='Box Number',
    )

    delivered_qty = fields.Integer(
        string='Delivered Quantity',
    )
    wait_deliver_qty = fields.Integer(
        string='Wait Deliver Quantity',
    )
    total_qty = fields.Integer()

    # Detail lines
    line_ids = fields.One2many(
        'galaxy.receipt.stock.delivery.order.line.wizard',
        'wizard_id',
        string='Transfer Lines'
    )

    message = fields.Text(string='Message')
    error_message = fields.Text(string='Message')

    @api.onchange('carton_input')
    def _onchange_carton_input(self):
        init_vals = {
            'carton': self.carton_input.strip() if self.carton_input else '',
            'carton_input': '',
            'error_message': '',
        }
        if not self.carton_input:
            return

        carton = init_vals['carton']

        line = self.delivery_order_id.line_ids.filtered(lambda x: x.box_id.name == carton)
        box = line.box_id

        if not line:
            init_vals['error_message'] = _("箱號 %s 不存在" % carton)
            self.write(init_vals)
            return

        if line.state not in ['wait_confirm']:
            init_vals['error_message'] = _("箱號 %s 狀態必須為待確認才能進行出貨" % carton)
            self.write(init_vals)
            return
        
        wizard_line = self.line_ids.filtered(lambda x: x.box_id.id == box.id)
        wizard_line.write({
            'state': 'confirmed',
        })

        # init_vals['line_ids'] = [(0, 0, {
        #     'location_id': box.location_id.id,
        #     'carton': box.name,
        #     'box_id': box.id,
        #     'qty': box.picking_unit_qty,
        #     'glot_number': box.glot_number,
        #     'delivery_order_line_id': line.id,
        # })]
        init_vals['delivered_qty'] = self.delivered_qty + 1
        init_vals['wait_deliver_qty'] = self.wait_deliver_qty - 1
        self.write(init_vals)

        if self.delivery_order_id.state == 'ready':
            self.delivery_order_id.state = 'delivery'

        line.write({
            'state': 'confirmed',
            'user_id': self.env.user.id,
            'delivery_date': fields.Datetime.now(),
        })
        box.write({'status': 'delivering'})

    @api.depends('line_ids')
    def compute_count(self):
        for wizard in self:
            lines = wizard.delivery_order_id.line_ids
            wizard.total_qty = len(lines)
            delivered = 0
            wait_deliver = 0
            for line in lines:
                if line.state == 'confirmed':
                    delivered += 1
                elif line.state == 'wait_confirm':
                    wait_deliver += 1
            wizard.delivered_qty = delivered
            wizard.wait_deliver_qty = wait_deliver

    def action_show_all_undelivered_box(self):
        lines = self.env['galaxy.stock.delivery.order.line'].search([
            ('delivery_order_id', '=', self.delivery_order_id.id),
            ('state', '=', 'wait_confirm'),
        ])
        return {
            'name': _('Undelivered Boxes'),
            'type': 'ir.actions.act_window',
            'res_model': 'galaxy.stock.delivery.order.line',
            'view_mode': 'tree',
            'target': 'new',
            'views': [(False, 'tree')],
            'domain': [('id', 'in', lines.ids)],
        }

    def action_close_and_finished(self):
        self.delivery_order_id.action_confirm()
        return {
            'name': _('Stock Delivery Order'),
            'type': 'ir.actions.act_window',
            'views': [(False, 'form')],
            'res_model': 'galaxy.stock.delivery.order',
            'view_mode': 'form',
            'res_id': self.delivery_order_id.id,
            'target': 'current',
        }

    def action_close_and_continue(self):
        return self.delivery_order_id.action_open_delivery_wizard()


class StockDeliveryOrderLineWizard(models.TransientModel):
    _name = 'galaxy.receipt.stock.delivery.order.line.wizard'
    _description = 'Stock Transfer Out Wizard Lines'

    wizard_id = fields.Many2one(
        'galaxy.receipt.stock.delivery.order.wizard',
        string='Wizard Reference',
        index=True,
    )
    delivery_order_line_id = fields.Many2one('galaxy.stock.delivery.order.line', index=True)
    box_id = fields.Many2one('galaxy.box')
    location_id = fields.Many2one('stock.location')
    carton = fields.Char(
        string='Box Number',
    )
    qty = fields.Float(
        string='Quantity',
    )
    glot_number = fields.Char(
        string='GLot'
    )
    
    state = fields.Selection([
        ('wait_confirm', 'Wait Confirm'),
        ('confirmed', 'Confirmed'),
    ], string='State', default='wait_confirm')

    def unlink_record_from_wizard(self):
        wizard = self.wizard_id
        self.delivery_order_line_id.write({
            'state': 'wait_confirm',
        })
        self.box_id.write({'status': 'picked'})
        self.unlink()
        wizard.compute_count()

        return {
            'success': True,
            'message': ''
        }
