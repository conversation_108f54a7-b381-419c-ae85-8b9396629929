<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_galaxy_receipt_stock_delivery_order_audit_wizard_form" model="ir.ui.view">
        <field name="name">galaxy.receipt.stock.delivery.order.audit.wizard.form</field>
        <field name="model">galaxy.receipt.stock.delivery.order.audit.wizard</field>
        <field name="arch" type="xml">
            <form string="Stock Delivery Order Audit" js_class="galaxy_receipt_delivery_audit_form">
                <sheet>
                    <group>
                        <field name="delivery_no_input" invisible="1"/>
                        <field name="pick_no_input" invisible="1"/>
                        <label for='delivery_no' string='Delivery No'/>
                        <div class='o_row'>
                            <field name='delivery_no' widget="delivery_audit_delivery_field" force_save="1"/>
                        </div>
                        <label for='pick_no' string='Picking No'/>
                        <div class='o_row'>
                            <field name='pick_no' widget="delivery_audit_pick_field" force_save="1"/>
                        </div>
                    </group>
                </sheet>
                <footer>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <record id="action_galaxy_receipt_stock_delivery_order_audit_wizard" model="ir.actions.act_window">
        <field name="name">Stock Delivery Order Audit</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">galaxy.receipt.stock.delivery.order.audit.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>
</odoo> 