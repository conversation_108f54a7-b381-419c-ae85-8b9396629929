<!-- delivery_pick_putaway_wizard_form.xml -->
<odoo>
    <record id="view_galaxy_receipt_stock_delivery_order_out_wizard_form" model="ir.ui.view">
        <field name="name">galaxy.receipt.stock.delivery.order.out.wizard.form</field>
        <field name="model">galaxy.receipt.stock.delivery.order.out.wizard</field>
        <field name="arch" type="xml">
            <form string="Stock Delivery Order Wizard" js_class="galaxy_receipt_delivery_out_form">
                <div class="row my-1 mx-3 bg-white">
                    <div class="col-4 border p-3 text-center">
                        <strong>Confirmed Box Count</strong>
                        <br/>
                        <div class='display-4 font-weight-light'>
                            <field name='delivered_qty' nolabel="1" readonly='1' force_save='1'/>
                        </div>
                    </div>
                    <div class="col-4 border p-3 text-center">
                        <strong>Wait Confirm Box Count</strong>
                        <br/>
                        <button name="action_show_all_undelivered_box" class="btn btn-link" type="object">
                            <field name="wait_deliver_qty" widget="statinfo" nolabel="1" readonly='1' force_save='1' style="font-size: 3rem;"/>
                        </button>
                    </div>
                    <div class="col-4 border p-3 text-center">
                        <strong>Total Box Count</strong>
                        <br/>
                        <div class='display-4 font-weight-light'>
                            <field name='total_qty' nolabel="1" readonly='1' force_save='1'/>
                        </div>
                    </div>
                </div>
                <sheet>
                    <group>
                        <field name="delivery_no_input" invisible="1"/>
                        <field name="box_no_input" invisible="1"/>
                        <field name="delivery_order_id" invisible="1" force_save="1"/>
                        <label for='delivery_no' string='Delivery No'/>
                        <div class='o_row'>
                            <field name='delivery_no' widget="delivery_out_delivery_no_field" force_save="1"/>
                            <span>
                                <field name='delivery_error_message' class="text-danger"
                                       readonly='1'
                                       force_save='1'
                                       attrs="{'invisible': [('delivery_error_message', '=', '')]}"/>
                                <field name='delivery_info_message' readonly='1' class="text-success"
                                       force_save='1'
                                       attrs="{'invisible': [('delivery_info_message', '=', '')]}"/>
                            </span>
                        </div>
                        <label for='box_no' string='Box No'/>
                        <div class='o_row'>
                            <field name='box_no' widget="delivery_out_box_no_field" force_save="1"/>
                            <span>
                                <field name='box_no_error_message' class="text-danger"
                                       readonly='1'
                                       force_save='1'
                                       attrs="{'invisible': [('box_no_error_message', '=', '')]}"/>
                                <field name='box_no_info_message' readonly='1' class="text-success"
                                       force_save='1'
                                       attrs="{'invisible': [('box_no_info_message', '=', '')]}"/>
                            </span>
                        </div>
                    </group>
                    <notebook>
                        <page string="Confirmed">
                            <field name="line_ids" options="{'no_open': True, 'no_create': True}" nolabel="1"
                                   readonly="1"
                                   force_save="1">
                                <tree string="Stock Delivery Order Lines" editable="bottom" delete="1" create="0" edit="0" 
                                    js_class='direct_unlink_line_without_save'
                                    options='{"unlink_method": "unlink_record_from_wizard"}'>
                                    <field name="box_id" invisible="1"/>
                                    <field name="location_id"/>
                                    <field name="carton"/>
                                    <field name="qty"/>
                                    <field name="glot_number"/>
                                    <field name="delivery_order_line_id" invisible="1"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <footer>
                    <button name="action_confirm" string="Confirm" type="object" class="btn-primary"/>
                    <button name="action_confirm_and_continue" string="Confirm And Continue" type="object" class="btn-secondary"/>
                </footer>
            </form>
        </field>
    </record>

</odoo>
