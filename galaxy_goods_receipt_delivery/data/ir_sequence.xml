<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <data noupdate="1">
        <record id="sequence_galaxy_stock_pick_order" model="ir.sequence">
            <field name="name">Galaxy Stock Pick Order</field>
            <field name="code">galaxy.stock.pick.order</field>
            <field name="prefix">PK</field>
            <field name="padding">4</field>
            <field name="number_next">1</field>
            <field name="number_increment">1</field>
            <field name="company_id" eval="False"/>
        </record>


        <record id="sequence_galaxy_stock_delivery_order" model="ir.sequence">
            <field name="name">Galaxy Stock Delivery Order</field>
            <field name="code">galaxy.stock.delivery.order</field>
            <field name="prefix">DO</field>
            <field name="padding">4</field>
            <field name="number_next">1</field>
            <field name="number_increment">1</field>
            <field name="company_id" eval="False"/>
        </record>
    </data>

</odoo>
