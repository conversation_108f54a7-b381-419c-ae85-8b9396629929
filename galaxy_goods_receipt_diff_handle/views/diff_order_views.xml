<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Tree View -->
    <record id="view_galaxy_stock_diff_order_tree" model="ir.ui.view">
        <field name="name">galaxy.stock.diff.order.tree</field>
        <field name="model">galaxy.stock.diff.order</field>
        <field name="arch" type="xml">
            <tree string="Stock Difference Orders">
                <field name="name"/>
                <field name="purchase_ids" widget="many2many_tags" optional="show"/>
                <field name="process_method" optional="show"/>
                <field name="currency_id" optional="hide"/>
                <field name="total_qty" optional="show"/>
                <field name="amount_total" optional="show" widget="monetary"/>
                <field name="state" optional="show" widget="badge"
                       decoration-muted="state == 'draft'"
                       decoration-info="state == 'processing'"
                       decoration-success="state == 'done'"/>
                <field name="create_date" optional="show"/>
                <field name="create_uid" optional="hide"/>
                <field name="write_date" optional="hide"/>
                <field name="write_uid" optional="hide"/>
            </tree>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_galaxy_stock_diff_order_form" model="ir.ui.view">
        <field name="name">galaxy.stock.diff.order.form</field>
        <field name="model">galaxy.stock.diff.order</field>
        <field name="arch" type="xml">
            <form string="Stock Difference Order">
                <header>
                    <button name="action_start_process"
                            type="object"
                            string="Confirm"
                            class="btn btn-primary"
                            attrs="{'invisible': [('state', '!=', 'draft')]}"
                            confirm="Are you sure to start process?"
                            />
                    <button name="action_done"
                            type="object"
                            string="Done"
                            class="btn btn-primary"
                            attrs="{'invisible': [('state', '!=', 'processing')]}"
                            confirm="Are you sure to done?"
                            />
                    <button name="action_cancel"
                            type="object"
                            string="Cancel"
                            attrs="{'invisible': [('state', 'in', ['draft', 'cancel'])]}"
                            confirm="Are you sure to cancel?"
                            />
                            
                    <field name="state" widget="statusbar" statusbar_visible="draft,processing,done"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_open_purchase_order"
                                type="object"
                                string="Purchase Order"
                                class="oe_stat_button"
                                icon="fa-list-alt">
                        </button>
                    </div>
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="vendor_id" options="{'no_create': True}" domain="[('supplier_rank', '>', 0)]" required="1"/>
                            <field name="purchase_ids" widget="many2many_tags"
                                   context="{'search_from_diff_handle':1}"
                                   domain="[('state', 'in', ['purchase', 'processing']), ('partner_id', '=', vendor_id)]"
                                   options="{'no_create': True}" required="1"/>
                            <field name="process_method"/>
                            <field name="note"/>
                        </group>
                        <group>
                            <field name="currency_id" options="{'no_create': True}" readonly="1" force_save="1"/>
                            <field name="amount_total" widget="monetary" currency_field="currency_id" optional="show" readonly="1" force_save="1"/>
                            <field name="total_qty"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Difference Lines">
                            <field name="line_ids">
                                <tree editable="bottom">
                                    <field name="currency_id" optional="hide" readonly="1" force_save="1"/>
                                    <field name="purchase_order_line_id" domain="[('order_id', 'in', parent.purchase_ids), ('product_id.type', '!=', 'service'), ('missing_qty', '!=', 0)]" options="{'no_create': True, 'no_open': True}" context="{'from_diff_order': 1}"/>
                                    <field name="won_order_line_id" optional="hide" readonly="1" force_save="1"/>
                                    <field name="won_order_lot_line_id" optional="hide" readonly="1" force_save="1"/>
                                    <field name="glot_number" optional="show" readonly="1" force_save="1"/>
                                    <field name="sku_id" optional="show" readonly="1" force_save="1"/>
                                    <field name="model_number" optional="hide" readonly="1" force_save="1"/>
                                    <field name="price_unit" optional="show" readonly="1" force_save="1"/>
                                    <field name="purchase_expected_qty" optional="show" readonly="1" force_save="1"/>
                                    <field name="purchase_missing_qty" optional="show" readonly="1" force_save="1"/>
<!--                                    <field name="process_method" optional="show" required="1"/>-->
                                    <field name="process_qty" optional="show" required="1"/>
                                    <field name="price_subtotal" widget="monetary" currency_field="currency_id" optional="show" readonly="1" force_save="1"/>
                                    <!-- <button name="action_show_details" type="object" icon="fa-list" width="0.1"/> -->
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Search View -->
    <record id="view_galaxy_stock_diff_order_search" model="ir.ui.view">
        <field name="name">galaxy.stock.diff.order.search</field>
        <field name="model">galaxy.stock.diff.order</field>
        <field name="arch" type="xml">
            <search string="Stock Difference Orders">
            <field name="name" string="Search All" filter_domain="['|','|',
                    ('name','ilike',self),
                    ('vendor_id.name','ilike',self),
                    ('vendor_id.ref','ilike',self)
                    ]"/>
                <field name="name"/>
                <field name="vendor_id"/>
                <separator/>
                <filter string="Waiting" name="waiting" domain="[('state','=','draft')]"/>
                <filter string="Processing" name="processing" domain="[('state','=','processing')]"/>
                <filter string="Done" name="done" domain="[('state','=','done')]"/>
                <group expand="0" string="Group By">
                    <filter string="Vendor" name="group_by_vendor" context="{'group_by':'vendor_id'}"/>
                </group>
                <searchpanel>
                    <field name="vendor_id" string="Vendor" enable_counters="1"/>
                </searchpanel>
            </search>
        </field>
    </record>

    <!-- Action -->
    <record id="action_galaxy_stock_diff_order" model="ir.actions.act_window">
        <field name="name">Diff Handle Orders</field>
        <field name="res_model">galaxy.stock.diff.order</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_galaxy_stock_diff_order_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first stock difference order
            </p>
        </field>
    </record>
</odoo> 