<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="purchase_order_form_inherit_packing_list" model="ir.ui.view">
        <field name="name">purchase.order.form.inherit.packing.list</field>
        <field name="model">purchase.order</field>
        <field name="inherit_id" ref="galaxy_goods_receipt.galaxy_purchase_order_form_view"/>
        <field name="arch" type="xml">
            <div name="button_box" position="inside">
                <button name="action_view_diff_order"
                    type="object"
                    class="oe_stat_button"
                    icon="fa-list-alt"
                    attrs="{'invisible': [('diff_order_count', '=', 0)]}">
                    <field string="Diff Order" name="diff_order_count" widget="statinfo"/>
                </button>
            </div>
            
            <xpath expr="//button[@name='action_create_count_order']" position="after">
                <button name="action_create_diff_order"
                    type="object"
                    string="Create Diff Order"
                    class="btn"
                    attrs="{'invisible': [('state', 'not in', ['purchase', 'processing'])]}"/>
            </xpath>
        </field>
    </record>
</odoo> 