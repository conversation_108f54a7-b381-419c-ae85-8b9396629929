# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* galaxy_goods_receipt_diff_handle
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0+e-20230306\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-03 06:52+0000\n"
"PO-Revision-Date: 2025-06-03 06:52+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: galaxy_goods_receipt_diff_handle
#: code:addons/galaxy_goods_receipt_diff_handle/models/diff_order.py:0
#, python-format
msgid ""
"- Line for Glot [%s] %s: Total process quantity (%s) exceeds missing "
"quantity (%s)"
msgstr ""

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order__message_needaction
msgid "Action Needed"
msgstr "需要您注意"

#. module: galaxy_goods_receipt_diff_handle
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_diff_handle.view_galaxy_stock_diff_order_form
msgid "Are you sure to cancel?"
msgstr "確定取消？"

#. module: galaxy_goods_receipt_diff_handle
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_diff_handle.view_galaxy_stock_diff_order_form
msgid "Are you sure to done?"
msgstr "確定完成？"

#. module: galaxy_goods_receipt_diff_handle
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_diff_handle.view_galaxy_stock_diff_order_form
msgid "Are you sure to start process?"
msgstr "確定開始處理？"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order__message_attachment_count
msgid "Attachment Count"
msgstr "附件數量"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields.selection,name:galaxy_goods_receipt_diff_handle.selection__galaxy_stock_diff_order__state__cancel
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_diff_handle.view_galaxy_stock_diff_order_form
msgid "Cancel"
msgstr "取消"

#. module: galaxy_goods_receipt_diff_handle
#: code:addons/galaxy_goods_receipt_diff_handle/models/diff_order.py:0
#, python-format
msgid ""
"Cannot process this difference order due to the following issues:\n"
"%s"
msgstr ""
"由於以下問題，無法處理此採購差異：\n"
"%s"

#. module: galaxy_goods_receipt_diff_handle
#: code:addons/galaxy_goods_receipt_diff_handle/models/diff_order.py:0
#, python-format
msgid ""
"Cannot process this difference order. The following purchase orders are not in valid states (purchase or processing):\n"
"%s"
msgstr ""
"無法處理此採購差異。以下採購訂單處於無效狀態（採購或處理中）：\n"
"%s"

#. module: galaxy_goods_receipt_diff_handle
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_diff_handle.view_galaxy_stock_diff_order_form
msgid "Confirm"
msgstr "確認"

#. module: galaxy_goods_receipt_diff_handle
#: code:addons/galaxy_goods_receipt_diff_handle/models/count_order.py:0
#, python-format
msgid ""
"Count has multi Purchase Order, please alloc extra units to one purchase "
"order."
msgstr "存在多張採購訂單，請將多到機器分配到其中一張採購訂單。"

#. module: galaxy_goods_receipt_diff_handle
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_diff_handle.purchase_order_form_inherit_packing_list
msgid "Create Diff Order"
msgstr "創建採購差異"

#. module: galaxy_goods_receipt_diff_handle
#: model_terms:ir.actions.act_window,help:galaxy_goods_receipt_diff_handle.action_galaxy_stock_diff_order
msgid "Create your first stock difference order"
msgstr "創建您的第一個採購差異"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order__create_uid
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order_line__create_uid
msgid "Created by"
msgstr "創建者"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order__create_date
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order_line__create_date
msgid "Created on"
msgstr "創建於"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order__currency_id
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order_line__currency_id
msgid "Currency"
msgstr "貨幣"

#. module: galaxy_goods_receipt_diff_handle
#: code:addons/galaxy_goods_receipt_diff_handle/models/diff_order.py:0
#, python-format
msgid "Currency Inconsistency"
msgstr "貨幣不一致"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.ui.menu,name:galaxy_goods_receipt_diff_handle.menu_galaxy_stock_diff_order
msgid "Diff Handle"
msgstr "採購差異"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.actions.act_window,name:galaxy_goods_receipt_diff_handle.action_galaxy_stock_diff_order
msgid "Diff Handle Orders"
msgstr "採購差異"

#. module: galaxy_goods_receipt_diff_handle
#: code:addons/galaxy_goods_receipt_diff_handle/models/purchase_order.py:0
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_diff_handle.purchase_order_form_inherit_packing_list
#, python-format
msgid "Diff Order"
msgstr "採購差異"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_purchase_order__diff_order_count
msgid "Diff Order Count"
msgstr "採購差異數量"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_purchase_order__diff_order_ids
msgid "Diff Orders"
msgstr "採購差異"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order__line_ids
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_diff_handle.view_galaxy_stock_diff_order_form
msgid "Difference Lines"
msgstr "採購差異行"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_count_order__display_name
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order__display_name
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order_line__display_name
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_purchase_order__display_name
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_purchase_order_line__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields.selection,name:galaxy_goods_receipt_diff_handle.selection__galaxy_stock_diff_order__state__done
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_diff_handle.view_galaxy_stock_diff_order_form
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_diff_handle.view_galaxy_stock_diff_order_search
msgid "Done"
msgstr "完成"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields.selection,name:galaxy_goods_receipt_diff_handle.selection__galaxy_stock_diff_order__state__draft
msgid "Draft"
msgstr "草稿"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order__message_follower_ids
msgid "Followers"
msgstr "關注者"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order__message_channel_ids
msgid "Followers (Channels)"
msgstr "關注者 (通道)"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order__message_partner_ids
msgid "Followers (Partners)"
msgstr "關注者 (夥伴)"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order_line__glot_number
msgid "GLot"
msgstr ""

#. module: galaxy_goods_receipt_diff_handle
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_diff_handle.view_galaxy_stock_diff_order_search
msgid "Group By"
msgstr "分組"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_count_order__is_has_diff_order
msgid "Has Difference Order"
msgstr "是否已生成採購差異"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_count_order__id
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order__id
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order_line__id
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_purchase_order__id
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_purchase_order_line__id
msgid "ID"
msgstr "編號"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,help:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order__message_needaction
#: model:ir.model.fields,help:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order__message_unread
msgid "If checked, new messages require your attention."
msgstr "如果勾選，新訊息需要您的注意"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,help:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order__message_has_error
#: model:ir.model.fields,help:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "如果勾選，某些訊息有傳送錯誤"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order__message_is_follower
msgid "Is Follower"
msgstr "是否關注"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_count_order____last_update
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order____last_update
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order_line____last_update
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_purchase_order____last_update
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_purchase_order_line____last_update
msgid "Last Modified on"
msgstr "修改時間"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order__write_uid
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order_line__write_uid
msgid "Last Updated by"
msgstr "最後修改者"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order__write_date
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order_line__write_date
msgid "Last Updated on"
msgstr "最後修改於"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order_line__won_order_line_id
msgid "Lot number"
msgstr "Lot No"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order__message_main_attachment_id
msgid "Main Attachment"
msgstr "主要附件"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order__message_has_error
msgid "Message Delivery error"
msgstr "訊息傳送錯誤"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order__message_ids
msgid "Messages"
msgstr "訊息"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order_line__purchase_missing_qty
msgid "Missing Qty"
msgstr "未到數量"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order_line__model_number
msgid "Model Number"
msgstr "機型"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order__name
msgid "Name"
msgstr "名稱"

#. module: galaxy_goods_receipt_diff_handle
#: code:addons/galaxy_goods_receipt_diff_handle/models/diff_order.py:0
#, python-format
msgid "New"
msgstr ""

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields.selection,name:galaxy_goods_receipt_diff_handle.selection__galaxy_stock_diff_order__process_method__no_action
msgid "No Action"
msgstr "不處理"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order__note
msgid "Notes"
msgstr "備註"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order__message_needaction_counter
msgid "Number of Actions"
msgstr "需要您注意的訊息數量"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order__message_has_error_counter
msgid "Number of errors"
msgstr "錯誤訊息數量"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,help:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "需要您注意的訊息數量"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,help:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "傳送錯誤的訊息數量"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,help:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order__message_unread_counter
msgid "Number of unread messages"
msgstr "未讀訊息計數器"

#. module: galaxy_goods_receipt_diff_handle
#: code:addons/galaxy_goods_receipt_diff_handle/models/diff_order.py:0
#, python-format
msgid "Only draft Diff Order can be deleted"
msgstr "只有處理中的差異處理可以刪除"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order_line__order_id
msgid "Order"
msgstr "訂單"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields.selection,name:galaxy_goods_receipt_diff_handle.selection__galaxy_stock_diff_order__process_method__pay_money
msgid "Pay Money"
msgstr "補錢"

#. module: galaxy_goods_receipt_diff_handle
#: code:addons/galaxy_goods_receipt_diff_handle/models/diff_order.py:0
#, python-format
msgid "Picking Unit"
msgstr "點貨單位"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order_line__price_unit
msgid "Price Unit"
msgstr "單價"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order__process_method
msgid "Process Method"
msgstr "處理方式"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order_line__process_qty
msgid "Process Qty"
msgstr "處理數量"

#. module: galaxy_goods_receipt_diff_handle
#: code:addons/galaxy_goods_receipt_diff_handle/models/diff_order.py:0
#, python-format
msgid "Process lines are required before processing."
msgstr "處理行是處理前必須的"

#. module: galaxy_goods_receipt_diff_handle
#: code:addons/galaxy_goods_receipt_diff_handle/models/diff_order.py:0
#, python-format
msgid "Process method is required before Done."
msgstr "完成前必須填寫处理方式"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields.selection,name:galaxy_goods_receipt_diff_handle.selection__galaxy_stock_diff_order__state__processing
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_diff_handle.view_galaxy_stock_diff_order_search
msgid "Processing"
msgstr "處理中"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order_line__product_tmpl_id
msgid "Product Template"
msgstr "SPU"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model,name:galaxy_goods_receipt_diff_handle.model_purchase_order
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_diff_handle.view_galaxy_stock_diff_order_form
msgid "Purchase Order"
msgstr "採購訂單"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model,name:galaxy_goods_receipt_diff_handle.model_purchase_order_line
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order_line__purchase_order_line_id
msgid "Purchase Order Line"
msgstr "採購訂單項目"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order__purchase_ids
msgid "Purchase Orders"
msgstr "採購訂單"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order__purchase_qty
msgid "Purchase Qty"
msgstr "採購數量"

#. module: galaxy_goods_receipt_diff_handle
#: code:addons/galaxy_goods_receipt_diff_handle/models/diff_order.py:0
#, python-format
msgid "Purchase orders are required before processing."
msgstr "採購訂單是處理前必須的"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order_line__sku_id
msgid "SKU"
msgstr ""

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS 傳送錯誤"

#. module: galaxy_goods_receipt_diff_handle
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_diff_handle.view_galaxy_stock_diff_order_search
msgid "Search All"
msgstr "搜尋全部"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order__state
msgid "Status"
msgstr "狀態"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model,name:galaxy_goods_receipt_diff_handle.model_galaxy_stock_diff_order
msgid "Stock Diff Handle Order"
msgstr "採購差異"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model,name:galaxy_goods_receipt_diff_handle.model_galaxy_stock_diff_order_line
msgid "Stock Diff Handle Order Line"
msgstr "採購差異行"

#. module: galaxy_goods_receipt_diff_handle
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_diff_handle.view_galaxy_stock_diff_order_form
msgid "Stock Difference Order"
msgstr "採購差異"

#. module: galaxy_goods_receipt_diff_handle
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_diff_handle.view_galaxy_stock_diff_order_search
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_diff_handle.view_galaxy_stock_diff_order_tree
msgid "Stock Difference Orders"
msgstr "採購差異"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order_line__price_subtotal
msgid "Subtotal"
msgstr "金額"

#. module: galaxy_goods_receipt_diff_handle
#: code:addons/galaxy_goods_receipt_diff_handle/models/diff_order.py:0
#, python-format
msgid ""
"The selected purchase orders have different currencies. Please select orders"
" with the same currency."
msgstr ""

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order__amount_total
msgid "Total Amount"
msgstr "金額"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order__total_qty
msgid "Total Quantity"
msgstr "數量"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order__message_unread
msgid "Unread Messages"
msgstr "未讀訊息"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order__message_unread_counter
msgid "Unread Messages Counter"
msgstr "未讀訊息計數器"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order__vendor_id
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_diff_handle.view_galaxy_stock_diff_order_search
msgid "Vendor"
msgstr "供應商"

#. module: galaxy_goods_receipt_diff_handle
#: code:addons/galaxy_goods_receipt_diff_handle/models/diff_order.py:0
#, python-format
msgid "Vendor is required before processing."
msgstr "供應商必填"

#. module: galaxy_goods_receipt_diff_handle
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_diff_handle.view_galaxy_stock_diff_order_search
msgid "Waiting"
msgstr "等待處理"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order__website_message_ids
msgid "Website Messages"
msgstr "網站訊息"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,help:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order__website_message_ids
msgid "Website communication history"
msgstr "網站溝通歷史"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model.fields,field_description:galaxy_goods_receipt_diff_handle.field_galaxy_stock_diff_order_line__won_order_lot_line_id
msgid "Won Order Lot Line"
msgstr "中標單Lot行"

#. module: galaxy_goods_receipt_diff_handle
#: model:ir.model,name:galaxy_goods_receipt_diff_handle.model_galaxy_stock_count_order
msgid "galaxy.stock.count.order"
msgstr "點貨單"
