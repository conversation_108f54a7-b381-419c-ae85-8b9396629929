# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError


class GalaxyStockCountOrder(models.Model):
    _inherit = 'galaxy.stock.count.order'
    
    is_has_diff_order = fields.Boolean(string='Has Difference Order', default=False)
    
    def check_is_need_diff_handle(self):
        for line in self.line_ids:
            # 没有glot number的是完全多到，自留
            if not line.glot_number:
                continue
            if abs(line.purchase_qty - line.count_qty) > 0:
                return True
        return False
    
    # def action_finished(self):
    #     res = super().action_finished()
    #     # 检查是否要差异处理 【应到数量与实到数量不一致】
    #     need_diff_handle = self.check_is_need_diff_handle()
    #     if need_diff_handle:
    #         self.generate_diff_order()
    #     return res
    
    # def action_generate_diff_order(self):
    #     need_diff_handle = self.check_is_need_diff_handle()
    #     if not need_diff_handle:
    #         raise UserError(_('No need to generate diff order'))
    #     diff_order = self.env['galaxy.stock.diff.order'].search(
    #         [('count_id', '=', self.id)]
    #     )
    #     if not diff_order:
    #         diff_order = self.generate_diff_order()
    #     action = self.sudo().env.ref('galaxy_goods_receipt_diff_handle.action_galaxy_stock_diff_order').read()[0]
    #     if len(diff_order) == 1:
    #         action['views'] = [(False, 'form')]
    #         action['res_id'] = diff_order.id
    #     elif len(diff_order) > 1:
    #         action['domain'] = [('id', 'in', diff_order.ids)]
    #     return action
        
    def generate_diff_order(self):
        """
        # 生成差异单
        """
        vals = {}
        if len(self.purchase_ids) == 1:
            default_purchase_order = self.purchase_ids[0]
        else:
            default_purchase_order = None
        for line in self.line_ids:
            if not line.purchase_order_id:
                if not default_purchase_order:
                    raise UserError(_('Count has multi Purchase Order, please alloc extra units to one purchase order.'))
                else:
                    purchase_order_id = default_purchase_order
            else:
                purchase_order_id = line.purchase_order_id
            
            if purchase_order_id not in vals:
                vals[purchase_order_id] = {
                    'name': f"{self.name} - {purchase_order_id.name}",
                    'vendor_id': self.vendor_id.id,
                    'count_id': self.id,
                    'purchase_order_id': purchase_order_id.id,
                    'purchase_qty': 0,
                    'packing_qty': 0,
                    'count_qty': 0,
                    'line_ids': []
                }
            line_vals = {
                'won_order_line_id': line.won_order_line_id.id,
                'won_order_lot_line_id': line.won_order_lot_line_id.id,
                'glot_number': line.glot_number,
                'sku_id': line.sku_id.id,
                'product_tmpl_id': line.product_tmpl_id.id,
                'purchase_qty': line.purchase_qty,
                'packing_qty': line.packing_qty,
                'count_qty': line.count_qty,
                'diff_qty': abs(line.purchase_qty - line.count_qty),
                'model_number': line.model_number,
                'purchase_order_line_id': line.purchase_order_line_id.id,
            }
            vals[purchase_order_id]['line_ids'].append((0, 0, line_vals))
            vals[purchase_order_id]['purchase_qty'] += line.purchase_qty
            vals[purchase_order_id]['packing_qty'] += line.packing_qty
            vals[purchase_order_id]['count_qty'] += line.count_qty
        diff_order = self.env['galaxy.stock.diff.order']
        for order_vals in vals.values():
            diff_qty = order_vals['purchase_qty'] - order_vals['count_qty']
            if diff_qty == 0:
                continue
            new_diff_order = diff_order.create(order_vals)
            diff_order += new_diff_order
        self.is_has_diff_order = True
        return diff_order
    
    def action_view_diff_order(self):
        diff_order = self.env['galaxy.stock.diff.order'].search(
            [('count_id', '=', self.id)]
        )
        action = self.sudo().env.ref('galaxy_goods_receipt_diff_handle.action_galaxy_stock_diff_order').read()[0]
        if len(diff_order) == 1:
            action['views'] = [(False, 'form')]
            action['res_id'] = diff_order.id
        elif len(diff_order) > 1:
            action['domain'] = [('id', 'in', diff_order.ids)]
        return action
