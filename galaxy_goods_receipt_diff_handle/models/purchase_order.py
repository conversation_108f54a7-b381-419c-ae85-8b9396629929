# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
from odoo.osv import expression


class GalaxyPurchaseOrder(models.Model):
    _inherit = 'purchase.order'
    
    diff_order_ids = fields.Many2many('galaxy.stock.diff.order', 'galaxy_stock_diff_order_purchase_order_rel', 'purchase_order_id', 'galaxy_stock_diff_order_id', string='Diff Orders')
    diff_order_count = fields.Integer(string='Diff Order Count', compute='_compute_diff_order_count')

    @api.model
    def name_search(self, name='', args=None, operator='ilike', limit=100):
        if args is None:
            args = []
        search_from_diff_handle = self._context.get('search_from_diff_handle')
        if search_from_diff_handle:
            domain = ['|',
                      ('name', 'ilike', name),
                      ('g_number', 'ilike', name)]
            new_args = expression.AND([domain, args])
            name = ''
            return super().name_search(name=name, args=new_args, operator=operator, limit=limit)
        return super().name_search(name=name, args=args, operator=operator, limit=limit)
    
    def _compute_diff_order_count(self):
        for po in self:
            po.diff_order_count = len(po.diff_order_ids)

    # diff_order_id = fields.Many2one('galaxy.stock.diff.order', 'Diff Order', index=True)
    # backorder_id = fields.Many2one('purchase.order', 'Backorder', index=True)
    # is_backorder = fields.Boolean('Is Backorder', default=False)
    
    # def action_view_backorder(self):
    #     self.ensure_one()
    #     action = {
    #         'name': _('Purchase Order'),
    #         'type': 'ir.actions.act_window',
    #         'res_model': 'purchase.order',
    #         'view_mode': 'form',
    #         'target': 'current',
    #         'res_id': self.backorder_id.id,
    #     }
    #     return action
    
    # def action_open_original_po(self):
    #     self.ensure_one()
    #     action = self.sudo().env.ref('purchase.purchase_order_form').read()[0]
    #     original_po = self.env['purchase.order'].search(
    #         [('backorder_id', '=', self.id)]
    #     )
    #     if len(original_po) == 1:
    #         action['views'] = [(False, 'form')]
    #         action['res_id'] = original_po.id
    #     elif len(original_po) > 1:
    #         action['domain'] = [('id', 'in', original_po.ids)]
    #     return action
    
    def action_view_diff_order(self):
        self.ensure_one()
        diff_order = self.diff_order_ids
        action = self.sudo().env.ref('galaxy_goods_receipt_diff_handle.action_galaxy_stock_diff_order').read()[0]
        if len(diff_order) == 1:
            action['views'] = [(False, 'form')]
            action['res_id'] = diff_order.id
        else:
            action['domain'] = [('id', 'in', diff_order.ids)]
        return action
    
    def action_create_diff_order(self):
        self.ensure_one()
        diff_order = self.env['galaxy.stock.diff.order'].create({
            'purchase_ids': self.ids,
            'vendor_id': self.partner_id.id,
            'currency_id': self.currency_id.id,
        })
        return {
            'name': _('Diff Order'),
            'type': 'ir.actions.act_window',
            'res_model': 'galaxy.stock.diff.order',
            'view_mode': 'form',
            'target': 'current',
            'res_id': diff_order.id,
        }


class GalaxyPurchaseOrderLine(models.Model):
    _inherit = 'purchase.order.line'
    
    def name_get(self):
        if self.env.context.get('from_diff_order'):
            return [(record.id, f"{record.glot_number} - {record.name}") for record in self]
        return super(GalaxyPurchaseOrderLine, self).name_get()
