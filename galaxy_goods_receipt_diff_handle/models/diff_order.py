# -*- coding: utf-8 -*-

import logging
from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError

_logger = logging.getLogger(__name__)


class GalaxyStockDiffOrder(models.Model):
    _name = 'galaxy.stock.diff.order'
    _description = 'Stock Diff Handle Order'
    _inherit = ['mail.thread']
    _order = 'create_date desc'

    name = fields.Char(string='Name', required=True, copy=False, readonly=True,
                       default=lambda self: _('New'))
    vendor_id = fields.Many2one('res.partner', string='Vendor')
    purchase_ids = fields.Many2many('purchase.order', 'galaxy_stock_diff_order_purchase_order_rel', 'galaxy_stock_diff_order_id', 'purchase_order_id', string='Purchase Orders')
    currency_id = fields.Many2one('res.currency', string='Currency')

    process_method = fields.Selection([
        ('pay_money', 'Pay Money'),
        ('no_action', 'No Action'),
    ], string='Process Method')
    
    # invoice_ids = fields.Many2many('galaxy.vendor.invoice', string='Invoices',)

    state = fields.Selection([
        ('draft', 'Draft'),
        ('processing', 'Processing'),
        ('done', 'Done'),
        ('cancel', 'Cancel'),
    ], string='Status', default='draft', tracking=True)

    line_ids = fields.One2many('galaxy.stock.diff.order.line', 'order_id', string='Difference Lines')

    purchase_qty = fields.Integer()

    note = fields.Text('Notes')
    
    # 以下都已废弃
    # count_id = fields.Many2one('galaxy.stock.count.order', string='Count Order', index=True)
    # purchase_order_id = fields.Many2one('purchase.order', string='Purchase Order', index=True)
    # packing_list_id = fields.Many2one('packing.list', string='Packing List')
    # packing_qty = fields.Integer()
    # count_qty = fields.Integer()
    
    # 新增计算字段：总金额和总数量
    amount_total = fields.Monetary(string='Total Amount', compute='_compute_totals', store=True, currency_field='currency_id')
    total_qty = fields.Integer(string='Total Quantity', compute='_compute_totals', store=True)
    
    @api.depends('line_ids.price_subtotal', 'line_ids.process_qty')
    def _compute_totals(self):
        for order in self:
            order.amount_total = sum(order.line_ids.mapped('price_subtotal'))
            order.total_qty = sum(order.line_ids.mapped('process_qty'))
    
    @api.onchange('purchase_ids')
    def onchange_purchase_ids(self):
        if not self.purchase_ids:
            self.currency_id = False
            self.line_ids = [(5, 0, 0)]  # Clear all lines when no purchase orders selected
            return
            
        # 获取所有采购订单的币种
        currencies = self.purchase_ids.mapped('currency_id')
        
        # 检查是否所有采购订单使用相同币种
        if len(currencies) == 1:
            self.currency_id = currencies[0]
        else:
            self.currency_id = False
            # 当币种不一致时显示警告
            return {
                'warning': {
                    'title': _('Currency Inconsistency'),
                    'message': _('The selected purchase orders have different currencies. Please select orders with the same currency.')
                }
            }
            
        # 移除不在当前采购订单中的行
        if self.line_ids:
            purchase_order_lines = self.env['purchase.order.line'].search([
                ('order_id', 'in', self.purchase_ids.ids)
            ])
            valid_po_line_ids = purchase_order_lines.ids
            lines_to_remove = self.line_ids.filtered(
                lambda line: line.purchase_order_line_id.id not in valid_po_line_ids
            )
            if lines_to_remove:
                self.line_ids = [(3, line.id) for line in lines_to_remove]

    @api.model_create_multi
    def create(self, vals_list):
        for vals in vals_list:
            vals['name'] = self.env['ir.sequence'].with_context(galaxy_base_32=True).next_by_code('galaxy.stock.diff.order')
        return super().create(vals_list)
    
    def unlink(self):
        if self.state != 'draft':
            raise UserError(_('Only draft Diff Order can be deleted'))
        return super().unlink()

    def action_open_purchase_order(self):
        purchase_list = self.purchase_ids.ids

        # 根据purchase_list的长度决定视图类型
        if len(purchase_list) > 1:
            action = self.sudo().env.ref('galaxy_goods_receipt.galaxy_purchase_form_action').read()[0]
            action['domain'] = [('id', 'in', purchase_list)]
            return action
        else:
            return {
                'type': 'ir.actions.act_window',
                'res_model': 'purchase.order',
                'res_id': purchase_list[0],
                'view_mode': 'form',
                'target': 'current',
            }
            
    def action_start_process(self):
        # 验证必填字段
        if not self.vendor_id:
            raise UserError(_('Vendor is required before processing.'))
        if not self.purchase_ids:
            raise UserError(_('Purchase orders are required before processing.'))
        if not self.line_ids:
            raise UserError(_('Process lines are required before processing.'))
        
        self.state = 'processing'
        self.purchase_ids.write({'state': 'processing'})
        
    def action_done(self):
        if not self.process_method:
            raise UserError(_('Process method is required before Done.'))
        # 1.校验采购订单状态是否为"purchase"或"执行中"状态，不满足条件给出相应提示并终止操作
        for order in self:
            if order.purchase_ids.filtered(lambda po: po.state not in ['purchase', 'processing']):
                invalid_pos = order.purchase_ids.filtered(lambda po: po.state not in ['purchase', 'processing'])
                raise UserError(_('Cannot process this difference order. The following purchase orders are not in valid states (purchase or processing):\n%s') % 
                               '\n'.join(['- %s: %s' % (po.name, dict(po._fields['state'].selection).get(po.state)) for po in invalid_pos]))
        
        # 2.校验每行的purchase_order_line_id，比较process_qty和po line的missing qty，要process_qty小于等于missing qty
        invalid_lines = []
        
        # 按purchase_order_line_id分组汇总process_qty
        po_line_qty_map = {}
        for line in self.line_ids:
            if line.process_qty <= 0:
                invalid_lines.append(
                    _('- Line for Glot [%s] %s: Process quantity is less than or equal to 0') % 
                    (line.glot_number or '', line.sku_id.name)
                )
            po_line_id = line.purchase_order_line_id.id
            if po_line_id not in po_line_qty_map:
                po_line_qty_map[po_line_id] = {
                    'po_line': line.purchase_order_line_id,
                    'total_process_qty': 0,
                    'glot_number': line.glot_number,
                    'sku_name': line.sku_id.name
                }
            po_line_qty_map[po_line_id]['total_process_qty'] += line.process_qty
        
        # 比较汇总后的数量与missing_qty
        for po_line_id, data in po_line_qty_map.items():
            po_line = data['po_line']
            total_process_qty = data['total_process_qty']
            missing_qty = po_line.missing_qty
            
            if total_process_qty > missing_qty:
                invalid_lines.append(
                    _('- Line for Glot [%s] %s: Total process quantity (%s) exceeds missing quantity (%s)') % 
                    (data['glot_number'] or '', data['sku_name'], total_process_qty, missing_qty)
                )
        
        if invalid_lines:
            raise UserError(_('Cannot process this difference order due to the following issues:\n%s') % '\n'.join(invalid_lines))
        
        for line in self.line_ids:
            po_line = line.purchase_order_line_id
            po_line.handle_qty += line.process_qty
            
        for po in self.purchase_ids:
            po.check_and_complete_if_fully_received(auto_finish=True)
            po.create_purchase_order_lot()
        
        self.state = 'done'
        
    def action_cancel(self):
        # 如果已经完成了，此时取消，需要把数量加回去，处理中的不需要
        if self.state == 'done':
            for line in self.line_ids:
                po_line = line.purchase_order_line_id
                po_line.handle_qty -= line.process_qty
        self.state = 'cancel'

        for po in self.purchase_ids:
            is_finished = po.check_and_complete_if_fully_received(auto_finish=True)
            if not is_finished:
                po.write({'state': 'processing'})
                po.create_purchase_order_lot()

    # def action_open_vpi(self):
    #     invoices = self.count_id.invoice_ids
    #     action = self.sudo().env.ref('vendor_invoice.galaxy_vendor_invoice_act_window').read()[0]
    #     action['domain'] = [('id', 'in', invoices.ids)]
    #     return action


class GalaxyStockDiffOrderLine(models.Model):
    _name = 'galaxy.stock.diff.order.line'
    _description = 'Stock Diff Handle Order Line'

    order_id = fields.Many2one('galaxy.stock.diff.order', index=True, required=True, ondelete='cascade')
    currency_id = fields.Many2one('res.currency', string='Currency', related='order_id.currency_id')
    purchase_order_line_id = fields.Many2one('purchase.order.line', string='Purchase Order Line', index=True, required=True)
    price_unit = fields.Float()

    won_order_line_id = fields.Many2one('bid.won.order.line', string='Lot number', index=True)
    won_order_lot_line_id = fields.Many2one("bid.won.order.line.detail", index=True)
    glot_number = fields.Char(string='GLot', index=True)
    sku_id = fields.Many2one('product.product', string='SKU', index=True)
    product_tmpl_id = fields.Many2one('product.template', 'Product Template', related='sku_id.product_tmpl_id',
                                      store=True, index=True)

    model_number = fields.Char(related='sku_id.spu_model_name', store=True)

    process_qty = fields.Integer()
    purchase_missing_qty = fields.Integer(string='Missing Qty')
    purchase_expected_qty = fields.Integer(string='Expected Qty')
    
    price_subtotal = fields.Monetary(compute='_compute_amount', string='Subtotal', store=True)
    
    @api.depends('price_unit', 'process_qty')
    def _compute_amount(self):
        for line in self:
            line.price_subtotal = line.price_unit * line.process_qty
    
    @api.onchange('purchase_order_line_id')
    def onchange_purchase_order_line_id(self):
        if not self.purchase_order_line_id:
            self.won_order_line_id = False
            self.won_order_lot_line_id = False
            self.glot_number = False
            self.sku_id = False
            self.product_tmpl_id = False
            self.model_number = False
            self.price_unit = 0
            self.purchase_missing_qty = 0
            self.purchase_expected_qty = 0
        else:
            self.won_order_line_id = self.purchase_order_line_id.won_order_line_id
            self.won_order_lot_line_id = self.purchase_order_line_id.won_order_line_detail_id
            self.glot_number = self.purchase_order_line_id.glot_number
            self.sku_id = self.purchase_order_line_id.product_id
            self.product_tmpl_id = self.purchase_order_line_id.product_id.product_tmpl_id
            self.model_number = self.purchase_order_line_id.model_number
            self.price_unit = self.purchase_order_line_id.price_unit
            self.purchase_missing_qty = self.purchase_order_line_id.missing_qty
            self.purchase_expected_qty = self.purchase_order_line_id.expected_qty
            
    def action_show_details(self):
        self.ensure_one()
        view = self.sudo().env.ref('galaxy_goods_receipt.view_galaxy_picking_unit_tree')
        return {
            'name': _('Picking Unit'),
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'res_model': 'galaxy.stock.picking.unit',
            'views': [(view.id, 'tree')],
            'domain': [
                ('count_id', '=', self.order_id.count_id.id),
                ('won_order_lot_line_id', '=', self.won_order_lot_line_id.id),
            ],
            'target': 'new',
            'context': {'create': False}
        }
