#!/usr/bin/env python
# coding=utf-8

import logging
# import numpy as np
import pandas as pd

from .base_won_parse import BaseWonParse

_logger = logging.getLogger(__name__)


class ShineWonParse(BaseWonParse):

    def validate_file(self):
        pass

    def supported_parser(self, file_type=''):
        _logger.info(f"---- in supported_parser haah:{file_type}")
        parser_list = []
        if 'pdf' in file_type.lower():
            parser_list = [self.parse_pdf_won]
        elif 'rtf' in file_type.lower() or 'msword' in file_type.lower():
            parser_list = [self.parse_rtf_won]
        elif 'xlsx' in file_type.lower():
            parser_list = [self.parse_xlsx_won]
        return parser_list

    def parse_xlsx_won(self, file_name, file_content):
        _logger.info('in shine parse_xlsx_won:')
        df = pd.read_excel(file_content, header=0)
        # df = df.loc[:, ~df.columns.str.contains('Unnamed')]
        # df.columns = df.columns.map(lambda x: x.lower().strip())
        excel_list_tmp = []
        for i in df.index:
            data = df.loc[i].values
            excel_list_tmp.append(data.tolist())
        # 确定数据起始行
        start_row = 0
        for row in excel_list_tmp:
            start_row += 1
            if 'Quantity' in row and 'SKU' in row:
                break
        _logger.info(f"start_row: {start_row}")
        df = pd.read_excel(file_content, header=start_row)
        df = df.where(df.notnull(), '')
        df = df.loc[:, ~df.columns.str.contains('Unnamed')]
        df.columns = df.columns.map(lambda x: x.lower().strip())
        excel_list, new_excel_list = [], []
        for i in df.index:
            data = df.loc[i].values
            excel_list.append(data.tolist())
        for row in excel_list:
            tmp_row = row[0:len(list(df))]
            tmp_dict = dict(zip(list(df), tmp_row))
            new_excel_list.append(tmp_dict)
        bid_result_lines = []
        for row in new_excel_list:
            if not row.get('sku', '') or not row.get('quantity', '') or not row.get('disp', ''):
                continue
            bid_result_line = {}
            lot_id_tmp = '.'.join([row.get('sku', ''), row.get('disp', ''), str(int(row.get('quantity', '')))])
            bid_result_line['lot_id'] = lot_id_tmp
            des_tmp = '.'.join([str(i) for i in [row.get('sku', ''), row.get('disp', ''), row.get('description', '')]])
            bid_result_line['reference'] = des_tmp
            bid_result_line['qty'] = row.get('quantity', None)
            bid_result_line['unit_price'] = row.get('unit price', '')
            bid_result_line['sub_total'] = row.get('amount', '')
            bid_result_line['item_number'] = bid_result_line['lot_id']
            bid_result_lines.append(bid_result_line)
        _logger.info(f"bid_result_lines: {bid_result_lines}")
        return bid_result_lines, ''

    def parse_pdf_won(self, file_name, file_content):
        pass

    def parse_rtf_won(self, file_name, file_content):
        pass
