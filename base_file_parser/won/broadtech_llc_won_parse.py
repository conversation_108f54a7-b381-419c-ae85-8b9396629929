# coding=utf-8

import io
import re
import logging
import pdfplumber

from odoo.addons.base_galaxy.models.public_func import parse_float
from .base_won_parse import BaseWonParse

_logger = logging.getLogger(__name__)

# pylint: disable=chained-comparison,too-many-nested-blocks,too-many-branches,too-many-statements

class BroadTechLLCWonParse(BaseWonParse):
    """
    BROADTECH LLC是Hyla的另外一家公司
    : BROADTECH LLC 日单的中标产品行列名包含Item #、Description、Qrd Qty、Price、Ext Price
    """

    def __init__(self):
        """
        :
        """
        self.keys = ['Item #', 'Description', 'Ext Price', 'Payment Method']
    
    def validate_file(self, **kwargs):
        """
        # 判断是不是日单
        """
        pages = kwargs.get('pages')
        text_result = ''
        for page in pages:
            text = page.extract_text()
            if text:
                text_result += text.lower()
        for key in self.keys:
            if key.lower() not in text_result:
                return False, f'key {key} not found in file'
        return True, 'validate success'
    
    def supported_parser(self, file_type=''):
        _logger.info("---- in Hyla supported_parser:%s", file_type)
        parser_list = []
        if 'pdf' in file_type.lower():
            parser_list = [self.parse_pdf_won]
        elif 'rtf' in file_type.lower() or 'msword' in file_type.lower():
            parser_list = [self.parse_rtf_won]
        elif 'xlsx' in file_type.lower():
            parser_list = [self.parse_xlsx_won]
        return parser_list

    def parse_xlsx_won(self, file_name, file_content):
        bid_result_lines = []
        return bid_result_lines, ''

    def is_number(self, string):
        """
        :
        """
        try:
            if string == 'NaN' or string is None:
                return False
            float(string.replace(',', ''))
            return True
        except ValueError:
            return False

    def find_key(self, target, array):
        if len(array) == 0:
            return -1
        for index, item in enumerate(array):
            if str(item).find(target) != -1:
                return index
        return -1

    def parse_pdf_won(self, file_name, file_content, vpi_match_won_order_mode=None, options=None):
        """
        : Hyla(日单)
        : return 解析成功: return required_data, '' ; 解析失败: return {}, '解析失败的原因'
        """
        _logger.info('--in Broad Tech LLC parse_pdf_won2, file_name: %s', file_name)
        if options and 'warehouse' in options:
            store = options.get('warehouse').strip()
            _logger.info("store: %s", store)
        else:
            return {}, 'Hyla(日单)需要上传时将仓库值填写进去.'
        pdf = pdfplumber.PDF(io.BytesIO(file_content))
        success, message = self.validate_file(pages=pdf.pages)
        if not success:
            return {}, message

        rfq = options.get('rfq', None)
        rfq_line_map = {}
        if rfq and vpi_match_won_order_mode == 'item':
            for lot in rfq.vendor_lot_ids:
                for line in lot.vendor_lot_line_ids:
                    rfq_line_map[(line.rfq_item_number, line.warehouse)] = line


        line_item = []
        start_row_str = 'Item #'
        end_row_str = 'All Sales are'
        start_row_page = None
        end_row_page = None
        start_row_line_index = None
        end_row_line_index = None
        checkout_fees = 0
        pdf_text = ''
        for page_index, page in enumerate(pdf.pages):
            pdf_text += page.extract_text(layout=True)
            table = page.extract_table({"vertical_strategy": "text", "horizontal_strategy": "text", })
            if checkout_fees == 0:
                page_text = page.extract_text()
                # checkout_fees
                text_list = page_text.split('\n')
                item_index = self.find_key('Fees:', text_list)
                if item_index != -1:
                    checkout_fees = parse_float(text_list[item_index].split('Fees:')[1].strip())
            for line_index, item in enumerate(table):
                item_row = ''.join(item)
                if start_row_str in item_row:
                    start_row_page = page_index
                    start_row_line_index = line_index
                if end_row_str in item_row:
                    end_row_page = page_index
                    end_row_line_index = line_index
        _logger.info("start_row_page: %s", start_row_page)
        _logger.info("start_row_line_index: %s", start_row_line_index)
        _logger.info("end_row_page: %s", end_row_page)
        _logger.info("end_row_line_index: %s", end_row_line_index)

        text_result_lines = [line.strip()
                             for line in pdf_text.split('\n') if line.strip()]
        items_start_num = 0
        items_end_num = 0
        for line_num, line in enumerate(text_result_lines):
            line_lower = line.lower()
            if 'Ext Price'.lower() in line_lower and 'Canc'.lower() in line_lower:
                items_start_num = line_num
            if items_start_num and 'Qty'.lower() in line_lower:
                items_start_num = line_num
            if ('All Sales are'.lower() in line_lower or 'Subtotal'.lower() in line_lower) and not items_end_num:
                items_end_num = line_num

        bid_result_lines = []
        item_lines = text_result_lines[items_start_num + 1: items_end_num]
        for item_str in item_lines:
            item_str_lower = item_str.lower()
            # 碰到 Order # ..可能是页尾的行，需要移除
            if 'Order #'.lower() in item_str_lower:
                continue
            # 如果整行不包括价格，说明是lot描述折行，需要向上合并
            if '$' not in item_str:
                bid_result_lines[-1]['reference'] += ' ' + item_str
                continue

            parts = item_str.split()
            item_number = parts[0]
            lot_description = ' '.join(parts[1:-6])
            qty = parse_float(parts[-6])
            unit_price = parse_float(parts[-2])
            sub_total = parse_float(parts[-1])

            lot_id = f"{item_number}.{store}"
            if rfq_line_map.get((lot_id, store)):
                rfq_line = rfq_line_map[(lot_id, store)]
                lot_id = rfq_line.vendor_lot_id.bid_id
            else:
                lot_id = item_number

            bid_result_lines.append(
                {
                    'lot_id': lot_id,
                    # 'item_number': item_number,
                    'reference': lot_description,
                    'qty': qty,
                    'unit_price': unit_price,
                    'sub_total': sub_total,
                }
            )

        # 对lot id二次处理，满足格式要求, 因为处理的逻辑跟reference有关联
        for line_val in bid_result_lines:
            reference = line_val['reference']
            lot_id = line_val['lot_id']

            if 'UNLOCKED'.lower() in reference.lower():
                line_val['lot_id'] = '.'.join([lot_id, store, 'UNLOCKED'])
            elif 'LOCKED'.lower() in reference.lower():
                line_val['lot_id'] = '.'.join([lot_id, store, 'LOCKED'])
            else:
                line_val['lot_id'] = '.'.join([lot_id, store])


        # bid_result_lines = []
        # for line_list in line_item:
        #     # True则用新的解析方式来获取sub_total
        #     flag_for_sub_total = False
        #     line = line_list[0]
        #     number_list = line[2].split(' ')
        #     if '$' in number_list[-2]:
        #         unit_price_tmp = number_list[-2].replace('$', '').replace(',', '')
        #         if unit_price_tmp.isdigit():
        #             unit_price = float(unit_price_tmp)
        #         else:
        #             unit_price = float((number_list[-3]+number_list[-2]).replace('$', '').replace(',', ''))
        #     else:
        #         unit_price = float((number_list[-3]+number_list[-2]).replace('$', '').replace(',', ''))
        #         if not unit_price:
        #             try:
        #                 unit_price = float(number_list[-1].split('$')[-2].replace(',', ''))
        #                 flag_for_sub_total = True
        #             except ValueError as error:
        #                 raise ValueError('hyla type transfer error occured') from error
        #     if not flag_for_sub_total:
        #         sub_total = float(number_list[-1].replace('$', '').replace(',', ''))
        #     else:
        #         try:
        #             sub_total = float(number_list[-1].split('$')[-1].replace(',', ''))
        #         except ValueError as error:
        #             raise ValueError('hyla type transfer error occured') from error
        #     qty_tmp = int(sub_total / unit_price) if unit_price else 0
        #     vals = {
        #         'lot_id': '.'.join([line[0], store]),
        #         'reference': line[1],
        #         'qty': qty_tmp if str(qty_tmp) in line[2] else int(number_list[0]),
        #         'unit_price': unit_price,
        #         'sub_total': sub_total,
        #     }
        #     if 'UNLOCKED'.lower() in line[1].lower():
        #         vals['lot_id'] = '.'.join([line[0], store, 'UNLOCKED'])
        #     elif 'LOCKED'.lower() in line[1].lower():
        #         vals['lot_id'] = '.'.join([line[0], store, 'LOCKED'])
        #     bid_result_lines.append(vals)
        # # 数目不准确的解析补偿
        # text_all = ""
        # for page_index, page in enumerate(pdf.pages):
        #     text = page.extract_text()
        #     text_all += text + '\n'
        # won_result_line = []
        # text_list = text_all.split('\n')
        # for line in text_list:
        #     line_list = line.split(' ')
        #     if len(line_list) > 3:
        #         if '$' in line_list[-1] and "$" in line_list[-2]:
        #             # won_result_line.append(line)
        #             won_result_line += [line_list]
        # for line in bid_result_lines:
        #     line_item = line.get('lot_id', '').split('.')[0]
        #     for wrl in won_result_line:
        #         if line_item == wrl[0].strip():
        #             if str(line.get('qty')) != str(wrl[-6]):
        #                 _logger.info('--hyla invoice qty not match in two parsers: %s', line)
        #                 line['qty'] = wrl[-6]
        _logger.info('-- bid_result_lines: %s', bid_result_lines)
        if checkout_fees:
            bid_result_lines.append({
                'lot_id': 'CheckOut Fees',
                'reference': 'CheckOut Fees',
                'qty': 1,
                'line_type': 'service',
                'unit_price': checkout_fees,
                'sub_total': checkout_fees,
            })
        return bid_result_lines, ''

    def parse_rtf_won(self):
        """
        :
        """
        return
