#!/usr/bin/env python
# coding=utf-8

import io
import re
import copy
import time
import base64
import logging
import datetime
import numpy as np
import pandas as pd
from striprtf.striprtf import rtf_to_text

from .base_won_parse import BaseWonParse

_logger = logging.getLogger(__name__)


class EcycleWonParse(BaseWonParse):

    def validate_file(self):
        pass

    def supported_parser(self, file_type=''):
        _logger.info(f"---- in supported_parser haah:{file_type}")
        parser_list = []
        if 'pdf' in file_type.lower():
            parser_list = [self.parse_pdf_won]
        elif 'rtf' in file_type.lower() or 'msword' in file_type.lower():
            parser_list = [self.parse_rtf_won]
        elif 'xlsx' in file_type.lower():
            parser_list = [self.parse_xlsx_won]
        return parser_list

    def parse_xlsx_won(self, file_name, file_content):
        _logger.info('in ecycle parse_xlsx_won:')
        """
        return [{},{},...], error_msg
        """
        # file_content为未经base64加密的二进制流
        # xlsx_stream = io.BytesIO(file_content)
        df = pd.read_excel(file_content, header=0)
        df = df.where(df.notnull(), '')
        df = df.loc[:, ~df.columns.str.contains('Unnamed')]
        df.columns = df.columns.map(lambda x: x.lower().strip())
        # _logger.info(f'tag 1: {df}')
        excel_list, new_excel_list = [], []
        for i in df.index:
            data = df.loc[i].values
            excel_list.append(data.tolist())
        for row in excel_list:
            tmp_row = row[0:len(list(df))]
            tmp_dict = dict(zip(list(df), tmp_row))
            new_excel_list.append(tmp_dict)

        bid_result_lines = []
        for row in new_excel_list:
            bid_result_line = {}
            bid_result_line['lot_id'] = row.get('sku description', '')
            bid_result_line['reference'] = row.get('sku description', '')
            bid_result_line['qty'] = row.get('total', '')
            bid_result_line['unit_price'] = row.get('win', None)
            bid_result_line['sub_total'] = bid_result_line['qty'] * bid_result_line['unit_price']
            bid_result_line['item_number'] = bid_result_line['lot_id']
            bid_result_lines.append(bid_result_line)
        _logger.info(f"--- bid_result_lines: {bid_result_lines}")
        _logger.info(f"--- len bid_result_lines: {len(bid_result_lines)}")
        return bid_result_lines, ''

    def parse_pdf_won(self, file_name, file_content):
        pass

    def parse_rtf_won(self, file_name, file_content):
        # file_content为未经base64加密的二进制流
        _logger.info('-- in parse_rtf_won:')
        rtf_stream = io.BytesIO(file_content)
        rtf_content = rtf_stream.read()
        rtf_text = rtf_to_text(rtf_content.decode('utf-8'))
        rtf_data_list, error = self.extract_datas_to_list(rtf_text)
        bid_result_lines = []
        if rtf_data_list:
            for line in rtf_data_list:
                description = line.get('Description', '')
                if description.find('Bank Service Fee') != -1:
                    continue
                bid_result_line = {}
                bid_result_line['reference'] = line.get('Description', '')
                bid_result_line['lot_id'] = line.get('Description', '')
                bid_result_line['qty'] = ''.join(re.findall(r"\d+\.?\d*", line.get('Quantity Ordered', '')))
                bid_result_line['unit_price'] = ''.join(re.findall(r"\d+\.?\d*", line.get('Selling Price', '')))
                bid_result_line['sub_total'] = ''.join(re.findall(r"\d+\.?\d*", line.get('Extended Price', '')))
                bid_result_line['item_number'] = bid_result_line['lot_id']
                bid_result_lines.append(bid_result_line)
        _logger.info(f"bid_result_lines: {bid_result_lines}")
        return bid_result_lines, ''

    def extract_datas_to_list(self, rtf_text):
        msg = ''
        final_data = []
        whole_rtf_text_list = rtf_text.strip().split('\n')
        i = -1
        sindex = eindex = None
        for s in whole_rtf_text_list:
            i += 1
            if 'Line No.|' in s:
                sindex = i
            elif 'Tax Total|' or 'Order Total|' in s:
                eindex = i
        if sindex and eindex:
            rtf_text_list_temp = whole_rtf_text_list[sindex:eindex]
            rtf_text_list = [i for i in rtf_text_list_temp if i]  # 去除空行
            column_list = rtf_text_list[0].split('|')
            data_list = rtf_text_list[1:]
            for data in data_list:
                line_list = data.split('|')
                t = dict(zip(column_list, line_list))
                final_data.append(t)
        else:
            msg += 'can not locate start index or end index.'
        return final_data, msg
