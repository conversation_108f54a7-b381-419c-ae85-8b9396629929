import io
from collections import defaultdict

import pandas as pd
import numpy as np
import base64
from dataclasses import dataclass, field
from typing import Optional, List, Union
from io import BytesIO


def get_df_unique_or_mix(df, key):
    uniques = df[key].unique()
    if len(uniques) == 1:
        value = uniques[0]
    else:
        value = 'Mix'
    return value


@dataclass
class AlchemyLotItem:
    auction_id: str
    location: str
    sku: str
    category: str
    manufacturer: str
    model: str
    capacity: str
    colour: str
    network: str
    variant: str
    grade: str
    qty: int

    sheet_index: str
    excel_line: str


@dataclass
class AlchemyLot:
    location: str
    auction_id: str

    sheet_index: str
    excel_line: str

    qty: int

    items: Optional[List[AlchemyLotItem]] = field(default_factory=list)


"""
之前是csv格式，调整了xlsx格式

模版3解析，xlsx格式
https://conf.galaxy02.com/pages/viewpage.action?pageId=218202128
"""


def process_excel(file_bytes: Union[bytes, BytesIO]) -> List[AlchemyLot]:
    file_like = io.BytesIO(file_bytes)
    df = pd.read_excel(file_like)
    df.columns = df.columns.str.lower()
    
    # # Convert all string values to lowercase
    # for col in df.select_dtypes(include=['object']).columns:
    #     df[col] = df[col].str.lower()
    
    result = []

    df['excel_line'] = df.index + 2
    # Use lowercase field names
    grouped = df.groupby(['reference', 'location'])
    for name, group in grouped:
        lot = AlchemyLot(
            auction_id=name[0],
            location=name[1],
            sheet_index='0',
            excel_line=','.join(map(str, group['excel_line'].tolist())),
            qty=group['quantity'].sum(),
        )
        items = []
        for _, row in group.iterrows():
            item = AlchemyLotItem(
                auction_id=row['reference'],
                location=row['location'],
                sku=row['sku'],
                category=row['model'],
                manufacturer=row['manufacturer'],
                model=row['model'],
                capacity=row['capacity'],
                colour=row['colour'],
                network=row['network'],
                variant=row['variant'],
                grade=row['grade'],
                qty=row['quantity'],
                sheet_index='0',
                excel_line=row['excel_line'],
            )
            items.append(item)
        lot.items = items
        result.append(lot)

    return result


if __name__ == '__main__':
    with open('/Users/<USER>/Documents/galaxy/rfq/alchemy/auctions (1).csv', 'rb') as file:
        res = process_excel(file)
