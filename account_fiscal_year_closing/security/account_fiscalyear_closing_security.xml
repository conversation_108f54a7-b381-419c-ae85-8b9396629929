<?xml version="1.0" encoding="utf-8" ?>
<!-- Copyright 2016 <PERSON> <<EMAIL>>
     License AGPL-3.0 or later (http://www.gnu.org/licenses/agpl). -->
<odoo noupdate="1">

    <record id="fiscalyear_closing_multi_company_rule" model="ir.rule">
        <field name="name">Fiscal year closing multi-company</field>
        <field name="model_id" ref="model_account_fiscalyear_closing" />
        <field name="global" eval="True" />
        <field
            name="domain_force"
        >['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
    </record>

    <record id="fiscalyear_closing_template_multi_company_rule" model="ir.rule">
        <field name="name">Fiscal year closing template multi-company</field>
        <field name="model_id" ref="model_account_fiscalyear_closing_template" />
        <field name="global" eval="True" />
        <field
            name="domain_force"
        >['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
    </record>

</odoo>
