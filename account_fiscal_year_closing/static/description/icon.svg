<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with Inkscape (http://www.inkscape.org/) -->

<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:xlink="http://www.w3.org/1999/xlink"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   height="128"
   id="svg1"
   inkscape:export-filename="icon.png"
   inkscape:export-xdpi="90"
   inkscape:export-ydpi="90"
   inkscape:version="0.91 r13725"
   sodipodi:docname="icon.svg"
   sodipodi:version="0.32"
   width="128"
   version="1.1">
  <metadata
     id="metadata3">
    <rdf:RDF>
      <cc:Work
         rdf:about="">
        <dc:title>spain_provinces</dc:title>
        <dc:description />
        <dc:subject>
          <rdf:Bag>
            <rdf:li>spain</rdf:li>
            <rdf:li>geography</rdf:li>
          </rdf:Bag>
        </dc:subject>
        <dc:publisher>
          <cc:Agent
             rdf:about="http://www.openclipart.org">
            <dc:title>sherrera</dc:title>
          </cc:Agent>
        </dc:publisher>
        <dc:creator>
          <cc:Agent>
            <dc:title>sherrera</dc:title>
          </cc:Agent>
        </dc:creator>
        <dc:rights>
          <cc:Agent>
            <dc:title>sherrera</dc:title>
          </cc:Agent>
        </dc:rights>
        <dc:date />
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <cc:license
           rdf:resource="http://web.resource.org/cc/PublicDomain" />
        <dc:language>en</dc:language>
      </cc:Work>
      <cc:License
         rdf:about="http://web.resource.org/cc/PublicDomain">
        <cc:permits
           rdf:resource="http://web.resource.org/cc/Reproduction" />
        <cc:permits
           rdf:resource="http://web.resource.org/cc/Distribution" />
        <cc:permits
           rdf:resource="http://web.resource.org/cc/DerivativeWorks" />
      </cc:License>
    </rdf:RDF>
  </metadata>
  <defs
     id="defs3">
    <linearGradient
       id="linearGradient3032"
       inkscape:collect="always">
      <stop
         id="stop3034"
         offset="0"
         style="stop-color:#800000;stop-opacity:1;" />
      <stop
         id="stop3036"
         offset="1"
         style="stop-color:#ff0000;stop-opacity:1" />
    </linearGradient>
    <linearGradient
       inkscape:collect="always"
       id="linearGradient3026">
      <stop
         style="stop-color:#ececec;stop-opacity:1"
         offset="0"
         id="stop3028" />
      <stop
         style="stop-color:#ececec;stop-opacity:1"
         offset="1"
         id="stop3030" />
    </linearGradient>
    <linearGradient
       id="linearGradient3016"
       inkscape:collect="always">
      <stop
         id="stop3018"
         offset="0"
         style="stop-color:#cccccc;stop-opacity:1;" />
      <stop
         id="stop3020"
         offset="1"
         style="stop-color:#ececec;stop-opacity:1" />
    </linearGradient>
    <linearGradient
       gradientTransform="matrix(0.95285955,0,0,0.95285955,-92.360098,923.91674)"
       gradientUnits="userSpaceOnUse"
       y2="116.36218"
       x2="131"
       y1="131.36218"
       x1="131"
       id="linearGradient3022"
       xlink:href="#linearGradient3016"
       inkscape:collect="always" />
    <linearGradient
       gradientTransform="matrix(0.95285955,0,0,0.95285955,-92.360098,923.91674)"
       gradientUnits="userSpaceOnUse"
       y2="85.362183"
       x2="131"
       y1="130.36218"
       x1="131"
       id="linearGradient3024"
       xlink:href="#linearGradient3026"
       inkscape:collect="always" />
    <linearGradient
       gradientTransform="matrix(0.95285955,0,0,0.95285955,-92.360098,923.91674)"
       gradientUnits="userSpaceOnUse"
       y2="83.362183"
       x2="131"
       y1="113.36218"
       x1="131"
       id="linearGradient3038"
       xlink:href="#linearGradient3032"
       inkscape:collect="always" />
  </defs>
  <sodipodi:namedview
     bordercolor="#666666"
     borderopacity="1.0"
     id="base"
     inkscape:current-layer="svg1"
     inkscape:cx="102.45031"
     inkscape:cy="56.835562"
     inkscape:pageopacity="0.0"
     inkscape:pageshadow="2"
     inkscape:window-height="1176"
     inkscape:window-width="1855"
     inkscape:window-x="65"
     inkscape:window-y="24"
     inkscape:zoom="3.6235294"
     pagecolor="#ffffff"
     showgrid="false"
     inkscape:window-maximized="1"
     fit-margin-top="0"
     fit-margin-left="0"
     fit-margin-right="0"
     fit-margin-bottom="0" />
  <g
     id="layer1"
     inkscape:groupmode="layer"
     inkscape:label="Layer 1"
     transform="translate(-14.567953,-599.45664)" />
  <g
     id="layer1-9"
     inkscape:label="Layer 1"
     transform="translate(1400.7975,748.89372)" />
  <image
     y="-11.582519"
     x="-1.1578679"
     id="image3408"
     xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAKO0lEQVR42u2aW3AT1xnH/7u625Is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"
     style="image-rendering:optimizeQuality"
     preserveAspectRatio="none"
     height="118.09091"
     width="118.09091" />
  <g
     transform="matrix(1.2767369,0,0,1.2767369,68.881287,-1213.278)"
     id="layer1-6"
     inkscape:label="Layer 1">
    <path
       inkscape:connector-curvature="0"
       id="path2987"
       d="m 2.9258568,1007.1729 0,42.8787 28.5857862,0 14.292894,-14.2929 0,-28.5858 -42.8786802,0 z"
       style="fill:url(#linearGradient3024);fill-opacity:1;stroke:none" />
    <rect
       y="1007.1606"
       x="2.9258568"
       height="10.481455"
       width="42.878681"
       id="rect2994"
       style="fill:url(#linearGradient3038);fill-opacity:1;fill-rule:evenodd;stroke:none" />
    <path
       inkscape:connector-curvature="0"
       id="path3001"
       d="m 37.347908,1021.8528 -9.76681,5.9852 1.756835,3.0373 3.037239,-1.5484 0,13.4591 4.972736,-0.4169 0,-20.5163 z m -19.593174,0.7742 c -1.796569,0 -3.355686,0.4606 -4.674967,1.3996 -0.446234,0.3026 -0.884626,0.6983 -1.280405,1.1911 -0.39579,0.4928 -0.633176,0.8508 -0.714645,1.0719 l 3.483893,2.3524 c 0.174611,-0.4928 0.557241,-0.9027 1.13152,-1.2208 0.554869,-0.3027 1.141135,-0.4467 1.727058,-0.4467 0.636358,0 1.147462,0.1713 1.578174,0.5359 0.430702,0.3648 0.655081,0.8538 0.655091,1.4592 -10e-6,0.745 -0.317769,1.2482 -0.923083,1.4888 -0.620855,0.2522 -1.345952,0.3573 -2.203488,0.3573 -0.221177,-0.015 -0.420925,-0.014 -0.595537,-0.03 -0.178489,0 -0.309536,0 -0.387099,0 l 0,3.6328 c 0.0776,-0.015 0.199491,-0.044 0.327545,-0.059 0.128036,0 0.365413,0 0.714645,0 1.001103,0 1.792338,0.079 2.382149,0.2382 0.333691,0.1086 0.59831,0.3356 0.803975,0.6848 0.190096,0.3492 0.267982,0.7592 0.267992,1.2209 -10e-6,0.6208 -0.221864,1.1241 -0.714645,1.4889 -0.508331,0.3647 -1.090709,0.5748 -1.727058,0.6252 -0.523844,-0.035 -1.106222,-0.1931 -1.727058,-0.4764 -0.442355,-0.2095 -0.813351,-0.4754 -1.13152,-0.7742 -0.318189,-0.3026 -0.527046,-0.54 -0.655091,-0.7146 l -3.335009,3.1266 c 0.919615,1.0515 1.989743,1.8285 3.215901,2.3523 1.206759,0.5239 2.533644,0.7742 3.930546,0.7742 2.258296,0 3.974396,-0.5007 5.181174,-1.5484 0.333682,-0.2871 0.678703,-0.6575 1.012413,-1.1017 0.333691,-0.4443 0.608687,-0.9599 0.833752,-1.5187 0.205637,-0.5548 0.297759,-1.1645 0.297769,-1.8163 -1e-5,-0.9235 -0.236119,-1.8199 -0.744422,-2.7395 -0.508322,-0.9235 -1.155571,-1.585 -1.935496,-1.9652 0.03103,-0.015 0.203531,-0.1751 0.506207,-0.4467 0.287125,-0.2678 0.546608,-0.6491 0.833752,-1.1613 0.267725,-0.4889 0.46874,-1.0843 0.56576,-1.7866 0.03106,-0.2367 0.02977,-0.4741 0.02978,-0.7147 -9e-6,-0.9196 -0.236118,-1.7666 -0.744421,-2.531 -0.65189,-0.9352 -1.537792,-1.646 -2.620364,-2.1737 -1.094245,-0.5083 -2.204603,-0.7742 -3.364785,-0.7742 z"
       style="font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:25.01973534px;line-height:125%;font-family:Kabel;-inkscape-font-specification:Kabel;text-align:start;letter-spacing:0px;word-spacing:0px;writing-mode:lr-tb;text-anchor:start;fill:#b3b3b3;fill-opacity:1;stroke:none" />
    <path
       inkscape:connector-curvature="0"
       id="path2992"
       d="m 31.511644,1050.0516 14.292893,-14.2929 -14.292893,0 0,14.2929 z"
       style="fill:url(#linearGradient3022);fill-opacity:1;stroke:#666666;stroke-width:0.99999994px;stroke-linecap:butt;stroke-linejoin:round;stroke-opacity:1" />
    <path
       inkscape:connector-curvature="0"
       style="fill:none;stroke:#666666;stroke-width:0.99999994px;stroke-linecap:butt;stroke-linejoin:miter;stroke-opacity:1"
       d="m 2.9258568,1007.1729 0,42.8787 28.5857872,0 14.292893,-14.2929 0,-28.5858 -42.8786802,0 z"
       id="path3040" />
  </g>
</svg>
