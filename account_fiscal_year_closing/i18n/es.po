# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_fiscal_year_closing
#
# Translators:
# <AUTHOR> <EMAIL>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-12-22 23:09+0000\n"
"PO-Revision-Date: 2023-12-09 18:33+0000\n"
"Last-Translator: Ivorra78 <<EMAIL>>\n"
"Language-Team: Spanish (https://www.transifex.com/oca/teams/23907/es/)\n"
"Language: es\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 4.17\n"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move_line__account_id
msgid "Account"
msgstr "Cuenta"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,help:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping_template__dest_account
msgid ""
"Account code pattern for the mapping destination account. Only the first "
"match will be considered. If this field is not filled, the performed "
"operation will be to remove any existing balance on the source accounts with "
"an equivalent counterpart in the same account."
msgstr ""
"Patrón del código de cuenta para mapear la cuenta destino. Solo se "
"considerará la primera ocurrencia. Si este campo no se completa, la "
"operación realizada será eliminar cualquier saldo existente en las cuentas "
"origen con una contrapartida equivalente en la misma cuenta."

#. module: account_fiscal_year_closing
#: model:ir.model.fields,help:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping_template__src_accounts
msgid "Account code pattern for the mapping source accounts"
msgstr "Patrón de código de cuenta para el mapeo de las cuentas origen"

#. module: account_fiscal_year_closing
#: model:ir.model,name:account_fiscal_year_closing.model_account_fiscalyear_closing_abstract
msgid "Account fiscalyear closing abstract"
msgstr "Resumen de cierre del ejercicio contable"

#. module: account_fiscal_year_closing
#: model:ir.model,name:account_fiscal_year_closing.model_account_fiscalyear_closing_config_abstract
msgid "Account fiscalyear closing config abstract"
msgstr "Resumen de la configuración del cierre del ejercicio"

#. module: account_fiscal_year_closing
#: model:ir.model,name:account_fiscal_year_closing.model_account_fiscalyear_closing_mapping_abstract
msgid "Account fiscalyear closing mapping abstract"
msgstr "Resumen de la asignación de cuentas al cierre del ejercicio"

#. module: account_fiscal_year_closing
#: model:ir.model,name:account_fiscal_year_closing.model_account_fiscalyear_closing_type_abstract
msgid "Account fiscalyear closing type abstract"
msgstr "Resumen del tipo de cierre del ejercicio de la cuenta"

#. module: account_fiscal_year_closing
#: model:ir.model,name:account_fiscal_year_closing.model_account_fiscalyear_closing_unbalanced_move
msgid "Account fiscalyear closing unbalanced move"
msgstr "Movimiento de desequilibrio en el cierre del ejercicio"

#. module: account_fiscal_year_closing
#: model:ir.model,name:account_fiscal_year_closing.model_account_fiscalyear_closing_unbalanced_move_line
msgid "Account fiscalyear closing unbalanced move line"
msgstr ""
"Línea de movimiento de desequilibrio de cierre de ejercicio de la cuenta"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config__mapping_ids
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_template__mapping_ids
msgid "Account mappings"
msgstr "Asignaciones de cuenta"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type__account_type_id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type_abstract__account_type_id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type_template__account_type_id
msgid "Account type"
msgstr "Tipo de cuenta"

#. module: account_fiscal_year_closing
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_form
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_template_form
msgid "Accounts closing types"
msgstr "Tipos de cierre de cuentas"

#. module: account_fiscal_year_closing
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_form
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_template_form
msgid "Accounts mapping"
msgstr "Asignación de cuentas"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_template__chart_template_ids
msgid "Available for"
msgstr "Disponible para"

#. module: account_fiscal_year_closing
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_config__closing_type_default__balance
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_config_abstract__closing_type_default__balance
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_config_template__closing_type_default__balance
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_type__closing_type__balance
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_type_abstract__closing_type__balance
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_type_template__closing_type__balance
msgid "Balance"
msgstr "Balance"

#. module: account_fiscal_year_closing
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_form
msgid "Calculate"
msgstr "Calcular"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing__calculation_date
msgid "Calculation date"
msgstr "Fecha de cálculo"

#. module: account_fiscal_year_closing
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_form
msgid "Cancel"
msgstr "Cancelar"

#. module: account_fiscal_year_closing
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing__state__cancelled
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_search
msgid "Cancelled"
msgstr "Cancelado"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing__chart_template_id
msgid "Chart template"
msgstr "Plantilla de gráfico"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing__check_draft_moves
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_abstract__check_draft_moves
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_template__check_draft_moves
msgid "Check draft moves"
msgstr "Verificar movimientos borrador"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,help:account_fiscal_year_closing.field_account_fiscalyear_closing__check_draft_moves
#: model:ir.model.fields,help:account_fiscal_year_closing.field_account_fiscalyear_closing_abstract__check_draft_moves
#: model:ir.model.fields,help:account_fiscal_year_closing.field_account_fiscalyear_closing_template__check_draft_moves
msgid ""
"Checks that there are no draft moves on the fiscal year that is being "
"closed. Non-confirmed moves won't be taken in account on the closing "
"operations."
msgstr ""
"Comprueba que no hay movimientos en borrador en el año fiscal que se está "
"cerrando. Los movimientos no confirmados no se tomarán en cuenta en las "
"operaciones de cierre."

#. module: account_fiscal_year_closing
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_unbalanced_move_form
msgid "Close"
msgstr "Cerrar"

#. module: account_fiscal_year_closing
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_config__move_type__closing
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_config_abstract__move_type__closing
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_config_template__move_type__closing
msgid "Closing"
msgstr "Cerrando"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_bank_statement_line__closing_type
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_move__closing_type
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_payment__closing_type
msgid "Closing Type"
msgstr "Tipo de Cierre"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing__closing_template_id
msgid "Closing template"
msgstr "Cerrando plantilla"

#. module: account_fiscal_year_closing
#: model:ir.actions.act_window,name:account_fiscal_year_closing.action_account_fiscalyear_closing_template
#: model:ir.ui.menu,name:account_fiscal_year_closing.menu_account_fiscalyear_closing_template
msgid "Closing templates"
msgstr "Cerrando plantillas"

#. module: account_fiscal_year_closing
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_move_filter
msgid "Closing type"
msgstr "Tipo de cierre"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config__closing_type_ids
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_template__closing_type_ids
msgid "Closing types"
msgstr "Tipos de cierre"

#. module: account_fiscal_year_closing
#: code:addons/account_fiscal_year_closing/models/account_fiscalyear_closing.py:0
#: model:ir.model.constraint,message:account_fiscal_year_closing.constraint_account_fiscalyear_closing_config_code_uniq
#: model:ir.model.constraint,message:account_fiscal_year_closing.constraint_account_fiscalyear_closing_config_template_code_uniq
#, python-format
msgid "Code must be unique per fiscal year closing!"
msgstr "El código debe ser único para el cierre de ejercicio!"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing__company_id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_abstract__company_id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_template__company_id
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_search
msgid "Company"
msgstr "Empresa"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,help:account_fiscal_year_closing.field_account_fiscalyear_closing_config__inverse
#: model:ir.model.fields,help:account_fiscal_year_closing.field_account_fiscalyear_closing_config_abstract__inverse
#: model:ir.model.fields,help:account_fiscal_year_closing.field_account_fiscalyear_closing_config_template__inverse
msgid "Configuration code to inverse its move"
msgstr "Código de configuración para invertir el movimiento"

#. module: account_fiscal_year_closing
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_form
msgid "Confirm and post moves"
msgstr "Confirmar y publicar movimientos"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing__create_uid
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config__create_uid
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_template__create_uid
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping__create_uid
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping_template__create_uid
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_template__create_uid
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type__create_uid
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type_template__create_uid
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move__create_uid
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move_line__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing__create_date
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config__create_date
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_template__create_date
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping__create_date
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping_template__create_date
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_template__create_date
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type__create_date
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type_template__create_date
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move__create_date
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move_line__create_date
msgid "Created on"
msgstr "Creado el"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move_line__credit
msgid "Credit"
msgstr "Crédito"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move__date
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move_line__date
msgid "Date"
msgstr "Fecha"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move_line__debit
msgid "Debit"
msgstr "Débito"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config__closing_type_default
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_abstract__closing_type_default
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_template__closing_type_default
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type__closing_type
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type_abstract__closing_type
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type_template__closing_type
msgid "Default closing type"
msgstr "Tipo de cierre predeterminado"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing__name
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_abstract__name
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config__name
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_abstract__name
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_template__name
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping__name
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping_abstract__name
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping_template__name
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_template__name
msgid "Description"
msgstr "Descripción"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping__dest_account_id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping_template__dest_account
msgid "Destination account"
msgstr "Cuenta de destino"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing__display_name
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_abstract__display_name
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config__display_name
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_abstract__display_name
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_template__display_name
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping__display_name
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping_abstract__display_name
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping_template__display_name
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_template__display_name
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type__display_name
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type_abstract__display_name
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type_template__display_name
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move__display_name
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move_line__display_name
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_move__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: account_fiscal_year_closing
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing__state__draft
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_form
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_search
msgid "Draft"
msgstr "Borrador"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config__enabled
msgid "Enabled"
msgstr "Activado"

#. module: account_fiscal_year_closing
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_config_template__move_date__first_opening
msgid "First date of the opening period"
msgstr "Primera fecha del periodo de apertura"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config__fyc_id
#: model:ir.ui.menu,name:account_fiscal_year_closing.menu_account_fiscalyear_closing_configuration
msgid "Fiscal Year Closing"
msgstr "Cierre del año fiscal"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_template__template_id
msgid "Fiscal Year Closing Template"
msgstr "Plantilla de cierre del año fiscal"

#. module: account_fiscal_year_closing
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_form
msgid "Fiscal closing"
msgstr "Cierre fiscal"

#. module: account_fiscal_year_closing
#: code:addons/account_fiscal_year_closing/models/account_fiscalyear_closing.py:0
#, python-format
msgid "Fiscal closing move lines"
msgstr "Cierre fiscal lineas movimiento"

#. module: account_fiscal_year_closing
#: code:addons/account_fiscal_year_closing/models/account_fiscalyear_closing.py:0
#, python-format
msgid "Fiscal closing moves"
msgstr "Cierre fiscal movimientos"

#. module: account_fiscal_year_closing
#: model:ir.model,name:account_fiscal_year_closing.model_account_fiscalyear_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_bank_statement_line__fyc_id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_move__fyc_id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_payment__fyc_id
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_form
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_search
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_template_form
msgid "Fiscal year closing"
msgstr "Cierre de ejercicio"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping__fyc_config_id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type__fyc_config_id
msgid "Fiscal year closing config"
msgstr "Configuración cierre ejercicio"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping_template__template_config_id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type_template__template_config_id
msgid "Fiscal year closing config template"
msgstr "Plantilla configuración cierre ejercicio"

#. module: account_fiscal_year_closing
#: model:ir.model,name:account_fiscal_year_closing.model_account_fiscalyear_closing_config
msgid "Fiscal year closing configuration"
msgstr "Configuración del cierre del ejercicio"

#. module: account_fiscal_year_closing
#: model:ir.model,name:account_fiscal_year_closing.model_account_fiscalyear_closing_config_template
msgid "Fiscal year closing configuration template"
msgstr "Plantilla de configuración del cierre del ejercicio"

#. module: account_fiscal_year_closing
#: model:ir.model,name:account_fiscal_year_closing.model_account_fiscalyear_closing_mapping
msgid "Fiscal year closing mapping"
msgstr "Cartografía de cierre del ejercicio"

#. module: account_fiscal_year_closing
#: model:ir.model,name:account_fiscal_year_closing.model_account_fiscalyear_closing_mapping_template
msgid "Fiscal year closing mapping template"
msgstr "Plantilla de cartografía de cierre del ejercicio"

#. module: account_fiscal_year_closing
#: model:ir.model,name:account_fiscal_year_closing.model_account_fiscalyear_closing_template
msgid "Fiscal year closing template"
msgstr "Plantilla de cierre del ejercicio"

#. module: account_fiscal_year_closing
#: model:ir.model,name:account_fiscal_year_closing.model_account_fiscalyear_closing_type
msgid "Fiscal year closing type"
msgstr "Tipo de cierre del ejercicio"

#. module: account_fiscal_year_closing
#: model:ir.model,name:account_fiscal_year_closing.model_account_fiscalyear_closing_type_template
msgid "Fiscal year closing type template"
msgstr "Plantilla de tipo de cierre de ejercicio"

#. module: account_fiscal_year_closing
#: model:ir.actions.act_window,name:account_fiscal_year_closing.action_account_fiscalyear_closing
#: model:ir.ui.menu,name:account_fiscal_year_closing.menu_account_fiscalyear_closing
msgid "Fiscal year closings"
msgstr "Cierres de ejercicio"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing__date_start
msgid "From date"
msgstr "Desde la fecha"

#. module: account_fiscal_year_closing
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_unbalanced_move_form
msgid ""
"Generating the closing moves, an unbalanced move has been detected.\n"
"                        You need to check the closing configuration or your "
"journal entries\n"
"                        to find the problem, fix it and then press again on "
"\"Calculate\"."
msgstr ""
"Generando los movimientos de cierre, se ha detectado un descuadre en un "
"movimiento.\n"
"                        Necesita verificar la configuración de cierre o los "
"asientos de los diarios\n"
"                        para encontrar el problema, una vez solucionado "
"pulse otra vez \"Calcular\"."

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing__id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_abstract__id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config__id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_abstract__id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_template__id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping__id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping_abstract__id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping_template__id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_template__id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type__id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type_abstract__id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type_template__id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move__id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move_line__id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_move__id
msgid "ID"
msgstr "ID"

#. module: account_fiscal_year_closing
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_search
msgid "In process"
msgstr "En proceso"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,help:account_fiscal_year_closing.field_account_fiscalyear_closing__year
msgid ""
"Introduce here the year to close. If the fiscal year is between several "
"natural years, you have to put here the last one."
msgstr ""
"Introducir aquí el año para cerrar. Si el año fiscal está entre varios años "
"naturales, tiene que poner aquí el último."

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config__inverse
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_abstract__inverse
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_template__inverse
msgid "Inverse config"
msgstr "Configuración inversa"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config__journal_id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_abstract__journal_id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_template__journal_id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move__journal_id
msgid "Journal"
msgstr "Diario"

#. module: account_fiscal_year_closing
#: model:ir.model,name:account_fiscal_year_closing.model_account_move
msgid "Journal Entry"
msgstr "Entrada en diario"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing____last_update
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_abstract____last_update
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config____last_update
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_abstract____last_update
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_template____last_update
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping____last_update
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping_abstract____last_update
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping_template____last_update
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_template____last_update
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type____last_update
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type_abstract____last_update
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type_template____last_update
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move____last_update
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move_line____last_update
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_move____last_update
msgid "Last Modified on"
msgstr "Última modificación el"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing__write_uid
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config__write_uid
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_template__write_uid
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping__write_uid
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping_template__write_uid
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_template__write_uid
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type__write_uid
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type_template__write_uid
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move__write_uid
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move_line__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing__write_date
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config__write_date
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_template__write_date
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping__write_date
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping_template__write_date
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_template__write_date
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type__write_date
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type_template__write_date
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move__write_date
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move_line__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: account_fiscal_year_closing
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_config_template__move_date__last_ending
msgid "Last date of the ending period"
msgstr "Última fecha del período final"

#. module: account_fiscal_year_closing
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_config__move_type__loss_profit
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_config_abstract__move_type__loss_profit
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_config_template__move_type__loss_profit
msgid "Loss & Profit"
msgstr "Pérdidas y ganancias"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config__move_id
msgid "Move"
msgstr "Movimiento"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config__date
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_template__move_date
msgid "Move date"
msgstr "Fecha del movimiento"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config__move_type
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_abstract__move_type
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_template__move_type
msgid "Move type"
msgstr "Tipo de movimiento"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing__move_ids
msgid "Moves"
msgstr "Movimientos"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing__move_config_ids
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_template__move_config_ids
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_form
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_template_form
msgid "Moves configuration"
msgstr "Configuración de movimientos"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move_line__name
msgid "Name"
msgstr "Nombre"

#. module: account_fiscal_year_closing
#: code:addons/account_fiscal_year_closing/models/account_fiscalyear_closing.py:0
#, python-format
msgid "No destination account '%s' found."
msgstr "No se ha encontrado la cuenta de destino '%s'."

#. module: account_fiscal_year_closing
#: code:addons/account_fiscal_year_closing/models/account_move.py:0
#, python-format
msgid "None"
msgstr "Ninguno"

#. module: account_fiscal_year_closing
#: code:addons/account_fiscal_year_closing/models/account_fiscalyear_closing.py:0
#, python-format
msgid "One or more draft moves found: \n"
msgstr "Se encontraron uno o más movimientos en borrador: \n"

#. module: account_fiscal_year_closing
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_config__move_type__opening
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_config_abstract__move_type__opening
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_config_template__move_type__opening
msgid "Opening"
msgstr "Apertura"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing__date_opening
msgid "Opening date"
msgstr "Fecha de apertura"

#. module: account_fiscal_year_closing
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_config__move_type__other
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_config_abstract__move_type__other
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_config_template__move_type__other
msgid "Other"
msgstr "Otro"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move_line__partner_id
msgid "Partner"
msgstr "Asociado"

#. module: account_fiscal_year_closing
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing__state__posted
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_search
msgid "Posted"
msgstr "Publicado"

#. module: account_fiscal_year_closing
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing__state__calculated
msgid "Processed"
msgstr "Procesado"

#. module: account_fiscal_year_closing
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_form
msgid "Recalculate"
msgstr "Volver a calcular"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move__ref
msgid "Reference"
msgstr "Referencia"

#. module: account_fiscal_year_closing
#: code:addons/account_fiscal_year_closing/models/account_fiscalyear_closing.py:0
#, python-format
msgid "Result"
msgstr "Resultado"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config__sequence
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_abstract__sequence
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_template__sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: account_fiscal_year_closing
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_form
msgid "Show move lines"
msgstr "Mostrar líneas de movimiento"

#. module: account_fiscal_year_closing
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_form
msgid "Show moves"
msgstr "Mostrar movimientos"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping__src_accounts
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping_template__src_accounts
msgid "Source accounts"
msgstr "Cuentas de origen"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing__state
msgid "State"
msgstr "Estado"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,help:account_fiscal_year_closing.field_account_fiscalyear_closing__chart_template_id
msgid "The chart template for the company (if any)"
msgstr "Plantilla gráfica para la empresa (si existe)"

#. module: account_fiscal_year_closing
#: code:addons/account_fiscal_year_closing/models/account_fiscalyear_closing.py:0
#: model:ir.model.constraint,message:account_fiscal_year_closing.constraint_account_fiscalyear_closing_year_company_uniq
#, python-format
msgid "There should be only one fiscal year closing for that year and company!"
msgstr "Debería haber un solo cierre de ejercicio para este año y empresa!"

#. module: account_fiscal_year_closing
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_unbalanced_move_form
msgid ""
"This screen will help you to identify the problem, showing you\n"
"                        the unbalanced journal entry:"
msgstr ""
"Esta pantalla le ayudará a identificar el problema mostrándole\n"
"                        el asiento descuadrado en el diario:"

#. module: account_fiscal_year_closing
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_form
msgid ""
"This will remove all the closing moves. Are you sure you want to continue?"
msgstr ""
"Esto eliminará todos los movimientos de cierre. Estás seguro que quiere "
"continuar?"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing__date_end
msgid "To date"
msgstr "Hasta la fecha"

#. module: account_fiscal_year_closing
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_unbalanced_move_form
msgid "Total credit"
msgstr "Crédito total"

#. module: account_fiscal_year_closing
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_unbalanced_move_form
msgid "Total debit"
msgstr "Débito total"

#. module: account_fiscal_year_closing
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_config__closing_type_default__unreconciled
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_config_abstract__closing_type_default__unreconciled
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_config_template__closing_type_default__unreconciled
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_type__closing_type__unreconciled
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_type_abstract__closing_type__unreconciled
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_type_template__closing_type__unreconciled
msgid "Un-reconciled"
msgstr "Sin reconciliar"

#. module: account_fiscal_year_closing
#: code:addons/account_fiscal_year_closing/models/account_fiscalyear_closing.py:0
#, python-format
msgid "Unbalanced journal entry found"
msgstr "Se ha encontrado un asiento de diario descuadrado"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move_line__move_id
msgid "Unbalanced move"
msgstr "Movimiento descuadrado"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move__line_ids
msgid "Unbalanced move lines"
msgstr "Líneas de movimiento descuadradas"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config__code
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_abstract__code
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_template__code
msgid "Unique code"
msgstr "Código único"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing__year
msgid "Year"
msgstr "Año"

#. module: account_fiscal_year_closing
#: code:addons/account_fiscal_year_closing/models/account_fiscalyear_closing.py:0
#, python-format
msgid "You can't remove any closing that is not in draft or cancelled state."
msgstr ""
"No puede eliminar ningún cierre que no se encuentre en borrador o estado "
"cancelado."

#, fuzzy
#~ msgid "Is New Template"
#~ msgstr "Es una plantilla nueva"

#, fuzzy
#~ msgid "Journal Entries"
#~ msgstr "Diario"

#~ msgid "Load template"
#~ msgstr "Cargar plantilla"

#~ msgid "Stored closing template"
#~ msgstr "Plantilla de cierre guardado"

#~ msgid "account.fiscalyear.closing.config"
#~ msgstr "account.fiscalyear.closing.config"

#~ msgid "account.fiscalyear.closing.config.template"
#~ msgstr "account.fiscalyear.closing.config.template"

#~ msgid "account.fiscalyear.closing.mapping"
#~ msgstr "account.fiscalyear.closing.mapping"

#~ msgid "account.fiscalyear.closing.mapping.template"
#~ msgstr "account.fiscalyear.closing.mapping.template"

#~ msgid "account.fiscalyear.closing.template"
#~ msgstr "account.fiscalyear.closing.template"

#~ msgid "account.fiscalyear.closing.type"
#~ msgstr "account.fiscalyear.closing.type"

#~ msgid "account.fiscalyear.closing.type.template"
#~ msgstr "account.fiscalyear.closing.type.template"

#~ msgid "Account Entry"
#~ msgstr "Apunte contable"

#~ msgid "Reconcile"
#~ msgstr "Reconciliar"

#~ msgid "Reconcile inverse move"
#~ msgstr "Conciliar movimiento inverso"
