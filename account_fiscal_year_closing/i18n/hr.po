# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_fiscal_year_closing
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-04-20 22:12+0000\n"
"PO-Revision-Date: 2018-04-20 22:12+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Croatian (https://www.transifex.com/oca/teams/23907/hr/)\n"
"Language: hr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n"
"%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move_line__account_id
msgid "Account"
msgstr "Konto"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,help:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping_template__dest_account
msgid ""
"Account code pattern for the mapping destination account. Only the first "
"match will be considered. If this field is not filled, the performed "
"operation will be to remove any existing balance on the source accounts with "
"an equivalent counterpart in the same account."
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model.fields,help:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping_template__src_accounts
msgid "Account code pattern for the mapping source accounts"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model,name:account_fiscal_year_closing.model_account_fiscalyear_closing_abstract
#, fuzzy
msgid "Account fiscalyear closing abstract"
msgstr "Tipovi zatvaranja"

#. module: account_fiscal_year_closing
#: model:ir.model,name:account_fiscal_year_closing.model_account_fiscalyear_closing_config_abstract
msgid "Account fiscalyear closing config abstract"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model,name:account_fiscal_year_closing.model_account_fiscalyear_closing_mapping_abstract
msgid "Account fiscalyear closing mapping abstract"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model,name:account_fiscal_year_closing.model_account_fiscalyear_closing_type_abstract
#, fuzzy
msgid "Account fiscalyear closing type abstract"
msgstr "Tipovi zatvaranja"

#. module: account_fiscal_year_closing
#: model:ir.model,name:account_fiscal_year_closing.model_account_fiscalyear_closing_unbalanced_move
msgid "Account fiscalyear closing unbalanced move"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model,name:account_fiscal_year_closing.model_account_fiscalyear_closing_unbalanced_move_line
msgid "Account fiscalyear closing unbalanced move line"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config__mapping_ids
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_template__mapping_ids
msgid "Account mappings"
msgstr "Mapiranje konta"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type__account_type_id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type_abstract__account_type_id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type_template__account_type_id
msgid "Account type"
msgstr "Tip konta"

#. module: account_fiscal_year_closing
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_form
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_template_form
msgid "Accounts closing types"
msgstr "Tipovi zatvaranja"

#. module: account_fiscal_year_closing
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_form
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_template_form
msgid "Accounts mapping"
msgstr "Mapiranje konta"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_template__chart_template_ids
msgid "Available for"
msgstr "Raspoloživo za"

#. module: account_fiscal_year_closing
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_config__closing_type_default__balance
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_config_abstract__closing_type_default__balance
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_config_template__closing_type_default__balance
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_type__closing_type__balance
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_type_abstract__closing_type__balance
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_type_template__closing_type__balance
msgid "Balance"
msgstr "Saldo"

#. module: account_fiscal_year_closing
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_form
msgid "Calculate"
msgstr "Izračunaj"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing__calculation_date
msgid "Calculation date"
msgstr "Datum izračuna"

#. module: account_fiscal_year_closing
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_form
msgid "Cancel"
msgstr "Otkaži"

#. module: account_fiscal_year_closing
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing__state__cancelled
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_search
msgid "Cancelled"
msgstr "Otkazano"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing__chart_template_id
msgid "Chart template"
msgstr "Predložak plana"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing__check_draft_moves
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_abstract__check_draft_moves
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_template__check_draft_moves
msgid "Check draft moves"
msgstr "Provjeri knjiženja u nacrtu"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,help:account_fiscal_year_closing.field_account_fiscalyear_closing__check_draft_moves
#: model:ir.model.fields,help:account_fiscal_year_closing.field_account_fiscalyear_closing_abstract__check_draft_moves
#: model:ir.model.fields,help:account_fiscal_year_closing.field_account_fiscalyear_closing_template__check_draft_moves
msgid ""
"Checks that there are no draft moves on the fiscal year that is being "
"closed. Non-confirmed moves won't be taken in account on the closing "
"operations."
msgstr ""

#. module: account_fiscal_year_closing
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_unbalanced_move_form
msgid "Close"
msgstr "Zatvori"

#. module: account_fiscal_year_closing
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_config__move_type__closing
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_config_abstract__move_type__closing
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_config_template__move_type__closing
msgid "Closing"
msgstr "Zatvaranje"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_bank_statement_line__closing_type
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_move__closing_type
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_payment__closing_type
#, fuzzy
msgid "Closing Type"
msgstr "Vrsta zatvaranja"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing__closing_template_id
msgid "Closing template"
msgstr "Predložak zatvaranja"

#. module: account_fiscal_year_closing
#: model:ir.actions.act_window,name:account_fiscal_year_closing.action_account_fiscalyear_closing_template
#: model:ir.ui.menu,name:account_fiscal_year_closing.menu_account_fiscalyear_closing_template
msgid "Closing templates"
msgstr "Predlošci zatvaranja"

#. module: account_fiscal_year_closing
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_move_filter
msgid "Closing type"
msgstr "Vrsta zatvaranja"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config__closing_type_ids
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_template__closing_type_ids
msgid "Closing types"
msgstr "Vrtse zatvaranja"

#. module: account_fiscal_year_closing
#: code:addons/account_fiscal_year_closing/models/account_fiscalyear_closing.py:0
#: model:ir.model.constraint,message:account_fiscal_year_closing.constraint_account_fiscalyear_closing_config_code_uniq
#: model:ir.model.constraint,message:account_fiscal_year_closing.constraint_account_fiscalyear_closing_config_template_code_uniq
#, python-format
msgid "Code must be unique per fiscal year closing!"
msgstr "Šifra mora biti jedinstvena za zatvaranje fiskale godine!"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing__company_id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_abstract__company_id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_template__company_id
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_search
msgid "Company"
msgstr "Tvrtka"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,help:account_fiscal_year_closing.field_account_fiscalyear_closing_config__inverse
#: model:ir.model.fields,help:account_fiscal_year_closing.field_account_fiscalyear_closing_config_abstract__inverse
#: model:ir.model.fields,help:account_fiscal_year_closing.field_account_fiscalyear_closing_config_template__inverse
msgid "Configuration code to inverse its move"
msgstr ""

#. module: account_fiscal_year_closing
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_form
msgid "Confirm and post moves"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing__create_uid
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config__create_uid
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_template__create_uid
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping__create_uid
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping_template__create_uid
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_template__create_uid
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type__create_uid
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type_template__create_uid
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move__create_uid
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move_line__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing__create_date
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config__create_date
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_template__create_date
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping__create_date
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping_template__create_date
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_template__create_date
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type__create_date
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type_template__create_date
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move__create_date
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move_line__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move_line__credit
msgid "Credit"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move__date
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move_line__date
msgid "Date"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move_line__debit
msgid "Debit"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config__closing_type_default
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_abstract__closing_type_default
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_template__closing_type_default
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type__closing_type
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type_abstract__closing_type
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type_template__closing_type
msgid "Default closing type"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing__name
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_abstract__name
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config__name
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_abstract__name
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_template__name
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping__name
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping_abstract__name
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping_template__name
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_template__name
msgid "Description"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping__dest_account_id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping_template__dest_account
msgid "Destination account"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing__display_name
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_abstract__display_name
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config__display_name
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_abstract__display_name
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_template__display_name
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping__display_name
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping_abstract__display_name
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping_template__display_name
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_template__display_name
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type__display_name
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type_abstract__display_name
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type_template__display_name
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move__display_name
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move_line__display_name
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_move__display_name
msgid "Display Name"
msgstr "Naziv"

#. module: account_fiscal_year_closing
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing__state__draft
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_form
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_search
msgid "Draft"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config__enabled
msgid "Enabled"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_config_template__move_date__first_opening
msgid "First date of the opening period"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config__fyc_id
#: model:ir.ui.menu,name:account_fiscal_year_closing.menu_account_fiscalyear_closing_configuration
msgid "Fiscal Year Closing"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_template__template_id
msgid "Fiscal Year Closing Template"
msgstr ""

#. module: account_fiscal_year_closing
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_form
msgid "Fiscal closing"
msgstr ""

#. module: account_fiscal_year_closing
#: code:addons/account_fiscal_year_closing/models/account_fiscalyear_closing.py:0
#, python-format
msgid "Fiscal closing move lines"
msgstr ""

#. module: account_fiscal_year_closing
#: code:addons/account_fiscal_year_closing/models/account_fiscalyear_closing.py:0
#, python-format
msgid "Fiscal closing moves"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model,name:account_fiscal_year_closing.model_account_fiscalyear_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_bank_statement_line__fyc_id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_move__fyc_id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_payment__fyc_id
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_form
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_search
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_template_form
msgid "Fiscal year closing"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping__fyc_config_id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type__fyc_config_id
msgid "Fiscal year closing config"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping_template__template_config_id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type_template__template_config_id
msgid "Fiscal year closing config template"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model,name:account_fiscal_year_closing.model_account_fiscalyear_closing_config
msgid "Fiscal year closing configuration"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model,name:account_fiscal_year_closing.model_account_fiscalyear_closing_config_template
msgid "Fiscal year closing configuration template"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model,name:account_fiscal_year_closing.model_account_fiscalyear_closing_mapping
msgid "Fiscal year closing mapping"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model,name:account_fiscal_year_closing.model_account_fiscalyear_closing_mapping_template
msgid "Fiscal year closing mapping template"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model,name:account_fiscal_year_closing.model_account_fiscalyear_closing_template
msgid "Fiscal year closing template"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model,name:account_fiscal_year_closing.model_account_fiscalyear_closing_type
msgid "Fiscal year closing type"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model,name:account_fiscal_year_closing.model_account_fiscalyear_closing_type_template
msgid "Fiscal year closing type template"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.actions.act_window,name:account_fiscal_year_closing.action_account_fiscalyear_closing
#: model:ir.ui.menu,name:account_fiscal_year_closing.menu_account_fiscalyear_closing
msgid "Fiscal year closings"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing__date_start
msgid "From date"
msgstr ""

#. module: account_fiscal_year_closing
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_unbalanced_move_form
msgid ""
"Generating the closing moves, an unbalanced move has been detected.\n"
"                        You need to check the closing configuration or your "
"journal entries\n"
"                        to find the problem, fix it and then press again on "
"\"Calculate\"."
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing__id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_abstract__id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config__id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_abstract__id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_template__id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping__id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping_abstract__id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping_template__id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_template__id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type__id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type_abstract__id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type_template__id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move__id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move_line__id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_move__id
msgid "ID"
msgstr "ID"

#. module: account_fiscal_year_closing
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_search
msgid "In process"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model.fields,help:account_fiscal_year_closing.field_account_fiscalyear_closing__year
msgid ""
"Introduce here the year to close. If the fiscal year is between several "
"natural years, you have to put here the last one."
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config__inverse
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_abstract__inverse
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_template__inverse
msgid "Inverse config"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config__journal_id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_abstract__journal_id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_template__journal_id
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move__journal_id
msgid "Journal"
msgstr "Dnevnik"

#. module: account_fiscal_year_closing
#: model:ir.model,name:account_fiscal_year_closing.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing____last_update
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_abstract____last_update
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config____last_update
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_abstract____last_update
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_template____last_update
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping____last_update
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping_abstract____last_update
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping_template____last_update
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_template____last_update
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type____last_update
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type_abstract____last_update
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type_template____last_update
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move____last_update
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move_line____last_update
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_move____last_update
msgid "Last Modified on"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing__write_uid
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config__write_uid
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_template__write_uid
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping__write_uid
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping_template__write_uid
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_template__write_uid
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type__write_uid
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type_template__write_uid
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move__write_uid
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move_line__write_uid
msgid "Last Updated by"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing__write_date
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config__write_date
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_template__write_date
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping__write_date
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping_template__write_date
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_template__write_date
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type__write_date
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_type_template__write_date
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move__write_date
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move_line__write_date
msgid "Last Updated on"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_config_template__move_date__last_ending
msgid "Last date of the ending period"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_config__move_type__loss_profit
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_config_abstract__move_type__loss_profit
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_config_template__move_type__loss_profit
msgid "Loss & Profit"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config__move_id
msgid "Move"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config__date
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_template__move_date
msgid "Move date"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config__move_type
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_abstract__move_type
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_template__move_type
msgid "Move type"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing__move_ids
msgid "Moves"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing__move_config_ids
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_template__move_config_ids
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_form
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_template_form
msgid "Moves configuration"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move_line__name
msgid "Name"
msgstr ""

#. module: account_fiscal_year_closing
#: code:addons/account_fiscal_year_closing/models/account_fiscalyear_closing.py:0
#, python-format
msgid "No destination account '%s' found."
msgstr ""

#. module: account_fiscal_year_closing
#: code:addons/account_fiscal_year_closing/models/account_move.py:0
#, python-format
msgid "None"
msgstr ""

#. module: account_fiscal_year_closing
#: code:addons/account_fiscal_year_closing/models/account_fiscalyear_closing.py:0
#, python-format
msgid "One or more draft moves found: \n"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_config__move_type__opening
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_config_abstract__move_type__opening
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_config_template__move_type__opening
msgid "Opening"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing__date_opening
msgid "Opening date"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_config__move_type__other
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_config_abstract__move_type__other
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_config_template__move_type__other
msgid "Other"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move_line__partner_id
msgid "Partner"
msgstr "Partner"

#. module: account_fiscal_year_closing
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing__state__posted
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_search
msgid "Posted"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing__state__calculated
msgid "Processed"
msgstr ""

#. module: account_fiscal_year_closing
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_form
msgid "Recalculate"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move__ref
msgid "Reference"
msgstr ""

#. module: account_fiscal_year_closing
#: code:addons/account_fiscal_year_closing/models/account_fiscalyear_closing.py:0
#, python-format
msgid "Result"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config__sequence
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_abstract__sequence
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_template__sequence
msgid "Sequence"
msgstr ""

#. module: account_fiscal_year_closing
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_form
msgid "Show move lines"
msgstr ""

#. module: account_fiscal_year_closing
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_form
msgid "Show moves"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping__src_accounts
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_mapping_template__src_accounts
msgid "Source accounts"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing__state
msgid "State"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model.fields,help:account_fiscal_year_closing.field_account_fiscalyear_closing__chart_template_id
msgid "The chart template for the company (if any)"
msgstr ""

#. module: account_fiscal_year_closing
#: code:addons/account_fiscal_year_closing/models/account_fiscalyear_closing.py:0
#: model:ir.model.constraint,message:account_fiscal_year_closing.constraint_account_fiscalyear_closing_year_company_uniq
#, python-format
msgid "There should be only one fiscal year closing for that year and company!"
msgstr ""

#. module: account_fiscal_year_closing
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_unbalanced_move_form
msgid ""
"This screen will help you to identify the problem, showing you\n"
"                        the unbalanced journal entry:"
msgstr ""

#. module: account_fiscal_year_closing
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_form
msgid ""
"This will remove all the closing moves. Are you sure you want to continue?"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing__date_end
msgid "To date"
msgstr ""

#. module: account_fiscal_year_closing
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_unbalanced_move_form
msgid "Total credit"
msgstr ""

#. module: account_fiscal_year_closing
#: model_terms:ir.ui.view,arch_db:account_fiscal_year_closing.view_account_fiscalyear_closing_unbalanced_move_form
msgid "Total debit"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_config__closing_type_default__unreconciled
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_config_abstract__closing_type_default__unreconciled
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_config_template__closing_type_default__unreconciled
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_type__closing_type__unreconciled
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_type_abstract__closing_type__unreconciled
#: model:ir.model.fields.selection,name:account_fiscal_year_closing.selection__account_fiscalyear_closing_type_template__closing_type__unreconciled
msgid "Un-reconciled"
msgstr ""

#. module: account_fiscal_year_closing
#: code:addons/account_fiscal_year_closing/models/account_fiscalyear_closing.py:0
#, python-format
msgid "Unbalanced journal entry found"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move_line__move_id
msgid "Unbalanced move"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_unbalanced_move__line_ids
msgid "Unbalanced move lines"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config__code
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_abstract__code
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing_config_template__code
msgid "Unique code"
msgstr ""

#. module: account_fiscal_year_closing
#: model:ir.model.fields,field_description:account_fiscal_year_closing.field_account_fiscalyear_closing__year
msgid "Year"
msgstr ""

#. module: account_fiscal_year_closing
#: code:addons/account_fiscal_year_closing/models/account_fiscalyear_closing.py:0
#, python-format
msgid "You can't remove any closing that is not in draft or cancelled state."
msgstr ""

#, fuzzy
#~ msgid "Journal Entries"
#~ msgstr "Dnevnik"
