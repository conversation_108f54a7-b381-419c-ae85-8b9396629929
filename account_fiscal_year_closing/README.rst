===================
Fiscal year closing
===================

.. 
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! This file is generated by oca-gen-addon-readme !!
   !! changes will be overwritten.                   !!
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! source digest: sha256:25582265fa480c943eb00b407ff450e043aa806eec30ab5d65385f52350032fc
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

.. |badge1| image:: https://img.shields.io/badge/maturity-Beta-yellow.png
    :target: https://odoo-community.org/page/development-status
    :alt: Beta
.. |badge2| image:: https://img.shields.io/badge/licence-AGPL--3-blue.png
    :target: http://www.gnu.org/licenses/agpl-3.0-standalone.html
    :alt: License: AGPL-3
.. |badge3| image:: https://img.shields.io/badge/github-OCA%2Faccount--closing-lightgray.png?logo=github
    :target: https://github.com/OCA/account-closing/tree/14.0/account_fiscal_year_closing
    :alt: OCA/account-closing
.. |badge4| image:: https://img.shields.io/badge/weblate-Translate%20me-F47D42.png
    :target: https://translation.odoo-community.org/projects/account-closing-14-0/account-closing-14-0-account_fiscal_year_closing
    :alt: Translate me on Weblate
.. |badge5| image:: https://img.shields.io/badge/runboat-Try%20me-875A7B.png
    :target: https://runboat.odoo-community.org/builds?repo=OCA/account-closing&target_branch=14.0
    :alt: Try me on Runboat

|badge1| |badge2| |badge3| |badge4| |badge5|

This module implements a generic fiscal year closing system for those
countries where closing/opening moves or other kind of closing operations are
mandatory in accounting books.

It includes a template mechanism that can be used in localizations for
providing the possible configurations to be used by the closing engine.

**Table of contents**

.. contents::
   :local:

Configuration
=============

If you want to add a closing template or check existing ones:

#. Go to *Accounting/Invoicing > Configuration > Fiscal year closing > Closing templates*.
#. Click on Create.
#. Put a name for the template.
#. Mark if you want to check if there's any draft move in the date range
   before making the closing operations.
#. Select the chart templates for which companies that have this chart you want
   this template to be available for.
#. Add one line for each of the journal entries you want to create on the
   closing operation.
#. This line has a name, a code, an optional journal (selectable per company),
   a sequence for ordering this line with the others, and the type we want to
   assign on the resulting journal entry created.
#. Then you can configure accounts for being mapped on the section
   "Accounts mapping".
#. If you put a destination account on each mapping line, then the balance of
   the source account will be transferred to that account.
#. If there's no destination account, then the balance of the account will be
   nullified through one or several journal items with the opposite balance.
#. The way these opposite journal items will be created is determined by the
   closing type in the section "Account closing types":

   * Balance: There will be only one journal item with the opposite balance.
   * Un-reconciled: The opposite balance will be grouped by the partner field
     in the period journal entries.

#. There's a default closing type to use, and you can specify others by
   account type.
#. You can configure a closing operation for being the reverse move of another
   previous move generated by a closing operation (typically, the opening
   move after the closing one). For making that, fill the "Inverse config"
   field with the code of the closing operation you want to reverse, and
   place this operation after that one (not necessarily immediately after).
#. The balances of the inverted journal items and the initial ones are
   reconciled by default.

Usage
=====

For closing a fiscal year:

#. Go to *Accounting > Adviser > Fiscal year closings*
#. Click on create.
#. Select the year for which you want to perform the closing. If your fiscal
   year doesn't coincide with a natural year, input the last year of both of
   the involved years.
#. Select the closing template you want to use.
#. Click on "Calculate".
#. Check the result clicking on the "Show Moves" or "Show Move Lines" buttons.
#. If everything is OK, then click on "Confirm and post moves" for finishing
   the closing, posting and reconciling the journal entries.
#. You can cancel the closing in any moment pressing "Cancel" button, which
   unreconciles and removes closing journal entries.
#. If one of the created journal entries is unbalanced, as Odoo doesn't allow
   to create unbalanced entries, a new screen will be shown for checking the
   problem on the created entry.

Bug Tracker
===========

Bugs are tracked on `GitHub Issues <https://github.com/OCA/account-closing/issues>`_.
In case of trouble, please check there if your issue has already been reported.
If you spotted it first, help us to smash it by providing a detailed and welcomed
`feedback <https://github.com/OCA/account-closing/issues/new?body=module:%20account_fiscal_year_closing%0Aversion:%2014.0%0A%0A**Steps%20to%20reproduce**%0A-%20...%0A%0A**Current%20behavior**%0A%0A**Expected%20behavior**>`_.

Do not contact contributors directly about support or help with technical issues.

Credits
=======

Contributors
~~~~~~~~~~~~

* Antonio Espinosa <<EMAIL>>
* Pedro M. Baeza <<EMAIL>>
* Jordi Ballester Alomar <https://github.com/JordiBForgeFlow>
* Sergio Corato <https://github.com/sergiocorato>
* `CorporateHub <https://corporatehub.eu/>`__

  * Alexey Pelykh <<EMAIL>>

* `Ooops <https://ooops404.com>`__

  * Giovanni Serra <<EMAIL>>

Maintainers
~~~~~~~~~~~

This module is maintained by the OCA.

.. image:: https://odoo-community.org/logo.png
   :alt: Odoo Community Association
   :target: https://odoo-community.org

OCA, or the Odoo Community Association, is a nonprofit organization whose
mission is to support the collaborative development of Odoo features and
promote its widespread use.

This module is part of the `OCA/account-closing <https://github.com/OCA/account-closing/tree/14.0/account_fiscal_year_closing>`_ project on GitHub.

You are welcome to contribute. To learn how please visit https://odoo-community.org/page/Contribute.
