If you want to add a closing template or check existing ones:

#. Go to *Accounting/Invoicing > Configuration > Fiscal year closing > Closing templates*.
#. Click on Create.
#. Put a name for the template.
#. Mark if you want to check if there's any draft move in the date range
   before making the closing operations.
#. Select the chart templates for which companies that have this chart you want
   this template to be available for.
#. Add one line for each of the journal entries you want to create on the
   closing operation.
#. This line has a name, a code, an optional journal (selectable per company),
   a sequence for ordering this line with the others, and the type we want to
   assign on the resulting journal entry created.
#. Then you can configure accounts for being mapped on the section
   "Accounts mapping".
#. If you put a destination account on each mapping line, then the balance of
   the source account will be transferred to that account.
#. If there's no destination account, then the balance of the account will be
   nullified through one or several journal items with the opposite balance.
#. The way these opposite journal items will be created is determined by the
   closing type in the section "Account closing types":

   * Balance: There will be only one journal item with the opposite balance.
   * Un-reconciled: The opposite balance will be grouped by the partner field
     in the period journal entries.

#. There's a default closing type to use, and you can specify others by
   account type.
#. You can configure a closing operation for being the reverse move of another
   previous move generated by a closing operation (typically, the opening
   move after the closing one). For making that, fill the "Inverse config"
   field with the code of the closing operation you want to reverse, and
   place this operation after that one (not necessarily immediately after).
#. The balances of the inverted journal items and the initial ones are
   reconciled by default.
