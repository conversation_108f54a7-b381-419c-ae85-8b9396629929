<?xml version="1.0" encoding="utf-8" ?>
<!-- License AGPL-3.0 or later (http://www.gnu.org/licenses/agpl). -->
<odoo>

    <record id="view_account_fiscalyear_closing_template_tree" model="ir.ui.view">
        <field name="model">account.fiscalyear.closing.template</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name" />
                <field name="company_id" groups="base.group_multi_company" />
                <field name="move_config_ids" />
            </tree>
        </field>
    </record>

    <record id="view_account_fiscalyear_closing_template_form" model="ir.ui.view">
        <field name="model">account.fiscalyear.closing.template</field>
        <field name="arch" type="xml">
            <form string="Fiscal year closing">
                <sheet>
                    <h1 class="oe_title">
                        <label for="name" clas="oe_edit_only" />
                        <field name="name" />
                    </h1>
                    <group>
                        <group>
                            <field name="check_draft_moves" />
                        </group>
                        <group>
                            <field name="chart_template_ids" widget="many2many_tags" />
                            <field
                                name="company_id"
                                groups="base.group_multi_company"
                            />
                        </group>
                    </group>
                    <group name='moves_config' string="Moves configuration">
                        <field name="move_config_ids" nolabel="1">
                            <tree>
                                <field name="sequence" widget="handle" />
                                <field name="name" />
                                <field name="code" />
                                <field name="inverse" />
                                <field name="move_type" />
                                <field name="journal_id" />
                            </tree>
                            <form>
                                <sheet>
                                    <group>
                                        <group>
                                            <field name="name" />
                                            <field name="move_date" />
                                            <field name="journal_id" />
                                            <field name="sequence" />
                                        </group>
                                        <group>
                                            <field name="code" />
                                            <field name="inverse" />
                                            <field name="move_type" />
                                        </group>
                                    </group>
                                    <group
                                        name="accounts_mapping"
                                        string="Accounts mapping"
                                    >
                                        <field name="mapping_ids" nolabel="1">
                                            <tree editable="bottom">
                                                <field name="name" />
                                                <field name="src_accounts" />
                                                <field name="dest_account" />
                                            </tree>
                                        </field>
                                    </group>
                                    <group
                                        name="accounts_closing_types"
                                        string="Accounts closing types"
                                    >
                                        <field name="closing_type_default" />
                                    </group>
                                    <group>
                                        <field name="closing_type_ids" nolabel="1">
                                            <tree editable="bottom">
                                                <field name="account_type_id" />
                                                <field name="closing_type" />
                                            </tree>
                                        </field>
                                    </group>
                                </sheet>
                            </form>
                        </field>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record
        id="action_account_fiscalyear_closing_template"
        model="ir.actions.act_window"
    >
        <field name="name">Closing templates</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">account.fiscalyear.closing.template</field>
        <field name="view_mode">tree,form</field>
    </record>

    <menuitem
        id="menu_account_fiscalyear_closing_configuration"
        parent="account.menu_finance_configuration"
        sequence="40"
        name="Fiscal Year Closing"
    />
    <menuitem
        id="menu_account_fiscalyear_closing_template"
        parent="menu_account_fiscalyear_closing_configuration"
        sequence="10"
        name="Closing templates"
        action="action_account_fiscalyear_closing_template"
    />

</odoo>
