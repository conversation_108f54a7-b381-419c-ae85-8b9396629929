<?xml version="1.0" encoding="utf-8" ?>
<!-- License AGPL-3.0 or later (http://www.gnu.org/licenses/agpl). -->
<odoo>

    <record id="view_move_form" model="ir.ui.view">
        <field name="name">account.move.form</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_move_form" />
        <field name="arch" type="xml">
            <field name="ref" position="after">
                <field name="closing_type" />
            </field>
        </field>
    </record>

    <record id="view_account_move_filter" model="ir.ui.view">
        <field name="name">account.move.select</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_account_move_filter" />
        <field name="arch" type="xml">
            <filter name="posted" position="after">
                <filter
                    string="Closing type"
                    name="closing_type"
                    domain="[]"
                    context="{'group_by': 'closing_type'}"
                />
            </filter>
        </field>
    </record>

</odoo>
