# -*- coding: utf-8 -*-
from odoo import models, fields, api, _
import io,logging,traceback
import pdfplumber
import xlwt
import pandas as pd
from odoo.exceptions import UserError, ValidationError
import odoo.addons.base_galaxy.models.galaxy_func as gf
  
class bid_user_access(models.Model):
    _name = 'bid.user.access'
    _description = 'bid user access'
    _rec_name='bid_apply_status'

    customer_id=fields.Many2one('res.partner',string="Name",domain="[('customer_rank','>=',1)]")
    name = fields.Char(string='Customer Name',related="customer_id.name")
    mobile = fields.Char(string='Mobile',related="customer_id.phone")
    bid_user_id = fields.Char(string='Customer Code',related="customer_id.bid_user_id")
    bid_standard_type_id = fields.Many2one('bid.standard.bid.type',string='Partner Bid Type')
    bid_standard_id = fields.Many2one(related="bid_standard_type_id.standard_id",string="Partner Standard")
    bid_type = fields.Selection(string='Bid Type',related="bid_standard_type_id.bid_type")
    bid_apply_status = fields.Selection(string='Status',
        selection=[ ("WFA", "Waiting for approval"), 
                    ("APP", "Approved"),
                    ("REJ", "Rejected"),
                    ("CAN", "Cancelled"),
                    ],
                    required = False,default='WFA')

    active = fields.Boolean(
        'Active', default=True,
        help="If unchecked, it will allow you to hide the bid company without removing it.")
    
    _sql_constraints = [
        ('bid_user_bid_type_uniq', 'unique(customer_id,bid_standard_type_id)', 'Same customer can only has one standard bid type')
    ]

    def action_change_approve_status(self,approvalStatus):
        for item in self:
            if item.bid_apply_status in ('REJ','CAN','WFA') and approvalStatus=='CAN':
                raise UserError('Please note that only the approved applications can be cancelled')
        self.bid_apply_status = approvalStatus
        

    

    
