#!/usr/bin/env python
# coding=utf-8
# author: zhuangweijia

import logging
import datetime
import time
import random
import json
import demjson
from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
# from ...base_galaxy.models.public_func import display_notification
from odoo.tools.profiler import profile
from odoo.addons.base_galaxy.models.public_func import display_notification
from odoo.addons.base_galaxy.models.gol import get_value, set_value, del_value

_logger = logging.getLogger(__name__)


class GalaxyVendorSku(models.Model):
    _inherit = "galaxy.vendor.sku"
    _description = "vendor sku vs galaxy sku"
    _order = 'write_date desc'
    standard_id = fields.Many2one('bid.standard', string='Standard Id')

    supplier_brand = fields.Many2one('supplier.brand', string='Vendor Brand', compute="recommend_supplier_brand",
                                     store=True, readonly=False, ondelete='restrict',
                                     domain="[('standard_id','=',standard_id)]")

    init_tag = fields.Boolean(compute="_first_time_attr_line_recommend")

    vendor_category_id = fields.Many2one(
        'vendor.category', string="Vendor Category", ondelete='restrict', readonly=False, store=True,
        inverse="inverse_get_category_id", compute="recommend_category_id", domain="[('standard_id','=',standard_id)]", index=True)

    verify_state = fields.Selection(
        [('verified', 'Verified'), ('not_verify', 'Not Verify')], default='not_verify', tracking=True)
    spec_attr = fields.Char(string='Spec Type', required=False)
    spec_value = fields.Char(string='Spec Model Identifier', required=False)
    spec_model_number = fields.Char(string='Spec Model Number', required=False)
    spec_part_number = fields.Char(string='Spec Part Number', required=False)
    spec_status = fields.Selection([('not_match', 'Not Match'), ('match', 'Match')],
                                   default='not_match',
                                   string='Spec Status',
                                   )
    spu_match_type = fields.Selection(
        [('bm', 'Brand+Model'), ('aps', 'Apple Spec')], string='SPU Match Type', default='bm')
    apple_spu_id = fields.Many2one(
        'apple.spu', string='Apple SPU', ondelete='restrict', index=True)
    apple_spu_domain_list = fields.Many2many(
        'apple.spu', compute='_compute_apple_spu_domain_list')
    is_apple_spu_readonly = fields.Boolean(string='Apple SPU Readonly', default=False,
                                           compute="_compute_is_apple_spu_readonly", inverse="_inverse_is_apple_spu_readonly")

    _sql_constraints = [
        ('bid_standard_sku_uniq', 'unique(standard_id,item_number)',
         'standard_id+item_number must be unique(ignoring case, ignoring leading and trailing spaces)'),
    ]

    def write(self, vals):
        ret = super(GalaxyVendorSku, self).write(vals)
        for rec in self:
            rec.check_duplicate_attr_lines()
        return ret

    def check_duplicate_attr_lines(self):
        """
        检查是否有重复的属性值
        """
        l = []
        inner_attr = set()
        for i in self.attr_line:
            if i.inner_attr_value.id:
                l.append(i.inner_attr_value.id)
                inner_attr.add(i.inner_attr_value.id)
        if len(l) != len(inner_attr):
            raise models.ValidationError(
                'The attribute values are duplicate, please refresh page and check again')

    def _inverse_is_apple_spu_readonly(self):
        pass
    
    def develop_revise_clover_standard_sku(self):
        """
        初始化开发使用--修正clover标准sku
        更新并归档23年10月1日之前的数据
        """
        for rec in self.env['galaxy.vendor.sku'].search([('standard_id', '=', 5), ('state', '=', 'match'), ('confirm_match_time', '<=', '2023-10-01')]):
            rec.item_number = rec.item_number + '.archive'
            rec.active=False

    def develop_revise_clover_standard_sku_1(self):
        """
        初始化开发使用--修正clover标准sku
        更新并重新触发External SKU No.末尾为"Off"内容的数据
        """
        for rec in self.env['galaxy.vendor.sku'].search([('standard_id', '=', 5), ('state', '=', 'match'), ('item_number', 'ilike', '.Off')]):
            item_number = rec.item_number.replace('.Off', '')
            check_items = rec.env['galaxy.vendor.sku'].search([('item_number', '=', item_number)], order='confirm_match_time desc')
            latest_item = None
            if check_items:
                latest_item = check_items[0]
                old_items = check_items - latest_item
                old_items.write({'item_number': rec.item_number + '.archive', 'active': False})
            need_revise_item = self.env['galaxy.vendor.sku']
            if latest_item:
                print(latest_item)
                print(latest_item.confirm_match_time, rec.confirm_match_time)
                if latest_item.confirm_match_time > rec.confirm_match_time:
                    rec.write({'item_number': rec.item_number + '.archive', 'active': False})
                    need_revise_item = latest_item
                else:
                    latest_item.write({'item_number': latest_item.item_number + '.archive', 'active': False})
                    self.env.cr.commit()
                    rec.write({'item_number': item_number})
                    need_revise_item = rec
                    self.env.cr.commit()
            else:
                rec.write({'item_number': item_number})
                need_revise_item = rec
            '''
                属性更新：
                FMIP/FRP Lock的Vendor Attr Value为"Off"：
                清空Vendor Attr Value
                Internal Attr Value设置为Unknown
            '''
            for attr_line in need_revise_item.attr_line:
                if attr_line.attr.name == 'FMIP/FRP Lock':
                    if not attr_line.supplier_attr_value or attr_line.supplier_attr_value.id == 29453:
                        attr_line.supplier_attr_value = False
                        attr_line.inner_attr_value = 7
            rec.action_confirm_sku_match_record()

    def develop_revise_gamestop_standard_sku_1(self):
        """
        初始化开发使用--修正gamestop标准sku
        更新并重新触发External SKU No.末尾为"Off"内容的数据
        """
        for rec in self.env['galaxy.vendor.sku'].search([('standard_id', '=', 74), ('state', '=', 'match'), ('item_number', 'ilike', '.Off')]):
            item_number = rec.item_number.replace('.Off', '')
            check_items = rec.env['galaxy.vendor.sku'].search([('item_number', '=', item_number)], order='confirm_match_time desc')
            latest_item = None
            if check_items:
                latest_item = check_items[0]
                old_items = check_items - latest_item
                old_items.write({'item_number': rec.item_number + '.archive', 'active': False})
            need_revise_item = self.env['galaxy.vendor.sku']
            if latest_item:
                print(latest_item)
                print(latest_item.confirm_match_time, rec.confirm_match_time)
                if latest_item.confirm_match_time > rec.confirm_match_time:
                    rec.write({'item_number': rec.item_number + '.archive', 'active': False})
                    need_revise_item = latest_item
                else:
                    latest_item.write({'item_number': latest_item.item_number + '.archive', 'active': False})
                    self.env.cr.commit()
                    rec.write({'item_number': item_number})
                    need_revise_item = rec
                    self.env.cr.commit()
            else:
                rec.write({'item_number': item_number})
                need_revise_item = rec
            '''
                属性更新：
                FMIP/FRP Lock的Vendor Attr Value为"Off"：
                清空Vendor Attr Value
                Internal Attr Value设置为Unknown
            '''
            for attr_line in need_revise_item.attr_line:
                if attr_line.attr.name == 'FMIP/FRP Lock':
                    if not attr_line.supplier_attr_value or attr_line.supplier_attr_value.id == 30425:
                        attr_line.supplier_attr_value = False
                        attr_line.inner_attr_value = 7
            rec.action_confirm_sku_match_record()
    
    def develop_revise_alchemy_standard_sku_1(self):
        """
        初始化开发使用--修正alchemy标准sku
        更新并重新触发External SKU No.末尾为"Off"内容的数据
        """
        for rec in self.env['galaxy.vendor.sku'].search([('standard_id', '=', 41), ('state', '=', 'match'), ('item_number', 'ilike', '.Off')]):
            item_number = rec.item_number.replace('.Off', '')
            check_items = rec.env['galaxy.vendor.sku'].search([('item_number', '=', item_number)], order='confirm_match_time desc')
            latest_item = None
            if check_items:
                latest_item = check_items[0]
                old_items = check_items - latest_item
                old_items.write({'item_number': rec.item_number + '.archive', 'active': False})
            need_revise_item = self.env['galaxy.vendor.sku']
            if latest_item:
                print(latest_item)
                print(latest_item.confirm_match_time, rec.confirm_match_time)
                if latest_item.confirm_match_time > rec.confirm_match_time:
                    rec.write({'item_number': rec.item_number + '.archive', 'active': False})
                    need_revise_item = latest_item
                else:
                    latest_item.write({'item_number': latest_item.item_number + '.archive', 'active': False})
                    self.env.cr.commit()
                    rec.write({'item_number': item_number})
                    need_revise_item = rec
                    self.env.cr.commit()
            else:
                rec.write({'item_number': item_number})
                need_revise_item = rec
            '''
                属性更新：
                FMIP/FRP Lock的Vendor Attr Value为"Off"：
                清空Vendor Attr Value
                Internal Attr Value设置为Unknown
            '''
            for attr_line in need_revise_item.attr_line:
                if attr_line.attr.name == 'FMIP/FRP Lock':
                    if not attr_line.supplier_attr_value or attr_line.supplier_attr_value.id == 28895:
                        attr_line.supplier_attr_value = False
                        attr_line.inner_attr_value = 7
            rec.action_confirm_sku_match_record()
    
    def develop_revise_mannapov_standard_sku_1(self):
        """
        初始化开发使用--修正mannapov标准sku
        更新并重新触发External SKU No.末尾为"Off"内容的数据
        """
        count_rec = 0
        verified_rec = {}
        for rec in self.env['galaxy.vendor.sku'].search([('standard_id', '=', 31), ('state', '=', 'match'), ('item_number', 'ilike', 'M')]):
            item_number = rec.item_number
            item_number_array = item_number.split('.')
            if len(item_number_array) > 1:
                first_part = item_number_array[0]
                if first_part.find('M') != -1 and first_part.find('-') != -1:
                    second_part = item_number_array[1]
                    # 弟二列数据为纯数字
                    if second_part.isdigit():
                        new_item_number = first_part + '.' + second_part
                        if new_item_number in verified_rec:
                            if rec.confirm_match_time > verified_rec[new_item_number].confirm_match_time:
                                verified_rec[new_item_number].write({'item_number': new_item_number + '.archive', 'active': False})
                                rec.write({'item_number': new_item_number})
                                verified_rec[new_item_number] = rec
                                count_rec += 1
                            else:
                                rec.write({'item_number': item_number + '.archive', 'active': False})
                                count_rec += 1
                        else:
                            rec.write({'item_number': new_item_number})
                            verified_rec[new_item_number] = rec
                            count_rec += 1
            '''
                属性更新：
                FMIP/FRP Lock的Vendor Attr Value为"Off" 或空：
                清空Vendor Attr Value
                Internal Attr Value设置为Unknown
            '''
            for attr_line in rec.attr_line:
                if attr_line.attr.name == 'FMIP/FRP Lock':
                    if not attr_line.supplier_attr_value or attr_line.supplier_attr_value.id == 25691:
                        attr_line.supplier_attr_value = False
                        attr_line.inner_attr_value = 7
                        rec.action_confirm_sku_match_record()
                        count_rec += 1
            if count_rec >= 100:
                count_rec = 0
                print(f"已处理100条数据")
                self.env.cr.commit()
        self.env.cr.commit()

    def develop_revise_asurion_us_standard_sku_1(self):
        """
        初始化开发使用--修正ausrion US标准sku
        """
        for rec in self.env['galaxy.vendor.sku'].search([('standard_id', '=', 39), ('state', '=', 'match'), 
                                                         '|', '|', ('item_number', 'ilike', '.Unknown'), ('item_number', 'ilike', '.US'), ('item_number', 'ilike', 'US.')]):
            item_number = rec.item_number.replace('.Unknown', '')
            item_number = item_number.replace('.US', '')
            item_number = item_number.replace('US.', '')
            check_items = rec.env['galaxy.vendor.sku'].search([('item_number', '=', item_number)], order='confirm_match_time desc')
            latest_item = None
            if check_items:
                latest_item = check_items[0]
                old_items = check_items - latest_item
                old_items.write({'item_number': rec.item_number + '.archive', 'active': False})
            if latest_item:
                print(latest_item)
                print(latest_item.confirm_match_time, rec.confirm_match_time)
                if latest_item.confirm_match_time > rec.confirm_match_time:
                    rec.write({'item_number': rec.item_number + '.archive', 'active': False})
                else:
                    latest_item.write({'item_number': latest_item.item_number + '.archive', 'active': False})
                    rec.write({'item_number': item_number})
            else:
                rec.write({'item_number': item_number})
            self.env.cr.commit()
            print(f"已处理{rec.item_number}")

    def develop_revise_ecoatm_standard_sku(self):
        """
        初始化开发使用--修正ecoatm标准sku
        更新并重新触发External SKU No.末尾为"Off"内容的数据
        """
        for rec in self.env['galaxy.vendor.sku'].search([('standard_id', '=', 8), ('state', '=', 'match')]):
            '''
            属性更新：
            FMIP/FRP Lock的Vendor Attr Value为"Off"：
            清空Vendor Attr Value
            Internal Attr Value设置为Unknown
            '''
            for attr_line in rec.attr_line:
                if attr_line.attr.name == 'FMIP/FRP Lock':
                    if not attr_line.supplier_attr_value or attr_line.supplier_attr_value.id == 22965:
                        attr_line.supplier_attr_value = False
                        attr_line.inner_attr_value = 7
            rec.action_confirm_sku_match_record()
            print(f"已处理{rec.item_number}")
    
    def develop_revise_hyla_standard_sku(self):
        """
        初始化开发使用--修正Hyla标准sku
        更新并重新触发External SKU No.末尾为“.Off”内容的数据
        """
        need_fix_sku = self.env['galaxy.vendor.sku'].search([('standard_id', '=', 10),
                                                             ('internal_sku', 'ilike', 'FMIP OFF'),
                                                             ('state', '=', 'match')])
        error_rec = []
        count_qty = 0
        for rec in need_fix_sku:
            '''
            属性更新：
            FMIP/FRP Lock的Vendor Attr Value为“Off”：
            触发重新确认
            '''
            for attr_line in rec.attr_line:
                if attr_line.attr.name == 'FMIP/FRP Lock':
                    if attr_line.inner_attr_value.id == 7:
                        print(f"已处理{rec.id}")
                        try:
                            rec.action_confirm_sku_match_record()
                            count_qty += 1
                            if count_qty >= 200:
                                count_qty = 0
                                self.env.cr.commit()
                                print(f"已处理200条数据")
                            break
                        except Exception as e:
                            print(f"处理失败{rec.id}")
                            error_rec.append(rec.id)
                            break
        if error_rec:
            print(f"处理失败的记录：{error_rec}")

    @api.depends('spec_value')
    def _compute_apple_spu_domain_list(self):
        for rec in self:
            apple_spu = rec.get_pt_map_spu(
                rec.spec_value, rec.spec_model_number, rec.spec_part_number)[1]
            if apple_spu:
                rec.apple_spu_domain_list = tuple(rec.get_pt_map_spu(
                    rec.spec_value, rec.spec_model_number, rec.spec_part_number)[1].ids)
            else:
                rec.apple_spu_domain_list = tuple(
                    self.env['apple.spu'].search([]).ids)

    def _compute_is_apple_spu_readonly(self):
        for rec in self:
            spu, apple_spu = rec.get_pt_map_spu(
                rec.spec_value, rec.spec_model_number, rec.spec_part_number)
            if len(apple_spu) == 1:
                rec.is_apple_spu_readonly = True
                rec.apple_spu_id = apple_spu.id
            else:
                rec.is_apple_spu_readonly = False

    @api.onchange('category_id')
    def _onchange_category_id(self):
        if self.category_id:
            self.vendor_category_id.category_id = self.category_id.id

    @api.onchange('apple_spu_id')
    def _onchange_apple_spu_id(self):
        if self.apple_spu_id and self.apple_spu_id.product_tmpl_id:
            self.spu = self.apple_spu_id.product_tmpl_id.id
            self.inner_brand = self.spu.brand.id
            self.inner_model = self.spu.model_name.id
            self.spec_status = 'match'
            self.brand_status = 'match'
            self.model_status = 'match'
            self.category_id = self.spu.categ_id.id
        else:
            self.inner_brand = False
            self.inner_model = False
            self.spu = False
            self.spec_status = 'not_match'
            self.brand_status = 'not_match'
            self.model_status = 'not_match'

    @api.onchange('vendor_category_id')
    def _onchange_vendor_category_id(self):
        if self.vendor_category_id and self.vendor_category_id.category_id:
            self.category_id = self.vendor_category_id.category_id.id

    def action_batch_update_category(self):
        """
        批量更新类别
        """
        standard_ids = tuple(set(self.standard_id.ids))
        standard_ids_count = len(standard_ids)
        res_id = self.env['batch.update.category'].create({
            'standard_ids': standard_ids,
            'standard_ids_count': standard_ids_count,
            'vendor_sku_ids': [(6, 0, self.ids)],
            'vendor_category_id': self[0].vendor_category_id.id,
            'category_id': self[0].category_id.id
        }).id
        return {
            'name': 'Batch Update Category',
            'type': 'ir.actions.act_window',
            'res_model': 'batch.update.category',
            'view_mode': 'form',
            'view_type': 'form',
            'target': 'new',
            'res_id': res_id,
        }

    def action_verify(self):
        if self.state == 'match':
            self.write({'verify_state': 'verified', 'verify_by': self.env.uid,
                       'verify_time': datetime.datetime.now()})
            next_act = {
                'type': 'ir.actions.client',
                'tag': 'jump_to_next',
                'target': 'current'}
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'SKU Verify Result',
                    'message': f"Success: {self.item_number} Verified",
                    'type': 'success',
                    'next': next_act
                }
            }
        else:
            raise models.UserError(_('Not match sku'))

    def action_not_verify(self):
        self.verify_state = 'not_verify'

    @api.onchange('origin_data_brand')
    def recommend_supplier_brand(self):
        for rec in self:
            if not rec.supplier_brand:
                rec.supplier_brand = rec.recommend_supplier_brand_pub(rec)

    @api.depends('vendor_category_id', 'origin_category')
    def inverse_get_category_id(self):
        pass

    @api.depends('vendor_category_id', 'origin_category')
    def recommend_category_id(self):
        for rec in self:
            if not rec.origin_category:
                continue
            if not rec.vendor_category_id:
                rec.vendor_category_id = rec.recommend_supplier_category_pub(
                    rec)
                if not rec.category_id:
                    rec.category_id = rec.vendor_category_id.category_id.id if rec.vendor_category_id else None

    def recommend_supplier_category_pub(self, rec):
        if not rec.origin_category:
            return None
        self.env.cr.execute(""" SELECT name, id
                                    FROM vendor_category
                                    WHERE origin_category=%s
                                        AND standard_id = %s
                                    ORDER BY length(name) desc
                                    LIMIT 1; """, (rec.origin_category, rec.standard_id.id))
        res = self.env.cr.dictfetchall()
        if len(res) > 0:
            return res[0]['id']
        elif rec.standard_id and rec.origin_category:
            self.env.cr.execute(""" SELECT name, id
                                    FROM vendor_category
                                    WHERE POSITION(LOWER(name) in %s) > 0
                                        AND standard_id = %s
                                    ORDER BY length(name) desc
                                    LIMIT 1; """, ((rec.origin_category or '').lower(), rec.standard_id.id))
            res = self.env.cr.dictfetchall()
            return res[0]['id'] if len(res) > 0 else None
        else:
            return None

    def recommend_supplier_brand_pub(self, rec):
        res = self.env['supplier.brand.correspond.rule'].search([('origin_data', '=', rec.origin_data_brand),
                                                                ('standard_id', '=', rec.standard_id.id)], limit=1)
        if res:
            return res.supplier_brand.id
        elif rec.vendor_id:
            self.env.cr.execute(""" SELECT name, id
                                    FROM supplier_brand
                                    WHERE POSITION(LOWER(name) in %s) > 0
                                        AND standard_id = %s
                                    ORDER BY length(name) desc
                                    LIMIT 1; """, ((rec.origin_data_brand or '').lower(), rec.standard_id.id))
            res = self.env.cr.dictfetchall()
            if len(res) > 0:
                rec.supplier_brand = res[0]['id']
            return res[0]['id'] if len(res) > 0 else None
        else:
            rec.supplier_brand = None

    @api.onchange('origin_data_brand', 'supplier_brand', 'inner_brand')
    def recommend_brand_status(self):
        for rec in self:
            rec.brand_status = rec.recommend_brand_status_pub(rec)

    def recommend_brand_status_pub(self, rec):
        res = rec.env['supplier.brand.correspond.rule'].search([('origin_data', '=', rec.origin_data_brand),
                                                                ('supplier_brand', '=',
                                                                 rec.supplier_brand.id),
                                                                ('inner_brand', '=',
                                                                 rec.inner_brand.id),
                                                                ('standard_id', '=', rec.standard_id.id)], limit=1)
        return res.status if res else 'not_match'

    @api.onchange('inner_brand')
    def recommend_supplier_model(self):
        for rec in self:
            if rec.inner_brand:
                rec.supplier_model = rec.recommend_supplier_model_pub(rec)
                rec.brand_status = 'match'
            else:
                rec.supplier_model = None

    def recommend_supplier_model_pub(self, rec):
        my_supplier_model = None
        if rec.inner_brand:
            inner_models = self.env['galaxy.model'].search(
                [('brand', '=', rec.inner_brand.id)])
            vendor_models = self.env['supplier.model'].search([('erp_model', 'in', inner_models.ids),
                                                               ('standard_id', '=', rec.standard_id.id)])
            range_ids = vendor_models.ids
            # 3.查找supplier.model.correspond.rule
            res = self.env['supplier.model.correspond.rule'].search([
                ('origin_data', '=', rec.origin_data_model),
                ('standard_id', '=', rec.standard_id.id)],
                limit=1, order="write_date desc")
            if res:
                if res.supplier_model.id in range_ids:
                    my_supplier_model = res.supplier_model.id
            # 4.根据字符串推荐
            if not my_supplier_model:
                if rec.vendor_id:
                    self.env.cr.execute("""SELECT name, id
                                            FROM supplier_model
                                            WHERE POSITION(LOWER(name) in %s) > 0
                                                AND standard_id = %s
                                                AND erp_model is not null
                                            ORDER BY priority asc,length(name) desc,id desc
                                        """, ((rec.origin_data_model or "").lower(), rec.standard_id.id))
                    res = self.env.cr.dictfetchall()
                    if res:
                        for r in res:
                            if r['id'] in range_ids:
                                my_supplier_model = r['id']
                                break
            # 5.根据内部sku下spu_model_name字段推荐
            if not my_supplier_model:
                if rec.vendor_id:
                    self.env.cr.execute("""SELECT
                                                pt.model_name as model_name,
                                                pp.spu_model_name as spu_model_name
                                            FROM product_product pp
                                            LEFT JOIN product_template pt on pp.product_tmpl_id = pt.id
                                            WHERE length(pp.spu_model_name) > 0
                                                AND POSITION(LOWER(pp.spu_model_name) in %s) > 0
                                            """, ((rec.origin_data_model or "").lower(), ))
                    res = self.env.cr.dictfetchall()  # [{},{}]
                    if res:
                        res_tuple = tuple([i['model_name'] for i in res])
                        if len(res_tuple) > 1:
                            self.env.cr.execute("""SELECT id
                                                    FROM supplier_model
                                                    WHERE standard_id = %s
                                                        AND erp_model in %s
                                                    ORDER BY priority asc,length(name) desc,id desc
                                                    """, (rec.standard_id.id, res_tuple))
                        else:
                            self.env.cr.execute("""SELECT id
                                                    FROM supplier_model
                                                    WHERE standard_id = %s
                                                        AND erp_model in (%s)
                                                    ORDER BY priority asc,length(name) desc,id desc
                                                    """, (rec.standard_id.id, res_tuple[0]))
                        model_res = self.env.cr.dictfetchall()
                        if model_res:
                            for r in model_res:
                                if r['id'] in range_ids:
                                    my_supplier_model = r['id']
                                    break
        return my_supplier_model

    @api.onchange('origin_data_model', 'supplier_model', 'inner_model')
    def recommend_model_status(self):
        for rec in self:
            if not rec.inner_model:
                rec.model_status = rec.recommend_model_status_pub(rec)
            else:
                rec.model_status = 'match'

    def recommend_model_status_pub(self, rec):
        my_model_status = 'not_match'
        res = rec.env['supplier.model.correspond.rule'].search([('origin_data', '=', rec.origin_data_model),
                                                                ('supplier_model', '=',
                                                                 rec.supplier_model.id),
                                                                ('inner_model', '=',
                                                                 rec.inner_model.id),
                                                                ('standard_id', '=', rec.standard_id.id)], limit=1)
        if res:
            my_model_status = res.status
        return my_model_status

    def get_pt_map_spu(self, spec_value, spec_model_number='', spec_part_number=''):
        """
        根据spec_value获取apple_spu_id
        spec_value: model identifier
        """
        if not spec_value and not spec_model_number and not spec_part_number:
            return self.env['product.template'], self.env['apple.spu']
        apple_spu = apple_by_model_identifier_spu = apple_by_model_number_spu = apple_by_part_number_spu = self.env[
            'apple.spu']
        joined_name = '|'.join([item for item in [spec_value, spec_model_number, spec_part_number] if item])
        # 查找供应商spec表
        # 模糊查找
        apple_spu = self.env['standard.spec'].search(
            [('joined_name', 'ilike', joined_name), ('standard_id', '=', self.standard_id.id)]).apple_spu_id
        # if not apple_spu:
        if spec_value:
            apple_by_model_identifier_spu = self.env['standard.spec'].search(
                    [('name', 'ilike', spec_value), ('standard_id', '=', self.standard_id.id)]).apple_spu_id
        if spec_model_number:
            apple_by_model_number_spu = self.env['standard.spec'].search(
                    [('model_number', 'ilike', spec_model_number), ('standard_id', '=', self.standard_id.id)]).apple_spu_id
        if spec_part_number:
            apple_by_part_number_spu = self.env['standard.spec'].search(
                    [('part_number', 'ilike', spec_part_number), ('standard_id', '=', self.standard_id.id)]).apple_spu_id
        # 查找标准apple sepc表
        # 模糊查找
        if spec_value:
            apple_spu += self.env['apple.spu'].search([('apple_spu_spec_ids', 'ilike', spec_value)])
        if spec_model_number:
            apple_spu += self.env['apple.spu'].search([('apple_spu_spec_ids', 'ilike', spec_model_number)])
        if spec_part_number:
            apple_spu += self.env['apple.spu'].search([('apple_spu_spec_ids', 'ilike', spec_part_number)])
        apple_spu_dict = {}
        if apple_spu:
            for item in apple_spu:
                start_len = 3
                joined_name_list = [item1.upper() for item1 in joined_name.split('|')]
                for vendor_spec_name in joined_name_list:
                    if vendor_spec_name in item.joined_name.upper():
                        start_len -= 1
                apple_spu_dict[item] = start_len
            sorted_dict = dict(sorted(apple_spu_dict.items(), key=lambda item: item[1]))
            selected_item = list(sorted_dict.keys())[0]
            # 至少有2个匹配项，可以返回
            if sorted_dict[selected_item] <= 1:
                apple_spu = selected_item
                return apple_spu.product_tmpl_id, apple_spu
        apple_spu += apple_by_model_identifier_spu + apple_by_model_number_spu + apple_by_part_number_spu
        unique_records = {
            record.apple_spu: record for record in apple_spu}.values()
        unique_records = self.env['apple.spu'].browse(
            [record.id for record in unique_records])
        return unique_records.product_tmpl_id, unique_records

    def _check_same_origin_data_records_new(self):
        brand_diff_res = self.env['galaxy.vendor.sku'].search(['|', ('supplier_brand', '!=', self.supplier_brand.id),
                                                               ('inner_brand', '!=',
                                                                self.inner_brand.id),
                                                               ('origin_data_brand', '=',
                                                                self.origin_data_brand),
                                                               ('standard_id', '=',
                                                                self.standard_id.id),
                                                               ('state', '=', 'match')])
        model_diff_res = self.env['galaxy.vendor.sku'].search(['|', ('supplier_model', '!=', self.supplier_model.id),
                                                               ('inner_model', '!=',
                                                                self.inner_model.id),
                                                               ('origin_data_model', '=',
                                                                self.origin_data_model),
                                                               ('standard_id', '=',
                                                                self.standard_id.id),
                                                               ('state', '=', 'match')])
        if brand_diff_res or model_diff_res:
            brand_list = [r.id for r in brand_diff_res]
            model_list = [r.id for r in model_diff_res]
            rec_list = list(set(brand_list + model_list))
            domain = [('id', 'in', rec_list)]
            form_view = self.env.ref('vendor_standard_match_check_form')
            return {
                'type': 'ir.actions.act_window',
                'name': _("Attention!!!"),
                'res_model': 'vendor.sku.match.check',
                'view_mode': 'form',
                'views': [[form_view.id, 'form']],
                'context': {
                    'rec_list': rec_list,
                    'default_standard_id': tuple(rec_list),
                    'ori_sku_id': self.id,
                },
                'target': 'new'
            }
        else:
            return None

    def _check_same_origin_data_records(self):
        brand_diff_res = self.env['galaxy.vendor.sku'].search(['|', ('supplier_brand', '!=', self.supplier_brand.id),
                                                               ('inner_brand', '!=',
                                                                self.inner_brand.id),
                                                               ('origin_data_brand', '=',
                                                                self.origin_data_brand),
                                                               ('standard_id', '=',
                                                                self.standard_id.id),
                                                               ('state', '=', 'match')])
        model_diff_res = self.env['galaxy.vendor.sku'].search(['|', ('supplier_model', '!=', self.supplier_model.id),
                                                               ('inner_model', '!=',
                                                                self.inner_model.id),
                                                               ('origin_data_model', '=',
                                                                self.origin_data_model),
                                                               ('standard_id', '=',
                                                                self.standard_id.id),
                                                               ('state', '=', 'match')])
        if brand_diff_res or model_diff_res:
            brand_list = [r.id for r in brand_diff_res]
            model_list = [r.id for r in model_diff_res]
            rec_list = list(set(brand_list + model_list))
            domain = [('id', 'in', rec_list)]
            list_view = self.env.ref(
                'partner_sku_standard.galaxy_view_partner_sku_domain_tree')
            return {
                'type': 'ir.actions.act_window',
                'name': _("The following records'state will modify to 'Not Match'!"),
                'res_model': 'galaxy.vendor.sku',
                'view_mode': 'list',
                'views': [[list_view.id, 'list']],
                'domain': domain,
                'limit': 80,
                'context': {
                    'search_view_ref': 'partner_sku_standard.product_galaxy_standard_sku_filter_domain',
                    'rec_list': rec_list,
                },
                'target': 'new'
            }
        else:
            return None

    def check_unmatch_records_and_intelligent_complement(self):
        # 1 检查同供应商未匹配数据
        whole_recs = self.env['galaxy.vendor.sku'].search([('standard_id', '=', self.standard_id.id),
                                                           ('state', '=', 'unmatched'), ('id', '!=', self.id)])
        if whole_recs:
            for rec in whole_recs:
                if rec.origin_data_model == self.origin_data_model:
                    rec.write({
                        'supplier_brand': self.supplier_brand,
                        'supplier_model': self.supplier_model,
                        'spu': self.spu.id
                    })
                elif rec.origin_data_brand == self.origin_data_brand:
                    rec.write({
                        'supplier_brand': self.supplier_brand,
                    })
                elif self.supplier_brand.name in rec.origin_data_brand and not rec.supplier_brand:
                    rec.write({
                        'supplier_brand': self.supplier_brand,
                    })
                elif self.supplier_model.name in rec.origin_data_model and not rec.supplier_model:
                    rec.write({
                        'supplier_model': self.supplier_model,
                    })
            self.env.cr.commit()

    def write_back_to_supplier_brand(self):
        stime = time.time()
        if self.origin_data_brand and self.supplier_brand and self.inner_brand:
            if self.supplier_brand.standard_id != self.standard_id:
                res = self.env['supplier.brand'].search([('name', '=', self.supplier_brand.name),
                                                         ('standard_id', '=', self.standard_id.id)], limit=1)
                if res:
                    self.supplier_brand = res
            # update supplier.brand
            if not self.supplier_brand.erp_brand:
                self.supplier_brand.write({'erp_brand': self.inner_brand.id})
            # update supplier.brand.correspond.rule
            vals = {
                'supplier_brand': self.supplier_brand.id,
                'inner_brand': self.inner_brand.id,
                'status': 'match'
            }
            res = self.env['supplier.brand.correspond.rule'].search([('origin_data', '=', self.origin_data_brand),
                                                                     ('standard_id', '=', self.standard_id.id)])
            if res:
                res.write(vals)
            else:
                vals['origin_data'] = self.origin_data_brand
                vals['supplier'] = self.vendor_id.id
                vals['standard_id'] = self.standard_id.id
                self.env['supplier.brand.correspond.rule'].create(vals)
            self.write({'brand_status': 'match'})
        else:
            # origin brand有可能为空，忽略即可
            pass
            # if self.spu_match_type == 'bm':
            #     raise models.ValidationError(
            #         'Brand information is incomplete, please check.')
        _logger.info(
            f"完成write_back_to_supplier_brand,耗时: {time.time() - stime}")

    def write_back_to_vendor_category(self):
        self.ensure_one()
        if self.origin_category and self.vendor_category_id and self.category_id:
            self.vendor_category_id.write({'category_id': self.category_id.id})
            self.write({'category_status': 'match'})

    def write_back_to_standard_spec(self):
        if self.spu_match_type == 'aps':
            if self.apple_spu_id:
                # 回填model identifier
                joined_name = '.'.join([item for item in [self.spec_value, self.spec_model_number, self.spec_part_number] if item])
                standard_apple_spu = self.env['standard.spec'].search(
                    [
                        ('joined_name', '=', joined_name),
                        ('standard_id', '=', self.standard_id.id)])
                if not standard_apple_spu:
                    standard_apple_spu = self.env['standard.spec'].create({
                        'name': self.spec_value,
                        'model_number': self.spec_model_number,
                        'part_number': self.spec_part_number,
                        'standard_id': self.standard_id.id,
                        'vendor_id': self.vendor_id.id,
                        'apple_spu_id': self.apple_spu_id.id
                    })
                else:
                    standard_apple_spu.write({
                        'name': self.spec_value,
                        'model_number': self.spec_model_number,
                        'part_number': self.spec_part_number,
                        'standard_id': self.standard_id.id,
                        'vendor_id': self.vendor_id.id,
                        'apple_spu_id': self.apple_spu_id.id
                    })

    def write_back_to_supplier_brand_new(self, record):
        if record.origin_data_brand and record.supplier_brand and record.inner_brand:
            # update supplier.brand
            if not record.supplier_brand.erp_brand:
                record.supplier_brand.write(
                    {'erp_brand': record.inner_brand.id})
            # update supplier.brand.correspond.rule
            vals = {
                'supplier_brand': record.supplier_brand.id,
                'inner_brand': record.inner_brand.id,
                'status': 'match'
            }
            res = record.env['supplier.brand.correspond.rule'].search([('origin_data', '=', record.origin_data_brand),
                                                                       ('standard_id', '=', record.standard_id.id)])
            if res:
                res.write(vals)
            else:
                vals['origin_data'] = record.origin_data_brand
                vals['supplier'] = self.vendor_id.id
                vals['standard_id'] = self.standard_id.id
                record.env['supplier.brand.correspond.rule'].create(vals)
            record.write({'brand_status': 'match'})
        else:
            # origin brand有可能为空，忽略即可
            pass
            # if self.spu_match_type == 'bm':
            #     raise models.ValidationError(
            #         'Brand information is incomplete, please check.')

    def write_back_to_supplier_model(self):
        stime = time.time()
        if self.origin_data_model and self.supplier_model and self.inner_model:
            if self.supplier_model.standard_id != self.standard_id:
                res = self.env['supplier.model'].search([('name', '=', self.supplier_model.name),
                                                         ('standard_id', '=', self.standard_id.id)], limit=1)
                if res:
                    self.supplier_model = res
            # update supplier.model
            if not self.supplier_model.erp_model:
                self.supplier_model.write({'erp_model': self.inner_model.id})
            # update supplier.model.correspond.rule
            res = self.env['supplier.model.correspond.rule'].search([('origin_data', '=', self.origin_data_model),
                                                                     ('standard_id', '=', self.standard_id.id)])
            vals = {
                'supplier_model': self.supplier_model.id,
                'inner_model': self.inner_model.id,
                'status': 'match'
            }
            if res:
                res.write(vals)
            else:
                vals['origin_data'] = self.origin_data_model
                vals['supplier'] = self.vendor_id.id
                vals['standard_id'] = self.standard_id.id
                self.env['supplier.model.correspond.rule'].create(vals)
            self.write({'model_status': 'match'})
        else:
            # origin brand有可能为空，忽略即可
            pass
            # if self.spu_match_type == 'bm':
            #     raise models.ValidationError(
            #         'Model information is incomplete, please check.')
        _logger.info(
            f"完成write_back_to_supplier_model,耗时: {time.time() - stime}")

    def write_back_to_supplier_model_new(self, record):
        if record.origin_data_model and record.supplier_model and record.inner_model:
            # update supplier.model
            if not record.supplier_model.erp_model:
                record.supplier_model.write(
                    {'erp_model': record.inner_model.id})
            # update supplier.model.correspond.rule
            res = record.env['supplier.model.correspond.rule'].search([('origin_data', '=', record.origin_data_model),
                                                                       ('standard_id', '=', record.standard_id.id)])
            vals = {
                'supplier_model': record.supplier_model.id,
                'inner_model': record.inner_model.id,
                'status': 'match'
            }
            if res:
                res.write(vals)
            else:
                vals['origin_data'] = record.origin_data_model
                vals['supplier'] = self.vendor_id.id
                vals['standard_id'] = record.standard_id.id
                record.env['supplier.model.correspond.rule'].create(vals)
            record.write({'model_status': 'match'})
        else:
            # origin brand有可能为空，忽略即可
            pass
            # if self.spu_match_type == 'bm':
            #     raise models.ValidationError(
            #         'Model information is incomplete, please check.')

    def name_get(self):
        res = []
        for item in self:
            sku = item.internal_sku.name_get(attr_line
                                             )[0][1] if item.internal_sku else item.item_number or 'unknown sku'
            res.append((item.id, sku))
        return res

    def develop_set_current_category_id(self):
        """
        初始化开发使用--设置默认的category_id
        """
        for rec in self.search([]):
            _logger.info(rec.vendor_sku_reference)
            if rec.state == 'match':
                rec.category_id = rec.inner_model.category.id if rec.inner_model else None
            else:
                if not rec.origin_category:
                    rec.origin_category = rec.vendor_sku_reference

    # @profile
    def action_confirm_sku_match_record(self):
        _logger.info(
            f"1****** {self.item_number}开始confirm SKU记录: {time.strftime('%Y-%m-%d %H:%M:%S',time.localtime(time.time()))}, 当前时间戳: {time.time()}"
        )
        stime = time.time()
        self._check_sku_match_fields_before_confirm()
        self._check_sku_records()
        self.write_back_to_product_attribute_value_alias()
        self.write_back_to_supplier_brand()
        self.write_back_to_supplier_model()
        self.update_galaxy_vendor_sku_attr_line()
        # self.check_unmatch_records_and_intelligent_complement()
        self.write_back_to_vendor_category()
        self.write_back_to_standard_spec()
        self.write({'state': 'match', 'confirm_match_by': self.env.uid,
                   'confirm_match_time': datetime.datetime.now()})
        _logger.info(f"完成confirm SKU记录,耗时: {time.time() - stime}")
        next_act = {'type': 'ir.actions.client',
                    'tag': 'jump_to_next', 'target': 'current'}
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'SKU Confirm Result',
                'message': f"Success: {self.item_number} Confirmed",
                'type': 'success',
                'next': next_act
            }
        }

    def force__first_time_attr_line_recommend(self):
        for rec in self:
            rec.with_context(
                force_recompute=True)._first_time_attr_line_recommend()
        self.env.user.notify_success(
            message='Force First Time Attr Line Recommend Success')

    def _first_time_attr_line_recommend(self):
        for rec in self:
            force_recompute = self.env.context.get('force_recompute', False)
            stime = time.time()
            if (rec.state == 'match' or rec.attr_init_tag) and not force_recompute:
                rec.init_tag = False
                return
            _logger.info(
                f"进入SKU首次推荐: {time.strftime('%Y-%m-%d %H:%M:%S',time.localtime(time.time()))}")
            # 品类推荐
            if not rec.vendor_category_id:
                rec.vendor_category_id = rec.recommend_supplier_category_pub(
                    rec)
                if rec.vendor_category_id and rec.vendor_category_id.category_id:
                    rec.category_id = rec.vendor_category_id.category_id
            # 填充supplier_brand 和 supplier_model
            if not rec.supplier_brand:
                rec.supplier_brand = rec.recommend_supplier_brand_pub(rec)
            if not rec.supplier_model:
                rec.supplier_model = rec.recommend_supplier_model_pub(rec)
            # SPU推荐
            if rec.spu_match_type == 'bm':
                # 品牌+型号 推荐模式
                if rec.supplier_brand:
                    rec.inner_brand = rec.recommend_inner_brand_pub(rec)
                if rec.supplier_brand and rec.inner_brand:
                    rec.brand_status = rec.recommend_brand_status_pub(rec)
                if rec.supplier_brand and rec.inner_brand:
                    rec.supplier_model = rec.recommend_supplier_model_pub(rec)
                if rec.supplier_brand and rec.inner_brand and rec.supplier_model:
                    rec.inner_model = rec.recommend_inner_model_pub(rec)
                if rec.supplier_model and rec.inner_model:
                    rec.model_status = rec.recommend_model_status_pub(rec)
                _logger.info(f"-tag 1,首次推荐到机型耗时：{time.time() - stime}")
                # 如果匹配类型是品牌+型号，使用品牌+型号推荐SPU，如果匹配类型是Apple Spec，使用Apple Spec推荐SPU
                if rec.inner_brand and rec.inner_model:
                    rec.spu = rec.recommend_spu_pub(rec)
                _logger.info(f"-tag 2,首次推荐到SPU耗时：{time.time() - stime}")
            elif rec.spu_match_type == 'aps':
                # Apple Spec 推荐模式
                spu, apple_spu = rec.get_pt_map_spu(
                    rec.spec_value, rec.spec_model_number, rec.spec_part_number)
                if len(spu) == 1:
                    rec.inner_brand = spu.brand.id
                    rec.inner_model = spu.model_name.id
                    rec.category_id = spu.categ_id.id
                    rec.spu = spu.id
                    rec.apple_spu_id = apple_spu.id
                    rec.is_apple_spu_readonly = True
                    rec.spec_status = 'match'
                    rec.brand_status = 'match'
                    rec.model_status = 'match'
                elif len(spu) > 1:
                    rec.is_apple_spu_readonly = False
                    rec.spu = False
                    rec.apple_spu_id = False
                else:
                    rec.is_apple_spu_readonly = False
                    rec.spu = False
                    rec.apple_spu_id = False
            rec.recommend_attr_line_and_vendor_sku()

    def recommend_attr_line_and_vendor_sku(self):
        # 如果已经推荐到SPU，填充属性值
        stime = time.time()
        rec = self
        if rec.spu:
            rec.attr_line = [(6, 0, [])]
            rec.attr_line = rec.recommend_attr_line_pub(rec)
        _logger.info(f"-tag 3,首次推荐到ATTR LINE耗时：{time.time() - stime}")
        if rec.spu and rec.attr_line.inner_attr_value:
            rec.model_number = rec.search_spu_model_number_pub(rec)
        _logger.info(f"-tag 4,首次推荐到MODEL NUMBER耗时：{time.time() - stime}")
        # 获取vendor sku数据
        if not rec.vendor_sku:
            rec.vendor_sku = rec.concat_vendor_sku_pub(rec)
        _logger.info(f"-tag 5,首次推荐到VENDOR SKU耗时：{time.time() - stime}")
        rec.init_tag = False
        rec.write({'attr_init_tag': True})
        _logger.info(
            f"2****** 完成SKU首次推荐,耗时: {time.time() - stime}, 当前时间戳: {time.time()}")

    def develop_revise_verizon_standard_sku(env):
        """
        初始化开发使用--修正 Verizon 标准 SKU
        处理 Verizon 运营商相关的 Carrier Lock 属性修正
        """
        # 查找 Verizon 且包含 iPhone 的记录
        for rec in env['galaxy.vendor.sku'].search([
            ('standard_id', '=', 14),  # Verizon 的 standard_id
            ('inner_model.name', 'ilike', 'iPhone'),
        ]):
            need_confirm = False
            carrier_attr_line = None
            carrier_lock_attr_line = None
            # 遍历属性行查找 Carrier 和 Carrier Lock 属性
            for attr_line in rec.attr_line:
                if attr_line.attr.name == 'Carrier':
                    carrier_attr_line = attr_line
                elif attr_line.attr.name == 'Carrier Lock':
                    carrier_lock_attr_line = attr_line
                    
            if carrier_attr_line and carrier_lock_attr_line:
                # 情况1: Carrier 为 Verizon，设置 Carrier Lock 为 Unknown
                if carrier_attr_line.inner_attr_value.name.lower() == 'verizon':
                    carrier_lock_attr_line.write({
                        'inner_attr_value': 8,  # Unknown 的 ID
                        'supplier_attr_value': False
                    })
                    need_confirm = True
                    
                # 情况2: Carrier 不为 Verizon 且不为 Unknown，且 Carrier Lock 为 Unlocked
                elif (carrier_attr_line.inner_attr_value.name.lower() != 'verizon' and 
                      carrier_attr_line.inner_attr_value.name.lower() != 'unknown' and 
                      carrier_lock_attr_line.inner_attr_value.name.lower() == 'unlocked'):
                    carrier_lock_attr_line.write({
                        'inner_attr_value': 41,  # Locked 的 ID
                        'supplier_attr_value': False
                    })
                    need_confirm = True
                
            # 如果有修改，重新触发 confirm 流程
            if need_confirm:
                rec.action_confirm_sku_match_record()
                env.cr.commit()
