# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo import _, api, fields, models
from odoo.exceptions import UserError, ValidationError


class ProductionLot(models.Model):
    _name = 'stock.production.lot'
    _inherit = 'stock.production.lot'

    name = fields.Char(
        'Lot/Serial Number', default=lambda self: self.with_context(galaxy_base_32=True).env['ir.sequence'].next_by_code('GSN'),
        required=True, help="Unique Lot/Serial Number", index=True, copy=False)
    product_id = fields.Many2one(
        'product.product', 'Product',
        domain=lambda self: self._domain_product_id(), required=False, check_company=True, index=True)
    glot_number = fields.Char(
        'GLOT Number',
        required=False,
        help="GLOT Number",
        copy=False,
        index=True,
    )
    won_order_lot_line_id = fields.Many2one("bid.won.order.line.detail", index=True)
    purchase_order_line_id = fields.Many2one('purchase.order.line', string='Purchase Order Line')
    purchase_price = fields.Float('Purchase Price', digits='Product Price', help="这个价格是采购单价，是供应商币种的价格")
    vendor_currency_id = fields.Many2one('res.currency', 'Vendor Currency', help="供应商币种")
    to_hkd_exchange_rate = fields.Float('HKD Exchange Rate', help="HKD Exchange Rate")
    warehouse_date = fields.Date()
    serial_number = fields.Char(index=True)
    imei = fields.Char(index=True)

    state = fields.Selection([
        ('not_used', 'Not Used'),
        ('store_count', 'Used'),
        ('put_away', 'Put Away'),
        ('pick', 'Picked'),
        ('delivery', 'Delivered'),
        ],
        default='not_used')

    def develop_set_up_warehouse_date(self):
        """
        开发使用，修改历史数据，获取warehouse_date
        """
        need_fix_lots = self.search([('quant_ids.location_id.usage', '=', 'internal'),
                                     ('quant_ids.box_state', '=', 'store'),
                                     ('warehouse_date', '=', False),
                                     ('state', '=', 'store_count')])
        for lot in need_fix_lots:
            # 按box收货的可以用下面的查询
            unit_id = self.env['galaxy.stock.picking.unit'].search([('gsn_id', '=', lot.id)], limit=1)
            if unit_id:
                print('unit_id', unit_id)
                lot.warehouse_date = unit_id.box_id.pick_up_date or unit_id.write_date
            else:
                # 按package收货的可以用下面的查询
                package_id = self.env['stock.quant'].search([('location_id.usage', '=', 'internal'), ('lot_id', '=', lot.id)], limit=1).package_id
                if package_id:
                    print('package_id', package_id)
                    lot.warehouse_date = package_id.galaxy_box_ids[0].pick_up_date or package_id.write_date

    def action_print_gsn(self, print_mode):
        """
        Print Goods SN
        """
        can_not_be_print_gsn = self.filtered(lambda lot: lot.state != 'not_used').mapped('name')
        can_be_print_gsn = self.filtered(lambda lot: lot.state == 'not_used')
        if len(can_not_be_print_gsn) > 0:
            error_gsn = ', '.join(can_not_be_print_gsn)
            raise ValidationError(_(f'Used GSN {error_gsn} can not be print'))
        if len(can_be_print_gsn) == 0:
            raise ValidationError(_('Please select GRN to print'))
        # grn_code_data_list = self.env['batch.create.gsn.wizard'].convert_to_dict_list(can_be_print_gsn.mapped('name'), print_mode)
        self.env.user.notify_success(_('Print GSN successfully'))
        return self.env['batch.create.gsn.wizard'].action_print_grn_code_local(can_be_print_gsn, print_mode)
