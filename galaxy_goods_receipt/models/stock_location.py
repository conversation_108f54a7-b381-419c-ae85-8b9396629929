from odoo import models, api, fields


class StockLocation(models.Model):
    _inherit = 'stock.location'

    name = fields.Char('Location Name', required=True, index=True)
    is_over_delivery_location = fields.Boolean(
        string='Is a Over Delivery Location?',
        default=False,
        help='If the location is a over delivery location, the system will ingore the location when pack.'
    )

    @api.model
    def name_search(self, name, args=None, operator='ilike', limit=100):
        method_id = self._context.get('galaxy_picking_method_id')
        if method_id:
            method = self.env['picking.method.wizard'].search([('id', '=', method_id)], limit=1)
            location_ids = method.pre_shelves_ids.mapped('location_id').ids
            args += [('id', 'in', location_ids)]

        from_picking_path = self.env.context.get('from_picking_path')
        if from_picking_path:
            pickings = self.env['picking.path'].search([])
            args += [('id', 'not in', pickings.mapped('location_id.id'))]
        return super(StockLocation, self).name_search(name, args=args, operator=operator, limit=limit)

    def search_read(self, domain=None, fields=None, offset=0, limit=None, order=None):
        method_id = self._context.get('galaxy_picking_method_id')
        if method_id:
            method = self.env['picking.method.wizard'].search([('id', '=', method_id)], limit=1)
            location_ids = method.pre_shelves_ids.mapped('location_id').ids
            domain += [('id', 'in', location_ids)]

        from_picking_path = self.env.context.get('from_picking_path')
        if from_picking_path:
            pickings = self.env['picking.path'].search([])
            domain.append(('id', 'not in', pickings.mapped('location_id.id')))
        return super(StockLocation, self).search_read(domain=domain, fields=fields, offset=offset, limit=limit, order=order)