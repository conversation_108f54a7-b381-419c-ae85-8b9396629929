import json

with open('/home/<USER>/odoo/fork_galaxy_addons/galaxy_goods_receipt/data/glot.txt', 'r') as file:
    lines = file.readlines()

# 过滤出非空行并清理空白字符
glot_numbers = [line.strip() for line in lines if line.strip() and not line.startswith('//')]

# 转换为JSON并保存
with open('glot_numbers.json', 'w') as out_file:
    json.dump(glot_numbers, out_file, indent=2)

print(glot_numbers)

print(f"已转换 {len(glot_numbers)} 个Glot编号为JSON格式")
