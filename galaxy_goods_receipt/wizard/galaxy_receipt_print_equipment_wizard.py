from odoo import models, fields, api, _
from odoo.exceptions import UserError
import json


class GalaxyReceiptPrintEquipmentWizard(models.TransientModel):
    _name = 'galaxy.receipt.print.equipment.wizard'
    _description = 'Print Equipment Wizard'

    count_order_id = fields.Many2one(
        'galaxy.stock.count.order',
        string='Count Order',
    )
    sn_rules = fields.Text()

    equipment_submit = fields.Char(string='Equipment Input')
    equipment = fields.Char(string='Equipment Code')

    error_message = fields.Char(string='Error Message')
    info_message = fields.Char(string='Info Message')

    print_unit_data = fields.Text()

    @api.onchange('equipment_submit')
    def _onchange_equipment_submit(self):
        if self.equipment_submit == False:
            return
        equipment = self.equipment_submit.strip()
        initial_values = {
            'equipment': '',
            'equipment_submit': '',
            'error_message': '',
            'info_message': '',
            'print_unit_data': '',
        }

        # 机身码处理, 增加处理多个可能性匹配，用|分隔
        equipment_code_list = equipment.split('|')

        # 按照优先级依次查找: equipment_code -> imei -> serial_number
        pick_units = self.env['galaxy.stock.picking.unit'].search([
            ('count_id', '=', self._origin.count_order_id.id),
            '|',
            ('equipment_code', 'in', equipment_code_list),
            '|',
            ('imei', 'in', equipment_code_list),
            ('serial_number', 'in', equipment_code_list)
        ])

        if pick_units:
            # 按优先级顺序筛选匹配的记录
            for code in equipment_code_list:
                # 优先检查 equipment_code
                equipment_matches = pick_units.filtered(lambda x: x.equipment_code == code)
                if equipment_matches:
                    pick_units = equipment_matches
                    break

                # 其次检查 imei
                imei_matches = pick_units.filtered(lambda x: x.imei == code)
                if imei_matches:
                    pick_units = imei_matches
                    break

                # 最后检查 serial_number
                serial_matches = pick_units.filtered(lambda x: x.serial_number == code)
                if serial_matches:
                    pick_units = serial_matches
                    break

        # pick_unit = self.env['galaxy.stock.picking.unit'].search([('count_id', '=', self.count_order_id.id), ('equipment_code', '=', equipment)])
        if not pick_units:
            initial_values.update({
                'error_message': _("%s 不存在" % equipment),
            })
            self.write(initial_values)
            return False

        if len(pick_units) == 1:
            pick_unit = pick_units[0]
        else:
            initial_values.update({
                'error_message': _("%s 匹配到多個機身碼,請人工輸入機身碼" % equipment_code_list),
            })
            self.write(initial_values)
            return
        
        if pick_unit.state == 'wait_pick':
            initial_values.update({
                'error_message': _("%s 未點貨" % equipment),
            })
            self.write(initial_values)
            return False

        initial_values.update({
            'info_message': _("%s 掃碼成功" % equipment),
            'print_unit_data': json.dumps(pick_unit.prepare_print_data()),
        })
        self.write(initial_values)
        return
