<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
         <!--同步管家婆状态为1的销售单-->
        <record id="sync_gjp_sale_order_data" model="ir.cron">
            <field name="name">Sync GJP Sale Order Data</field>
            <field name="model_id" ref="galaxy_account_bank_statement_r2.model_gjp_sale_order"/>
            <field name="state">code</field>
            <field name='active'>True</field>
            <field name="code">model.sync_gjp_sale_order()</field>
            <field name="interval_number">5</field>
            <field name="interval_type">minutes</field>
            <field name="numbercall">-1</field>
            <field name="nextcall" eval="(DateTime.now() + timedelta(minutes=5)).replace(minute=0, second=0, microsecond=0)"/>
        </record>
    </data>
    <data noupdate="1">
         <!--发送预收款邮件-->
        <record id="send_advance_payment_customer_report" model="ir.cron">
            <field name="name">Send Advance Payment Customer Report</field>
            <field name="model_id" ref="galaxy_account_bank_statement_r2.model_res_partner"/>
            <field name="state">code</field>
            <field name='active'>True</field>
            <field name="code">model.send_advance_balance_report_email_auto()</field>
            <field name="interval_number">1</field>
            <field name="interval_type">minutes</field>
            <field name="numbercall">-1</field>
            <field name="nextcall" eval="(DateTime.now() + timedelta(minutes=5)).replace(minute=0, second=0, microsecond=0)"/>
        </record>
    </data>
</odoo>
