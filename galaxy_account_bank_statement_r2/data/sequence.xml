<odoo>
	<data noupdate='1'>
        <record id="gjp_order_summary_seqn" model="ir.sequence">
            <field name="name">gjp_order_summary_seqn</field>
            <field name="code">gjp.order.summary.seqn</field>
            <field name="prefix">SS</field>
            <field eval="1" name="number_next"/>
            <field name="padding" eval="3"/>
            <field eval="1" name="number_increment"/>
            <field eval="False" name="company_id"/>
        </record>
        <record id="customer_statement_seqn" model="ir.sequence">
            <field name="name">customer_statement_seqn</field>
            <field name="code">customer.statement.seqn.sn</field>
            <field name="prefix">ST</field>
            <field eval="1" name="number_next"/>
            <field name="padding" eval="3"/>
            <field eval="1" name="number_increment"/>
            <field eval="False" name="company_id"/>
        </record>
        <record id="galaxy_fund_transfer_order_seq" model="ir.sequence">
            <field name="name">Fund Transfer Order</field>
            <field name="code">galaxy.fund.transfer.order</field>
            <field name="prefix">TO</field>
            <field name='padding'>3</field>
            <field eval="1" name="number_next"/>
            <field eval="1" name="number_increment"/>
            <field eval="False" name="company_id"/>        
        </record>
        <record id="galaxy_gjp_order_invoice_seq" model="ir.sequence">
            <field name="name">Customer Invoice</field>
            <field name="code">galaxy.gjp.order.invoice</field>
            <field name="prefix">INV</field>
            <field name='padding'>7</field>
            <field eval="1" name="number_next"/>
            <field eval="1" name="number_increment"/>
            <field eval="True" name="company_id"/>        
        </record>
	</data>
</odoo>
