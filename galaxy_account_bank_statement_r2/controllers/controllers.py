# -*- coding: utf-8 -*-
# from odoo import http


# class AccountBankStatement(http.Controller):
#     @http.route('/account_bank_statement/account_bank_statement/', auth='public')
#     def index(self, **kw):
#         return "Hello, world"

#     @http.route('/account_bank_statement/account_bank_statement/objects/', auth='public')
#     def list(self, **kw):
#         return http.request.render('account_bank_statement.listing', {
#             'root': '/account_bank_statement/account_bank_statement',
#             'objects': http.request.env['account_bank_statement.account_bank_statement'].search([]),
#         })

#     @http.route('/account_bank_statement/account_bank_statement/objects/<model("account_bank_statement.account_bank_statement"):obj>/', auth='public')
#     def object(self, obj, **kw):
#         return http.request.render('account_bank_statement.object', {
#             'object': obj
#         })
