# -*- coding: utf-8 -*-

import binascii
from odoo import http, fields, _, SUPERUSER_ID
from odoo.http import request
from odoo.addons.portal.controllers.portal import CustomerPortal, pager as portal_pager, get_records_pager
from odoo.addons.portal.controllers.mail import _message_post_helper
from odoo.exceptions import AccessError, MissingError


class CustomerPortal(CustomerPortal):

    def _prepare_home_portal_values(self, counters):
        values = super()._prepare_home_portal_values(counters)
        partner = request.env.user.partner_id

        CustomerSettlement = request.env['customer.settlement']
        if 'settlement_count' in counters:
            values['settlement_count'] = CustomerSettlement.search_count(self._prepare_settlements_domain(partner)) \
                if CustomerSettlement.check_access_rights('read', raise_exception=False) else 0
        return values

    def _prepare_settlements_domain(self, partner):
        return [
            ('message_partner_ids', 'child_of', [partner.commercial_partner_id.id]),
            ('state', 'in', ['confirm', 'audited', 'partial_paid', 'paid'])
        ]

    def _get_settlement_searchbar_sortings(self):
        return {
            'date': {'label': _('Create Date'), 'order': 'create_date desc'},
            'name': {'label': _('Reference'), 'order': 'name'},
            'state': {'label': _('Status'), 'order': 'state'},
        }

    def _settlement_get_page_view_values(self, settlement, access_token, **kwargs):
        """
        Get values for settlement portal page view
        """
        values = {
            'settlement': settlement,
            'token': access_token,
            'bootstrap_formatting': True,
            'report_type': 'html',
        }
        
        # if settlement.company_id:
        #     values['res_company'] = settlement.company_id

        return values

    @http.route(['/my/settlements', '/my/settlements/page/<int:page>'], type='http', auth="user", website=True)
    def portal_my_settlements(self, page=1, date_begin=None, date_end=None, sortby=None, **kw):
        values = self._prepare_portal_layout_values()
        partner = request.env.user.partner_id
        CustomerSettlement = request.env['customer.settlement']

        domain = self._prepare_settlements_domain(partner)

        searchbar_sortings = self._get_settlement_searchbar_sortings()

        # default sortby order
        if not sortby:
            sortby = 'date'
        sort_order = searchbar_sortings[sortby]['order']

        if date_begin and date_end:
            domain += [('create_date', '>', date_begin), ('create_date', '<=', date_end)]

        # count for pager
        settlement_count = CustomerSettlement.search_count(domain)
        # make pager
        pager = portal_pager(
            url="/my/settlements",
            url_args={'date_begin': date_begin, 'date_end': date_end, 'sortby': sortby},
            total=settlement_count,
            page=page,
            step=self._items_per_page
        )
        # search the count to display, according to the pager data
        settlements = CustomerSettlement.search(domain, order=sort_order, limit=self._items_per_page, offset=pager['offset'])
        request.session['my_settlements_history'] = settlements.ids[:100]

        values.update({
            'date': date_begin,
            'settlements': settlements.sudo(),
            'page_name': 'settlement',
            'pager': pager,
            'default_url': '/my/settlements',
            'searchbar_sortings': searchbar_sortings,
            'sortby': sortby,
        })
        return request.render("galaxy_account_bank_statement_r2.portal_my_settlements", values)

    @http.route(['/my/settlements/<int:settlement_id>'], type='http', auth="public", website=True)
    def portal_settlement_page(self, settlement_id, report_type=None, access_token=None, message=False, download=False, **kw):
        try:
            settlement_sudo = self._document_check_access('customer.settlement', settlement_id, access_token=access_token)
        except (AccessError, MissingError):
            return request.redirect('/my')

        if report_type in ('html', 'pdf', 'text'):
            return self._show_report(model=settlement_sudo, report_type=report_type, report_ref='galaxy_account_bank_statement_r2.action_customer_settlement_report', download=download)

        values = self._settlement_get_page_view_values(settlement_sudo, access_token, **kw)
        values['message'] = message

        # Log only once a day
        if settlement_sudo:
            now = fields.Date.today().isoformat()
            session_obj_date = request.session.get('view_settlement_%s' % settlement_sudo.id)
            if session_obj_date != now and request.env.user.share and access_token:
                request.session['view_settlement_%s' % settlement_sudo.id] = now
                body = _('Settlement viewed by customer %s', settlement_sudo.payer_id.name)
                settlement_sudo.message_post(
                    body=body,
                    message_type="notification",
                    subtype_xmlid="mail.mt_note",
                    # partner_ids=settlement_sudo.user_id.sudo().partner_id.ids,
                )

        return request.render('galaxy_account_bank_statement_r2.portal_settlement_page', values)

    @http.route(['/my/settlements/<int:settlement_id>/accept'], type='json', auth="public", website=True)
    def portal_settlement_accept(self, settlement_id, access_token=None, name=None, signature=None):
        """
        Accept settlement with signature
        """
        # get from query string if not on json param
        access_token = access_token or request.httprequest.args.get('access_token')
        try:
            settlement_sudo = self._document_check_access('customer.settlement', settlement_id, access_token=access_token)
        except (AccessError, MissingError):
            return {'error': _('Invalid settlement.')}

        if not settlement_sudo.has_to_be_signed():
            return {'error': _('The settlement is not in a state requiring customer signature.')}
        if not signature:
            return {'error': _('Signature is missing.')}

        try:
            settlement_sudo.write({
                'signed_by': name,
                'signed_on': fields.Datetime.now(),
                'signature': signature,
                'require_signature': False,
            })
            request.env.cr.commit()
        except (TypeError, binascii.Error) as e:
            return {'error': _('Invalid signature data.')}

        _message_post_helper(
            'customer.settlement', settlement_sudo.id, _('Settlement signed by %s') % (name,),
            **({'token': access_token} if access_token else {}))
        settlement_sudo.action_audited()

        query_string = '&message=sign_ok'
        return {
            'force_refresh': True,
            'redirect_url': settlement_sudo.get_portal_url(query_string=query_string),
        }
        
    @http.route(['/my/settlements/<int:settlement_id>/summary/<int:summary_id>'], type='http', auth='public')
    def settlement_summary_pdf(self, settlement_id, summary_id, access_token=None, **kw):
        """Download PDF for Sale Order Summary with token check."""
        # 校验 access_token 权限
        settlement = request.env['customer.settlement'].sudo().browse(settlement_id)
        if not settlement or settlement.access_token != access_token:
            # 权限校验失败
            return request.not_found()

        # 校验 summary_id 是否属于该 settlement
        summary = request.env['gjp.sale.order.summary'].sudo().browse(summary_id)
        if not summary or summary not in settlement.customer_statement_line_ids.mapped('gjp_sale_order_summary_id'):
            return request.not_found()

        # 渲染 PDF
        report_action = request.env.ref('galaxy_account_bank_statement_r2.action_report_sale_order_summary')
        if not report_action:
            return request.not_found()

        superuser_env = report_action.with_user(user=SUPERUSER_ID).sudo()
        pdf_content, report_type = superuser_env._render_qweb_pdf([summary.id])
        pdfhttpheaders = [
            ('Content-Type', 'application/pdf'),
            ('Content-Length', len(pdf_content)),
            ('Content-Disposition', 'attachment; filename="Sale_Order_Summary_%s.pdf"' % summary.name),
        ]
        return request.make_response(pdf_content, headers=pdfhttpheaders)