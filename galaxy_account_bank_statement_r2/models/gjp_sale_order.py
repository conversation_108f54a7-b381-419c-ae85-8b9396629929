# -*- coding: utf-8 -*-

import logging
import qrcode
import base64
from io import BytesIO

from odoo import models, fields, api, _
from .gjp_query_obj import GJPQuery

_logger = logging.getLogger(__name__)

class GjpSaleOrder(models.Model):
    _name = 'gjp.sale.order'
    _description = 'GJP Sale Order'
    _rec_name = 'gjp_order_no'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'id desc'

    gjp_order_no = fields.Char(string='GJP Order Number', required=True, index=True)
    gjp_order_sequence = fields.Char(string='GJP Order Sequence', index=True, compute='_compute_gjp_order_sequence', store=True)
    audit_level = fields.Char(string='Audit Level', tracking=True)
    glot = fields.Char(string='GLOT')
    dept = fields.Char(string='Department')
    user_code = fields.Char(string='User Code')
    order_summary = fields.Char(string='Order Summary')
    sale_person = fields.Char(string='Salesperson')
    delivery_number = fields.Char(string='Delivery Number')
    customer_id = fields.Many2one('res.partner', string='Customer', required=True, index=True)
    amount = fields.Float(string='Amount', compute="_compute_amount", store=True, digits=(12, 2))
    paid_amount = fields.Float(string='Paid Amount', default=0.0, tracking=True, digits=(12, 2))
    state = fields.Selection([
        ('not_paid', 'Not Paid'),
        ('partial_paid', 'Partial Paid'),
        ('paid', 'Paid'),
        ('cancelled', 'Cancelled')
    ], string='Status', default='not_paid', index=True, store=True, tracking=True, compute='_compute_get_state', inverse="_inverset_set_state")
    quantity = fields.Integer(string='Quantity', compute='_compute_quantity', store=True)
    customer_security_code = fields.Char(
        string='Delivery Verify Code',
        size=6,
        tracking=True,
        readonly=True,
        help='This is the 6-digit security code for the customer, used for delivery verification purposes.')
    odoo_so_number = fields.Char(string='Odoo SO Number', index=True)
    detail_line_ids = fields.One2many(
        'gjp.sale.order.line', 'gjp_sale_order_id',
        string='Detail Lines', copy=True)
    notes = fields.Text(string='Notes', help='Notes')
    sale_summary_order_id = fields.Many2one(
        'gjp.sale.order.summary', string='Sale Summary Order', index=True)
    stock_name = fields.Char(
        string='Stock Name',
        help='This field is used to store the stock name associated with the GJP Sale Order.')
    paid_date = fields.Datetime(string="Paid Date", tracking=True)
    invoice_number = fields.Char(
        string='Invoice Number',
        help='This field is used to store the invoice number associated with the GJP Sale Order.')
    invoice_qr_img = fields.Binary(
        string='QR Code', compute='_compute_invoice_qr_img',
        help='QR Code for the GJP Order Number'
    )
    invoice_date = fields.Date(
        string='Invoice Date',
        help='This field is used to store the invoice date associated with the GJP Sale Order.')

    invoice_res_partner_id = fields.Many2one(
        'res.partner', string='Invoice Partner',
        domain="[('parent_id', '=', customer_id)]",
        index=True,
        help='This field is used to store the partner associated with the invoice for the GJP Sale Order.',
    )
    
    @api.depends('invoice_number')
    def _compute_invoice_qr_img(self):
        """
        This function computes the QR code image for the GJP Order Number
        """
        for rec in self:
            if rec.invoice_number:
                qr = qrcode.QRCode(
                    version=1,
                    error_correction=qrcode.constants.ERROR_CORRECT_L,
                    box_size=10,
                    border=4,
                )
                qr.add_data(rec.invoice_number)
                qr.make(fit=True)
                img = qr.make_image(fill='black', back_color='white')
                temp = BytesIO()
                img.save(temp, format="PNG")
                rec.invoice_qr_img = base64.b64encode(temp.getvalue())
            else:
                rec.invoice_qr_img = False
    
    # 添加管家婆订单gjp_order_no这个值唯一索引限制
    _sql_constraints = [
        ('gjp_order_unique', 'unique(gjp_order_no)',
         'GJP Order no must be unique(ignoring case, ignoring leading and trailing spaces)'),
    ]

    def batch_action_print_invoice(self):
        """
        This function is used to print the delivery order (Sale Order Summary)
        for multiple records.
        """
        for rec in self:
            if not rec.invoice_number:
                rec.invoice_number = self.env['ir.sequence'].next_by_code('galaxy.gjp.order.invoice')
            if not rec.invoice_date:
                rec.invoice_date = fields.Datetime.now()
        return self.env.ref('galaxy_account_bank_statement_r2.action_report_gjp_order_invoice').report_action(self)

    def action_print_invoice(self):
        """
        This function is used to print the delivery order (Sale Order Summary)
        """
        self.ensure_one()
        if not self.invoice_number:
            self.invoice_number = self.env['ir.sequence'].next_by_code('galaxy.gjp.order.invoice')
        if not self.invoice_date:
            self.invoice_date = fields.Date.context_today(self)
        return self.env.ref('galaxy_account_bank_statement_r2.action_report_gjp_order_invoice').report_action(self)

    def toggle_password_visibility(self):
        """
        This method is called when the toggle password button is clicked.
        The actual toggling is handled by JavaScript in the frontend.
        This method is required for the button to work, but doesn't need to do anything.
        Returns an empty dict to prevent a page reload.
        """
        return {}
    
    def _inverset_set_state(self):
        pass

    def set_delivery_verify_code(self):
        self.customer_security_code = self.get_random_six_code()

    @api.depends('paid_amount')
    def _compute_get_state(self):
        for rec in self:
            rec.state = 'not_paid'
            if rec.paid_amount == 0.0:
                rec.state = 'not_paid'
            elif rec.paid_amount < rec.amount:
                rec.state = 'partial_paid'
            elif rec.paid_amount >= rec.amount:
                rec.state = 'paid'

    def action_sync_gjp_order(self):
        """
        This method is intended to be called from a button in the UI to trigger
        the synchronization of GJP Sale Orders.
        """
        for rec in self:
            self.sync_gjp_sale_order_by_one(rec.gjp_order_no)

    def action_select_gjp_sale_orders(self):
        """
        This method is intended to be called from a button in the UI to select
        GJP Sale Orders. It can be used to trigger the synchronization of GJP Sale Orders.
        """
        return {'type': 'ir.actions.act_window_close', 'infos': {'lot_ids': self.ids}}

    @api.depends('detail_line_ids.amount')
    def _compute_amount(self):
        for rec in self:
            rec.amount = sum(line.amount for line in rec.detail_line_ids)

    @api.depends('detail_line_ids.quantity')
    def _compute_quantity(self):
        for rec in self:
            rec.quantity = sum(line.quantity for line in rec.detail_line_ids)

    @api.depends('gjp_order_no')
    def _compute_gjp_order_sequence(self):
        for rec in self:
            rec.gjp_order_sequence = rec.gjp_order_no.split('-')[-1] if rec.gjp_order_no else ''

    @api.model
    def sync_gjp_sale_order(self):
        """
        This method is intended to sync GJP Sale Orders with an external system.
        The actual implementation would depend on the specific requirements and
        the external system's API.
        """
        # Placeholder for sync logic
        gjp_sale_order = self.env['gjp.sale.order']
        response, error_msg = GJPQuery.sync_sale_orders()
        if not response or error_msg:
            raise models.UserError(
                _('Failed to get GJP Sale Orders: %s') % error_msg)
        new_gjp_orders = []
        for gjp_so in response['message'][0]:
            gjp_order_no = gjp_so.get('so_number')
            audit_level = gjp_so.get('audit_level')
            glot = gjp_so.get('glot')
            dept = gjp_so.get('dept')
            user_code = gjp_so.get('user_code')
            if dept == 'XH':
                # 现货订单暂时不在odoo管理
                continue
            customer_id = self.env['res.partner'].search([
                ('bid_user_id', '=', user_code)], limit=1)
            if not customer_id:
                _logger.error(
                    _('GJP Order: %s,  Dept %s with user code %s not found.') % (gjp_order_no, dept, user_code))
                continue
            order_summary = gjp_so.get('order_summary')
            sale_person = gjp_so.get('sale_person')
            delivery_number = gjp_so.get('delivery_number')
            grand_total = gjp_so.get('grand_total')
            odoo_so_number = gjp_so.get('odoo_so_number')
            so_order_line = gjp_so.get('so_orders_line', [])
            stock_name = gjp_so.get('stock_name', '')
            # 检查是否已经存在相同的销售订单
            existing_gjp_order = self.env['gjp.sale.order'].search([
                ('gjp_order_no', '=', gjp_order_no)], limit=1)
            if not existing_gjp_order:
                # 如果不存在，则创建新的销售订单
                new_gjp_orders.append({
                    'gjp_order_no': gjp_order_no,
                    'customer_id': customer_id.id if customer_id else False,
                    'audit_level': audit_level,
                    'glot': glot,
                    'dept': dept,
                    'user_code': user_code,
                    'order_summary': order_summary,
                    'sale_person': sale_person,
                    'delivery_number': delivery_number,
                    'amount': grand_total,
                    'odoo_so_number': odoo_so_number,
                    'stock_name': stock_name,
                    'detail_line_ids': [
                        (0, 0, {
                            'product_code': line.get('product_code'),
                            'description': line.get('product_full_name'),
                            'quantity': line.get('qty'),
                            'line_summary': line.get('line_summary'),
                            'line_comment': line.get('line_comment'),
                            'price_unit': line.get('price'),
                            'amount': line.get('sub_total'),
                        }) for line in so_order_line
                    ]
                })
        if new_gjp_orders:
            gjp_sale_order = self.env['gjp.sale.order'].create(new_gjp_orders)
        if error_msg:
            _logger.error(error_msg)
        else:
            _logger.info("GJP Sale Orders Created: %s", gjp_sale_order.ids)
        return gjp_sale_order
    
    def get_random_six_code(self):
        """
        返回6位随机数字安全码
        """
        import random
        return ''.join(random.choices('0123456789', k=6))
    
    @api.model
    def develop_sync_r2_done_delivery(self):
        """
        This method is intended to be a placeholder for future development.
        It can be used to implement the logic for syncing GJP Sale Orders
        with the R2 Done Delivery system.
        """
        # Placeholder for future development
        _logger.info("Develop Sync R2 Done Delivery called.")
        done_delivery = self.env['galaxy.delivery'].search([('state','=','done')])
        for delivery_order in done_delivery:
            gjp_sale_order = self.create_gjp_sale_order_by_one(delivery_order.sale_order)
            for order in gjp_sale_order:
                order.audit_level = 1
                order.state = 'paid'

        

    @api.model
    def sync_gjp_sale_order_by_one(self, gjp_order_no):
        """
        This method is intended to sync GJP Sale Orders with an external system.
        The actual implementation would depend on the specific requirements and
        the external system's API.
        """
        # Placeholder for sync logic
        gjp_sale_order = self.env['gjp.sale.order']
        response, error_msg = GJPQuery.sync_sale_orders_by_one(gjp_order_no)
        if not response or error_msg:
            raise models.UserError(
                _('Failed to get GJP Sale Orders: %s') % error_msg)
        for gjp_so in response['message'][0]:
            gjp_order_no = gjp_so.get('so_number')
            audit_level = gjp_so.get('audit_level')
            glot = gjp_so.get('glot')
            dept = gjp_so.get('dept')
            user_code = gjp_so.get('user_code')
            customer_id = self.env['res.partner'].search([
                ('bid_user_id', '=', user_code)], limit=1)
            if not customer_id:
                _logger.error(
                    _('Customer with user code %s not found.') % user_code)
                continue
            order_summary = gjp_so.get('order_summary')
            sale_person = gjp_so.get('sale_person')
            delivery_number = gjp_so.get('delivery_number')
            grand_total = gjp_so.get('grand_total')
            odoo_so_number = gjp_so.get('odoo_so_number')
            so_order_line = gjp_so.get('so_orders_line', [])
            stock_name = gjp_so.get('stock_name', '')
            # 检查是否已经存在相同的销售订单
            existing_gjp_order = self.env['gjp.sale.order'].search([
                ('gjp_order_no', '=', gjp_order_no)], limit=1)
            if existing_gjp_order:
                existing_gjp_order.detail_line_ids.unlink()
                existing_gjp_order.write({
                    'gjp_order_no': gjp_order_no,
                    'customer_id': customer_id.id if customer_id else False,
                    'audit_level': audit_level,
                    'glot': glot,
                    'dept': dept,
                    'user_code': user_code,
                    'order_summary': order_summary,
                    'sale_person': sale_person,
                    'delivery_number': delivery_number,
                    'amount': grand_total,
                    'odoo_so_number': odoo_so_number,
                    'stock_name': stock_name,
                    'detail_line_ids': [
                        (0, 0, {
                            'product_code': line.get('product_code'),
                            'description': line.get('product_full_name'),
                            'quantity': line.get('qty'),
                            'line_summary': line.get('line_summary'),
                            'line_comment': line.get('line_comment'),
                            'price_unit': line.get('price'),
                            'amount': line.get('sub_total'),
                        }) for line in so_order_line
                    ]
                })
        if error_msg:
            _logger.error(error_msg)
        else:
            _logger.info("GJP Sale Orders Created: %s", gjp_sale_order.ids)
        return gjp_sale_order
    
    
    @api.model
    def create_gjp_sale_order_by_one(self, gjp_order_no):
        """
        This method is intended to sync GJP Sale Orders with an external system.
        The actual implementation would depend on the specific requirements and
        the external system's API.
        """
        # Placeholder for sync logic
        gjp_sale_order = self.env['gjp.sale.order']
        response, error_msg = GJPQuery.sync_sale_orders_by_one(gjp_order_no)
        if not response or error_msg:
            raise models.UserError(
                _('Failed to get GJP Sale Orders: %s') % error_msg)
        for gjp_so in response['message'][0]:
            gjp_order_no = gjp_so.get('so_number')
            audit_level = gjp_so.get('audit_level')
            glot = gjp_so.get('glot')
            dept = gjp_so.get('dept')
            user_code = gjp_so.get('user_code')
            customer_id = self.env['res.partner'].search([
                ('bid_user_id', '=', user_code)], limit=1)
            if not customer_id:
                _logger.error(
                    _('Customer with user code %s not found.') % user_code)
                continue
            order_summary = gjp_so.get('order_summary')
            sale_person = gjp_so.get('sale_person')
            delivery_number = gjp_so.get('delivery_number')
            grand_total = gjp_so.get('grand_total')
            odoo_so_number = gjp_so.get('odoo_so_number')
            so_order_line = gjp_so.get('so_orders_line', [])
            stock_name = gjp_so.get('stock_name', '')
            # 检查是否已经存在相同的销售订单
            existing_gjp_order = self.env['gjp.sale.order'].search([
                ('gjp_order_no', '=', gjp_order_no)], limit=1)
            if existing_gjp_order:
                gjp_sale_order = existing_gjp_order
            else:
                gjp_sale_order = self.env['gjp.sale.order'].create({
                    'gjp_order_no': gjp_order_no,
                    'customer_id': customer_id.id if customer_id else False,
                    'audit_level': audit_level,
                    'glot': glot,
                    'dept': dept,
                    'user_code': user_code,
                    'order_summary': order_summary,
                    'sale_person': sale_person,
                    'delivery_number': delivery_number,
                    'amount': grand_total,
                    'stock_name': stock_name,
                    'odoo_so_number': odoo_so_number,
                    'detail_line_ids': [
                        (0, 0, {
                            'product_code': line.get('product_code'),
                            'description': line.get('product_full_name'),
                            'quantity': line.get('qty'),
                            'line_summary': line.get('line_summary'),
                            'line_comment': line.get('line_comment'),
                            'price_unit': line.get('price'),
                            'amount': line.get('sub_total'),
                        }) for line in so_order_line
                    ]
                })
        if error_msg:
            _logger.error(error_msg)
        else:
            _logger.info("GJP Sale Orders Created: %s", gjp_sale_order.ids)
        return gjp_sale_order


class GjpSaleOrderSummaryLineDetail(models.Model):
    _name = 'gjp.sale.order.line'
    _description = 'GJP Sale Order Line'
    _rec_name = 'description'

    gjp_sale_order_id = fields.Many2one(
        'gjp.sale.order', string='GJP Sale Order',
        required=True, ondelete='cascade', index=True)
    product_code = fields.Char(string='Product Code')
    description = fields.Char(string='Product Description')
    line_summary = fields.Char(string='Line Summary')
    line_comment = fields.Char(string='Line Comment')
    quantity = fields.Integer(string='Quantity')
    price_unit = fields.Float(string='Unit Price', digits='Product Price')
    amount = fields.Float(string='Amount')
