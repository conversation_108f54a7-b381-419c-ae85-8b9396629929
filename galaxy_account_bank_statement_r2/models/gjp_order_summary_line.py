# -*- coding: utf-8 -*-

from odoo import models, fields, api


class GjpSaleOrderSummaryLine(models.Model):
    _name = 'gjp.sale.order.summary.line'
    _description = 'GJP Sale Order Summary Line'

    gjp_order_sequence = fields.Char(string='GJP Order Sequence', required=True)
    name = fields.Char(string='Line No', copy=False, readonly=True, store=True)
    summary_id = fields.Many2one(
        'gjp.sale.order.summary', string='Summary',
        required=True, ondelete='cascade')
    quantity = fields.Integer(string='Quantity', related='gjp_sale_order_id.quantity', store=True)
    amount = fields.Float(string='Amount', related='gjp_sale_order_id.amount', store=True, digits=(12, 2),)
    paid_amount = fields.Float(string='Paid Amount', readonly=True, digits=(12, 2),)
    order_summary = fields.Char(string='Order Summary', related="gjp_sale_order_id.order_summary", store=True)
    odoo_so_number = fields.Char(
        string='Odoo SO Number', related="gjp_sale_order_id.odoo_so_number", store=True)
    sale_person = fields.Char(string='Salesperson', related="gjp_sale_order_id.sale_person", store=True)
    currency_id = fields.Many2one(
        'res.currency', string='Currency',
        related='summary_id.customer_currency_id', store=True)
    gjp_sale_order_id = fields.Many2one(
        'gjp.sale.order', string='GJP Sale Order', index=True)
    customer_id = fields.Many2one(
        'res.partner', string='Customer', related='gjp_sale_order_id.customer_id',
        store=True, readonly=True, index=True)
    detail_line_ids = fields.One2many(
        'gjp.sale.order.summary.line.detail', 'summary_line_id',
        string='Detail Lines')
    glot = fields.Char(
        string='GLOT', related='gjp_sale_order_id.glot', store=True, index=True, readonly=True)
    note = fields.Text(string='Notes', help='Notes')

    @api.model
    def create(self, vals):
        """
        This function is used to generate the sequence number
        """
        created_lines = super(GjpSaleOrderSummaryLine, self).create(vals)
        for rec in created_lines:
            sequence_number = str(self.search_count([('summary_id', '=', rec.summary_id.id)])).zfill(3)
            rec.name = f"{rec.summary_id.name}-{sequence_number}"
            rec.detail_line_ids = [(5, 0, 0)]  # Clear existing detail lines
            rec.detail_line_ids = [(0, 0, {
                'gjp_sale_order_line_id': gjp_line.id,
                'description': gjp_line.description,
                'quantity': gjp_line.quantity,
                'price_unit': gjp_line.price_unit,
                'amount': gjp_line.amount,
            }) for gjp_line in rec.gjp_sale_order_id.detail_line_ids]
        return created_lines

    def update_line_numbers(self, created_lines):
        for rec in created_lines:
            rec.name = rec.summary_id.name + '-' + rec.name.split('-')[-1]

    def write(self, vals):
        """
        This function is used to update the sequence number
        """
        if 'gjp_sale_order_id' in vals:
            gjp_sale_order = self.env['gjp.sale.order'].browse(vals['gjp_sale_order_id'])
            detail_lines = [(0, 0, {
                'gjp_sale_order_line_id': gjp_line.id,
                'description': gjp_line.description,
                'quantity': gjp_line.quantity,
                'price_unit': gjp_line.price_unit,
                'amount': gjp_line.amount,
            }) for gjp_line in gjp_sale_order.detail_line_ids]
            vals['detail_line_ids'] = [(5, 0, 0)] + detail_lines
        else:
            for rec in self:
                if not rec.gjp_sale_order_id:
                    vals['detail_line_ids'] = [(5, 0, 0)]
        return super(GjpSaleOrderSummaryLine, self).write(vals)

    @api.onchange('gjp_sale_order_id')
    def _onchange_gjp_sale_order_id(self):
        """
        This function is used to update the state based on the GJP Sale Order ID
        """
        self.gjp_order_sequence = self.gjp_sale_order_id.gjp_order_sequence
        # self.detail_line_ids = [(5, 0, 0)]  # Clear existing detail lines
        # self.detail_line_ids = [
        #     (0, 0, {
        #         'gjp_sale_order_line_id': line.id,
        #         'description': line.description,
        #         'quantity': line.quantity,
        #         'price_unit': line.price_unit,
        #         'amount': line.amount
        #     }) for line in self.gjp_sale_order_id.detail_line_ids
        # ]


class GjpSaleOrderSummaryLineDetail(models.Model):
    _name = 'gjp.sale.order.summary.line.detail'
    _description = 'GJP Sale Order Summary Line'

    summary_line_id = fields.Many2one(
        'gjp.sale.order.summary.line', string='Summary Line',
        required=True, ondelete='cascade', idnex=True)
    gjp_sale_order_line_id = fields.Many2one(
        'gjp.sale.order.line', string='GJP Sale Order Line', index=True)
    line_summary = fields.Char(string='Line Summary', related='gjp_sale_order_line_id.line_summary')
    line_comment = fields.Char(string='Line Comment', related='gjp_sale_order_line_id.line_comment')
    description = fields.Char(string='Product Description')
    quantity = fields.Integer(string='Quantity')
    price_unit = fields.Float(string='Unit Price', digits='Product Price')
    amount = fields.Float(string='Amount', digits=(12, 2),)

    @api.model_create_multi
    def create(self, vals):
        """
        This function is used to generate the sequence number
        """
        created_lines = super(GjpSaleOrderSummaryLineDetail, self).create(vals)
        for rec in created_lines:
            rec.amount = rec.quantity * rec.price_unit
        return created_lines
