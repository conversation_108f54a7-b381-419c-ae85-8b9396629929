from odoo import models, fields, api
from odoo import models, fields, api


class CustomerAdvancePaymentHistory(models.Model):
    _name = 'customer.advance.payment.history'
    _description = 'Customer Advance Payment History'
    _order = 'transaction_date desc'

    transaction_date = fields.Datetime(
        string='Transaction Date',
        help='交易日期',
        required=True,
        index=True,
    )

    business_type = fields.Selection(
        selection=[
            ('receipt', 'Receipt'),
            ('goods_payment', 'Goods Payment'),
            ('reversal', 'Reversal'),
            ('internal_transfer_in', 'Internal Transfer In'),
            ('internal_transfer_out', 'Internal Transfer Out'),
            ('external_transfer_in', 'External Transfer In'),
            ('external_transfer_out', 'External Transfer Out'),
        ],
        string='Business Type',
        help='业务类型'
    )

    document_type = fields.Selection(
        selection=[
            ('payment_receipt', 'Payment receipt'),
            ('fund_transfer', 'Fund Transfer'),
            ('refund', 'Refund'),
        ],
        string="Document Type"
    )

    document_number = fields.Char(string="Document No.",
                                  compute="_compute_document_number",
                                  store=True,
                                  index=True,
                                  help="Document Number")

    funding_source = fields.Selection(
        string='Funding Source',
        selection=[('bank', 'Bank'), ('cash', 'Cash'), ('exchange', 'Exchange'), ('credit', 'Credit'), ('advance', 'Advance'), ('downpayment', 'Down Payment')],
        index=True,
        help='资金来源'
    )

    fund_type = fields.Selection(
        string='Fund Type',
        selection=[('advance', 'Advance Payment'), ('downpayment', 'Down Payment'), ('sopayment', 'SO Payment'), ('credit', 'Credit')],
        index=True,
        help='资金用途'
    )

    reconciliation_status = fields.Selection(
        selection=[
            ('not_applied', 'Not Applied'),
            ('partial_applied', 'Partial Applied'),
            ('fully_applied', 'Fully Applied')
        ],
        index=True,
        string='Reconciliation Status',
        help='核销狀態',
        default='not_applied'
    )

    notes = fields.Text(
        string='Notes',
        help='備註',
        compute="_compute_get_notes",
        store=True,
        inverse="_inverse_set_notes"
    )

    transaction_amount = fields.Float(
        string='Transaction Amount',
        help='Transaction Amount',
        digits=(16, 2)
    )

    advance_amount = fields.Float(
        string='Advance Amount',
        help='预收款金额',
        digits=(16, 2),
        store=True,
        compute="compute_get_advance_amount",
    )
    downpayment_amount = fields.Float(
        string='Down Payment Amount',
        help='首付款金额',
        store=True,
        digits=(16, 2)
    )

    advance_balance_amount = fields.Float(
        string='Advance Balance',
        help='预收款余额',
        digits=(16, 2)
    )

    downpayment_balance_amount = fields.Float(
        string='Down Payment Balance',
        help='预收款余额',
        digits=(16, 2)
    )

    operator = fields.Many2one(
        'res.users',
        string='Operator',
        help='操作人',
        default=lambda self: self.env.user
    )

    customer_id = fields.Many2one(
        'res.partner',
        string='Customer',
        help='客户',
        index=True,
    )
    related_customer_id = fields.Many2one(
        'res.partner',
        string='Related Customer',
        help='客户',
        index=True,
    )
    merged_bank_statement_ids = fields.Many2many(
        'galaxy.account.bank.merged.statement',
        relation="advance_payment_history_galaxy_bank_statment_rel",
        string='Transaction Number',
        help='银行对账单'
    )
    customer_advance_received_id = fields.Many2one(
        'customer.advance.received',
        string='Settlement Number',
        help='客户预收款',
        index=True,
    )

    customer_advance_received_line_id = fields.Many2one(
        'customer.advance.received.lines',
        string='Settlement Line Number',
        help='預收结算单行号',
        index=True
    )

    customer_received_statement_id = fields.Many2one(
        'customer.received.statement',
        string='Reconciliation Number',
        index=True,
        help="核銷號",
    )

    customer_received_statement_line_id = fields.Many2one(
        'customer.received.statement.lines',
        string='Reconciliation Line Number',
        index=True,
        help="核銷行號",
    )
    fund_transfer_order_id = fields.Many2one(
        'fund.transfer.order',
        string='Fund Transfer Order',
        help='资金划拨单',
        index=True
    )

    related_sale_summary_order_ids = fields.Many2many('gjp.sale.order.summary',
                                                      string="Sale Summary Orders", 
                                                      help="Related Sale Summary Orders", 
                                                      compute="_compute_get_sale_summary_orders", store=True)

    statement_counts = fields.Integer(
        string='Statement Counts',
        compute='_compute_statement_counts',
        help='对账单行数'
    )

    @api.model
    def create(self, vals):
        ret = super(CustomerAdvancePaymentHistory, self).create(vals)
        ret.customer_id.last_advance_payment_changed_date = fields.Datetime.now()
        if ret.funding_source != 'credit':
            # 如果不是信用额度，则更新相关客户的最后余额更新时间
            ret.related_customer_id.last_advance_payment_changed_date = fields.Datetime.now()
        ret.update_payment_history_group(ret)
        return ret

    def update_payment_history_group(self, records):
        """
        更新客户预收款历史记录分组
        """
        for rec in records:
            # 尝试查找可以合并的grou的记录
            related_records = self.env['customer.advance.payment.history.group'].search([
                ('transaction_date', '=', rec.transaction_date),
                ('document_number', '=', rec.document_number),
                ('customer_id', '=', rec.customer_id.id),
                ('fund_transfer_order_id', '=', rec.fund_transfer_order_id.id),
            ], limit=1)
            group_vals = {
                'transaction_date': rec.transaction_date,
                'business_type': rec.business_type,
                'document_type': rec.document_type,
                'document_number': rec.document_number,
                'funding_source': rec.funding_source,
                'fund_type': rec.fund_type,
                'reconciliation_status': rec.reconciliation_status,
                'notes': rec.notes,
                'transaction_amount': rec.transaction_amount,
                'advance_amount': rec.advance_amount,
                'downpayment_amount': rec.downpayment_amount,
                'advance_balance_amount': rec.advance_balance_amount,
                'downpayment_balance_amount': rec.downpayment_balance_amount,
                'operator': rec.operator.id,
                'customer_id': rec.customer_id.id,
                'fund_transfer_order_id': rec.fund_transfer_order_id.id,
                'customer_advance_received_id': rec.customer_advance_received_id.id,
                'customer_advance_received_line_id': rec.customer_advance_received_line_id.id,
                'customer_received_statement_id': rec.customer_received_statement_id.id,
                'customer_received_statement_line_id': rec.customer_received_statement_line_id.id,
                'merged_bank_statement_ids': [(6, 0, rec.merged_bank_statement_ids.ids)],
                'related_sale_summary_order_ids': [(6, 0, rec.related_sale_summary_order_ids.ids)],
                'related_customer_id': rec.related_customer_id.id,
            }
            if not related_records:
                group_rec = self.env['customer.advance.payment.history.group'].create(group_vals)
            else:
                group_rec = related_records[0]
                group_rec.transaction_amount += rec.transaction_amount
                if rec.funding_source == 'advance':
                    group_rec.advance_amount += rec.advance_amount
                    group_rec.advance_balance_amount += rec.transaction_amount
                elif rec.funding_source == 'downpayment':
                    # 如果是首付款，则增加首付款余额
                    group_rec.downpayment_amount += rec.downpayment_amount
                    group_rec.downpayment_balance_amount += rec.transaction_amount

    def develop_update_res_partner_last_advance_payment_date(self):
        """
        根据历史记录更新客户最后余额更新时间
        """
        payments_history = self.env['customer.advance.payment.history'].read_group(
            domain=[],  # 你的搜索条件
            fields=['customer_id', 'changed_date:max(create_date)'],  # 要读取的字段
            groupby=['customer_id'],  # 分组依据的字段
            orderby='id',  # 可选排序
            lazy=False
        )
        payments_history_group = {
            group["customer_id"]: group["changed_date"] for group in payments_history
        }
        for customer_id, changed_date in payments_history_group.items():
            customer = self.env['res.partner'].browse(customer_id[0])
            customer.last_advance_payment_changed_date = changed_date

    @api.depends('fund_transfer_order_id')
    def _compute_get_sale_summary_orders(self):
        """
        计算相关销售汇总订单
        """
        for rec in self:
            if rec.fund_transfer_order_id:
                rec.related_sale_summary_order_ids = rec.fund_transfer_order_id.customer_settlement_id.gjp_sale_order_summary_ids
            else:
                rec.related_sale_summary_order_ids = False

    @api.depends('transaction_amount', 'funding_source')
    def compute_get_advance_amount(self):
        for rec in self:
            if rec.fund_type == 'sopayment':
                if rec.funding_source in ('bank', 'cash', 'exchange', 'advance'):
                    rec.advance_amount = rec.transaction_amount
                    rec.downpayment_amount = 0.0
                elif rec.funding_source in ('downpayment',):
                    rec.downpayment_amount = rec.transaction_amount
                    rec.advance_amount = 0.0
            elif rec.fund_type == 'advance':
                if rec.funding_source == 'downpayment':
                    if rec.document_type != 'refund':
                        if rec.business_type == 'internal_transfer_in':
                            rec.advance_amount = rec.transaction_amount
                            rec.downpayment_amount = 0.0
                        if rec.business_type == 'internal_transfer_out':
                            rec.downpayment_amount = rec.transaction_amount
                            rec.advance_amount = 0.0
                        if rec.business_type == 'reversal':
                            if rec.transaction_amount > 0:
                                rec.downpayment_amount = rec.transaction_amount
                                rec.advance_amount = 0.0
                            else:
                                rec.advance_amount = rec.transaction_amount
                                rec.downpayment_amount = 0.0
                        else:
                            rec.advance_amount = rec.transaction_amount
                            rec.downpayment_amount = 0.0
                    else:
                        if rec.business_type == 'reversal':
                            if rec.transaction_amount > 0:
                                rec.downpayment_amount = rec.transaction_amount
                                rec.advance_amount = 0.0
                            else:
                                rec.advance_amount = rec.transaction_amount
                                rec.downpayment_amount = 0.0
                        else:
                            rec.advance_amount = rec.transaction_amount
                            rec.downpayment_amount = 0.0
                elif rec.funding_source in ('bank', 'cash', 'exchange'):
                    rec.advance_amount = rec.transaction_amount
                    rec.downpayment_amount = 0.0
                elif rec.funding_source == 'credit':
                    rec.advance_amount = rec.transaction_amount
                    rec.downpayment_amount = 0.0
                elif rec.funding_source == 'advance':
                    rec.advance_amount = rec.transaction_amount
                    rec.downpayment_amount = 0.0
            elif rec.fund_type == 'downpayment':
                if rec.funding_source == 'advance':
                    if rec.document_type != 'refund':
                        if rec.business_type == 'internal_transfer_in':
                            rec.downpayment_amount = rec.transaction_amount
                            rec.advance_amount = 0.0
                        if rec.business_type == 'internal_transfer_out':
                            rec.advance_amount = rec.transaction_amount
                            rec.downpayment_amount = 0.0
                        if rec.business_type == 'reversal':
                            if rec.transaction_amount > 0:
                                rec.advance_amount = rec.transaction_amount
                                rec.downpayment_amount = 0.0
                            else:
                                rec.downpayment_amount = rec.transaction_amount
                                rec.advance_amount = 0.0
                    else:
                        if rec.business_type == 'reversal':
                            if rec.transaction_amount > 0:
                                rec.advance_amount = rec.transaction_amount
                                rec.downpayment_amount = 0.0
                            else:
                                rec.downpayment_amount = rec.transaction_amount
                                rec.advance_amount = 0.0
                        else:
                            rec.downpayment_amount = rec.transaction_amount
                            rec.advance_amount = 0.0
                elif rec.funding_source in ('bank', 'cash', 'exchange'):
                    rec.downpayment_amount = rec.transaction_amount
                    rec.advance_amount = 0.0
                elif rec.funding_source == 'credit':
                    rec.downpayment_amount = rec.transaction_amount
                    rec.advance_amount = 0.0
                elif rec.funding_source == 'downpayment':
                    # DownPayment到downpayment，只能是自己to自己
                    rec.downpayment_amount = rec.transaction_amount
                    rec.advance_amount = 0.0
            elif rec.fund_type == 'credit':
                if rec.funding_source == 'advance':
                    rec.advance_amount = rec.transaction_amount
                    rec.downpayment_amount = 0.0
                elif rec.funding_source == 'downpayment':
                    rec.downpayment_amount = rec.transaction_amount
                    rec.advance_amount = 0.0
            else:
                pass

    def _inverse_set_notes(self):
        """
        Inverse function to set notes
        """
        pass

    @api.depends('related_customer_id', 'document_type', 'business_type', 'fund_type')
    def _compute_get_notes(self):
        """
        计算备注信息
        """
        funding_source_map = {
            'advance': '預收款',
            'downpayment': '首付款',
            'sopayment': '貨款',
            'bank': '銀行',
            'cash': '現金',
            'exchange': '結匯',
            'credit': '信用额度',
        }
        business_type_action_map = {
            'receipt': '收',
            'goods_payment': '付',
            'reversal': '冲销',
            'internal_transfer_in': '內部轉入',
            'internal_transfer_out': '內部轉出',
            'external_transfer_in': '外部轉入',
            'external_transfer_out': '外部轉出',
            'credit': '信用额度',
        }
        for rec in self:
            if rec.related_customer_id and rec.customer_id != rec.related_customer_id:
                if rec.fund_type == 'sopayment':
                    rec.notes = f"{business_type_action_map.get(rec.business_type)}客戶{rec.related_customer_id.name}貨款"
                if rec.fund_type == 'advance':
                    rec.notes = f"{business_type_action_map.get(rec.business_type)}客戶{rec.related_customer_id.name}預收款"
                if rec.fund_type == 'downpayment':
                    rec.notes = f"{business_type_action_map.get(rec.business_type)}客戶{rec.related_customer_id.name}首付款"
                if rec.fund_type == 'credit':
                    rec.notes = f"{business_type_action_map.get(rec.business_type)}客戶{rec.related_customer_id.name}信用额度"
            elif rec.related_customer_id  and rec.related_customer_id == rec.customer_id:
                if rec.fund_type == 'sopayment':
                    rec.notes = f"{funding_source_map.get(rec.funding_source)}{business_type_action_map.get(rec.business_type)}貨款"
                if rec.fund_type == 'advance':
                    rec.notes = f"{funding_source_map.get(rec.funding_source)}{business_type_action_map.get(rec.business_type)}預收款"
                if rec.fund_type == 'downpayment':
                    rec.notes = f"{funding_source_map.get(rec.funding_source)}{business_type_action_map.get(rec.business_type)}首付款"
                if rec.fund_type == 'credit':
                    rec.notes = f"{funding_source_map.get(rec.funding_source)}{business_type_action_map.get(rec.business_type)}信用额度"
            elif not rec.related_customer_id:
                if rec.fund_type == 'sopayment':
                    rec.notes = f"{funding_source_map.get(rec.funding_source)}{business_type_action_map.get(rec.business_type)}貨款"
                if rec.fund_type == 'advance':
                    rec.notes = f"{funding_source_map.get(rec.funding_source)}{business_type_action_map.get(rec.business_type)}預收款"
                if rec.fund_type == 'downpayment':
                    rec.notes = f"{funding_source_map.get(rec.funding_source)}{business_type_action_map.get(rec.business_type)}首付款"
                if rec.fund_type == 'credit':
                    rec.notes = f"{funding_source_map.get(rec.funding_source)}{business_type_action_map.get(rec.business_type)}信用额度"
            else:
                rec.notes = ''
    
    @api.depends('fund_transfer_order_id', 'customer_advance_received_line_id')
    def _compute_document_number(self):
        """
        计算单据编号
        """
        for rec in self:
            if rec.fund_transfer_order_id:
                rec.document_number = rec.fund_transfer_order_id.name
            elif rec.customer_advance_received_line_id:
                rec.document_number = rec.customer_advance_received_line_id.name
            else:
                rec.document_number = ''

    @api.depends('merged_bank_statement_ids')
    def _compute_statement_counts(self):
        """
        计算对账单行数
        """
        for rec in self:
            rec.statement_counts = len(rec.merged_bank_statement_ids)

    def action_view_statement(self):
        """
        This function opens the bank statement
        """
        self.ensure_one()
        action = self.sudo().env.ref('galaxy_account_bank_statement.action_galaxy_account_merged_bank_statement').read()[0]
        action['domain'] = [('id', 'in', self.merged_bank_statement_ids.ids)]
        action['context'] = {'create': False, 'edit': False, 'delete': False}
        return action
