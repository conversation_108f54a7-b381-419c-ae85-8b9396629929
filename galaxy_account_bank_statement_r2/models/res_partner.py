# -*- coding: utf-8 -*-
# -*- coding: utf-8 -*-

import logging
import pytz
import json
from dateutil.relativedelta import relativedelta

from odoo import fields, models, api, _
import re

_logger = logging.getLogger(__name__)


class ResPartner(models.Model):
    _inherit = 'res.partner'

    gjp_customer_name = fields.Char(string='GJP Customer Name', help="Customer name in GJP system", compute="_compute_get_gjp_customer_name", store=True)

    total_advance_payment_balance = fields.Float(string="Total Balance", compute="_compute_total_advance_balance")

    total_advance_payment_amount = fields.Float(string='Total Advance Payment Amount', readonly=True, digits=(12, 2),)
    total_downpayment_amount = fields.Float(string='Total Down Payment Amount', readonly=True, digits=(12, 2),)

    applied_advance_payment_amount = fields.Float(string='Applied Advance Payment Amount', readonly=True, digits=(12, 2),)
    applied_downpayment_amount = fields.Float(string='Applied Down Payment Amount', readonly=True, digits=(12, 2),)

    allocate_advance_payment_amount = fields.Float(string='Allocate Advance Payment Amount', readonly=True, digits=(12, 2),)
    allocate_downpayment_amount = fields.Float(string='Allocate Down Payment Amount', readonly=True, digits=(12, 2),)
    allocate_credit_amount = fields.Float(string='Allocate Credit Amount', readonly=True, digits=(12, 2),)

    reversal_advance_payment_amount = fields.Float(string='Reversal Advance Payment Amount', readonly=True, digits=(12, 2),)
    reversal_downpayment_amount = fields.Float(string='Reversal Advance Payment Amount', readonly=True, digits=(12, 2),)

    transfered_out_advance_payment_amount = fields.Float(string='Transfered Out Advance Payment Amount', readonly=True, digits=(12, 2),)
    transfered_out_downpayment_amount = fields.Float(string='Transfered Out Down Payment Amount', readonly=True, digits=(12, 2),)

    advance_payment_balance_amount = fields.Float(string='Advance Payment Balance', compute='_compute_advance_payment_balance_amount', store=True, digits=(12, 2),)
    downpayment_balance_amount = fields.Float(string='Down Payment Balance', compute='_compute_downpayment_balance_amount', store=True, digits=(12, 2),)
    last_advance_payment_changed_date = fields.Datetime(string="Last Advanche payment Changed Date")
    last_customer_credit_changed_date = fields.Datetime(string="Last Customer Credit Changed Date")

    @api.depends('advance_payment_balance_amount', 'downpayment_balance_amount')
    def _compute_total_advance_balance(self):
        for rec in self:
            rec.total_advance_payment_balance = rec.advance_payment_balance_amount + rec.downpayment_balance_amount

    @api.depends('total_advance_payment_amount', 'applied_advance_payment_amount', 'reversal_advance_payment_amount', 'transfered_out_advance_payment_amount')
    def _compute_advance_payment_balance_amount(self):
        for record in self:
            record.advance_payment_balance_amount = record.total_advance_payment_amount - record.applied_advance_payment_amount \
                - record.reversal_advance_payment_amount - record.transfered_out_advance_payment_amount

    @api.depends('total_downpayment_amount', 'applied_downpayment_amount', 'reversal_downpayment_amount', 'transfered_out_downpayment_amount')
    def _compute_downpayment_balance_amount(self):
        for record in self:
            record.downpayment_balance_amount = record.total_downpayment_amount - record.applied_downpayment_amount \
                - record.reversal_advance_payment_amount - record.transfered_out_downpayment_amount

    def develop_clear_error_email(self):
        """
        清除客户错误的邮件地址
        """
        for customer in self.env['res.partner'].search([]):
            check_email_result = [customer._is_valid_email(item) for item in customer.email.split(',')]
            if customer.email and not all(check_email_result):
                _logger.warning(f"Clearing invalid email for customer {customer.name}: {customer.email}")
                customer.email = ''

    def get_send_datetime(self, date, advance_payment_report_send_time_hour, advance_payment_report_send_time_minute):
        """返回指定日期的发送时间（UTC naive datetime）"""
        naive_datetime = fields.Datetime.from_string(
            f'{date} {advance_payment_report_send_time_hour}:{advance_payment_report_send_time_minute}:00')
        aware_datetime = fields.Datetime.context_timestamp(self, naive_datetime)
        return aware_datetime.astimezone(pytz.UTC).replace(tzinfo=None)

    @api.model
    def send_advance_balance_report_email_auto(self):
        """
        自动发送全部客户预收余额对账单邮件
        """
        # Calculate yesterday and today 20:30 boundaries
        current_tz = self.env.user.tz or 'Asia/Shanghai'
        user_tz = pytz.timezone(current_tz)
        current_time = fields.Datetime.now().astimezone(user_tz)
        advance_payment_report_send_time_hour = self.env['ir.config_parameter'].sudo().get_param('advance_payment_report_send_time_hour', default='20')
        advance_payment_report_send_time_minute = self.env['ir.config_parameter'].sudo().get_param('advance_payment_report_send_time_minute', default='30')
        yesterday = (current_time.date() - relativedelta(days=1))
        yesterday_evening = self.get_send_datetime(yesterday, advance_payment_report_send_time_hour, advance_payment_report_send_time_minute)
        today = current_time.date()
        today_evening = self.get_send_datetime(today, advance_payment_report_send_time_hour, advance_payment_report_send_time_minute)
        now = fields.Datetime.now()
        # 将当前时间转换为北京时间
        beijing_time = now.astimezone(user_tz)
        if advance_payment_report_send_time_hour and int(advance_payment_report_send_time_hour) == beijing_time.hour and \
                advance_payment_report_send_time_minute and int(advance_payment_report_send_time_minute) == beijing_time.minute:
            # 在北京时间20:30发送邮件
            need_to_send_customers_advance = self.env['res.partner'].search([
                ('email', '!=', ''),
                ('email', '!=', False),
                ('last_advance_payment_changed_date', '>=', yesterday_evening),
                ('last_advance_payment_changed_date', '<=', today_evening)
            ])
            need_to_send_customers_credit = self.env['res.partner'].search([
                ('email', '!=', ''),
                ('email', '!=', False),
                ('last_customer_credit_changed_date', '>=', yesterday_evening),
                ('last_customer_credit_changed_date', '<=', today_evening)
            ])
            # Combine both lists to get all customers that need to receive any report
            need_to_send_customers = need_to_send_customers_advance | need_to_send_customers_credit
            need_to_send_customers.with_context(is_auto=True).batch_send_advance_balance_report_email()

    def get_send_time(self):
        advance_payment_report_send_time_hour = self.env['ir.config_parameter'].sudo().get_param('advance_payment_report_send_time_hour', default='20')
        advance_payment_report_send_time_minute = self.env['ir.config_parameter'].sudo().get_param('advance_payment_report_send_time_minute', default='30')
        return f"{advance_payment_report_send_time_hour}:{advance_payment_report_send_time_minute}"

    def batch_send_advance_balance_report_email(self):
        """
        批量发送客户预收余额对账单邮件
        """
        for customer in self:
            check_email_result = [customer._is_valid_email(item) for item in customer.email.split(',')]
            if customer.email and all(check_email_result):
                customer.send_advance_balance_report_email()

    def get_customer_advance_payment_balance_email_table(self):
        """
        获取客户预收余额对账单邮件表格数据
        """
        is_auto = self.env.context.get('is_auto', False)
        if is_auto:
            # 检查最后更新时间是否在特定时间范围内
            current_tz = self.env.user.tz or 'Asia/Shanghai'
            user_tz = pytz.timezone(current_tz)
            current_time = fields.Datetime.now().astimezone(user_tz)
            advance_payment_report_send_time_hour = self.env['ir.config_parameter'].sudo().get_param('advance_payment_report_send_time_hour', default='20')
            advance_payment_report_send_time_minute = self.env['ir.config_parameter'].sudo().get_param('advance_payment_report_send_time_minute', default='30')
            yesterday = (current_time.date() - relativedelta(days=1))
            yesterday_evening = self.get_send_datetime(yesterday, advance_payment_report_send_time_hour, advance_payment_report_send_time_minute)
            today = current_time.date()
            today_evening = self.get_send_datetime(today, advance_payment_report_send_time_hour, advance_payment_report_send_time_minute)
            if not self.last_advance_payment_changed_date:
                # 如果没有最后更新时间，则不返回表格
                return ''
            # 如果最后更新时间不在昨天晚上到今天晚上之间，则不返回表格
            if not (yesterday_evening <= self.last_advance_payment_changed_date <= today_evening):
                return ''
        table_template = f"""
            <table style="width: 450px; border-collapse: collapse; margin: 10px 0;">
                <tr style="font-weight: bold;">
                    <td colspan="2" style="border: 1px solid #ddd; padding: 8px; text-align: center;">預收餘額(Prepaid Balance)</td>
                </tr>
                <tr style="font-weight: bold;">
                    <td style="border: 1px solid #ddd; padding: 8px;">預收款餘額 (Advance Balance)</td>
                    <td style="border: 1px solid #ddd; padding: 8px; text-align: right;">${self.customer_currency_id.name} ${'{:,.2f}'.format(self.advance_payment_balance_amount)}</td>
                </tr>
                <tr style="font-weight: bold;">
                    <td style="border: 1px solid #ddd; padding: 8px;">首付款餘額 (Downpayment Balance)</td>
                    <td style="border: 1px solid #ddd; padding: 8px; text-align: right;">${self.customer_currency_id.name} ${'{:,.2f}'.format(self.downpayment_balance_amount)}</td>
                </tr>
            </table>
        """
        return table_template

    def get_customer_credit_balance_email_table(self):
        """
        获取客户信用额度对账单邮件表格数据
        """
        if not self.customer_credit_amount:
            return ''
        is_auto = self.env.context.get('is_auto', False)
        if is_auto:
            # 检查最后更新时间是否在特定时间范围内
            current_tz = self.env.user.tz or 'Asia/Shanghai'
            user_tz = pytz.timezone(current_tz)
            current_time = fields.Datetime.now().astimezone(user_tz)
            advance_payment_report_send_time_hour = self.env['ir.config_parameter'].sudo().get_param('advance_payment_report_send_time_hour', default='20')
            advance_payment_report_send_time_minute = self.env['ir.config_parameter'].sudo().get_param('advance_payment_report_send_time_minute', default='30')
            yesterday = (current_time.date() - relativedelta(days=1))
            yesterday_evening = self.get_send_datetime(yesterday, advance_payment_report_send_time_hour, advance_payment_report_send_time_minute)
            today = current_time.date()
            today_evening = self.get_send_datetime(today, advance_payment_report_send_time_hour, advance_payment_report_send_time_minute)
            if not self.last_customer_credit_changed_date:
                # 如果没有最后更新时间，则不返回表格
                return ''
            # 如果最后更新时间不在昨天晚上到今天晚上之间，则不返回表格
            if not (yesterday_evening <= self.last_customer_credit_changed_date <= today_evening):
                return ''
        table_template = f"""
            <table style="width: 450px; border-collapse: collapse; margin: 10px 0;">
                <tr style="font-weight: bold;">
                    <td colspan="2" style="border: 1px solid #ddd; padding: 8px; text-align: center;"">信用額度(Credit Line)</td>
                </tr>
                <tr style="font-weight: bold;">
                    <td style="border: 1px solid #ddd; padding: 8px;">已用额度(Used Amount) (Advance Balance)</td>
                    <td style="border: 1px solid #ddd; padding: 8px; text-align: right;">${self.customer_currency_id.name} ${'{:,.2f}'.format(self.locked_customer_credit_amount)}<br></br>
                    <font color='red' style='font-size: 8px'>*為方便您的使用，請及时還款</font></td>
                </tr>
                <tr style="font-weight: bold;">
                    <td style="border: 1px solid #ddd; padding: 8px;">剩余可用额度(Available Amount)</td>
                    <td style="border: 1px solid #ddd; padding: 8px; text-align: right;">${self.customer_currency_id.name} ${'{:,.2f}'.format(self.available_customer_credit_amount)}</td>
                </tr>
            </table>
        """
        return table_template

    def _is_valid_email(self, email):
        """
        Validates if the email address is properly formatted
        :param email: Email address to validate
        :return: Boolean indicating if the email is valid
        """
        if not email:
            return False
        # Basic email validation using regex
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))

    def send_advance_balance_report_email(self):
        """
        发送客户预收余额对账单邮件
        """
        email = self.email or ''
        generic_res_id = self.ids[0]
        check_email_result = [self._is_valid_email(item) for item in self.email.split(',')]
        if not email or not all(check_email_result):
            return
        if email:
            mail_template = self.env.ref(
                'galaxy_account_bank_statement_r2.galaxy_advance_payment_balance_email_template')
            mail_template.send_mail(res_id=generic_res_id, force_send=True, email_values={
                                    'email_to': email})

    def action_advance_balance_send(self):
        ''' Opens a wizard to compose an email, with relevant mail template loaded by default '''
        self.ensure_one()
        email = self.email or ''
        lang = self.env.context.get('lang')
        check_email_result = [self._is_valid_email(item) for item in self.email.split(',')]
        if not email or not all(check_email_result):
            self.env.user.notify_warning(
                message=_("The customer does not have a valid email address."),
                title=_("Invalid Email"),
                sticky=False,
            )
            return
        template = self.sudo().env.ref('galaxy_account_bank_statement_r2.galaxy_advance_payment_balance_email_template')
        template_id = template.id if template else False
        if template.lang:
            lang = template._render_lang(self.ids)[self.id]
        ctx = {
            'default_model': 'res.partner',
            'default_res_id': self.ids[0],
            'default_partner_ids': self.ids,
            'default_use_template': bool(template_id),
            'default_template_id': template_id,
            'default_composition_mode': 'comment',
            'force_email': True,
            'lang': lang,
        }
        return {
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'res_model': 'mail.compose.message',
            'views': [(False, 'form')],
            'view_id': False,
            'target': 'new',
            'context': ctx,
        }

    @api.depends('bid_user_id')
    def _compute_get_gjp_customer_name(self):
        """
        This function computes the GJP customer name
        """
        for rec in self:
            gjp_customer = self.env['galaxy.gjp.customer'].search(
                [('number', '=', rec.bid_user_id)], limit=1)
            rec.gjp_customer_name = gjp_customer.name or ''

    def action_open_advance_receive_data(self):
        """
        This function opens the advance payment received data
        """
        self.ensure_one()
        action = self.sudo().env.ref('galaxy_lot_r2.action_account_customer_advance_received').read()[0]
        action['domain'] = [('customer_id', '=', self.id)]
        return action

    def action_open_advance_statement_data(self):
        """
        This function opens the advance payment statement data
        """
        self.ensure_one()
        action = self.sudo().env.ref('galaxy_lot_r2.action_account_customer_received_statement').read()[0]
        action['domain'] = [('customer_id', '=', self.id)]
        return action

    def action_view_details_tech(self):
        """
        This function opens the advance payment received data
        """
        self.ensure_one()
        action = self.sudo().env.ref('galaxy_account_bank_statement_r2.action_customer_advance_payment_history').read()[0]
        action['domain'] = [('customer_id', '=', self.id)]
        context = action.get('context', {})
        # Convert string context to dict if needed
        if isinstance(context, str) and context == "{}":
            context = {}
        else:
            context = json.loads(context) if isinstance(context, str) else context
        if isinstance(context, dict):
            context.update({"search_default_customer_id": self.id})
        else:
            context = {'search_default_customer_id': self.id}
        action['context'] = context
        return action

    def action_view_details(self):
        """
        This function opens the advance payment received data
        """
        self.ensure_one()
        action = self.sudo().env.ref('galaxy_account_bank_statement_r2.action_customer_advance_payment_history_group').read()[0]
        action['domain'] = [('customer_id', '=', self.id)]
        context = action.get('context', {})
        # Convert string context to dict if needed
        if isinstance(context, str) and context == "{}":
            context = {}
        else:
            context = json.loads(context) if isinstance(context, str) else context
        if isinstance(context, dict):
            context.update({"search_default_customer_id": self.id})
        else:
            context = {'search_default_customer_id': self.id}
        action['context'] = context
        return action

    def action_view_credit_details(self):
        """
        获取客户额度详情
        """
        self.ensure_one()
        action = self.sudo().env.ref('galaxy_account_bank_statement_r2.action_customer_credit_history').read()[0]
        action['domain'] = [('customer_id', '=', self.id)]
        action['context'] = {'search_default_customer_id': self.id}
        return action

    def get_advance_account_balance(self):
        """
        获取预付款余额
        """
        curreny_name = self.customer_currency_id.name
        balance_amount = self.total_advance_payment_balance
        advance_balance_amount = self.advance_payment_balance_amount
        downpayment_balance_amount = self.downpayment_balance_amount
        account_balance_info = {
            "name": self.display_name,
            "currency": curreny_name,
            "balance": f"{balance_amount:,.2f}",
            "advance_balance": f"{advance_balance_amount:,.2f}",
            "downpayment_balance": f"{downpayment_balance_amount:,.2f}",
        }
        return account_balance_info

    def get_customer_credit_balance(self):
        """
        获取信用额度余额
        """
        curreny_name = self.customer_currency_id.name
        customer_credit_amount = self.customer_credit_amount
        locked_customer_credit_amount = self.locked_customer_credit_amount
        available_customer_credit_amount = self.available_customer_credit_amount
        account_balance_info = {
            "name": self.display_name,
            "currency": curreny_name,
            "customer_credit_amount": f"{customer_credit_amount:,.2f}",
            "locked_customer_credit_amount": f"{locked_customer_credit_amount:,.2f}",
            "available_customer_credit_amount": f"{available_customer_credit_amount:,.2f}",
        }
        return account_balance_info
