# -- coding: utf-8 --

import time
import uuid
import hashlib
from odoo.tools import config


class GJPApi():

    def __init__(self):
        """
            初始化glx_app_id，glx_app_secret签名变量
        """
        self.glx_app_id = ''
        self.glx_app_secret = ''
        chat_gpt_config = config.misc.get("gjp_api", {})
        if chat_gpt_config:
            self.glx_app_id = chat_gpt_config.get('glx_app_id')
            self.glx_app_secret = chat_gpt_config.get('glx_app_secret')

    def get_sign(self):
        """
        获取签名
        """
        glx_timestamp = int(time.time())
        glx_nonce = str(uuid.uuid1())
        str_glx_sign = str(glx_timestamp) + str(self.glx_app_id) + \
            self.glx_app_secret + str(glx_nonce)
        glx_sign = hashlib.md5(str_glx_sign.encode('utf-8')).hexdigest().lower()
        return glx_sign, self.glx_app_id, str(glx_timestamp), glx_nonce

    def verify_sign(self, glx_app_id, glx_nonce, glx_sign, glx_timestamp):
        """
        验证签名
        """
        current_timestamp = int(time.time())
        if abs(current_timestamp - int(glx_timestamp)) > 300:
            return False, "Timestamp is not within the allowed range"
        str_sign = str(glx_timestamp) + str(glx_app_id) + \
            str(self.glx_app_secret) + str(glx_nonce)
        sign = hashlib.md5(str_sign.encode('utf-8')).hexdigest().lower()
        if sign == glx_sign:
            return True, 'valid'
        return False, 'invalid'

    def get_headers(self):
        """
        获取headers
        """
        glx_sign, glx_app_id, glx_timestamp, glx_nonce = self.get_sign()
        headers = {
            'accept': 'application/json',
            'glxAppId': glx_app_id,
            'glxTimestamp': glx_timestamp,
            'glxSign': glx_sign,
            'glxNonce': glx_nonce,
        }
        return headers
