import os
import logging
from pathlib import Path
import json
import requests
from ast import literal_eval
from requests.exceptions import RequestException
from odoo.tools import config
from odoo.addons.base_galaxy.models.public_func import parse_float
from .gjp_api import GJPApi

_logger = logging.getLogger(__name__)


class GJPQueryObj(GJPApi):

    def __init__(self):
        super().__init__()
        self.get_sale_order_url = ''
        chat_gpt_config = config.misc.get("gjp_api", {})
        if chat_gpt_config:
            self.get_sale_order_url = chat_gpt_config.get('gjp_get_sale_order')
            self.sync_sale_order_url = chat_gpt_config.get('gjp_sync_sale_order')
            self.sync_sale_order_url_by_one = chat_gpt_config.get('gjp_sync_sale_order_by_one')

    def get_sale_orders_list(self, sale_orders_list):
        """
        根据销售订单列表获取销售订单信息
        """
        if not self.get_sale_order_url:
            return {}, 'GJP get sales order url is not configured'
        data = {
            'sale_orders_list': sale_orders_list,
        }
        http_header = self.get_headers()
        try:
            _logger.info(data)
            response = requests.post(self.get_sale_order_url, headers=http_header, data=data, timeout=600)
            if response.status_code != 200:
                _logger.error('Get GJP Sales order error, status_code: %s', response.status_code)
                return {}, f'Get GJP Sales order error, status_code: {response.status_code}'
        except RequestException as e:
            _logger.error('Get GJP Sales order error, %s', e, exc_info=True)
            return {}, f'Get GJP Sales order error, {e}'
        response_dict = response.json()
        return response_dict, ''

    def sync_sale_orders(self):
        """
        全量同步管家婆销售单--状态是1
        """
        http_header = self.get_headers()
        try:
            _logger.info('Starting to sync GJP Sale Orders')
            data = {
            }
            response = requests.post(self.sync_sale_order_url, headers=http_header, data=data, timeout=600)
            if response.status_code != 200:
                _logger.error('Get GJP Sales order error, status_code: %s', response.status_code)
                return {}, f'Get GJP Sales order error, status_code: {response.status_code}'
        except RequestException as e:
            _logger.error('Get GJP Sales order error, %s', e, exc_info=True)
            return {}, f'Get GJP Sales order error, {e}'
        response_dict = response.json()
        return response_dict, ''

    def sync_sale_orders_by_one(self, gjp_order_no):
        """
        全量同步管家婆销售单--状态是1
        """
        http_header = self.get_headers()
        try:
            _logger.info('Starting to sync GJP Sale Orders')
            data = {'gjp_order_no': gjp_order_no}
            response = requests.post(self.sync_sale_order_url_by_one, headers=http_header, data=data, timeout=600)
            if response.status_code != 200:
                _logger.error('Get GJP Sales order error, status_code: %s', response.status_code)
                return {}, f'Get GJP Sales order error, status_code: {response.status_code}'
        except RequestException as e:
            _logger.error('Get GJP Sales order error, %s', e, exc_info=True)
            return {}, f'Get GJP Sales order error, {e}'
        response_dict = response.json()
        return response_dict, ''

GJPQuery = GJPQueryObj()
