from odoo import models, fields, api
from odoo import models, fields, api


class CustomerCreditHistory(models.Model):
    _name = 'customer.credit.history'
    _description = 'Customer credit History'
    _order = 'transaction_date desc'

    transaction_date = fields.Datetime(
        string='Transaction Date',
        help='交易日期',
        required=True
    )

    business_type = fields.Selection(
        selection=[
            ('receipt', 'Receipt'),
            ('goods_payment', 'Goods Payment'),
            ('reversal', 'Reversal'),
            ('internal_transfer_in', 'Internal Transfer In'),
            ('internal_transfer_out', 'Internal Transfer Out'),
            ('external_transfer_in', 'External Transfer In'),
            ('external_transfer_out', 'External Transfer Out'),
        ],
        string='Business Type',
        help='业务类型'
    )

    document_type = fields.Selection(
        selection=[
            ('payment_receipt', 'Payment receipt'),
            ('fund_transfer', 'Fund Transfer'),
        ],
        string="Document Type"
    )

    document_number = fields.Char(string="Document No.", compute="_compute_document_number", store=True, help="Document Number")

    funding_source = fields.Selection(
        string='Funding Source',
        selection=[('bank', 'Bank'), ('cash', 'Cash'), ('exchange', 'Exchange'), ('credit', 'Credit'), ('advance', 'Advance'), ('downpayment', 'Down Payment')],
        index=True,
        help='资金来源'
    )

    fund_type = fields.Selection(
        string='Fund Type',
        selection=[('advance', 'Advance Payment'), ('downpayment', 'Down Payment'), ('sopayment', 'SO Payment'), ('credit', 'Credit')],
        index=True,
        help='资金用途'
    )

    reconciliation_status = fields.Selection(
        selection=[
            ('not_applied', 'Not Applied'),
            ('partial_applied', 'Partial Applied'),
            ('fully_applied', 'Fully Applied')
        ],
        index=True,
        string='Reconciliation Status',
        help='核销狀態',
        default='not_applied'
    )

    notes = fields.Text(
        string='Notes',
        help='備註',
        compute="_compute_get_notes",
        store=True,
        inverse="_inverse_set_notes"
    )

    transaction_amount = fields.Float(
        string='Transaction Amount',
        help='Transaction Amount',
        digits=(16, 2)
    )

    advance_amount = fields.Float(
        string='Advance Amount',
        help='预收款金额',
        digits=(16, 2),
        store=True,
        compute="compute_get_advance_amount",
    )
    downpayment_amount = fields.Float(
        string='Down Payment Amount',
        help='首付款金额',
        store=True,
        digits=(16, 2)
    )
    credit_amount = fields.Float(
        string='Credit Amount',
        help='信用额度金额',
        digits=(16, 2),
        store=True,
    )

    advance_balance_amount = fields.Float(
        string='Advance Balance',
        help='预收款余额',
        digits=(16, 2)
    )

    downpayment_balance_amount = fields.Float(
        string='Down Payment Balance',
        help='预收款余额',
        digits=(16, 2)
    )

    credit_balance_amount = fields.Float(
        string='Credit Balance',
        help='预收款余额',
        digits=(16, 2)
    )

    operator = fields.Many2one(
        'res.users',
        string='Operator',
        help='操作人',
        default=lambda self: self.env.user
    )

    customer_id = fields.Many2one(
        'res.partner',
        string='Customer',
        help='客户'
    )
    related_customer_id = fields.Many2one(
        'res.partner',
        string='Related Customer',
        help='客户'
    )
    merged_bank_statement_ids = fields.Many2many(
        'galaxy.account.bank.merged.statement',
        relation="customer_credit_history_galaxy_bank_statment_rel",
        string='Transaction Number',
        help='银行对账单'
    )
    customer_advance_received_id = fields.Many2one(
        'customer.advance.received',
        string='Settlement Number',
        help='客户预收款',
        index=True
    )

    customer_advance_received_line_id = fields.Many2one(
        'customer.advance.received.lines',
        string='Settlement Line Number',
        help='預收结算单行号',
        index=True
    )

    customer_received_statement_id = fields.Many2one(
        'customer.received.statement',
        string='Reconciliation Number',
        index=True,
        help="核銷號",
    )

    customer_received_statement_line_id = fields.Many2one(
        'customer.received.statement.lines',
        string='Reconciliation Line Number',
        index=True,
        help="核銷行號",
    )
    fund_transfer_order_id = fields.Many2one(
        'fund.transfer.order',
        string='Fund Transfer Order',
        help='资金划拨单',
        index=True
    )

    statement_counts = fields.Integer(
        string='Statement Counts',
        compute='_compute_statement_counts',
        help='对账单行数'
    )

    @api.model
    def create(self, vals):
        ret = super(CustomerCreditHistory, self).create(vals)
        ret.customer_id.last_customer_credit_changed_date = fields.Datetime.now()
        ret.related_customer_id.last_advance_payment_changed_date = fields.Datetime.now()
        return ret

    @api.depends('transaction_amount', 'funding_source')
    def compute_get_advance_amount(self):
        for rec in self:
            rec.credit_amount = self.transaction_amount

    def _inverse_set_notes(self):
        """
        Inverse function to set notes
        """
        pass

    @api.depends('related_customer_id', 'document_type', 'business_type', 'fund_type')
    def _compute_get_notes(self):
        """
        计算备注信息
        """
        funding_source_map = {
            'advance': '預收款',
            'downpayment': '首付款',
            'sopayment': '貨款',
            'bank': '銀行',
            'cash': '現金',
            'exchange': '結匯',
            'credit': '信用额度',
        }
        business_type_action_map = {
            'receipt': '收',
            'goods_payment': '付',
            'reversal': '冲销',
            'internal_transfer_in': '內部轉入',
            'internal_transfer_out': '內部轉出',
            'external_transfer_in': '外部轉入',
            'external_transfer_out': '外部轉出',
            'credit': '信用额度',
        }
        for rec in self:
            if rec.related_customer_id and rec.customer_id != rec.related_customer_id:
                if rec.fund_type == 'sopayment':
                    rec.notes = f"{business_type_action_map.get(rec.business_type)}客戶{rec.related_customer_id.name}貨款"
                if rec.fund_type == 'advance':
                    rec.notes = f"{business_type_action_map.get(rec.business_type)}客戶{rec.related_customer_id.name}預收款"
                if rec.fund_type == 'downpayment':
                    rec.notes = f"{business_type_action_map.get(rec.business_type)}客戶{rec.related_customer_id.name}首付款"
                if rec.fund_type == 'credit':
                    rec.notes = f"{business_type_action_map.get(rec.business_type)}客戶{rec.related_customer_id.name}信用额度"
            elif rec.related_customer_id  and rec.related_customer_id == rec.customer_id:
                if rec.fund_type == 'sopayment':
                    rec.notes = f"{funding_source_map.get(rec.funding_source)}{business_type_action_map.get(rec.business_type)}貨款"
                if rec.fund_type == 'advance':
                    rec.notes = f"{funding_source_map.get(rec.funding_source)}{business_type_action_map.get(rec.business_type)}預收款"
                if rec.fund_type == 'downpayment':
                    rec.notes = f"{funding_source_map.get(rec.funding_source)}{business_type_action_map.get(rec.business_type)}首付款"
                if rec.fund_type == 'credit':
                    rec.notes = f"{funding_source_map.get(rec.funding_source)}{business_type_action_map.get(rec.business_type)}信用额度"
            elif not rec.related_customer_id:
                if rec.fund_type == 'sopayment':
                    rec.notes = f"{funding_source_map.get(rec.funding_source)}{business_type_action_map.get(rec.business_type)}貨款"
                if rec.fund_type == 'advance':
                    rec.notes = f"{funding_source_map.get(rec.funding_source)}{business_type_action_map.get(rec.business_type)}預收款"
                if rec.fund_type == 'downpayment':
                    rec.notes = f"{funding_source_map.get(rec.funding_source)}{business_type_action_map.get(rec.business_type)}首付款"
                if rec.fund_type == 'credit':
                    rec.notes = f"{funding_source_map.get(rec.funding_source)}{business_type_action_map.get(rec.business_type)}信用额度"
            else:
                rec.notes = ''
    
    @api.depends('fund_transfer_order_id', 'customer_advance_received_line_id')
    def _compute_document_number(self):
        """
        计算单据编号
        """
        for rec in self:
            if rec.fund_transfer_order_id:
                rec.document_number = rec.fund_transfer_order_id.name
            elif rec.customer_advance_received_line_id:
                rec.document_number = rec.customer_advance_received_line_id.name
            else:
                rec.document_number = ''

    @api.depends('merged_bank_statement_ids')
    def _compute_statement_counts(self):
        """
        计算对账单行数
        """
        for rec in self:
            rec.statement_counts = len(rec.merged_bank_statement_ids)

    def action_view_statement(self):
        """
        This function opens the bank statement
        """
        self.ensure_one()
        action = self.sudo().env.ref('galaxy_account_bank_statement.action_galaxy_account_merged_bank_statement').read()[0]
        action['domain'] = [('id', 'in', self.merged_bank_statement_ids.ids)]
        action['context'] = {'create': False, 'edit': False, 'delete': False}
        return action
