# -*- coding: utf-8 -*-

import logging
import os
import pandas as pd
import json
from odoo.exceptions import UserError
from odoo import models, fields, api, _
from odoo.tools.misc import formatLang

_logger = logging.getLogger(__name__)


class CustomerAdvanceReceived(models.Model):
    _inherit = 'customer.advance.received'

    # galaxy_account_bank_merged_statement_id = fields.Many2one('galaxy.account.bank.merged.statement', string='Bank Statement', index=True)
    bank_statement_line_ids = fields.Many2many('galaxy.account.bank.merged.statement', relation="advance_payment_galaxy_bank_statment_rel", copy=False, string='Bank Statement Lines')
    customer_settlement_id = fields.Many2one('customer.settlement', string="Customer Settlement")

    def action_confirm(self):
        self.state = 'confirm'

    def action_done(self, raise_not_bank_statement=True):
        """
        This function is used to confirm the customer advance received
        """
        for payment in self:
            if payment.state != 'confirm':
                raise UserError(_('Only confirm state can be done.'))
            payment.write({'state': 'done'})
            if payment.payer_id:
                payment.customer_advance_received_line_ids.payer_id = payment.payer_id.id
            if payment.received_payment_type == 'bank' and raise_not_bank_statement and not payment.bank_statement_line_ids:
                raise UserError(_("No bank statement lines selected."))
            if not all(bank_statement.state == 'done' for bank_statement in payment.bank_statement_line_ids):
                raise UserError(_("All selected bank statements must be in 'Audited' state."))
            for rec in payment.customer_advance_received_line_ids.filtered(lambda m: m.received_payment_type == 'offset'):
                payment.deduct_payer_balance(rec.payer_id, rec.amount, rec)
            for rec in payment.customer_advance_received_line_ids.filtered(lambda m: m.received_payment_type != 'offset' and m.received_type != 'deposit'):
                # 押金类型的，不能去核销之前的欠款，因为押金类型的，晚点需要退还给客户
                payment.apply_debt()
            payment.create_advance_payment_history()
            for line in payment.customer_advance_received_line_ids:
                if line.received_type == 'advance':
                    payment.customer_id.total_advance_payment_amount += line.balance_amount
                if line.received_type == 'downpayment':
                    payment.customer_id.total_downpayment_amount += line.balance_amount
            payment.customer_settlement_id.update_paid_amount(payment.balance_amount)
            for transfor_order in payment.customer_settlement_id.customer_fund_transfer_ids:
                transfor_order.action_done()
            payment.customer_settlement_id.action_done()
            payment.create_payment_allocation()

    def deduct_payer_balance(self, payer, amount, customer_advance_received_line):
        """
        检查代付的时候代付方余额是否足够
        """
        available_rec = self.search([('customer_receive_type', '=', 'normal'), ('customer_id', '=', payer.id),
                                     ('state', '=', 'done'), ('balance_amount', '>', 0)])
        available_amount = sum(available_rec.customer_advance_received_line_ids.filtered(lambda m: m.balance_amount > 0 and m.received_type == 'advance').mapped('balance_amount'))
        debt_rec = self.search([('customer_id', '=', payer.id), ('balance_amount', '>', 0), ('state', '=', 'done'),
                                ('customer_receive_type', '=', 'debt')])
        debt_amount = sum(debt_rec.mapped('balance_amount'))
        received_date = self.received_date
        credit_balance = payer.customer_credit_amount - debt_amount
        if available_amount + credit_balance < amount:
            raise UserError(_('The payer balance is not enough.'))
        compute_balance_amount = amount
        for rec in available_rec.customer_advance_received_line_ids.filtered(lambda m: m.balance_amount > 0 and m.received_type == 'advance'):
            if rec.balance_amount >= compute_balance_amount:
                rec.applied_amount += compute_balance_amount
                applied_amount = compute_balance_amount
                self.env['customer.advance.received.fund.transfer.lines'].create({
                    'customer_advance_received_line_id': customer_advance_received_line.id,
                    'applied_customer_advance_received_line_id': rec.id,
                    'applied_amount': compute_balance_amount,
                })
                self.env['customer.advance.received.fund.transfer.lines'].create({
                    'customer_advance_received_line_id': rec.id,
                    'applied_customer_advance_received_line_id': customer_advance_received_line.id,
                    'applied_amount': compute_balance_amount,
                })
                compute_balance_amount = 0
                self.create_payer_advance_payment_history(rec, amount)
                break
            else:
                compute_balance_amount -= rec.balance_amount
                self.env['customer.advance.received.fund.transfer.lines'].create({
                    'customer_advance_received_line_id': customer_advance_received_line.id,
                    'applied_customer_advance_received_line_id': rec.id,
                    'applied_amount': rec.balance_amount,
                })
                self.env['customer.advance.received.fund.transfer.lines'].create({
                    'customer_advance_received_line_id': rec.id,
                    'applied_customer_advance_received_line_id': customer_advance_received_line.id,
                    'applied_amount': rec.balance_amount,
                })
                rec.applied_amount += rec.balance_amount
                self.create_payer_advance_payment_history(rec, rec.balance_amount)
        if compute_balance_amount > 0:
            # 创建欠款记录
            debt_line = self.create_debt(payer, customer_advance_received_line.currency_id.name, compute_balance_amount,
                                         customer_advance_received_line, received_date).customer_advance_received_line_ids[0]
            customer_advance_received_line.customer_advance_debt_line_ids = [(0, 0, {
                                'customer_advance_debt_line_id': debt_line.id,
                                'applied_amount': compute_balance_amount,
                        })]

    def create_payer_advance_payment_history(self, payer_advance_received_line=None, amount=0.0):
        """
        This function is used to create advance payment history
        """
        history_line = []
        if self.state == 'cancel':
            business_type = 'receipt'
        if self.state == 'done':
            business_type = 'reversal'
        payer_customer_advance_received_id = payer_advance_received_line.customer_advance_received_id
        balance = payer_customer_advance_received_id.customer_id.advance_payment_balance_amount
        rec = payer_advance_received_line
        if business_type == 'receipt':
            balance += amount
            self.payer_id.transfered_out_advance_payment_amount -= amount
        else:
            balance -= amount
            self.payer_id.transfered_out_advance_payment_amount += amount
        history_line.append({
            'customer_id': payer_customer_advance_received_id.customer_id.id,
            'operator': self.env.user.id,
            'balance': balance,
            'income_amount': amount if business_type == 'receipt' else 0.0,
            'used_amount': amount if business_type == 'reversal' else 0.0,
            'reconciliation_status': payer_customer_advance_received_id.payment_state,
            'funding_source': payer_customer_advance_received_id.received_payment_type,
            'fund_type': rec.received_type,
            'business_type': business_type,
            'transaction_date': fields.Datetime.now(),
            'customer_advance_received_id': payer_customer_advance_received_id.id,
            'customer_advance_received_line_id': rec.id,
            'merged_bank_statement_ids': payer_customer_advance_received_id.bank_statement_line_ids,
        })
        self.env['customer.advance.payment.history'].create(history_line)

    def create_advance_payment_history(self):
        """
        This function is used to create advance payment history
        """
        history_line = []
        document_type = 'payment_receipt'
        if self.state == 'done':
            business_type = 'receipt'
        if self.state == 'cancel':
            business_type = 'reversal'
        advance_balance_amount = self.customer_id.advance_payment_balance_amount
        downpayment_balance_amount = self.customer_id.downpayment_balance_amount
        for rec in self.customer_advance_received_line_ids:
            if business_type == 'receipt':
                if rec.received_type == 'advance':
                    advance_balance_amount += rec.balance_amount
                elif rec.received_type == 'downpayment':
                    downpayment_balance_amount += rec.balance_amount
            else:
                if rec.received_type == 'advance':
                    advance_balance_amount -= rec.balance_amount
                elif rec.received_type == 'downpayment':
                    downpayment_balance_amount -= rec.balance_amount
            history_line.append({
                'customer_id': self.customer_id.id,
                'operator': self.env.user.id,
                'advance_balance_amount': advance_balance_amount,
                'downpayment_balance_amount': downpayment_balance_amount,
                'transaction_amount': rec.balance_amount if business_type == 'receipt' else -rec.balance_amount,
                'reconciliation_status': self.payment_state,
                'funding_source': self.received_payment_type,
                'fund_type': rec.received_type,
                'business_type': business_type,
                'document_type': document_type,
                'transaction_date': fields.Datetime.now(),
                'customer_advance_received_id': self.id,
                'customer_advance_received_line_id': rec.id,
                'merged_bank_statement_ids': self.bank_statement_line_ids,
            })
        self.env['customer.advance.payment.history'].create(history_line)

    def create_payment_allocation(self):
        """
        创建核销
        """
        # 如果预收款的行类型是SO货款或押金相关的，直接核销
        for payment in self:
            # 直接核销
            statement_amount = sum(payment.customer_advance_received_line_ids.filtered(lambda x: x.received_type == 'sopayment').mapped('balance_amount'))
            if statement_amount == 0:
                return
            statement_lines = []
            for line in payment.customer_advance_received_line_ids:
                if line.received_type in ('downpayment', 'sopayment'):
                    # 创建Customer received statement
                    statement_lines.append((0, 0, {
                        'customer_advance_received_line_id': line.id,
                        'allocate_amount': line.balance_amount,
                    }))
            statment = self.env['customer.received.statement'].create({
                'customer_id': payment.customer_id.id,
                'statement_date': fields.Date.today(),
                'amount': statement_amount,
                'statement_type': 'so',
                'statement_line_ids': statement_lines,
            })
            statment.action_confirm()

    def action_cancel_offsetlines(self):
        """
        取消核销offset记录
        """
        for rec in self.customer_advance_received_line_ids:
            for offset_line in rec.customer_advance_received_fund_transfer_line_ids:
                applied_amount = offset_line.applied_amount
                offset_line.applied_customer_advance_received_line_id.amount += offset_line.applied_amount
                offset_line.applied_customer_advance_received_line_id.applied_amount -= applied_amount
                self.create_payer_advance_payment_history(offset_line.applied_customer_advance_received_line_id)

    def action_cancel_debtlines(self):
        """
        取消debt line记录
        """
        for rec in self.customer_advance_received_line_ids:
            for dept_line in rec.customer_advance_debt_line_ids:
                dept_line.customer_advance_debt_line_id.customer_advance_received_id.action_cancel()

    def action_back_to_confirm(self):
        """
        This function is used to back the customer advance received to confirm state
        """
        for rec in self:
            if rec.state != 'done':
                raise UserError(_('Only done state can be back to confirm.'))
            rec.write({'state': 'confirm'})
            rec.customer_received_statement_ids.action_cancel()
            for line in rec.customer_advance_received_line_ids:
                if line.received_type == 'advance':
                    rec.customer_id.total_advance_payment_amount -= line.balance_amount
                if line.received_type == 'downpayment':
                    rec.customer_id.total_downpayment_amount -= line.balance_amount

    def action_cancel(self):
        """
        This function is used to cancel the customer advance received
        """
        for rec in self:
            if rec.customer_settlement_id and rec.customer_settlement_id.state not in ('draft', 'cancel'):
                raise UserError(_('You need cancel the customer settlement firstly.'))
            if rec.bank_statement_line_ids:
                raise UserError(_('You need remove link to bank statment firstly.'))
            if rec.state not in ('draft', 'cancel', 'confirm'):
                rec.write({'state': 'cancel'})
                rec.customer_received_statement_ids.action_cancel()
                # rec.customer_settlement_id.update_paid_amount(-rec.balance_amount)
                # rec.action_cancel_offsetlines()
                # self.action_cancel_debtlines()
                rec.create_advance_payment_history()
                for line in rec.customer_advance_received_line_ids:
                    if line.received_type == 'advance':
                        rec.customer_id.total_advance_payment_amount -= line.balance_amount
                    if line.received_type == 'downpayment':
                        rec.customer_id.total_downpayment_amount -= line.balance_amount
            else:
                rec.write({'state': 'cancel'})

    def action_draft(self):
        """
        This function is used to make the customer advance received to draft state
        """
        # if self.bank_statement_line_ids:
        #     raise UserError(_('You need remove link to bank statment firstly.'))
        if any(statement.state == 'done' for statement in self.customer_received_statement_ids):
            raise UserError(
                _('The statement has been confirmed, you cannot cancel it.'))
        self.customer_received_statement_ids.filtered(
            lambda m: m.state == 'draft').action_cancel()
        self.action_cancel_offsetlines()
        # self.action_cancel_debtlines()
        self.bank_statement_line_ids = [(5, 0, 0)]
        if self.state == 'done':
            self.write({'state': 'draft'})
            self.customer_id.total_advance_payment_amount -= self.net_amount

    def develop_create_advance_payment(self, customer, amount, received_payment_type='bank', received_type='advance'):
        currency_name = customer.customer_currency_id.name
        currency_id = self.env['account.daily.exchange.rate'].search([('name', '=', currency_name)], limit=1)
        received_date = fields.Date.context_today(self)
        # 先创建一个未分配预收款
        customer_advance_received_line_id = [
                (0, 0, {
                    'amount': amount,
                    'received_payment_type': received_payment_type,
                    'payer_id': customer.id,
                    'currency_id': currency_id.id,
                    'exchange_rate': currency_id.rate,
                    'received_type': received_type,
                    'customer_currency_amount': amount,
                    'note': '期初导入',
                })]
        return self.env['customer.advance.received'].create({
                'customer_id': customer.id,
                'customer_settlement_id': self.id,
                'received_date': received_date,
                'currency_id': currency_id.id,
                'received_payment_type': received_payment_type,
                'customer_receive_type': 'normal',
                'customer_advance_received_line_ids': customer_advance_received_line_id,
                'note': '期初导入',
            })

    def develop_initialize_advance_receive(self):
        """
        初始化预收款
        """
        # 清理之前的预收款记录
        self.env.cr.execute("""
                    delete from fund_transfer_order;
                    delete from customer_settlement;
                    delete from gjp_sale_order_summary;
                    delete from customer_received_statement;
                    delete from customer_advance_received;
                    delete from customer_advance_payment_history;
                    delete from customer_credit_history;
        """)
        self.env['fund.transfer.order'].search([]).unlink()
        self.env['customer.advance.payment.history'].search([]).unlink()
        self.env['res.partner'].search([]).write({
            'total_advance_payment_amount': 0.0,
            'total_downpayment_amount': 0,
            'applied_advance_payment_amount': 0.0,
            'applied_downpayment_amount': 0.0,
            'reversal_advance_payment_amount': 0.0,
            'reversal_downpayment_amount': 0.0,
            'allocate_advance_payment_amount': 0.0,
            'allocate_downpayment_amount': 0.0,
            'transfered_out_downpayment_amount': 0.0,
            'transfered_out_advance_payment_amount': 0.0,
            'downpayment_balance_amount': 0.0,
            'advance_payment_balance_amount': 0.0,
            'locked_customer_credit_amount': 0.0,
            })
        self.env['customer.settlement'].search([]).action_cancel()
        self.env['customer.settlement'].search([]).unlink()
        self.env['gjp.sale.order.summary'].search([]).unlink()
        self.env['gjp.sale.order'].search([]).unlink()
        self.env['customer.received.statement'].search([]).action_cancel()
        self.env['customer.received.statement'].search([]).unlink()
        self.env['customer.advance.received'].search([]).action_cancel()
        self.env['customer.advance.received'].search([]).unlink()
        base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        excel_file_path = os.path.join(base_dir, 'data', '预收款项2025.xlsx')
        try:
            # 读取第一个工作表
            df1 = pd.read_excel(excel_file_path, sheet_name=0)
            json_data_1 = json.loads(df1.to_json(
                orient='records', force_ascii=False))
            receive_type_dict = {'首付款': 'downpayment', '预收款': 'advance'}
            for rec in json_data_1:
                received_payment_type = 'bank'
                customer_no = rec.get('Odoo 代码') or ''
                gjp_name = rec.get('管家婆')
                received_type = rec.get('类别')
                odoo_received_type = receive_type_dict.get(received_type)
                if not odoo_received_type:
                    _logger.error(
                        _("Received type %s is not recognized.") % received_type)
                    continue
                if not customer_no and not gjp_name:
                    continue
                amount = rec.get('2025当前余额', 0)
                currency = rec.get('幣種').strip()
                odoo_customer = self.env['res.partner'].search([('bid_user_id', '=', customer_no.strip())], limit=1)
                if not odoo_customer:
                    _logger.error(
                        _("Customer %s not found.") % gjp_name)
                    continue
                odoo_currency = odoo_customer.customer_currency_id.name
                if odoo_currency != currency:
                    _logger.error(
                        _("Currency mismatch for customer %s: expected %s, got %s") % (gjp_name, odoo_currency, currency))
                    continue
                if amount <= 0:
                    _logger.error(
                        _("Invalid amount for customer %s: %s") % (gjp_name, amount))
                    continue
                adv_rec = self.develop_create_advance_payment(odoo_customer, amount, received_payment_type, odoo_received_type)
                adv_rec.action_confirm()
                adv_rec.action_done(raise_not_bank_statement=False)
        except FileNotFoundError:
            raise UserError(
                _("The specified Excel file was not found: %s") % excel_file_path)
        except Exception as e:
            raise UserError(
                _("An error occurred while importing the Excel file: %s") % str(e))
    
    def develop_initialize_advance_receive_tmp(self):
        """
        初始化预收款--来自于tmp目录文件，并且不删除管家婆订单
        """
        # 清理之前的预收款记录
        self.env.cr.execute("""
                    delete from fund_transfer_order;
                    delete from customer_settlement;
                    delete from gjp_sale_order_summary;
                    delete from customer_received_statement;
                    delete from customer_advance_received;
                    delete from customer_advance_payment_history;
                    delete from customer_credit_history;
        """)
        self.env['fund.transfer.order'].search([]).unlink()
        self.env['customer.advance.payment.history'].search([]).unlink()
        self.env['res.partner'].search([]).write({
            'total_advance_payment_amount': 0.0,
            'total_downpayment_amount': 0,
            'applied_advance_payment_amount': 0.0,
            'applied_downpayment_amount': 0.0,
            'reversal_advance_payment_amount': 0.0,
            'reversal_downpayment_amount': 0.0,
            'allocate_advance_payment_amount': 0.0,
            'allocate_downpayment_amount': 0.0,
            'transfered_out_downpayment_amount': 0.0,
            'transfered_out_advance_payment_amount': 0.0,
            'downpayment_balance_amount': 0.0,
            'advance_payment_balance_amount': 0.0,
            'locked_customer_credit_amount': 0.0,
            })
        self.env['customer.settlement'].search([]).action_cancel()
        self.env['customer.settlement'].search([]).unlink()
        self.env['gjp.sale.order.summary'].search([]).unlink()
        # self.env['gjp.sale.order'].search([]).unlink()
        self.env['customer.received.statement'].search([]).action_cancel()
        self.env['customer.received.statement'].search([]).unlink()
        self.env['customer.advance.received'].search([]).action_cancel()
        self.env['customer.advance.received'].search([]).unlink()
        excel_file_path = '/tmp/预收款项2025.xlsx'
        try:
            # 读取第一个工作表
            df1 = pd.read_excel(excel_file_path, sheet_name=0)
            json_data_1 = json.loads(df1.to_json(
                orient='records', force_ascii=False))
            receive_type_dict = {'首付款': 'downpayment', '预收款': 'advance'}
            for rec in json_data_1:
                received_payment_type = 'bank'
                customer_no = rec.get('Odoo 代码') or ''
                gjp_name = rec.get('管家婆')
                received_type = rec.get('类别')
                odoo_received_type = receive_type_dict.get(received_type)
                if not odoo_received_type:
                    _logger.error(
                        _("Received type %s is not recognized.") % received_type)
                    continue
                if not customer_no and not gjp_name:
                    continue
                amount = rec.get('2025当前余额', 0)
                currency = rec.get('幣種').strip()
                odoo_customer = self.env['res.partner'].search([('bid_user_id', '=', customer_no.strip())], limit=1)
                if not odoo_customer:
                    _logger.error(
                        _("Customer %s not found.") % gjp_name)
                    continue
                odoo_currency = odoo_customer.customer_currency_id.name
                if odoo_currency != currency:
                    _logger.error(
                        _("Currency mismatch for customer %s: expected %s, got %s") % (gjp_name, odoo_currency, currency))
                    continue
                if amount <= 0:
                    _logger.error(
                        _("Invalid amount for customer %s: %s") % (gjp_name, amount))
                    continue
                adv_rec = self.develop_create_advance_payment(odoo_customer, amount, received_payment_type, odoo_received_type)
                adv_rec.action_confirm()
                adv_rec.action_done(raise_not_bank_statement=False)
        except FileNotFoundError:
            raise UserError(
                _("The specified Excel file was not found: %s") % excel_file_path)
        except Exception as e:
            raise UserError(
                _("An error occurred while importing the Excel file: %s") % str(e))

    def develop_initialize_advance_receive_not_delete_summary(self):
        """
        初始化预收款
        """
        # 清理之前的预收款记录
        self.env.cr.execute("""
                    delete from fund_transfer_order;
                    delete from customer_settlement;
                    delete from customer_received_statement;
                    delete from customer_advance_received;
                    delete from customer_advance_payment_history;
        """)
        self.env['fund.transfer.order'].search([]).unlink()
        self.env['customer.advance.payment.history'].search([]).unlink()
        self.env['res.partner'].search([]).write({
            'total_advance_payment_amount': 0.0,
            'total_downpayment_amount': 0,
            'applied_advance_payment_amount': 0.0,
            'applied_downpayment_amount': 0.0,
            'reversal_advance_payment_amount': 0.0,
            'reversal_downpayment_amount': 0.0,
            'allocate_advance_payment_amount': 0.0,
            'allocate_downpayment_amount': 0.0,
            'transfered_out_downpayment_amount': 0.0,
            'transfered_out_advance_payment_amount': 0.0,
            'downpayment_balance_amount': 0.0,
            'advance_payment_balance_amount': 0.0,
            })
        self.env['customer.settlement'].search([]).action_cancel()
        self.env['customer.settlement'].search([]).unlink()
        self.env['customer.received.statement'].search([]).action_cancel()
        self.env['customer.received.statement'].search([]).unlink()
        self.env['customer.advance.received'].search([]).action_cancel()
        self.env['customer.advance.received'].search([]).unlink()
        base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        excel_file_path = os.path.join(base_dir, 'data', '预收款项2025.xlsx')
        try:
            # 读取第一个工作表
            df1 = pd.read_excel(excel_file_path, sheet_name=0)
            json_data_1 = json.loads(df1.to_json(
                orient='records', force_ascii=False))
            receive_type_dict = {'首付款': 'downpayment', '预收款': 'advance'}
            for rec in json_data_1:
                received_payment_type = 'bank'
                customer_no = rec.get('Odoo 代码')
                gjp_name = rec.get('管家婆')
                received_type = rec.get('类别')
                odoo_received_type = receive_type_dict.get(received_type)
                if not odoo_received_type:
                    _logger.error(
                        _("Received type %s is not recognized.") % received_type)
                    continue
                if not customer_no and not gjp_name:
                    continue
                amount = rec.get('2025当前余额', 0)
                currency = rec.get('幣種').strip()
                odoo_customer = self.env['res.partner'].search([('bid_user_id', '=', customer_no.strip())], limit=1)
                if not odoo_customer:
                    _logger.error(
                        _("Customer %s not found.") % gjp_name)
                    continue
                odoo_currency = odoo_customer.customer_currency_id.name
                if odoo_currency != currency:
                    _logger.error(
                        _("Currency mismatch for customer %s: expected %s, got %s") % (gjp_name, odoo_currency, currency))
                    continue
                if amount <= 0:
                    _logger.error(
                        _("Invalid amount for customer %s: %s") % (gjp_name, amount))
                    continue
                adv_rec = self.develop_create_advance_payment(odoo_customer, amount, received_payment_type, odoo_received_type)
                adv_rec.action_confirm()
                adv_rec.action_done(raise_not_bank_statement=False)
        except FileNotFoundError:
            raise UserError(
                _("The specified Excel file was not found: %s") % excel_file_path)
        except Exception as e:
            raise UserError(
                _("An error occurred while importing the Excel file: %s") % str(e))

class CustomerAdvanceReceivedLines(models.Model):
    _inherit = 'customer.advance.received.lines'

    customer_settlement_line_id = fields.Many2one('customer.settlement.line', string='Customer Settlement Line', index=True, copy=False, help="Customer Settlement Line")
    gjp_order_summary_id = fields.Many2one('gjp.sale.order.summary', string='GJP Sale Order Summary', index=True, copy=False, help="GJP Order Summary")