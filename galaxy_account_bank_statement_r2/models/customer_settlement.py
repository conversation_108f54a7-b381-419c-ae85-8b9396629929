# -*- coding: utf-8 -*-

import logging
import base64

from odoo.exceptions import UserError
from odoo import models, fields, api, _
from odoo.tools import float_compare
from odoo.addons.base_galaxy.models.public_func import parse_amount_to_chinese

_logger = logging.getLogger(__name__)


class CustomerSettlement(models.Model):
    _name = 'customer.settlement'
    _inherit = ['portal.mixin', 'mail.thread', 'mail.activity.mixin']
    _description = 'Customer Settlement'
    _order = "id desc"

    name = fields.Char(string='Number', copy=False, readonly=True,
                       index=True, default=lambda self: _('New'))
    advance_receipt_number = fields.Char(
        string='Advance Receipt Number', copy=False,
        compute="_compute_get_received_fee",
        help="The advance receipt number for this settlement.")
    state = fields.Selection([
        ('draft', 'Draft'), ('confirm', 'Confirm'), ('audited', 'Audited'),
        ('partial_paid', 'Partial Paid'), ('paid', 'Paid'),
        ('cancel', 'Cancel')], default='draft', tracking=True)
    payer_id = fields.Many2one('res.partner', string='Payer')
    customer_currency_id = fields.Many2one('res.currency',
                                           related='payer_id.customer_currency_id', string='Payer Currency', store=True, index=True)
    statement_amount = fields.Float(string='Statement Amount', digits=(12, 2), compute="_compute_get_statement_amount", store=True, inverse="_inverse_set_statement_amount")
    total_allocate_amount = fields.Float(
        digits=(12, 2), compute='_compute_allocate_amount', store=True)

    total_allocate_chinese_amount = fields.Char(compute='_compute_allocate_chinese_amount',)

    paid_amount = fields.Float(string='Paid Amount', digits=(12, 2))
    balance_amount = fields.Float(
        digits=(12, 2), compute='_compute_balance_amount', store=True)
    allocate_balance_amount = fields.Float(
        digits=(12, 2), compute='_compute_allocate_balance_amount')
    note = fields.Text()
    customer_statement_line_ids = fields.One2many(
        'customer.settlement.lines', 'customer_settlement_id', string='Statement Lines')
    advance_payment_counts = fields.Integer(compute='_compute_advancepayment_counts')
    advance_payments_ids = fields.Many2many(
        'customer.advance.received', string='Advance Payments', relation='galaxy_customer_settlement_advance_payments',
        help="This field contains the advance payments related to this settlement.")
    customer_fund_transfer_ids = fields.Many2many(
        'fund.transfer.order',
        string='Customer Fund Transfer',
        relation='galaxy_customer_settlement_fund_transfer')
    customer_fund_transfer_counts = fields.Integer(compute='_compute_fund_transfer_counts')
    gjp_sale_order_summary_ids = fields.Many2many(
        'gjp.sale.order.summary', string='GJP Sale Order Summaries',
        relation='galaxy_customer_settlement_gjp_sale_order_summary',
        compute='_compute_get_order_summary_ids', store=True,
        help="This field contains the GJP Sale Order Summaries related to this settlement.")
    gjp_sale_order_summary_counts = fields.Integer(
        string='GJP Sale Order Summary Counts',
        compute='_compute_order_summary_counts')
    received_payment_type = fields.Selection(
        [('bank', 'Bank'),
         ('cash', 'Cash'),
         ('advance', 'Advance Payment'),
         ('downpayment', 'Downpayment'),
         ('exchange', 'Exchange'),
         ('credit', 'Credit'),
         ], string='Payment Type', default="bank", required=True)
    make_by = fields.Many2one('res.users', string='Make By',
                              help="The user who created the AD Payment.", default=lambda self: self.env.user)
    advance_payment_balance_amount = fields.Float(
        string='Advance Payment Balance Amount',
        digits=(12, 2),
        compute="_compute_get_payment_balance_amount",
        help="The balance amount of advance payment for the payer.")
    downpayment_balance_amount = fields.Float(
        string='Down Payment Balance Amount', readonly=True,
        digits=(12, 2),
        help="The balance amount of down payment for the payer.")
    credit_balance_amount = fields.Float(
        string='Credit Balance Amount', readonly=True,
        digits=(12, 2),
        help="The balance amount of credit amount for the payer.")
    received_fee = fields.Float(
        string='Received Fee', digits=(12, 2), compute='_compute_get_received_fee',
        help="The fee received for this settlement.")
    total_amount = fields.Float(
        string='Total Amount', digits=(12, 2),
        compute='_compute_get_received_fee',
        help="The total amount of the settlement, including statement amount and received fee.")

    settlement_type = fields.Selection(
        [('advance', 'Advance Settlement'), ('sopayment', 'Payment Settlement'), ('credit', 'Repayment Settlement')],
        string='Settlement Type', required=True,
        help="The type of settlement, either normal or advance payment.")
    locked_customer_credit_amount = fields.Float(related="payer_id.locked_customer_credit_amount",
                                                 digits=(12, 2),
                                                 store=True, string='Locked Customer Credit Amount', readonly=True,)

    signature = fields.Image('Signature', help='Signature received through the portal.', copy=False, attachment=True, max_width=1024, max_height=1024)
    signed_by = fields.Char('Signed By', help='Name of the person that signed the settlement.', copy=False)
    signed_on = fields.Datetime('Signed On', help='Date of the signature.', copy=False)
    require_signature = fields.Boolean('Online Signature', default=True, readonly=True,
                                       states={'draft': [('readonly', False)], 'confirm': [('readonly', False)]},
                                       help='Request a online signature to the customer in order to confirm settlements automatically.')
    access_url = fields.Char(
        'Portal Access URL', compute='_compute_access_url',
        help='Customer Portal URL')
    payee_ids = fields.Many2many(
        'res.partner', string='Payees',
        help="This field contains the payees related to this settlement.",
        compute='_compute_payee_ids', store=True)
    lines_received_type = fields.Char(string="Received Type", compute="_compute_lines_received_type", store=True)
    paid_date = fields.Datetime(string="Paid Date", tracking=True)

    @api.depends('customer_statement_line_ids.received_type')
    def _compute_lines_received_type(self):
        """
        Compute the received type for each line in customer_statement_line_ids
        """
        for rec in self:
            if rec.customer_statement_line_ids:
                type_display_names = [dict(rec.customer_statement_line_ids._fields['received_type'].selection).get(rt, rt) for rt in rec.customer_statement_line_ids.mapped('received_type')]
                rec.lines_received_type = ','.join(type_display_names)
            else:
                rec.lines_received_type = ''

    @api.depends('customer_statement_line_ids.customer_id')
    def _compute_payee_ids(self):
        """
        Compute the payees related to this settlement
        """
        for rec in self:
            rec.payee_ids = rec.customer_statement_line_ids.mapped('customer_id')
    
    @api.onchange('settlement_type')
    def _onchange_settlement_type(self):
        if self.settlement_type != 'sopayment':
            for line in self.customer_statement_line_ids:
                line.gjp_sale_order_summary_id = False
        for line in self.customer_statement_line_ids:
            line.received_type = self.settlement_type

    def has_to_be_signed(self):
        """
        Check if the settlement needs to be signed by the customer
        """
        return self.state == 'confirm' and not self.signature and self.require_signature

    def get_payment_type_display_name(self):
        """
        Get display name for received_payment_type field
        """
        payment_type_mapping = {
            'bank': 'Bank Transfer(銀行)',
            'cash': 'Cash(現金)',
            'advance': 'Advance Payment(預收款)',
            'downpayment': 'Down Payment(首付款)',
            'exchange': 'Exchange(結匯)'
        }
        return payment_type_mapping.get(self.received_payment_type, self.received_payment_type)

    def action_audited(self):
        """
        This function is used to mark the customer settlement as audited
        """
        for rec in self:
            if rec.state != 'confirm':
                raise UserError(_('Only confirmed settlements can be audited.'))
            rec.state = 'audited'
            self.create_advance_payment()
            if not rec.advance_payments_ids:
                for fund_order in rec.customer_fund_transfer_ids:
                    fund_order.action_done()

    def action_print(self):
        """
        This function is used to print the customer settlement report
        """
        return self.env.ref('galaxy_account_bank_statement_r2.action_customer_settlement_report').report_action(self)
        # if self.received_payment_type in ['advance', 'downpayment']:
        #     transfer_orders = self.customer_fund_transfer_ids
        #     return self.env.ref('galaxy_account_bank_statement_r2.action_fund_transfer_order_report').report_action(transfer_orders)
        # else:
        #     return self.env.ref('galaxy_account_bank_statement_r2.action_customer_settlement_report').report_action(self)

    @api.depends('advance_payments_ids')
    def _compute_get_received_fee(self):
        """
        计算预付款的手续费
        """
        for rec in self:
            rec.received_fee = sum(line.received_fee for line in rec.advance_payments_ids.filtered(lambda m: m.state != 'cancel'))
            rec.total_amount = sum(line.total_amount for line in rec.advance_payments_ids.filtered(lambda m: m.state != 'cancel'))
            if not rec.received_fee and rec.received_payment_type == 'cash':
                if rec.customer_currency_id.name != 'HKD':
                    rec.received_fee = round(rec.statement_amount * 0.001, 2)
                else:
                    rec.received_fee = round(rec.statement_amount * 0.001, 0)
            if not rec.total_amount:
                rec.total_amount = rec.statement_amount + rec.received_fee
            rec.advance_receipt_number = ', '.join(
                line.name for line in rec.advance_payments_ids.filtered(lambda m: m.state != 'cancel'))

    def _inverse_set_statement_amount(self):
        pass

    @api.depends('customer_statement_line_ids', 'customer_statement_line_ids.gjp_sale_order_summary_id')
    def _compute_get_order_summary_ids(self):
        """
        计算与此结算相关的GJP销售订单汇总
        """
        for rec in self:
            rec.gjp_sale_order_summary_ids = rec.customer_statement_line_ids.mapped('gjp_sale_order_summary_id')

    @api.depends('customer_statement_line_ids.allocate_amount')
    def _compute_get_statement_amount(self):
        """
        计算结算单的总金额
        """
        for rec in self:
            rec.statement_amount = sum(item.allocate_amount for item in rec.customer_statement_line_ids)

    @api.depends('payer_id', 'payer_id.advance_payment_balance_amount', 'payer_id.allocate_advance_payment_amount',
                 'payer_id.downpayment_balance_amount', 'payer_id.allocate_downpayment_amount')
    def _compute_get_payment_balance_amount(self):
        """
        计算预付款和定金的余额金额
        """
        for rec in self:
            rec.advance_payment_balance_amount = rec.payer_id.advance_payment_balance_amount - rec.payer_id.allocate_advance_payment_amount
            rec.downpayment_balance_amount = rec.payer_id.downpayment_balance_amount - rec.payer_id.allocate_downpayment_amount
            rec.credit_balance_amount = rec.payer_id.available_customer_credit_amount - rec.payer_id.allocate_credit_amount

    @api.depends('statement_amount', 'total_allocate_amount')
    def _compute_allocate_balance_amount(self):
        for rec in self:
            rec.allocate_balance_amount = rec.statement_amount - rec.total_allocate_amount

    def action_done(self):
        """
        This function is used to mark the customer settlement as done
        """
        fund_transfer_done = all(item == 'done' for item in self.customer_fund_transfer_ids.mapped('state'))
        advance_payment_done = all(item == 'done' for item in self.advance_payments_ids.mapped('state'))
        if self.received_payment_type in ['advance', 'downpayment', 'credit']:
            paid_amount = self.balance_amount
        else:
            paid_amount = 0
        if paid_amount:
            self.update_paid_amount(paid_amount)
        if fund_transfer_done and advance_payment_done:
            self.state = 'paid'
            self.paid_date = fields.datetime.now()
            # 暂时注释
            # self.action_send_settlement_done_email()

    @api.depends('gjp_sale_order_summary_ids')
    def _compute_order_summary_counts(self):
        """
        计算与此结算相关的GJP销售订单汇总数量
        """
        for rec in self:
            rec.gjp_sale_order_summary_counts = len(rec.gjp_sale_order_summary_ids)

    def write(self, vals):
        """
        Override the write method to set the name field
        """
        ret = super(CustomerSettlement, self).write(vals)
        for rec in self:
            if rec.settlement_type == 'sopayment':
                if len(rec.customer_statement_line_ids) > 1:
                    raise UserError(_('Only one statement line is allowed for SO Payment settlement type.'))
        return ret

    @api.constrains('settlement_type')
    def check_settlement_type(self):
        if self.settlement_type == 'credit' and self.received_payment_type == 'credit':
            raise UserError(_('The settlement type cannot be Credit when the received payment type is Credit.'))

    @api.constrains('customer_statement_line_ids')
    def _check_statement_lines(self):
        """
        检查结算行的录入是否有错误
        """
        if self.settlement_type == 'sopayment':
            if len(self.customer_statement_line_ids) > 1:
                raise UserError(_('Only one statement line is allowed for SO Payment settlement type.'))
            if any(line.received_type != 'sopayment' for line in self.customer_statement_line_ids):
                raise UserError(_('The received type of the statement line must be SO Payment for SO Payment settlement.'))
        if self.settlement_type == 'advance':
            if any(line.received_type == 'sopayment' for line in self.customer_statement_line_ids):
                raise UserError(_('The received type of the statement line can not be SO Payment for Downpayment and Advance settlement.'))
        if self.settlement_type == 'credit' and self.received_payment_type == 'credit':
            raise UserError(_('The settlement type cannot be Credit when the received payment type is Credit.'))
        if self.settlement_type == 'credit' and self.statement_amount > self.locked_customer_credit_amount:
            raise UserError(_('Statement amount should not exceed the locked customer credit amount.'))
        for rec in self.customer_statement_line_ids:
            if not rec.customer_id:
                continue
            if rec.customer_id.customer_currency_id != self.customer_currency_id:
                raise UserError(_('All statement lines must have the same customer currency.'))
            if rec.gjp_sale_order_summary_id and rec.customer_id != rec.gjp_sale_order_summary_id.customer_id:
                raise UserError(_('The customer in the statement line must match the customer in the GJP Sale Order Summary.'))
            if self.payer_id == rec.customer_id and self.received_payment_type == rec.received_type:
                # 如果付款人是结算行的客户，并且付款类型与结算行的类型相同，则抛出错误
                raise UserError(_('The payer cannot be the same as the customer in the statement line with the same received type.'))
            if self.received_payment_type == 'downpayment' and self.payer_id != rec.customer_id:
                # 如果付款类型是定金，付款人必须是结算行的客户
                raise UserError(_('The payer must be the same as the customer in the statement line when the payment type is downpayment.'))
            # 分配金额不能大于结算行的未结金额
            if rec.sale_order_unsettlement_amount:
                if rec.allocate_amount > rec.sale_order_unsettlement_amount:
                    raise UserError(_('The allocated amount cannot exceed the unsettled amount of the GJP Sale Order Summary.'))
            if self.settlement_type == 'credit':
                if rec.received_type != 'credit':
                    raise UserError(_('The received type of the statement line must be Credit for Credit settlement.'))
                if rec.customer_id != self.payer_id:
                    raise UserError(_('The customer in the statement line must be the same as the payer for Credit settlement.'))
                # if self.received_payment_type not in ('bank', 'cash', 'exchange'):
                #     raise UserError(_('The received payment type must be Bank, Cash, or Exchange for Credit settlement.'))
        total_allocation_amount = sum(
            line.allocate_amount for line in self.customer_statement_line_ids)
        if self.received_payment_type == 'advance' and float_compare(total_allocation_amount, self.advance_payment_balance_amount, precision_digits=2) > 0:
            raise UserError(_('The total allocation amount cannot exceed the advance payment balance amount.'))
        if self.received_payment_type == 'downpayment' and total_allocation_amount > self.downpayment_balance_amount:
            raise UserError(_('The total allocation amount cannot exceed the down payment balance amount.'))

    @api.depends('advance_payments_ids')
    def _compute_advancepayment_counts(self):
        """
        计算与此结算相关的预付款数量
        """
        for rec in self:
            rec.advance_payment_counts = len(rec.advance_payments_ids)
            
    def action_view_summary_orders(self):
        """
        This function returns an action that display existing GJP Sale Order Summaries
        """
        return {
            'name': _('GJP Sale Order Summaries'),
            'type': 'ir.actions.act_window',
            'res_model': 'gjp.sale.order.summary',
            'view_mode': 'tree,form',
            'domain': [('id', 'in', self.gjp_sale_order_summary_ids.ids)],
            'context': {'default_customer_settlement_id': self.id}
        }

    def action_view_advance_payments(self):
        """
        This function returns an action that display existing advance payments
        """
        return {
            'name': _('Advance Payments'),
            'type': 'ir.actions.act_window',
            'res_model': 'customer.advance.received',
            'view_mode': 'tree,form',
            'domain': [('id', 'in', self.advance_payments_ids.ids)],
            'context': {'default_customer_settlement_id': self.id}
        }

    @api.depends('customer_fund_transfer_ids')
    def _compute_fund_transfer_counts(self):
        """
        计算与此结算相关的代付记录数量
        """
        for rec in self:
            rec.customer_fund_transfer_counts = len(rec.customer_fund_transfer_ids)

    def create_fund_transfer_order(self):
        """
        创建资金互转记录---如果是预付款或者downpayment在这边创建
        银行，现金，结汇在创建银行或者现金，结汇的时候创建互转单
        """
        data_rec = self.env['fund.transfer.order']
        if self.received_payment_type not in ['advance', 'downpayment', 'credit']:
            return data_rec
        if self.customer_fund_transfer_ids:
            raise UserError(_('An fund transfer order for this customer already exists.'))
        if self.received_payment_type == 'advance':
            if self.statement_amount > (self.payer_id.advance_payment_balance_amount - self.payer_id.allocate_advance_payment_amount):
                raise UserError(_('The statement amount cannot exceed the advance payment balance amount.'))
        elif self.received_payment_type == 'downpayment':
            if self.statement_amount > (self.payer_id.downpayment_balance_amount - self.payer_id.allocate_downpayment_amount):
                raise UserError(_('The statement amount cannot exceed the downpayment balance amount.'))
        elif self.received_payment_type == 'credit':
            if self.statement_amount > (self.credit_balance_amount):
                raise UserError(_('The statement amount cannot exceed the credit balance amount.'))
        transfer_date = fields.Date.context_today(self)
        for line in self.customer_statement_line_ids:
            transfer_type = 'internal' if self.payer_id == line.customer_id else 'external'
            rec = self.env['fund.transfer.order'].create({
                'payer_id': self.payer_id.id,
                'payee_id': line.customer_id.id,
                'transfer_date': transfer_date,
                'fund_source': self.received_payment_type,
                'fund_to': line.received_type,
                'transfer_type': transfer_type,
                'amount': line.allocate_amount,
                'customer_settlement_id': self.id,
                'customer_settlement_line_id': line.id,
            })
            rec.action_confirm()
            data_rec |= rec
        self.customer_fund_transfer_ids = tuple(data_rec.ids)

    def create_advance_payment(self):
        """
        创建预付款记录,在一个结算单里面，一个客户只创建一个预付款记录
        付款类型不是'advance', 'downpayment' 才生成付款单
        """
        data_rec = self.env['customer.advance.received']
        if self.received_payment_type in ['advance', 'downpayment', 'credit']:
            return data_rec
        if self.advance_payments_ids:
            raise UserError(_('An advance payment for this customer already exists.'))
        currency_name = self.customer_currency_id.name
        # customers_set = set(self.customer_statement_line_ids.mapped('customer_id'))
        currency_id = self.env['account.daily.exchange.rate'].search([('name', '=', currency_name)], limit=1)
        received_date = fields.Date.context_today(self)
        # 先创建一个未分配预收款
        customer_advance_received_line_id = [
                (0, 0, {
                    'amount': self.statement_amount,
                    'received_payment_type': self.received_payment_type,
                    'payer_id': self.payer_id.id,
                    'currency_id': currency_id.id,
                    'exchange_rate': currency_id.rate,
                    'received_type': 'advance',
                    # 'customer_settlement_line_id': line.id,
                    # 'gjp_order_summary_id': line.gjp_sale_order_summary_id.id,
                    'customer_currency_amount': self.statement_amount,
                })]
        data_rec |= self.env['customer.advance.received'].create({
                'customer_id': self.payer_id.id,
                'customer_settlement_id': self.id,
                'payer_id': self.payer_id.id,
                'received_date': received_date,
                'currency_id': currency_id.id,
                'received_payment_type': self.received_payment_type,
                'customer_receive_type': 'normal',
                'customer_advance_received_line_ids': customer_advance_received_line_id,
            })
        data_rec.action_confirm()
        # customer_advance_received_line_ids = []
        # for customer in customers_set:
        #     customer_settlement_lines = self.customer_statement_line_ids.filtered(lambda x: x.customer_id == customer)
        #     customer_advance_received_line_ids = [
        #         (0, 0, {
        #             'amount': line.allocate_amount,
        #             'received_payment_type': self.received_payment_type,
        #             'payer_id': self.payer_id.id,
        #             'currency_id': currency_id.id,
        #             'exchange_rate': currency_id.rate,
        #             'received_type': line.received_type,
        #             'customer_settlement_line_id': line.id,
        #             'gjp_order_summary_id': line.gjp_sale_order_summary_id.id,
        #             'customer_currency_amount': line.allocate_amount,
        #         }) for line in customer_settlement_lines]
        #     data_rec |= self.env['customer.advance.received'].create({
        #         'customer_id': customer.id,
        #         'customer_settlement_id': self.id,
        #         'payer_id': self.payer_id.id,
        #         'received_date': received_date,
        #         'currency_id': currency_id.id,
        #         'received_payment_type': self.received_payment_type,
        #         'customer_receive_type': 'normal',
        #         'customer_advance_received_line_ids': customer_advance_received_line_ids,
        #     })
        #     data_rec.action_confirm()
        self.advance_payments_ids = tuple(data_rec.ids)
        # 根据结算单的行的类型创建互转单
        # 如果结算单的行类型是advance不创建互转单
        data_rec = self.env['fund.transfer.order']
        if self.customer_fund_transfer_ids:
            raise UserError(_('An fund transfer order for this customer already exists.'))
        transfer_date = fields.Date.context_today(self)
        for line in self.customer_statement_line_ids:
            if line.customer_id == self.payer_id and line.received_type == 'advance':
                continue
            transfer_type = 'internal' if self.payer_id == line.customer_id else 'external'
            rec = self.env['fund.transfer.order'].create({
                'payer_id': self.payer_id.id,
                'payee_id': line.customer_id.id,
                'transfer_date': transfer_date,
                'fund_source': 'advance' if self.received_payment_type in ('bank', 'cash', 'exchange') else self.received_payment_type,
                'fund_to': line.received_type,
                'transfer_type': transfer_type,
                'amount': line.allocate_amount,
                'customer_settlement_id': self.id,
                'customer_settlement_line_id': line.id,
            })
            rec.with_context(lock_amount=False).action_confirm()
            data_rec |= rec
        self.customer_fund_transfer_ids = tuple(data_rec.ids)
        return data_rec

    @api.depends('customer_statement_line_ids.allocate_amount')
    def _compute_allocate_amount(self):
        for rec in self:
            rec.total_allocate_amount = sum(item.allocate_amount for item in rec.customer_statement_line_ids)

    @api.depends('total_allocate_amount')
    def _compute_allocate_chinese_amount(self):
        """
        计算总分配金额的中文表示
        """
        for rec in self:
            rec.total_allocate_chinese_amount = parse_amount_to_chinese(rec.total_allocate_amount)

    def update_gjp_sale_order_summary_paid_amount(self, paid_amount=0):
        """
        更新GJP销售订单汇总的已付金额
        """
        for line in self.customer_statement_line_ids.filtered(lambda m: m.gjp_sale_order_summary_id and m.received_type == 'sopayment'):
            if paid_amount < 0:
                # 小于0就是取消整个结算单
                line.gjp_sale_order_summary_id.update_paid_amount(-line.allocate_amount)
            else:
                line.gjp_sale_order_summary_id.update_paid_amount(line.allocate_amount)

    def update_paid_amount(self, paid_amount):
        self.paid_amount += paid_amount
        if float_compare(self.paid_amount, self.statement_amount, precision_digits=2) >= 0:
            # 如果已付金额大于等于对账单金额，并且没有结转单记录，则状态为已付
            # 否则等待结转单完结，使用action_done方法更新状态
            if not self.customer_fund_transfer_ids:
                self.state = 'paid'
                self.paid_date = fields.datetime.now()
            else:
                self.state = 'partial_paid'
        elif self.paid_amount > 1 and self.paid_amount < self.statement_amount:
            self.state = 'partial_paid'
        self.update_gjp_sale_order_summary_paid_amount(paid_amount)

    def action_open_transfer_fund(self):
        """
        打开代付记录
        """
        action = self.sudo().env.ref('galaxy_lot_r2.action_customer_advance_fund_transfer').read()[0]
        action['res_id'] = self.customer_fund_transfer_id.id
        action['views'] = [(False, 'form')]
        return action

    def action_select_payment(self):
        return {'type': 'ir.actions.act_window_close', 'infos': {'lot_ids': self.ids}}

    def action_confirm(self):
        """
        This function is used to confirm the customer advance received
        """
        if self.statement_amount == 0:
            raise UserError(_('The statement amount cannot be zero.'))
        if self.statement_amount != self.total_allocate_amount:
            raise UserError(_('The statement amount must equal to the total allocate amount.'))
        for line in self.customer_statement_line_ids:
            if line.gjp_sale_order_summary_id:
                unsettlement_amount = line.gjp_sale_order_summary_id.total_unsettlement_amount
                if line.allocate_amount > unsettlement_amount:
                    raise UserError(_('The allocated amount cannot exceed the unsettled amount of the GJP Sale Order Summary.'))
            if line.received_type == 'sopayment' and not line.gjp_sale_order_summary_id:
                raise UserError(_('You need input Sale Summary order for SO Payment line'))

        self.confirm()
        # return {
        #     'type': 'ir.actions.act_window',
        #     'name': '确认',
        #     'res_model': 'galaxy.common.confirm.wizard',
        #     'view_mode': 'form',
        #     'target': 'new',
        #     'context': {
        #         'default_res_model': self._name,
        #         'default_res_id': self.id,
        #         'default_res_function': 'confirm',
        #         'default_message': _('Confirm send the settlement to the Payer?'),
        #     }
        # }

    def confirm(self):
        # 更新total_unsettlement_amount
        for line in self.customer_statement_line_ids:
            if line.gjp_sale_order_summary_id and line.received_type == 'sopayment':
                line.gjp_sale_order_summary_id.total_unsettlement_amount -= line.allocate_amount

        self.create_fund_transfer_order()
        self.state = 'confirm'

    def action_send_settlement_sign_email(self):
        """
        发送结算邮件，并附带GJP销售订单汇总报告作为附件
        """        
        for settlement in self:
            template = self.env.ref('galaxy_account_bank_statement_r2.email_template_customer_settlement')
            
            # 删除原有的附件，避免重复发送时附件堆积
            existing_attachments = self.env['ir.attachment'].search([
                ('res_model', '=', 'customer.settlement'),
                ('res_id', '=', settlement.id),
                ('name', 'like', 'Sale_Order_Summary_%')
            ])
            if existing_attachments:
                existing_attachments.unlink()
            
            # 获取所有相关的GJP销售订单汇总报告作为附件
            attachment_ids = []
            for line in settlement.customer_statement_line_ids:
                if line.gjp_sale_order_summary_id:
                    # 生成GJP销售订单汇总报告
                    report_action = self.env.ref('galaxy_account_bank_statement_r2.action_report_sale_order_summary')
                    if report_action:
                        try:
                            # 生成PDF报告
                            pdf_content, report_type = report_action._render_qweb_pdf([line.gjp_sale_order_summary_id.id])
                            
                            # 将PDF内容编码为base64
                            pdf_base64 = base64.b64encode(pdf_content).decode('utf-8')
                            
                            # 创建附件记录
                            attachment_name = f"Sale_Order_Summary_{line.gjp_sale_order_summary_id.name}.pdf"
                            attachment = self.env['ir.attachment'].create({
                                'name': attachment_name,
                                'type': 'binary',
                                'datas': pdf_base64,
                                'res_model': 'customer.settlement',
                                'res_id': settlement.id,
                            })
                            attachment_ids.append(attachment.id)
                        except Exception as e:
                            _logger.error(f"Failed to generate report for GJP Sale Order Summary {line.gjp_sale_order_summary_id.name}: {str(e)}")
            
            # 发送邮件并附带附件
            template.send_mail(settlement.id, force_send=True, email_values={'attachment_ids': [(6, 0, attachment_ids)]})
        return True

    def action_send_settlement_done_email(self):
        """
        发送结算完成邮件
        """
        for fund_transfer in self.customer_fund_transfer_ids.filtered(lambda m:m.state == 'done'):
            template = self.env.ref('galaxy_account_bank_statement_r2.email_template_fund_transfer_done')
            template.send_mail(fund_transfer.id, force_send=True)
        return True

    def action_add_in_statement_confirm(self):
        """
        添加到对账单
        """
        self.action_confirm()
        return {
            'type': 'ir.actions.act_window_close',
            'infos': {'lot_ids': self.customer_advance_received_line_ids.ids}
        }

    def action_cancel(self):
        """
        This function is used to cancel the customer advance received--后续继承实现
        """
        # 更新total_unsettlement_amount
        for rec in self:
            if rec.state == 'draft':
                pass
            elif rec.state in ('confirm', 'audited', 'partial_paid', 'paid'):
                # 取消时需要更新GJP销售订单汇总的未结金额
                # 检查是否有互转单被其他互转单核销过
                used_found_transfers = rec.customer_fund_transfer_ids.filtered(lambda m: m.state == 'done' and m.applied_amount != 0)
                if used_found_transfers:
                    raise UserError(_('Please Check the applied transfer order and cancel related settlement'))
                for line in rec.customer_statement_line_ids:
                    if line.gjp_sale_order_summary_id and line.received_type == 'sopayment':
                        line.gjp_sale_order_summary_id.total_unsettlement_amount += line.allocate_amount
                if rec.state in ('partial_paid', 'paid'):
                    rec.paid_amount = 0
                    for line in rec.customer_statement_line_ids:
                        if line.gjp_sale_order_summary_id and line.received_type == 'sopayment':
                            line.gjp_sale_order_summary_id.update_paid_amount(-line.allocate_amount)
                            if float_compare(line.gjp_sale_order_summary_id.total_paid_amount, 0, precision_digits=2) == 0:
                                if line.gjp_sale_order_summary_id.state in ('paid', 'partial_paid'):
                                    line.gjp_sale_order_summary_id.state = 'confirm'
                            for gjp_line in line.gjp_sale_order_summary_id.line_ids:
                                # 此处管家婆订单的已付金额扣减无法确定,直设置为0
                                gjp_line.gjp_sale_order_id.paid_amount = 0
                                gjp_line.gjp_sale_order_id.state = 'not_paid'
                                gjp_line.gjp_sale_order_id.audit_level = "1"
                                gjp_line.gjp_sale_order_id.customer_security_code = ''
                                gjp_line.paid_amount -= line.allocate_amount
            rec.state = 'cancel'
            rec.customer_fund_transfer_ids.action_cancel()
            # 检查关联的收款单的余额是否大于或者等于已付金额
            # 下面的取消似乎可以不用，因为互转单的取消已经会取消付款的核销
            advance_payment = rec.advance_payments_ids
            advance_payment.customer_received_statement_ids.action_cancel()
            if advance_payment and float_compare(advance_payment.balance_amount, rec.paid_amount, precision_digits=2) < 0:
                raise UserError(_('The advance payment balance amount is less than the paid amount.'))
            rec.advance_payments_ids = [(5, 0, 0)]

    def unlink(self):
        """
        This function is used to delete the customer advance
        """
        if any(rec.state not in ('draft') for rec in self):
            raise UserError(
                _('you can only delete draft record'))
        return super(CustomerSettlement, self).unlink()

    @api.depends('customer_received_statement_ids')
    def _compute_statement_counts(self):
        for rec in self:
            rec.statement_counts = len(rec.customer_received_statement_ids)

    @api.depends('statement_amount', 'paid_amount')
    def _compute_balance_amount(self):
        for rec in self:
            rec.balance_amount = rec.statement_amount - rec.paid_amount

    def action_view_statement(self):
        """
        This function returns an action that display existing customer received statement
        """
        return {
            'name': _('Customer Received Statement'),
            'type': 'ir.actions.act_window',
            'res_model': 'customer.received.statement',
            'view_mode': 'tree,form',
            'domain': [('id', 'in', self.customer_received_statement_ids.ids)],
        }

    def action_view_fund_transfers(self):
        """
        This function returns an action that display existing fund transfers
        """
        return {
            'name': _('Customer Fund Transfers'),
            'type': 'ir.actions.act_window',
            'res_model': 'fund.transfer.order',
            'view_mode': 'tree,form',
            'domain': [('id', 'in', self.customer_fund_transfer_ids.ids)],
            'context': {'default_customer_settlement_id': self.id}
        }

    @api.model
    def create(self, vals):
        """
        This function is used to generate the sequence number
        在create之后更新name的原因是constrain的检查是在触发是在create之后进行的，（特别是对lines的检查）这样有可能导致序号不连续
        """
        ret = super(CustomerSettlement, self).create(vals)
        if vals.get('name', _('New')) == _('New'):
            vals['name'] = self.env['ir.sequence'].next_by_code(
                'customer.statement.seqn.sn') or _('New')
        ret.name = vals['name']
        ret.customer_statement_line_ids.update_line_numbers(ret.customer_statement_line_ids)
        return ret

    def get_portal_url(self, suffix=None, report_type=None, download=False, query_string=None, anchor=None):
        """
        Get a portal url for this model, including access_token.
        The associated route must handle the flags for them to have any effect.
        :param suffix: string to append to the url, before the query string
        :param report_type: report_type query string, often one of: html, pdf, text
        :param download: set the download query string to true
        :param query_string: additional query string
        :param anchor: optional anchor to add at the end of the url
        :return: the portal url
        """
        self.ensure_one()
        url = '/my/settlements/%s%s?access_token=%s%s%s%s%s' % (
            self.id,
            suffix if suffix else '',
            self._portal_ensure_token(),
            '&report_type=%s' % report_type if report_type else '',
            '&download=true' if download else '',
            query_string if query_string else '',
            '#%s' % anchor if anchor else ''
        )
        return url

    def preview_settlement(self):
        """
        Preview the settlement in portal view
        """
        self.ensure_one()
        return {
            'type': 'ir.actions.act_url',
            'target': 'self',
            'url': self.get_portal_url(),
        }

    def _compute_access_url(self):
        for record in self:
            record.access_url = '/my/settlements/%s' % record.id

    def get_cash_handling_fee(self):
        """
        计算现金付款手续费
        当received_payment_type为cash时，计算0.1%的手续费并四舍五入到整数
        """
        self.ensure_one()
        if self.received_payment_type == 'cash':
            # 计算0.1%的手续费
            fee_amount = self.statement_amount * 0.001
            return round(fee_amount)
        return 0

    def get_total_amount_with_fee(self):
        """
        获取包含手续费的总额
        """
        self.ensure_one()
        if self.received_payment_type == 'cash':
            # 如果付款类型是现金，直接返回get_cash_handling_fee的结果
            fee = self.get_cash_handling_fee()
            total_amount = self.statement_amount + fee
            return total_amount
        else:
            # 其他付款类型，格式化statement_amount
            return self.statement_amount


class CustomerSettlementLines(models.Model):
    _name = 'customer.settlement.lines'
    _description = 'Customer Settlement Lines'

    customer_settlement_id = fields.Many2one(
        'customer.settlement', string='Customer Advance Received', ondelete="cascade")
    name = fields.Char(string='Line No', copy=False, readonly=True, store=True)
    allocate_amount = fields.Float(string='Allocate Amount', digits=(12, 2))
    customer_id = fields.Many2one('res.partner', index=True)
    gjp_sale_order_summary_id = fields.Many2one('gjp.sale.order.summary', string='GJP Sale Order Summary', index=True)
    sale_order_unsettlement_amount = fields.Float(
        string='Unsettlement Amount', digits=(12, 2),
        related='gjp_sale_order_summary_id.total_unsettlement_amount', store=True)
    received_type = fields.Selection(
        [('advance', 'Advance Payment'),
         ('downpayment', 'Down Payment'),
         ('sopayment', 'SO Payment'),
         ('credit', 'Credit')],
        required=True)
    customer_currency_id = fields.Many2one(
        'res.currency', string='Currency',
        related="customer_id.customer_currency_id", store=True, index=True)
    customer_settlement_state = fields.Selection(
        related='customer_settlement_id.state', string='Settlement State', store=True)
    received_payment_type = fields.Selection(
        related='customer_settlement_id.received_payment_type', string='Payment Type', store=True)
    advance_payment_id = fields.Many2one(
        'customer.advance.received', string='Advance Payment',
        computute="_compute_advance_payment_id",
        store=True, index=True,
        help="This field contains the advance payment related to this settlement line.")
    customer_fund_transfer_ids = fields.Many2many(
        'fund.transfer.order', compute='_compute_fund_transfer_ids',
        string='Customer Fund Transfer', store=True,)
    payer_id = fields.Many2one(
        'res.partner', string='Payer',
        related='customer_settlement_id.payer_id', store=True)
    note = fields.Text()

    @api.onchange('gjp_sale_order_summary_id')
    def _onchange_gjp_sale_order_summary_id(self):
        """
        When the GJP Sale Order Summary is changed, update the customer_id in the statement line
        """
        self.allocate_amount = self.gjp_sale_order_summary_id.total_unsettlement_amount

    def get_received_type_display_name(self):
        """
        Get display name for received_type field
        """
        received_type_mapping = {
            'advance': 'Advance Payment(預收款)',
            'downpayment': 'Down Payment(首付款)',
            'sopayment': 'SO Payment(貨款尾款)'
        }
        return received_type_mapping.get(self.received_type, self.received_type)

    @api.depends('customer_settlement_id.customer_fund_transfer_ids')
    def _compute_fund_transfer_ids(self):
        """
        计算与此结算行相关的代付记录
        """
        for rec in self:
            rec.customer_fund_transfer_ids = tuple(rec.customer_settlement_id.customer_fund_transfer_ids.ids)

    @api.depends('customer_settlement_id.advance_payments_ids')
    def _compute_advance_payment_id(self):
        """
        计算与此结算行相关的预付款
        """
        for rec in self:
            advance_payments = rec.customer_settlement_id.advance_payments_ids.filtered(lambda m: m.state != 'cancel')
            if advance_payments:
                # 如果有多个预付款，取第一个
                rec.advance_payment_id = advance_payments[0].id
            else:
                rec.advance_payment_id = False

    @api.model
    def create(self, vals):
        """
        This function is used to generate the sequence number
        """
        created_lines = super(CustomerSettlementLines, self).create(vals)
        for rec in created_lines:
            count = self.search_count([
                ('customer_settlement_id', '=', rec.customer_settlement_id.id)
            ])
            sequence_number = str(count).zfill(3)
            rec.name = f"{rec.customer_settlement_id.name}-{sequence_number}"
        return created_lines

    def update_line_numbers(self, created_lines):
        for rec in created_lines:
            rec.name = rec.customer_settlement_id.name + '-' + rec.name.split('-')[-1]
