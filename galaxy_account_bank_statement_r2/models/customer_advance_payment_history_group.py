from odoo import models, fields, api
from odoo import models, fields, api


class CustomerAdvancePaymentHistory(models.Model):
    _name = 'customer.advance.payment.history.group'
    _description = 'Customer Advance Payment Group History'
    _order = 'transaction_date desc'

    transaction_date = fields.Datetime(
        string='Transaction Date',
        help='交易日期',
        required=True,
        index=True,
    )

    business_type = fields.Selection(
        selection=[
            ('receipt', 'Receipt'),
            ('goods_payment', 'Goods Payment'),
            ('reversal', 'Reversal'),
            ('internal_transfer_in', 'Internal Transfer In'),
            ('internal_transfer_out', 'Internal Transfer Out'),
            ('external_transfer_in', 'External Transfer In'),
            ('external_transfer_out', 'External Transfer Out'),
        ],
        string='Business Type',
        help='业务类型'
    )

    document_type = fields.Selection(
        selection=[
            ('payment_receipt', 'Payment receipt'),
            ('fund_transfer', 'Fund Transfer'),
            ('refund', 'Refund'),
        ],
        string="Document Type"
    )

    document_number = fields.Char(string="Document No.", help="Document Number", index=True)

    funding_source = fields.Selection(
        string='Funding Source',
        selection=[('bank', 'Bank'), ('cash', 'Cash'), ('exchange', 'Exchange'), ('credit', 'Credit'), ('advance', 'Advance'), ('downpayment', 'Down Payment')],
        index=True,
        help='资金来源'
    )

    fund_type = fields.Selection(
        string='Fund Type',
        selection=[('advance', 'Advance Payment'), ('downpayment', 'Down Payment'), ('sopayment', 'SO Payment'), ('credit', 'Credit')],
        index=True,
        help='资金用途'
    )

    reconciliation_status = fields.Selection(
        selection=[
            ('not_applied', 'Not Applied'),
            ('partial_applied', 'Partial Applied'),
            ('fully_applied', 'Fully Applied')
        ],
        index=True,
        string='Reconciliation Status',
        help='核销狀態',
        default='not_applied'
    )

    notes = fields.Text(
        string='Notes',
        help='備註',
        store=True,
    )

    transaction_amount = fields.Float(
        string='Transaction Amount',
        help='Transaction Amount',
        digits=(16, 2)
    )

    advance_amount = fields.Float(
        string='Advance Amount',
        help='预收款金额',
        digits=(16, 2),
        store=True,
    )
    downpayment_amount = fields.Float(
        string='Down Payment Amount',
        help='首付款金额',
        store=True,
        digits=(16, 2)
    )

    advance_balance_amount = fields.Float(
        string='Advance Balance',
        help='预收款余额',
        digits=(16, 2)
    )

    downpayment_balance_amount = fields.Float(
        string='Down Payment Balance',
        help='预收款余额',
        digits=(16, 2)
    )

    operator = fields.Many2one(
        'res.users',
        string='Operator',
        help='操作人',
    )

    customer_id = fields.Many2one(
        'res.partner',
        string='Customer',
        help='客户',
        index=True,
    )
    related_customer_id = fields.Many2one(
        'res.partner',
        string='Related Customer',
        help='客户',
        index=True,
    )
    merged_bank_statement_ids = fields.Many2many(
        'galaxy.account.bank.merged.statement',
        string='Transaction Number',
        relation="advance_payment_group_history_bank_statment_rel",
        help='银行对账单'
    )
    customer_advance_received_id = fields.Many2one(
        'customer.advance.received',
        string='Settlement Number',
        help='客户预收款',
        index=True,
    )

    customer_advance_received_line_id = fields.Many2one(
        'customer.advance.received.lines',
        string='Settlement Line Number',
        help='預收结算单行号',
        index=True
    )

    customer_received_statement_id = fields.Many2one(
        'customer.received.statement',
        string='Reconciliation Number',
        index=True,
        help="核銷號",
    )

    customer_received_statement_line_id = fields.Many2one(
        'customer.received.statement.lines',
        string='Reconciliation Line Number',
        index=True,
        help="核銷行號",
    )
    fund_transfer_order_id = fields.Many2one(
        'fund.transfer.order',
        string='Fund Transfer Order',
        help='资金划拨单',
        index=True
    )

    related_sale_summary_order_ids = fields.Many2many('gjp.sale.order.summary',
                                                      string="Sale Summary Orders",
                                                      relation="advance_payment_group_history_summary_order_rel",
                                                      help="Related Sale Summary Orders")

    statement_counts = fields.Integer(
        string='Statement Counts',
        compute='_compute_statement_counts',
        help='对账单行数'
    )

    @api.depends('fund_transfer_order_id')
    def _compute_get_sale_summary_orders(self):
        """
        计算相关销售汇总订单
        """
        for rec in self:
            if rec.fund_transfer_order_id:
                rec.related_sale_summary_order_ids = rec.fund_transfer_order_id.customer_settlement_id.gjp_sale_order_summary_ids
            else:
                rec.related_sale_summary_order_ids = False

    @api.depends('merged_bank_statement_ids')
    def _compute_statement_counts(self):
        """
        计算对账单行数
        """
        for rec in self:
            rec.statement_counts = len(rec.merged_bank_statement_ids)

    def action_view_statement(self):
        """
        This function opens the bank statement
        """
        self.ensure_one()
        action = self.sudo().env.ref('galaxy_account_bank_statement.action_galaxy_account_merged_bank_statement').read()[0]
        action['domain'] = [('id', 'in', self.merged_bank_statement_ids.ids)]
        action['context'] = {'create': False, 'edit': False, 'delete': False}
        return action

    def develop_initialize_advance_payment_history_group(self):
        """
        Initialize the advance payment history group
        """
        self.env['customer.advance.payment.history.group'].search([]).unlink()
        records = self.env['customer.advance.payment.history'].search([], order='id')
        for rec in records:
            # 尝试查找可以合并的grou的记录
            related_records = self.env['customer.advance.payment.history.group'].search([
                ('transaction_date', '=', rec.transaction_date),
                ('document_number', '=', rec.document_number),
                ('customer_id', '=', rec.customer_id.id),
                ('fund_transfer_order_id', '=', rec.fund_transfer_order_id.id),
            ], limit=1)
            group_vals = {
                'transaction_date': rec.transaction_date,
                'business_type': rec.business_type,
                'document_type': rec.document_type,
                'document_number': rec.document_number,
                'funding_source': rec.funding_source,
                'fund_type': rec.fund_type,
                'reconciliation_status': rec.reconciliation_status,
                'notes': rec.notes,
                'transaction_amount': rec.transaction_amount,
                'advance_amount': rec.advance_amount,
                'downpayment_amount': rec.downpayment_amount,
                'advance_balance_amount': rec.advance_balance_amount,
                'downpayment_balance_amount': rec.downpayment_balance_amount,
                'operator': rec.operator.id,
                'customer_id': rec.customer_id.id,
                'fund_transfer_order_id': rec.fund_transfer_order_id.id,
                'customer_advance_received_id': rec.customer_advance_received_id.id,
                'customer_advance_received_line_id': rec.customer_advance_received_line_id.id,
                'customer_received_statement_id': rec.customer_received_statement_id.id,
                'customer_received_statement_line_id': rec.customer_received_statement_line_id.id,
                'merged_bank_statement_ids': [(6, 0, rec.merged_bank_statement_ids.ids)],
                'related_sale_summary_order_ids': [(6, 0, rec.related_sale_summary_order_ids.ids)],
                'related_customer_id': rec.related_customer_id.id,
            }
            if not related_records:
                group_rec = self.create(group_vals)
            else:
                group_rec = related_records[0]
                group_rec.transaction_amount += rec.transaction_amount
                if rec.funding_source == 'advance':
                    group_rec.advance_amount += rec.advance_amount
                    group_rec.advance_balance_amount += rec.transaction_amount
                elif rec.funding_source == 'downpayment':
                    # 如果是首付款，则增加首付款余额
                    group_rec.downpayment_amount += rec.downpayment_amount
                    group_rec.downpayment_balance_amount += rec.transaction_amount
