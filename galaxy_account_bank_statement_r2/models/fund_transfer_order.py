import json

from odoo import models, fields, api, _
from odoo.tools import float_compare
from odoo.exceptions import UserError


class FundTransferOrder(models.Model):
    _name = "fund.transfer.order"
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = "Fund Transfer Order"
    _order = "id desc"

    name = fields.Char(
        string="Number",
        required=True,
        copy=False,
        index=True,
        default=lambda self: _("New"),
    )
    transfer_date = fields.Date(
        string="Transfer Date",
        required=True,
        default=fields.Date.context_today,
    )
    state = fields.Selection(
        [("draft", "Draft"), ("confirm", "Confirm"), ("done", "Done"), ("cancel", "Cancel")],
        string="State",
        default="draft",
        tracking=True
    )
    payer_id = fields.Many2one(
        "res.partner", string="Payer", required=True
    )
    payee_id = fields.Many2one(
        "res.partner", string="Payee", required=True
    )
    account_id = fields.Many2one(
        "account.account", string="Transfer Out Account"
    )
    fund_source = fields.Selection(
        [
         ('advance', 'Advance Payment'),
         ('downpayment', 'Down Payment'),
         ('credit', 'Credit'),
        ], string='Fund Source', default="advance", required=True)

    transfer_type = fields.Selection(
        [
         ('internal', 'Internal Transfer'),
         ('external', 'External Transfer'),
        ], string='Transfer Type', default="internal", required=True)

    fund_to = fields.Selection(
        [
         ('advance', 'To Advance Payment'),
         ('downpayment', 'To Down Payment'),
         ('sopayment', 'To SO Payment'),
         ('credit', 'Credit'),
        ], string='Fund To', default="advance", required=True)

    amount = fields.Float(string="Amount", required=True, digits=(12, 2))
    note = fields.Text(string="Note")
    currency_id = fields.Many2one("res.currency", string="Currency", related="payer_id.customer_currency_id", store=True, index=True, readonly=True)
    account_move_id = fields.Many2one("account.move", string="Journal Entry")
    customer_settlement_id = fields.Many2one("customer.settlement", string="Customer Settlement", index=True, ondelete="restrict")
    customer_settlement_line_id = fields.Many2one(
        "customer.settlement.line",
        string="Customer Settlement Line",
        index=True,
        ondelete="restrict",
    )
    applied_amount = fields.Float(
        string="Applied Amount",
        digits=(12, 2),
        tracking=True,
    )
    balance_amount = fields.Float(
        string="Balance Amount",
        compute="_compute_balance_amount",
        store=True,
        digits=(12, 2),
        tracking=True,
    )
    total_advance_balance_amount = fields.Float(
        string="Total Advance Balance Amount",
        digits=(12, 2),
    )
    applied_fund_transfer_order_ids = fields.Many2many(
        'fund.transfer.order',
        'fund_transfer_order_applied_relation',
        'fund_transfer_order_id',
        'applied_fund_transfer_order_id',
        string="Applied Fund Transfer Orders",
    )
    applied_fund_transfer_amount = fields.Float(
        string="Applied Fund Transfer Amount",
        tracking=True,
        digits=(12, 2),
    )
    applied_fund_transfer_amount_details = fields.Text(
        string="Applied Fund Transfer Amount Details",
    )
    make_by = fields.Many2one('res.users', related="customer_settlement_id.make_by", string="Make By", store=True)
    customer_received_statement_id = fields.Many2one('customer.received.statement', string='Customer Received Statement', index=True, ondelete='restrict')

    @api.depends('amount', 'applied_amount')
    def _compute_balance_amount(self):
        for record in self:
            if record.fund_to != 'sopayment':
                record.balance_amount = record.amount - record.applied_amount
            else:
                record.balance_amount = 0

    @api.model
    def create(self, vals):
        if vals.get("name", _("New")) == _("New"):
            vals["name"] = self.with_context(galaxy_base_32=True).env[
                "ir.sequence"
            ].next_by_code("galaxy.fund.transfer.order") or _("New")
        return super(FundTransferOrder, self).create(vals)

    def action_to_account_move(self):
        if self.account_move_id:
            return {
                "type": "ir.actions.act_window",
                "res_model": "account.move",
                "res_id": self.account_move_id.id,
                "view_mode": "form"}

    def action_to_customer_receipt(self):
        if self.customer_receipt_id:
            return {
                "type": "ir.actions.act_window",
                "res_model": "customer.receipt",
                "res_id": self.customer_receipt_id.id,
                "view_mode": "form"}

    def get_fund_source_display(self):
        fund_source_mapping = {
            'advance': 'Advance(預收款)',
            'downpayment': 'Down Payment(首付款)', 
        }
        return fund_source_mapping.get(self.fund_source, self.fund_source)

    def get_transfer_type_display(self):
        transfer_type_mapping = {
            'internal': 'Internal(內部轉賬)',
            'external': 'External(他人互轉)',
        }
        return transfer_type_mapping.get(self.transfer_type, self.transfer_type)

    def get_fund_to_display(self):
        fund_to_mapping = {
            'advance': 'Advance(轉預收款)',
            'downpayment': 'Down Payment(轉首付款)',
            'sopayment': 'SO Payment(轉貨款)',
        }
        return fund_to_mapping.get(self.fund_to, self.fund_to)

    def get_business_type(self, fund_transfer):
        """
        根据互转单的fund_to字段获取业务类型
        """
        if fund_transfer.fund_to != 'credit':
            if fund_transfer.payer_id == fund_transfer.payee_id:
                return 'internal_transfer_out'
            else:
                return 'external_transfer_out'
        else:
            if fund_transfer.payer_id == fund_transfer.payee_id:
                return 'internal_transfer_in'
            else:
                return 'external_transfer_in'

    def create_payment_allocation_credit(self):
        """
        使用信用额度付款的时候，使用特别的创建付款方法
        """
        for payment in self:
            # 直接核销
            statement_amount = payment.amount
            if statement_amount == 0:
                return
            if payment.applied_fund_transfer_order_ids:
                # 如果是付货款用到了互转单余额需要创建付款历史
                if payment.applied_fund_transfer_amount == 0:
                    raise UserError(_("The applied fund_transfer_amount is not correct."))
                for apply_transfer_order in payment.applied_fund_transfer_order_ids:
                    apply_transfer_order.applied_amount -= payment.applied_fund_transfer_amount
            # 创建特别的信用付款历史
            customer = payment.payer_id
            if self.state != 'cancel':
                business_type = self.get_business_type(payment)
            else:
                business_type = 'reversal'
            if business_type != 'reversal':
                if payment.fund_to != 'credit':
                    credit_balance_amount = payment.payer_id.available_customer_credit_amount - payment.amount
                    transaction_amount = -payment.amount
                else:
                    credit_balance_amount = payment.payer_id.available_customer_credit_amount + payment.amount
                    transaction_amount = payment.amount
            else:
                if payment.fund_to != 'credit':
                    credit_balance_amount = payment.payer_id.available_customer_credit_amount + payment.amount
                    transaction_amount = payment.amount
                else:
                    credit_balance_amount = payment.payer_id.available_customer_credit_amount - payment.amount
                    transaction_amount = -payment.amount
            document_type = 'fund_transfer'
            fund_type = payment.fund_to
            related_customer = payment.payee_id
            fund_transfer_order_id = payment.id
            # 创建信用额度核销历史
            self.create_credit_history_line(
                customer=customer,
                credit_balance_amount=credit_balance_amount,
                business_type=business_type,
                transaction_amount=transaction_amount,
                document_type=document_type,
                fund_type=fund_type,
                related_customer=related_customer,
                fund_transfer_order_id=fund_transfer_order_id,
            )
            # 创建正常的预收款历史
            payment.create_advance_payment_history()

    def create_credit_history_line(self,
                                   customer,
                                   credit_balance_amount,
                                   transaction_amount,
                                   business_type,
                                   document_type,
                                   fund_type,
                                   related_customer,
                                   fund_transfer_order_id=False,
                                   ):
        self.env['customer.credit.history'].create({
            'customer_id': customer.id,
            'related_customer_id': related_customer.id if related_customer else False,
            'operator': self.env.user.id,
            'credit_balance_amount': credit_balance_amount,
            'transaction_amount': transaction_amount,
            'funding_source': self.fund_source,
            'fund_type': fund_type,
            'business_type': business_type,
            'document_type': document_type,
            'fund_transfer_order_id': fund_transfer_order_id,
            'transaction_date': fields.Datetime.now(),
        })

    def create_payment_allocation(self):
        """
        创建核销
        """
        # 如果预收款的行类型是SO货款直接核销
        for payment in self:
            # 直接核销
            if payment.fund_to == 'sopayment':
                statement_type = 'so'
            elif payment.fund_to == 'credit':
                statement_type = 'credit'
            else:
                statement_type = 'fund_transfer'
            statement_amount = self.amount
            if statement_amount == 0:
                return
            statement_lines = []
            # 如果互转单对应的结算单--创建了收款,那么优先使用结算单创建的收款
            if payment.customer_settlement_id.advance_payments_ids:
                advance_payment = payment.customer_settlement_id.advance_payments_ids.filtered(
                    lambda x: x.customer_id.id == payment.payer_id.id and x.balance_amount > 0 and x.state == 'done'
                )
            else:
                # 否则使用历史预收款--按照FIFO原则
                advance_payment = self.env['customer.advance.received'].search([
                    ('customer_id', '=', payment.payer_id.id),
                    ('balance_amount', '>', 0),
                    ('state', '=', 'done'),
                ])
            # 从预收款行创建核销行
            advance_statement_amount = 0.0
            for line in advance_payment.customer_advance_received_line_ids:
                if line.received_type == payment.fund_source and line.balance_amount > 0:
                    # 创建Customer received statement
                    if line.balance_amount >= statement_amount:
                        statement_lines.append((0, 0, {
                            'customer_advance_received_line_id': line.id,
                            'allocate_amount': statement_amount,
                        }))
                        advance_statement_amount += statement_amount
                        statement_amount = 0
                        break
                    else:
                        statement_amount -= line.balance_amount
                        statement_lines.append((0, 0, {
                            'customer_advance_received_line_id': line.id,
                            'allocate_amount': line.balance_amount,
                        }))
                        advance_statement_amount += line.balance_amount
            # 类型是SO货款的互转单在创建核销行的时候会创建付款历史，影响预收款明细报表
            # 类型是互转单的不创建预收款报表明细
            if statement_lines:
                statment = self.env['customer.received.statement'].with_context(document_type='fund_transfer',
                                                                                funding_source=self.fund_source,
                                                                                fund_type=self.fund_to,
                                                                                related_customer_id=payment.payee_id.id,
                                                                                fund_transfer_order_id=payment.id,
                                                                                ).create({
                                                                                    'customer_id': payment.payer_id.id,
                                                                                    'statement_date': fields.Date.today(),
                                                                                    'amount': advance_statement_amount,
                                                                                    'statement_type': statement_type,
                                                                                    'statement_line_ids': statement_lines,
                                                                                    })
                statment.action_confirm()
                payment.customer_received_statement_id = statment.id
            # 如果收款记录的金额不够，尝试从互转记录获取,这种场景不创建核销行,但是需要创建付款历史
            # 这里有个场景是一部分用收款记录支付，一部分用互转记录支付
            fund_transfer_get_amount = {}
            if statement_amount > 0:
                fund_transfer_lines = self.env['fund.transfer.order'].search([
                    ('payee_id', '=', payment.payer_id.id),
                    ('fund_to', '=', payment.fund_source),
                    ('state', '=', 'done'),
                    ('balance_amount', '>', 0),
                ])
                for fund_transfer in fund_transfer_lines:
                    if fund_transfer.balance_amount >= statement_amount:
                        fund_transfer.applied_amount += statement_amount
                        payment.applied_fund_transfer_amount += statement_amount
                        fund_transfer_get_amount[fund_transfer.id] = statement_amount
                        statement_amount = 0
                        payment.applied_fund_transfer_order_ids = [(4, fund_transfer.id)]
                        break
                    else:
                        fund_transfer_get_amount[fund_transfer.id] = fund_transfer.balance_amount
                        fund_transfer.applied_amount += fund_transfer.balance_amount
                        payment.applied_fund_transfer_order_ids = [(4, fund_transfer.id)]
                        payment.applied_fund_transfer_amount += fund_transfer_get_amount.get(fund_transfer.id, 0.0)
                        statement_amount -= fund_transfer_get_amount.get(fund_transfer.id, 0.0)
            if round(statement_amount, 2) > 0:
                raise models.UserError(_("The amount of the fund transfer order is greater than the balance amount of the advance payment."))
            payment.applied_fund_transfer_amount_details = json.dumps(fund_transfer_get_amount)
            # 创建预收款历史
            if statement_type == 'fund_transfer':
                payment.create_advance_payment_history()
            if statement_type == 'so':
                # 如果是SO货款并且使用了互转单金额，需要创建付款历史
                if payment.applied_fund_transfer_order_ids:
                    # 如果部分使用了互转单支付, 更新余额
                    for fund_transfer in payment.applied_fund_transfer_order_ids:
                        statement_amount = fund_transfer_get_amount.get(fund_transfer.id, 0.0)
                        if payment.fund_source == 'advance' and payment.fund_to == 'sopayment':
                            payment.payer_id.applied_advance_payment_amount += statement_amount
                        if payment.fund_source == 'downpayment' and payment.fund_to == 'sopayment':
                            payment.payer_id.applied_downpayment_amount += statement_amount
                        if payment.fund_source == 'advance' and payment.fund_to != 'sopayment':
                            payment.payer_id.transfered_out_advance_payment_amount += statement_amount
                        if payment.fund_source == 'downpayment' and payment.fund_to != 'sopayment':
                            payment.payer_id.transfered_out_downpayment_amount += statement_amount
                        document_type = 'fund_transfer'
                        business_type = self.get_business_type(fund_transfer)
                        # 需要创建付款历史
                        advance_balance_amount = payment.payer_id.advance_payment_balance_amount
                        downpayment_balance_amount = payment.payer_id.downpayment_balance_amount
                        customer = payment.payer_id
                        transaction_amount = -fund_transfer_get_amount.get(fund_transfer.id, 0.0)
                        fund_type = payment.fund_to
                        related_customer = False
                        fund_source = payment.fund_source
                        fund_transfer_order_id = payment.id
                        history_line = payment.create_history_line_applied_fund_transfer(
                            customer,
                            advance_balance_amount,
                            downpayment_balance_amount,
                            transaction_amount,
                            business_type,
                            document_type,
                            fund_type,
                            fund_source,
                            related_customer,
                            fund_transfer_order_id,
                        )
                        self.env['customer.advance.payment.history'].create(history_line)
            if statement_type == 'credit':
                self.create_payment_allocation_credit()

    def create_history_line(self, customer,
                            advance_balance_amount,
                            downpayment_balance_amount,
                            transaction_amount,
                            business_type,
                            document_type,
                            fund_type,
                            related_customer,
                            fund_transfer_order_id=False,
                            ):
        return {
            'customer_id': customer.id,
            'related_customer_id': related_customer.id if related_customer else False,
            'operator': self.env.user.id,
            'advance_balance_amount': advance_balance_amount,
            'downpayment_balance_amount': downpayment_balance_amount,
            'transaction_amount': transaction_amount,
            'funding_source': self.fund_source,
            'fund_type': fund_type,
            'business_type': business_type,
            'document_type': document_type,
            'fund_transfer_order_id': fund_transfer_order_id,
            'transaction_date': fields.Datetime.now(),
        }

    def create_history_line_applied_fund_transfer(self, customer,
                            advance_balance_amount,
                            downpayment_balance_amount,
                            transaction_amount,
                            business_type,
                            document_type,
                            fund_type,
                            fund_source,
                            related_customer,
                            fund_transfer_order_id=False,
                            ):
        return {
            'customer_id': customer.id,
            'related_customer_id': related_customer.id if related_customer else False,
            'operator': self.env.user.id,
            'advance_balance_amount': advance_balance_amount,
            'downpayment_balance_amount': downpayment_balance_amount,
            'transaction_amount': transaction_amount,
            'funding_source': fund_source,
            'fund_type': fund_type,
            'business_type': business_type,
            'document_type': document_type,
            'fund_transfer_order_id': fund_transfer_order_id,
            'transaction_date': fields.Datetime.now(),
        }

    def create_advance_payment_history(self):
        """
        This function is used to create advance payment history
        statement_type = 'fund_transfer' or 'so'
        此函数并不会更新payer或者payee余额
        """
        history_line = []
        payer_advance_balance_amount = self.payer_id.advance_payment_balance_amount
        payer_downpayment_balance_amount = self.payer_id.downpayment_balance_amount
        payee_advance_balance_amount = self.payee_id.advance_payment_balance_amount
        payee_downpayment_balance_amount = self.payee_id.downpayment_balance_amount
        document_type = 'fund_transfer'
        document_number = self.name
        fund_transfer_order_id = self.id
        if self.state == 'done':
            if self.payer_id == self.payee_id:
                business_type = 'internal_transfer_out'
                transaction_amount = -self.amount
                fund_type = self.fund_to
                if self.fund_source == 'advance':
                    payer_advance_balance_amount -= self.amount
                    payee_advance_balance_amount = payer_advance_balance_amount
                    if self.fund_to == 'downpayment':
                        payee_downpayment_balance_amount += self.amount
                elif self.fund_source == 'downpayment':
                    payer_downpayment_balance_amount -= self.amount
                    payee_downpayment_balance_amount = payer_downpayment_balance_amount
                    if self.fund_to == 'advance':
                        payee_advance_balance_amount += self.amount
                elif self.fund_source == 'credit':
                    if self.fund_to == 'downpayment':
                        payee_downpayment_balance_amount += self.amount
                    if self.fund_to == 'advance':
                        payee_advance_balance_amount += self.amount
                # 对于来源是credit的因为转出在信用额度报告那边已经体现，
                # 如果是转入credit的，这边体现转出
                if self.fund_source != 'credit' or (self.fund_source != 'credit' and self.fund_to != 'credit'):
                    history_line.append(
                        self.create_history_line(self.payer_id,
                                                 payer_advance_balance_amount,
                                                 payer_downpayment_balance_amount,
                                                 transaction_amount,
                                                 business_type,
                                                 document_type,
                                                 fund_type,
                                                 related_customer=False,
                                                 fund_transfer_order_id=fund_transfer_order_id
                                            )
                    )
                business_type = 'internal_transfer_in'
                fund_type = self.fund_to
                transaction_amount = self.amount
                # 对于fund_to 是credit的，不放转入
                if self.fund_to != 'credit' or (self.fund_source != 'credit' and self.fund_to != 'credit'):
                    history_line.append(
                        self.create_history_line(
                                                self.payee_id,
                                                payee_advance_balance_amount,
                                                payee_downpayment_balance_amount,
                                                transaction_amount,
                                                business_type,
                                                document_type,
                                                fund_type,
                                                related_customer=False,
                                                fund_transfer_order_id=fund_transfer_order_id
                                                )
                                            )
            else:
                business_type = 'external_transfer_out'
                transaction_amount = -self.amount
                fund_type = self.fund_to
                if self.fund_source == 'advance':
                    payer_advance_balance_amount -= self.amount
                    if self.fund_to == 'advance':
                        payee_advance_balance_amount += self.amount
                    if self.fund_to == 'downpayment':
                        payee_downpayment_balance_amount += self.amount
                elif self.fund_source == 'downpayment':
                    payer_downpayment_balance_amount -= self.amount
                    if self.fund_to == 'advance':
                        payee_advance_balance_amount += self.amount
                    if self.fund_to == 'downpayment':
                        payee_downpayment_balance_amount += self.amount
                elif self.fund_source == 'credit':
                    if self.fund_to == 'downpayment':
                        payee_downpayment_balance_amount += self.amount
                    if self.fund_to == 'advance':
                        payee_advance_balance_amount += self.amount
                # 对于来源是credit的因为转出在信用额度报告那边已经体现，这边只放转入
                if self.fund_source != 'credit' or (self.fund_source != 'credit' and self.fund_to != 'credit'):
                    history_line.append(
                        self.create_history_line(
                                                self.payer_id,
                                                payer_advance_balance_amount,
                                                payer_downpayment_balance_amount,
                                                transaction_amount,
                                                business_type,
                                                document_type,
                                                fund_type,
                                                related_customer=self.payee_id,
                                                fund_transfer_order_id=fund_transfer_order_id
                                                )
                    )
                business_type = 'external_transfer_in'
                fund_type = self.fund_to
                transaction_amount = self.amount
                # 对于fund_to 是credit的，不放转入
                if self.fund_to != 'credit' or (self.fund_source != 'credit' and self.fund_to != 'credit'):
                    history_line.append(
                        self.create_history_line(
                                                self.payee_id,
                                                payee_advance_balance_amount,
                                                payee_downpayment_balance_amount,
                                                transaction_amount,
                                                business_type,
                                                document_type,
                                                fund_type,
                                                related_customer=self.payer_id,
                                                fund_transfer_order_id=fund_transfer_order_id
                                                )
                    )
        if self.state == 'cancel':
            business_type = 'reversal'
            if self.payer_id == self.payee_id:
                # business_type = 'internal_transfer_in'
                fund_type = self.fund_to
                transaction_amount = self.amount
                if self.fund_source == 'advance':
                    payer_advance_balance_amount += self.amount
                    payee_advance_balance_amount = payer_advance_balance_amount
                    if self.fund_to == 'downpayment':
                        payee_downpayment_balance_amount -= self.amount
                elif self.fund_source == 'downpayment':
                    payer_downpayment_balance_amount += self.amount
                    payee_downpayment_balance_amount = payer_downpayment_balance_amount
                    if self.fund_to == 'advance':
                        payee_advance_balance_amount -= self.amount
                elif self.fund_source == 'credit':
                    if self.fund_to == 'downpayment':
                        payee_downpayment_balance_amount -= self.amount
                        transaction_amount = -self.amount
                    if self.fund_to == 'advance':
                        payee_advance_balance_amount -= self.amount
                        transaction_amount = -self.amount
                if self.fund_source != 'credit':
                    history_line.append(
                            self.create_history_line(
                                                    self.payer_id,
                                                    payer_advance_balance_amount,
                                                    payer_downpayment_balance_amount,
                                                    transaction_amount,
                                                    business_type,
                                                    document_type,
                                                    fund_type,
                                                    related_customer=False,
                                                    fund_transfer_order_id=fund_transfer_order_id
                                                    )
                        )
                # business_type = 'internal_transfer_out'
                fund_type = self.fund_to
                transaction_amount = -self.amount
                if self.fund_source == 'credit' or (self.fund_source != 'credit' and self.fund_to != 'credit'):
                    history_line.append(
                            self.create_history_line(
                                                    self.payee_id,
                                                    payee_advance_balance_amount,
                                                    payee_downpayment_balance_amount,
                                                    transaction_amount,
                                                    business_type,
                                                    document_type,
                                                    fund_type,
                                                    related_customer=False,
                                                    fund_transfer_order_id=fund_transfer_order_id
                                                    )
                        )
            else:
                # business_type = 'external_transfer_in'
                fund_type = self.fund_to
                transaction_amount = self.amount
                if self.fund_source == 'advance':
                    payer_advance_balance_amount += self.amount
                    if self.fund_to == 'advance':
                        payee_advance_balance_amount -= self.amount
                    if self.fund_to == 'downpayment':
                        payee_downpayment_balance_amount -= self.amount
                elif self.fund_source == 'downpayment':
                    payer_downpayment_balance_amount += self.amount
                    if self.fund_to == 'advance':
                        payee_advance_balance_amount -= self.amount
                    if self.fund_to == 'downpayment':
                        payee_downpayment_balance_amount -= self.amount
                elif self.fund_source == 'credit':
                    if self.fund_to == 'downpayment':
                        payee_downpayment_balance_amount -= self.amount
                        transaction_amount = -self.amount
                    if self.fund_to == 'advance':
                        payee_advance_balance_amount -= self.amount
                        transaction_amount = -self.amount
                if self.fund_source != 'credit' or (self.fund_source != 'credit' and self.fund_to != 'credit'):
                    history_line.append(
                            self.create_history_line(
                                                    self.payer_id,
                                                    payer_advance_balance_amount,
                                                    payer_downpayment_balance_amount,
                                                    transaction_amount,
                                                    business_type,
                                                    document_type,
                                                    fund_type,
                                                    related_customer=self.payee_id,
                                                    fund_transfer_order_id=fund_transfer_order_id
                                                    )
                        )
                # business_type = 'external_transfer_out'
                fund_type = self.fund_to
                transaction_amount = -self.amount
                if self.fund_source == 'credit' or (self.fund_source != 'credit' and self.fund_to != 'credit'):
                    history_line.append(
                            self.create_history_line(
                                                    self.payee_id,
                                                    payee_advance_balance_amount,
                                                    payee_downpayment_balance_amount,
                                                    transaction_amount,
                                                    business_type,
                                                    document_type,
                                                    fund_type,
                                                    related_customer=self.payer_id,
                                                    fund_transfer_order_id=fund_transfer_order_id
                                                    )
                        )
        self.env['customer.advance.payment.history'].create(history_line)

    def action_confirm(self):
        for rec in self:
            if rec.state != 'draft':
                raise models.UserError(_("The fund transfer order must be in draft state before it can be confirmed."))
            rec.state = "confirm"
            if rec.fund_source == 'advance' and rec.customer_settlement_id.received_payment_type in ('advance', 'downpayment'):
                rec.payer_id.allocate_advance_payment_amount += rec.amount
            elif rec.fund_source == 'downpayment' and rec.customer_settlement_id.received_payment_type in ('advance', 'downpayment'):
                rec.payer_id.allocate_downpayment_amount += rec.amount
            elif rec.fund_source == 'credit' and rec.customer_settlement_id.received_payment_type == 'credit':
                rec.payer_id.allocate_credit_amount += rec.amount

    def action_done(self):
        if self.state != 'confirm':
            raise models.UserError(_("The fund transfer order must be confirmed before it can be done."))
        self.state = "done"
        advance_payment_done = all(item == 'done' for item in self.customer_settlement_id.advance_payments_ids.mapped('state'))
        if not advance_payment_done:
            raise models.UserError(_("The advance payment is not done yet, please check the advance payment first."))
        is_applied = (self.fund_to == 'sopayment')
        if self.fund_source == 'credit':
            self.with_context(is_applied=is_applied).create_payment_allocation_credit()
        else:
            self.with_context(is_applied=is_applied).create_payment_allocation()
        if self.fund_source == 'advance':
            if self.customer_settlement_id.received_payment_type in ('advance', 'downpayment'):
                self.payer_id.allocate_advance_payment_amount -= self.amount
            if self.fund_to != 'sopayment':
                # 因为applied金额已经扣减，这边不需要重复扣减
                self.payer_id.total_advance_payment_amount -= self.amount
            if self.fund_to == 'advance':
                self.payee_id.total_advance_payment_amount += self.amount
            if self.fund_to == 'downpayment':
                self.payee_id.total_downpayment_amount += self.amount
            if self.fund_to == 'credit':
                self.payee_id.locked_customer_credit_amount -= self.amount
        elif self.fund_source == 'downpayment':
            if self.customer_settlement_id.received_payment_type in ('advance', 'downpayment'):
                self.payer_id.allocate_downpayment_amount -= self.amount
            if self.fund_to != 'sopayment':
                # 因为applied金额已经扣减，这边不需要重复扣减
                self.payer_id.total_downpayment_amount -= self.amount
            if self.fund_to == 'downpayment':
                self.payee_id.total_downpayment_amount += self.amount
            if self.fund_to == 'advance':
                self.payee_id.total_advance_payment_amount += self.amount
            if self.fund_to == 'credit':
                self.payee_id.locked_customer_credit_amount -= self.amount
        elif self.fund_source == 'credit':
            if self.customer_settlement_id.received_payment_type == 'credit':
                self.payer_id.allocate_credit_amount -= self.amount
                self.payer_id.locked_customer_credit_amount += self.amount
                if self.fund_to == 'downpayment':
                    self.payee_id.total_downpayment_amount += self.amount
                if self.fund_to == 'advance':
                    self.payee_id.total_advance_payment_amount += self.amount
        fund_transfer_done = all(item == 'done' for item in self.customer_settlement_id.customer_fund_transfer_ids.mapped('state'))
        if fund_transfer_done and advance_payment_done:
            # 包含互转的结算单需要特别处理
            self.customer_settlement_id.action_done()
        total_advance_balance_amount = self.payer_id.advance_payment_balance_amount if self.fund_source == 'advance' else self.payer_id.downpayment_balance_amount
        self.total_advance_balance_amount = total_advance_balance_amount

    def unlink(self):
        """
        This function is used to delete the customer advance
        """
        if any(rec.state not in ('draft') for rec in self):
            raise UserError(
                _('you can only delete draft record'))
        return super(FundTransferOrder, self).unlink()

    def action_cancel(self):
        for rec in self:
            if rec.fund_to == 'credit' or rec.fund_source == 'credit':
                statement_type = 'credit'
            elif rec.fund_to == 'sopayment':
                statement_type = 'so'
            else:
                statement_type = 'fund_transfer'
            if rec.state == 'done':
                rec.state = "cancel"
                is_applied = (rec.fund_to == 'sopayment')
                # 取消互转单的时候，先判断是否有核销记录，如果有核销记录，则需要取消核销记录
                if rec.customer_received_statement_id:
                    rec.with_context(document_type='fund_transfer',
                                     funding_source=rec.fund_source,
                                     fund_type=rec.fund_to,
                                     related_customer_id=rec.payee_id.id,
                                     fund_transfer_order_id=rec.id,
                                     is_applied=is_applied,
                                     ).customer_received_statement_id.action_cancel()
                # 创建预收款历史
                if statement_type == 'fund_transfer':
                    if rec.applied_fund_transfer_order_ids:
                        # 如果是付货款用到了互转单,需要释放核销金额
                        if rec.applied_fund_transfer_amount == 0:
                            raise UserError(_("The applied fund_transfer_amount is not correct."))
                        for apply_transfer_order in rec.applied_fund_transfer_order_ids:
                            apply_transfer_order.applied_amount -= rec.applied_fund_transfer_amount
                    rec.create_advance_payment_history()
                elif statement_type == 'credit':
                    self.create_payment_allocation_credit()
                else:
                    # statement type 是 so
                    if rec.applied_fund_transfer_order_ids:
                        # 如果是付货款用到了互转单余额需要创建付款历史
                        if rec.applied_fund_transfer_amount == 0:
                            raise UserError(_("The applied fund_transfer_amount is not correct."))
                        if rec.applied_fund_transfer_amount_details:
                            applied_fund_transfer_amount_details = json.loads(rec.applied_fund_transfer_amount_details)
                        else:
                            raise UserError(_("The applied fund_transfer_amount_details is not correct."))
                        for apply_transfer_order in rec.applied_fund_transfer_order_ids:
                            apply_transfer_order.applied_amount -= applied_fund_transfer_amount_details.get(str(apply_transfer_order.id), 0.0)
                        # 如果部分使用了互转单支付, 更新余额并创建付款历史
                        for fund_transfer in rec.applied_fund_transfer_order_ids:
                            statement_amount = applied_fund_transfer_amount_details.get(str(fund_transfer.id), 0.0)
                            if rec.fund_source == 'advance' and rec.fund_to == 'sopayment':
                                rec.payer_id.applied_advance_payment_amount -= statement_amount
                            if rec.fund_source == 'downpayment' and rec.fund_to == 'sopayment':
                                rec.payer_id.applied_downpayment_amount -= statement_amount
                            if rec.fund_source == 'advance' and rec.fund_to != 'sopayment':
                                rec.payer_id.transfered_out_advance_payment_amount -= statement_amount
                            if rec.fund_source == 'downpayment' and rec.fund_to != 'sopayment':
                                rec.payer_id.transfered_out_downpayment_amount -= statement_amount
                            document_type = 'fund_transfer'
                            business_type = 'reversal'
                            # 需要创建付款历史
                            advance_balance_amount = rec.payer_id.advance_payment_balance_amount
                            downpayment_balance_amount = rec.payer_id.downpayment_balance_amount
                            customer = rec.payer_id
                            transaction_amount = rec.applied_fund_transfer_amount
                            fund_type = rec.fund_to
                            related_customer = False
                            fund_source = rec.fund_source
                            fund_transfer_order_id = rec.id
                            history_line = rec.create_history_line_applied_fund_transfer(
                                customer,
                                advance_balance_amount,
                                downpayment_balance_amount,
                                transaction_amount,
                                business_type,
                                document_type,
                                fund_type,
                                fund_source,
                                related_customer,
                                fund_transfer_order_id,
                            )
                            self.env['customer.advance.payment.history'].create(history_line)
                if rec.fund_source == 'advance':
                    if not is_applied:
                        # 如果是核销的，已经在applied里面体现了扣减的金额，不需要在总额里面返回
                        rec.payer_id.total_advance_payment_amount += rec.amount
                    # 付款人收款人相同的时候只会出现预收款转首付款
                    if rec.fund_to == 'credit':
                        rec.payee_id.locked_customer_credit_amount += rec.amount
                    if rec.payer_id == rec.payee_id:
                        if rec.fund_to == 'downpayment':
                            rec.payee_id.total_downpayment_amount -= rec.amount
                    # 付款人收款人不同的时候，两种都有可能
                    else:
                        if rec.fund_to == 'advance':
                            rec.payee_id.total_advance_payment_amount -= rec.amount
                        if rec.fund_to == 'downpayment':
                            rec.payee_id.total_downpayment_amount -= rec.amount
                elif rec.fund_source == 'downpayment':
                    if not is_applied:
                        # 如果是核销的，已经在applied里面体现了扣减的金额，不需要在总额里面返回
                        rec.payer_id.total_downpayment_amount += rec.amount
                    if rec.fund_to == 'credit':
                        rec.payee_id.locked_customer_credit_amount += rec.amount
                    # 付款人收款人相同的时候只会出现预收款转首付款
                    if rec.payer_id == rec.payee_id:
                        if rec.fund_to == 'advance':
                            rec.payee_id.total_advance_payment_amount -= rec.amount
                    # 付款人收款人不同的时候，两种都有可能
                    else:
                        if rec.fund_to == 'advance':
                            rec.payee_id.total_advance_payment_amount -= rec.amount
                        if rec.fund_to == 'downpayment':
                            rec.payee_id.total_downpayment_amount -= rec.amount
                elif rec.fund_source == 'credit':
                    rec.payer_id.locked_customer_credit_amount -= rec.amount
                    if rec.fund_to == 'downpayment':
                        rec.payee_id.total_downpayment_amount -= self.amount
                    if rec.fund_to == 'advance':
                        rec.payee_id.total_advance_payment_amount -= self.amount

            elif rec.state == 'confirm':
                if rec.fund_source == 'advance' and rec.customer_settlement_id.received_payment_type in ('advance', 'downpayment'):
                    rec.payer_id.allocate_advance_payment_amount -= rec.amount
                elif rec.fund_source == 'downpayment' and rec.customer_settlement_id.received_payment_type in ('advance', 'downpayment'):
                    rec.payer_id.allocate_downpayment_amount -= rec.amount
                elif rec.fund_source == 'credit' and rec.customer_settlement_id.received_payment_type == 'credit':
                    rec.payer_id.allocate_credit_amount -= rec.amount
                rec.state = "cancel"
            else:
                rec.state = "cancel"

    def to_reconcile(self):
        # 转入方如果和转出方一致，则不需要核销，否者需要核销
        to_reconcile_line = self.env['fund.transfer.order.line']
        for line in self.fund_transfer_order_line_ids:
            if line.partner_id.id != self.partner_id.id:
                to_reconcile_line |= line
        if to_reconcile_line:
            # 需要核销的转入方的科目要和转出方的科目一致
            # TODO 如果不一致，需要中间账户
            reconcile_type_ids = account_id_recordset = list(set(to_reconcile_line.mapped("account_id.user_type_id")))
            account_type_id = self.account_id.user_type_id.id
            if (len(reconcile_type_ids) == 1 and reconcile_type_ids[0].id == account_type_id):
                self = self.sudo()
                account_move = self.create_journal_entry(to_reconcile_line)
                self.account_move_id = account_move.id
                account_move.sudo().action_post()
                to_reconcile_move_lines = account_move.line_ids.filtered(
                    lambda x: x.partner_id.id == self.partner_id.id
                )
                account_move_lines = self.select_account_move_lines()
                if not account_move_lines:
                    raise models.UserError(_("Transfer out partner has not enough amount"))
                to_reconcile_move_lines |= account_move_lines

                self.reconcil_account_move_lines(to_reconcile_move_lines)
            else:
                raise models.UserError(
                    _(
                        "The tansfer out partner and transfer in partner have different account"
                    )
                )
        else:
            # don't need to reconcile
            pass

    def reconcil_account_move_lines(self, to_reconcile_move_lines):
        to_reconcile_move_lines.reconcile()

    def select_account_move_lines(self):
        if self.customer_receipt_id.payment_ids:
            account_move_lines = self.get_account_move_line_from_payment()
            """ # 验证payment的金额是否和资金互转单的的金额是否一致，如果不一致应该raise
            if float_compare(sum(account_move_lines.mapped("credit")), self.amount, precision_digits=2) != 0:
                raise models.UserError(_("the amount of payments is not equal to the amount of the fund transfer order")) """
            return account_move_lines
        else:
            move_lines = self.env["account.move.line"].search(
                [('parent_state', '=', 'posted'),
                    ("account_id", "=", self.account_id.id),
                    ("partner_id", "=", self.partner_id.id),
                    ("reconciled", "=", False),
                    ("credit", ">", 0),
                 ]
            )
            # 从move_lines选择一笔amount_residual金额大于等于amount的line
            for move_line in move_lines:
                if float_compare(move_line.amount_residual, self.amount, precision_digits=2) >= 0:
                    return move_line

            # 也可能需要多个line的金额之和大于等于amount
            account_move_lines = self.env["account.move.line"]
            many_amount_residual = 0
            for move_line in move_lines:
                many_amount_residual += abs(move_line.amount_residual)
                account_move_lines |= move_line
                if float_compare(many_amount_residual, self.amount, precision_digits=2) >= 0:
                    return account_move_lines

            return account_move_lines

    def get_account_move_line_from_payment(self):
        # 如果资金互转单对应的收款通知单有对应的payment,应用这些payment来做核销
        account_move_lines = self.customer_receipt_id.payment_ids.mapped("move_id").line_ids.filtered(lambda x: x.account_id.id == self.account_id.id
                                                                                                      and x.partner_id.id == self.partner_id.id
                                                                                                      and x.reconciled is False
                                                                                                      and x.credit > 0)
        return account_move_lines

    # 创建一个分录
    def create_journal_entry(self, to_reconcile_line):
        """此分录记录转入方和转出方的信息"""
        journal_id = self.get_journal_id()
        if not journal_id:
            raise models.UserError(_("please set journal first"))
        line_ids = [
            (
                0,
                0,
                {
                    "account_id": line.account_id.id,
                    "partner_id": line.partner_id.id,
                    "name": line.note,
                    "debit": 0,
                    "credit": line.amount,
                    "currency_id": line.currency_id.id,
                },
            )
            for line in to_reconcile_line
        ]
        line_ids.append(
            (
                0,
                0,
                {
                    "account_id": self.account_id.id,
                    "partner_id": self.partner_id.id,
                    "name": self.note,
                    "debit": sum(to_reconcile_line.mapped('amount')),
                    "credit": 0,
                    "currency_id": self.currency_id.id,
                },
            )
        )
        account_move = self.sudo().env["account.move"].create(
                {
                    "journal_id": int(journal_id),
                    "date": fields.Date.today(),
                    "line_ids": line_ids,
                }
            )

        return account_move

    # TODO 获取系统配置的日记账
    def get_journal_id(self):
        return (
            self.env["ir.config_parameter"]
            .sudo()
            .get_param("fund_transfer_order_journal_id")
        )
