# -*- coding: utf-8 -*-

import logging
from functools import reduce

from odoo import models, _
from odoo.exceptions import UserError


_logger = logging.getLogger(__name__)


class GalaxyDelivery(models.Model):
    _inherit = 'galaxy.delivery'

    def button_cancel(self):
        """
        增加一些额外的取消的时候需要更新的数据
        """
        super(GalaxyDelivery, self).button_cancel()
        # 现货和贸易库存的管家婆订单不在odoo，不处理
        if self.delivery_type in ('xh', 'to_trade_stk'):
            return
        so_order = self.sale_order
        gjp_sale_order = self.env['gjp.sale.order'].search([('gjp_order_no', '=', so_order),
                                                            ('state', '=', 'paid')])
        if not gjp_sale_order:
            raise UserError(_('GJP Sale Order not found or not paid: %s') % so_order)
        if gjp_sale_order.audit_level == 3:
            gjp_sale_order.audit_level = 2  # 设置订单状态为2
