# -*- coding: utf-8 -*-

import logging
from odoo.exceptions import UserError
from odoo.tools import float_compare
from odoo import models, fields, api, _

_logger = logging.getLogger(__name__)


class CustomerPaymentStatement(models.Model):
    _inherit = 'customer.received.statement'

    def action_confirm(self):
        """
        This function is used to confirm the statement
        """
        # if self.statement_type == 'so':
        #     raise UserError(_('The statement type should not be SO.'))
        # if any(self.currency_id != line.customer_advance_received_line_id.currency_id for line in self.statement_line_ids):
        #     raise UserError(_('Statement currency should be same as customer advance received currency'))
        if self.balance_amount != 0:
            raise UserError(_('The balance amount shoudl be 0'))
        for line in self.statement_line_ids:
            if float_compare(line.allocate_amount, line.customer_advance_received_balane_amount, precision_digits=2) > 0:
                raise UserError(_('The allocated amount is greater than the balance amount of the advance received.'))
            line.customer_advance_received_line_id.write({'applied_amount': line.customer_advance_received_line_id.applied_amount + line.allocate_amount})
            line.customer_advance_received_line_id.customer_advance_statement_line_ids = [(0, 0, {
                            'customer_received_statement_line_id': line.id,
                            'allocate_amount': line.allocate_amount,
                            })]
            line.customer_advance_received_line_id.customer_advance_received_id.write({'customer_received_statement_ids': [(4, self.id)]})
        self.write({'state': 'done'})
        if self.statement_type not in ('fund_transfer', 'credit'):
            self.create_advance_payment_history()
        for line in self.statement_line_ids:
            if self._context.get('is_applied', False) or self.statement_type == 'refund':
                if line.customer_advance_received_line_id.received_type == 'advance':
                    line.customer_id.applied_advance_payment_amount += line.allocate_amount
                if line.customer_advance_received_line_id.received_type == 'downpayment':
                    line.customer_id.applied_downpayment_amount += line.allocate_amount

    def action_cancel(self):
        """
        This function is used to cancel the statement
        """
        for rec in self:
            if rec.state not in ('draft', 'cancel'):
                rec.write({'state': 'cancel'})
                for line in rec.statement_line_ids:
                    if line.customer_advance_received_line_id.customer_advance_received_id.customer_receive_type == 'normal':
                        # 正常类型的预收款
                        line.customer_advance_received_line_id.applied_amount -= line.allocate_amount
                        line.customer_advance_received_line_id.customer_advance_statement_line_ids.filtered(lambda m: m.customer_received_statement_line_id == line).unlink()
                    else:
                        # 欠款类型的预收款
                        line.customer_advance_received_line_id.customer_advance_statement_line_ids.filtered(lambda m: m.customer_received_statement_line_id == line).unlink()
                    line.customer_advance_received_line_id.customer_advance_received_id.customer_received_statement_ids -= rec
                if rec.statement_type not in ('fund_transfer', 'credit'):
                    rec.create_advance_payment_history()
                for line in rec.statement_line_ids:
                    if rec._context.get('is_applied', False):
                        if line.customer_advance_received_line_id.received_type == 'advance':
                            line.customer_id.applied_advance_payment_amount -= line.allocate_amount
                        if line.customer_advance_received_line_id.received_type == 'downpayment':
                            line.customer_id.applied_downpayment_amount -= line.allocate_amount
                    elif rec.statement_type == 'refund':
                        # 退款类型的取消
                        if line.customer_advance_received_line_id.received_type == 'advance':
                            line.customer_id.applied_advance_payment_amount -= line.allocate_amount
                        if line.customer_advance_received_line_id.received_type == 'downpayment':
                            line.customer_id.applied_downpayment_amount -= line.allocate_amount
            else:
                rec.write({'state': 'cancel'})

    def create_advance_payment_history(self):
        """
        This function is used to create advance payment history
        """
        history_line = []
        if self.statement_type == 'refund':
            document_type = 'refund'
        elif self.statement_type == 'credit':
            document_type = 'payment_receipt'
        else:
            document_type = self._context.get('document_type', 'fund_transfer')
        if self.statement_type == 'refund':
            if self.state != 'cancel':
                business_type = 'receipt'
            else:
                business_type = 'reversal'
        else:
            if self.state == 'done' and self.statement_type == 'so':
                business_type = 'goods_payment'
            if self.state == 'done' and self.statement_type == 'credit':
                business_type = 'receipt'
            if self.state == 'cancel':
                business_type = 'reversal'
        advance_balance_amount = self.customer_id.advance_payment_balance_amount
        downpayment_balance_amount = self.customer_id.downpayment_balance_amount
        for rec in self.statement_line_ids:
            advance_receive_line = rec.customer_advance_received_line_id
            if business_type == 'reversal' and self.statement_type != 'refund':
                # 只有不是退款类型并且取消的时候才走这个逻辑
                if rec.customer_advance_received_type == 'advance':
                    advance_balance_amount += rec.allocate_amount
                elif rec.customer_advance_received_line_id.received_type == 'downpayment':
                    downpayment_balance_amount += rec.allocate_amount
            else:
                if self.state != 'cancel':
                    # 正常用款
                    if rec.customer_advance_received_type == 'advance':
                        advance_balance_amount -= rec.allocate_amount
                    elif rec.customer_advance_received_type == 'downpayment':
                        downpayment_balance_amount -= rec.allocate_amount
                else:
                    # 退款类型的取消
                    if rec.customer_advance_received_type == 'advance':
                        advance_balance_amount += rec.allocate_amount
                    elif rec.customer_advance_received_type == 'downpayment':
                        downpayment_balance_amount += rec.allocate_amount
            funding_source = self._context.get('funding_source', advance_receive_line.customer_advance_received_id.received_payment_type)
            fund_type = self._context.get('fund_type', advance_receive_line.received_type)
            related_customer_id = self._context.get('related_customer_id', False)
            fund_transfer_order_id = self._context.get('fund_transfer_order_id', False)
            if business_type == 'reversal' and self.statement_type != 'refund':
                transaction_amount = rec.allocate_amount
            elif self.statement_type == 'refund' and self.state == 'cancel':
                transaction_amount = rec.allocate_amount
            else:
                transaction_amount = -rec.allocate_amount
            history_line.append({
                'customer_id': self.customer_id.id,
                'related_customer_id': related_customer_id,
                'operator': self.env.user.id,
                'advance_balance_amount': advance_balance_amount,
                'downpayment_balance_amount': downpayment_balance_amount,
                'transaction_amount': transaction_amount,
                'reconciliation_status': advance_receive_line.customer_advance_received_id.payment_state,
                'customer_received_statement_id': self.id,
                'customer_received_statement_line_id': rec.id,
                'funding_source': funding_source,
                'fund_type': fund_type,
                'business_type': business_type,
                'document_type': document_type,
                'transaction_date': fields.Datetime.now(),
                'customer_advance_received_id': advance_receive_line.customer_advance_received_id.id,
                'customer_advance_received_line_id': advance_receive_line.id,
                'fund_transfer_order_id': fund_transfer_order_id,
                'merged_bank_statement_ids': advance_receive_line.customer_advance_received_id.bank_statement_line_ids,
            })
        self.env['customer.advance.payment.history'].create(history_line)
