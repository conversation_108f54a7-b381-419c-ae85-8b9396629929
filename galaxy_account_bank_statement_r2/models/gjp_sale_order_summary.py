# -*- coding: utf-8 -*-

import logging
import qrcode
import base64
from io import BytesIO

from odoo import models, fields, api, _
from odoo.exceptions import UserError
from .gjp_query_obj import GJPQuery
from odoo.tools import float_compare

_logger = logging.getLogger(__name__)


class GjpSaleOrderSummary(models.Model):
    _name = 'gjp.sale.order.summary'
    _description = 'GJP Sale Order Summary'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _rec_name = 'name'
    _order = 'id desc'

    name = fields.Char('Number', index=True, default=_('New'), required=True, copy=False)
    line_ids = fields.One2many('gjp.sale.order.summary.line', 'summary_id', string='Order Lines')
    customer_id = fields.Many2one('res.partner', string='Customer', required=True)
    gjp_customer_name = fields.Char(string='GJP Customer Name', compute="_compute_get_gjp_customer", readonly=True, store=True, index=True)
    customer_currency_id = fields.Many2one('res.currency', string='Customer Currency', readonly=True, related="customer_id.customer_currency_id", store=True, index=True)
    total_amount = fields.Float(string='Total Amount', compute="_get_total_amount", store=True, digits=(12, 2),)
    total_unsettlement_amount = fields.Float(string='Unsettlement Amount', readonly=True, digits=(12, 2),)
    total_paid_amount = fields.Float(string='Paid Amount', readonly="1", tracking=True, digits=(12, 2),)
    total_quantity = fields.Integer(string='Total Quantity', compute='_compute_get_total_quantity', store=True)
    balance_amount = fields.Float(string='Balance Amount', compute="_get_balance_amount", store=True, digits=(12, 2),)
    customer_advance_settlement_ids = fields.Many2many(
        'customer.settlement', string='Customer Settlements',
        relation='galaxy_customer_settlement_gjp_sale_order_summary')
    customer_advance_settlement_counts = fields.Integer(
        string='Settlement Count',
        compute='_compute_customer_advance_settlement_counts',
        help='Number of customer settlements linked to this summary'
    )
    glot = fields.Char(
        string='GLOT', compute="_compute_get_glot", store=True, index=True, readonly=True)
    state = fields.Selection([
        ('draft', 'Draft'),
        ('confirm', 'Confirmed'),
        ('partial_paid', 'Partial Paid'),
        ('paid', 'Paid'),
        ('cancel', 'Cancelled'),
    ], string='Payment Status', default='draft', required=True, tracking=True,)
    customer_advance_settlement_line_ids = fields.Many2many(
        'customer.settlement.lines', string='Customer Settlement Lines',
        relation='galaxy_customer_settlement_line_gjp_sale_order_summary',
        compute='_compute_customer_advance_settlement_lines',
        help='Lines from customer settlements linked to this summary'
    )
    advance_payment_ids = fields.Many2many(
        'customer.advance.received', string='Advance Payments',
        relation='galaxy_advance_payment_gjp_sale_order_summary_rel',
        compute='_compute_get_advance_payments_ids',
        help='Advance payments linked to this summary'
    )
    customer_fund_transfer_ids = fields.Many2many(
        'fund.transfer.order', string='Customer Fund Transfers',
        relation='fund_transfer_order_gjp_sale_order_summary_rel',
        help='Fund transfers linked to this summary'
    )
    advance_payment_counts = fields.Integer(
        string='Advance Payment Count',
        compute='_compute_get_advance_payments_count',
        help='Number of advance payments linked to this summary'
    )
    customer_fund_transfer_counts = fields.Integer(
        string='Fund Transfer Count',
        compute='_compute_get_advance_payments_count',
        help='Number of fund transfers linked to this summary'
    )

    note = fields.Text(string='Notes', help='Notes')
    qr_img = fields.Binary(
        string='QR Code', compute='_compute_qr_img',
        help='QR Code for the GJP Order Number'
    )
    customer_security_code = fields.Char(
        tracking=True,
        size=6,
        default="000000",
        required=True,
        string='Delivery Verify Code',
        help='This is the 6-digit security code for the customer, used for delivery verification purposes.')
    
    
    def check_latest_gjp_sale_order(self):
        """
        检查最新的管家婆销售单金额是否有变动
        """
        error_order = []
        for summary_line in self.line_ids:
            gjp_sale_order_no = summary_line.gjp_sale_order_id.gjp_order_no
            gjp_sale_order = self.env['gjp.sale.order']
            response, error_msg = GJPQuery.sync_sale_orders_by_one(gjp_sale_order_no)
            if not response or error_msg:
                raise models.UserError(
                    _('Failed to get GJP Sale Orders: %s') % error_msg)
            for gjp_so in response['message'][0]:
                grand_total = gjp_so.get('grand_total')
                if float_compare(summary_line.amount, grand_total, precision_digits=2) != 0:
                    error_order.append(gjp_sale_order_no)
        if error_order:
            error_order_str = ', '.join(error_order)
            raise models.UserError(_(f'GJP Sale Order {error_order_str} amount has changed. Please sync sale order.'))

    def unlink(self):
        """
        This function is used to delete the customer advance
        """
        if any(rec.state not in ('draft') for rec in self):
            raise UserError(
                _('you can only delete draft record'))
        return super(GjpSaleOrderSummary, self).unlink()

    def action_sync_gjp_sale_orders(self):
        """
        同步GJP销售单
        """
        for summary_line in self.line_ids:
            gjp_sale_order_no = summary_line.gjp_sale_order_id.gjp_order_no
            summary_line.gjp_sale_order_id.sync_gjp_sale_order_by_one(gjp_sale_order_no)
        for rec in self.line_ids:
            rec.detail_line_ids = [(5, 0, 0)]  # Clear existing detail lines
            rec.detail_line_ids = [(0, 0, {
                'gjp_sale_order_line_id': gjp_line.id,
                'description': gjp_line.description,
                'quantity': gjp_line.quantity,
                'price_unit': gjp_line.price_unit,
                'amount': gjp_line.amount,
            }) for gjp_line in rec.gjp_sale_order_id.detail_line_ids]
    
    def action_back_to_draft(self):
        """
        从确认状态转回到草稿状态
        """
        if self.customer_advance_settlement_counts != 0:
            raise models.UserError(_('There are linked settlements, can not back to draft!'))
        if self.state != 'confirm':
            raise models.UserError(_('Only confirm state can back to draft!'))
        if self.state == 'confirm':
            self.state = 'draft'

    def set_delivery_verify_code(self):
        """
        This function sets the delivery verify code for the customer
        """
        self.customer_security_code = self.env['gjp.sale.order'].get_random_six_code()
        for line in self.line_ids:
            if line.gjp_sale_order_id:
                line.gjp_sale_order_id.customer_security_code = self.customer_security_code

    # @api.onchange('customer_security_code')
    # def onchange_customer_security_code(self):
    #     for line in self.line_ids:
    #         if line.gjp_sale_order_id:
    #             line.gjp_sale_order_id.customer_security_code = self.customer_security_code
    
    def write(self, vals):
        """
        This function is used to update the summary
        """
        ret = super(GjpSaleOrderSummary, self).write(vals)
        for rec in self:
            if rec.customer_security_code or 'customer_security_code' in vals:
                rec.line_ids.gjp_sale_order_id.customer_security_code = rec.customer_security_code or ''
        return ret
    
    @api.depends('line_ids.gjp_sale_order_id.glot')
    def _compute_get_glot(self):
        """
        This function computes the GLOT from the first line's GJP Sale Order
        """
        for rec in self:
            if rec.line_ids:
                rec.glot = ','.join(rec.line_ids.gjp_sale_order_id.mapped('glot'))
            else:
                rec.glot = ''

    @api.depends('name')
    def _compute_qr_img(self):
        """
        This function computes the QR code image for the GJP Order Number
        """
        for rec in self:
            if rec.name:
                qr = qrcode.QRCode(
                    version=1,
                    error_correction=qrcode.constants.ERROR_CORRECT_L,
                    box_size=10,
                    border=4,
                )
                qr.add_data(rec.name)
                qr.make(fit=True)
                img = qr.make_image(fill='black', back_color='white')
                temp = BytesIO()
                img.save(temp, format="PNG")
                rec.qr_img = base64.b64encode(temp.getvalue())
            else:
                rec.qr_img = False

    def action_view_advance_payments(self):
        """
        This function opens the advance payments linked to this summary
        """
        action = self.sudo().env.ref('galaxy_lot_r2.action_account_customer_advance_received').read()[0]
        action['domain'] = [('id', 'in', self.advance_payment_ids.ids)]
        action['context'] = {'default_summary_id': self.id}
        return action

    def action_view_fund_transfers(self):
        """
        This function opens the fund transfers linked to this summary
        """
        action = self.sudo().env.ref('galaxy_account_bank_statement_r2.fund_transfer_order_action').read()[0]
        action['domain'] = [('id', 'in', self.customer_fund_transfer_ids.ids)]
        action['context'] = {'default_summary_id': self.id}
        return action

    @api.depends('advance_payment_ids', 'customer_fund_transfer_ids')
    def _compute_get_advance_payments_count(self):
        """
        This function computes the count of advance payments and fund transfers linked to this summary
        """
        for rec in self:
            rec.advance_payment_counts = len(rec.advance_payment_ids)
            rec.customer_fund_transfer_counts = len(rec.customer_fund_transfer_ids)

    @api.depends('customer_advance_settlement_line_ids')
    def _compute_get_advance_payments_ids(self):
        """
        This function computes the advance payments linked to this summary
        """
        for rec in self:
            rec.advance_payment_ids = self.env['customer.advance.received']
            rec.customer_fund_transfer_ids = self.env['fund.transfer.order']
            for line in rec.customer_advance_settlement_line_ids:
                rec.advance_payment_ids |= line.advance_payment_id
                rec.customer_fund_transfer_ids |= line.customer_fund_transfer_ids
            rec.advance_payment_ids |= rec.customer_advance_settlement_ids.mapped('advance_payments_ids')

    def _compute_customer_advance_settlement_lines(self):
        """
        This function computes the count of customer settlements linked to this summary
        and updates the customer_advance_settlement_line_ids field
        """
        for rec in self:
            linked_settlement_lines = self.env['customer.settlement.lines'].search([
                ('gjp_sale_order_summary_id', '=', rec.id), ('customer_settlement_id.state', '!=', 'cancel')
            ])
            rec.customer_advance_settlement_line_ids = tuple(linked_settlement_lines.ids)

    @api.depends('customer_advance_settlement_ids')
    def _compute_customer_advance_settlement_counts(self):
        """
        This function computes the count of customer settlements linked to this summary
        """
        for rec in self:
            rec.customer_advance_settlement_counts = len(rec.customer_advance_settlement_ids)

    def action_view_customer_settlements(self):
        """
        This function opens the customer settlements linked to this summary
        """
        action = self.sudo().env.ref('galaxy_account_bank_statement_r2.action_customer_settlement').read()[0]
        action['domain'] = [('id', 'in', self.customer_advance_settlement_ids.ids)]
        action['context'] = {'default_summary_id': self.id}
        return action

    def update_paid_amount(self, paid_amount):
        """
        正向逻辑金额更新
        """
        self.total_paid_amount += paid_amount
        if float_compare(self.total_paid_amount, self.total_amount, precision_digits=2) >= 0:
            self.state = 'paid'
            for line in self.line_ids:
                line.paid_amount = line.amount
                line.gjp_sale_order_id.state = 'paid'
                line.gjp_sale_order_id.paid_date = fields.Datetime.now()
                # line.gjp_sale_order_id.customer_security_code = line.gjp_sale_order_id.get_random_six_code()
                line.gjp_sale_order_id.audit_level = '2'
                line.gjp_sale_order_id.paid_amount = line.paid_amount
        elif self.total_paid_amount > 1:
            self.state = 'partial_paid'

    def action_confirm(self):
        """
        This function is used to confirm the summary and exit edit mode
        """
        self.ensure_one()
        self.state = 'confirm'
        self.check_latest_gjp_sale_order()
        if self.total_amount == 0:
            raise models.UserError(_('Amount can not be 0'))
        for line in self.line_ids:
            line.gjp_sale_order_id.sale_summary_order_id = self.id
        # Return display action to exit edit mode without reloading
        return {
            'type': 'ir.actions.act_window',
            'res_model': self._name,
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'current',
            'flags': {'mode': 'readonly'},
        }

    def action_cancel(self):
        """
        This function is used to cancel the summary
        """
        for rec in self:
            self.line_ids.gjp_sale_order_id.sale_summary_order_id = False
            if rec.state == 'draft':
                rec.state = 'cancel'
            # 检查是否关联了结算单
            elif rec.state in ('confirm', 'partial_paid', 'paid'):
                if rec.customer_advance_settlement_ids:
                    rec.customer_advance_settlement_ids.action_cancel()
                    if all(customer_settlement.state == 'cancel' for customer_settlement in rec.customer_advance_settlement_ids):
                        rec.state = 'cancel'
                    else:
                        raise models.ValidationError(_('Cannot cancel summary with linked settlements that are not cancelled.'))
                else:
                    rec.state = 'cancel'

    @api.depends('line_ids.quantity')
    def _compute_get_total_quantity(self):
        """
        This function computes the total quantity of all lines in the summary
        """
        for rec in self:
            rec.total_quantity = sum(line.quantity for line in rec.line_ids)

    @api.depends('line_ids.paid_amount')
    def _get_paid_amount(self):
        """
        This function computes the total paid amount of all lines in the summary
        """
        for rec in self:
            rec.total_paid_amount = sum(line.paid_amount for line in rec.line_ids)

    @api.constrains('line_ids')
    def _check_gjp_order_sequence(self):
        """
        This function checks if the GJP order sequence is unique across all lines
        """
        gjp_orders = self.line_ids.mapped('gjp_sale_order_id.gjp_order_no')
        if len(gjp_orders) != len(set(gjp_orders)):
            raise models.ValidationError(_('GJP Order must be unique across all lines.'))
        # 检查是否Order Sequence是不是五位数字
        sequences = self.line_ids.mapped('gjp_order_sequence')
        for sequence in sequences:
            if not sequence.isdigit() or len(sequence) != 5:
                raise models.ValidationError(_('GJP Order Sequence must be a five-digit number.'))
        if any([not line.gjp_sale_order_id for line in self.line_ids]):
            raise models.ValidationError(_('GJP Sale Order can not be empty when creating a new summary.'))
        if any([line.customer_id != self.customer_id for line in self.line_ids]):
            raise models.ValidationError(_('All lines must have the same customer as the summary.'))
        if any([line.gjp_order_sequence != line.gjp_sale_order_id.gjp_order_no.split('-')[-1] for line in self.line_ids]):
            raise models.ValidationError(_('GJP Order Sequence must match the last part of GJP Order Number in the sale order.'))
        for line in self.line_ids:
            if line.gjp_sale_order_id:
                check_gjp_sale_order = self.env['gjp.sale.order.summary.line'].search([
                    ('gjp_sale_order_id', '=', line.gjp_sale_order_id.id),
                    ('summary_id', '!=', self.id),
                    ('summary_id.state', '!=', 'cancel'),
                ])
                if check_gjp_sale_order:
                    raise models.ValidationError(
                        _('GJP Sale Order %s is already linked to summary %s.') % (line.gjp_sale_order_id.gjp_order_no,
                                                                                   check_gjp_sale_order.summary_id.name))

    @api.depends('line_ids.amount')
    def _get_total_amount(self):
        for rec in self:
            rec.total_amount = sum(line.amount for line in rec.line_ids)
            rec.total_unsettlement_amount = rec.total_amount

    @api.depends('total_amount', 'total_paid_amount')
    def _get_balance_amount(self):
        for rec in self:
            rec.balance_amount = rec.total_amount - rec.total_paid_amount

    @api.depends('customer_id')
    def _compute_get_gjp_customer(self):
        for rec in self:
            gjp_customer = self.env['galaxy.gjp.customer'].search(
                [('number', '=', rec.customer_id.bid_user_id)], limit=1)
            rec.gjp_customer_name = gjp_customer.name or ''

    @api.model
    def create(self, vals):
        """
        This function is used to generate the sequence number
        在create之后更新name的原因是constrain的检查是在触发是在create之后进行的，（特别是对lines的检查）这样有可能导致序号不连续
        """
        ret = super(GjpSaleOrderSummary, self).create(vals)
        if vals.get('name', _('New')) == _('New'):
            vals['name'] = self.env['ir.sequence'].next_by_code(
                'gjp.order.summary.seqn') or _('New')
        ret.name = vals.get('name', _('New'))
        ret.line_ids.update_line_numbers(ret.line_ids)
        if 'customer_security_code' in vals:
            ret.line_ids.gjp_sale_order_id.customer_security_code = vals.get('customer_security_code', '')
        return ret

    @api.onchange('line_ids', 'line_ids.gjp_order_sequence')
    def change_line_ids(self):
        """
        This function is used to update the state based on the line amounts
        """
        # 获取新建的或更新的行记录
        # if not self.line_ids:
        #     return
        # # 获取新建的行记录
        # new_lines = self.line_ids.filtered(lambda l: l._origin.id is False)
        orders_list = self.line_ids.mapped('gjp_order_sequence')
        if not orders_list:
            return
        exists_gjp_sale_order = self.env['gjp.sale.order'].search([
            ('gjp_order_sequence', 'in', orders_list), ('customer_id', '=', self.customer_id.id), ('sale_person', 'ilike', 'Yan 阿颜')])
        need_create_gjp_sale_order = set(orders_list) - set(exists_gjp_sale_order.mapped('gjp_order_sequence'))
        if need_create_gjp_sale_order:
            gjp_sale_order = self.create_gjp_sale_order(list(need_create_gjp_sale_order))
        else:
            gjp_sale_order = exists_gjp_sale_order
        gjp_sale_order += exists_gjp_sale_order
        selected_gjp_sale_order = self.env['gjp.sale.order']
        for line in self.line_ids:
            suggest_sale_order = gjp_sale_order.filtered(
                lambda so: so.gjp_order_sequence == line.gjp_order_sequence and so.user_code == self.customer_id.bid_user_id and so not in selected_gjp_sale_order)
            if suggest_sale_order:
                line.gjp_sale_order_id = suggest_sale_order[0].id
            else:
                line.gjp_sale_order_id = False
            selected_gjp_sale_order |= line.gjp_sale_order_id
            # 返回一个警告信息
            if not line.gjp_sale_order_id:
                self.env.user.notify_warning(
                    title=_('Warning'),
                    message=_('No GJP Sale Order found for the selected order sequence: %s. Please check the order sequence.') % line.gjp_order_sequence,
                )
                line.gjp_order_sequence = ''

    def create_gjp_sale_order(self, orders_list):
        """
        This function is used to get GJP Sale Order based on the sale order list
        """
        gjp_sale_order = self.env['gjp.sale.order']
        response, error_msg = GJPQuery.get_sale_orders_list(orders_list)
        if not response or error_msg:
            raise models.UserError(
                _('Failed to get GJP Sale Orders: %s') % error_msg)
        for gjp_so in response['message'][0]:
            gjp_order_no = gjp_so.get('so_number')
            audit_level = gjp_so.get('audit_level')
            glot = gjp_so.get('glot')
            dept = gjp_so.get('dept')
            user_code = gjp_so.get('user_code')
            if user_code != self.customer_id.bid_user_id:
                continue
            customer_id = self.env['res.partner'].search([
                ('bid_user_id', '=', user_code)], limit=1)
            order_summary = gjp_so.get('order_summary')
            sale_person = gjp_so.get('sale_person')
            delivery_number = gjp_so.get('delivery_number')
            grand_total = gjp_so.get('grand_total')
            odoo_so_number = gjp_so.get('odoo_so_number')
            so_order_line = gjp_so.get('so_orders_line', [])
            # 检查是否已经存在相同的销售订单
            existing_gjp_order = self.env['gjp.sale.order'].search([
                ('gjp_order_no', '=', gjp_order_no)], limit=1)
            if not existing_gjp_order:
                # 如果不存在，则创建新的销售订单
                new_gjp_order = self.env['gjp.sale.order'].create({
                    'gjp_order_no': gjp_order_no,
                    'customer_id': customer_id.id if customer_id else False,
                    'audit_level': audit_level,
                    'glot': glot,
                    'dept': dept,
                    'user_code': user_code,
                    'order_summary': order_summary,
                    'sale_person': sale_person,
                    'delivery_number': delivery_number,
                    'amount': grand_total,
                    'odoo_so_number': odoo_so_number,
                })
                for line in so_order_line:
                    self.env['gjp.sale.order.line'].create({
                        'gjp_sale_order_id': new_gjp_order.id,
                        'product_code': line.get('product_code'),
                        'description': line.get('product_full_name'),
                        'quantity': line.get('qty'),
                        'line_summary': line.get('line_summary'),
                        'line_comment': line.get('line_comment'),
                        'price_unit': line.get('price'),
                        'amount': line.get('sub_total'),
                    })
                gjp_sale_order |= new_gjp_order
        if error_msg:
            _logger.error(error_msg)
        else:
            _logger.info("GJP Sale Orders Response: %s", response)
        return gjp_sale_order

    def action_print_delivery(self):
        """
        This function is used to print the delivery order (Sale Order Summary)
        """
        self.ensure_one()
        return self.env.ref('galaxy_account_bank_statement_r2.action_report_delivery_order').report_action(self)
    
    
    def action_send_email(self):
        """
        发送GJP销售订单汇总邮件，并附带GJP销售订单汇总报告作为附件
        """
        # 邮件模板的XML ID（请根据实际模板调整）
        template = self.env.ref('galaxy_account_bank_statement_r2.mail_template_sale_order_summary', raise_if_not_found=False)
        if not template:
            raise models.UserError(_('Email template not found.'))

        for summary in self:
            attachment_ids = []
            try:
                # 生成PDF报告
                report_action = self.env.ref('galaxy_account_bank_statement_r2.action_report_sale_order_summary')
                if not report_action:
                    raise models.UserError(_('Report action not found.'))
                # 生成PDF内容
                pdf_content, _ = report_action._render_qweb_pdf([summary.id])
                # 编码为base64
                pdf_base64 = base64.b64encode(pdf_content)
                # 创建附件
                attachment = self.env['ir.attachment'].create({
                    'name': f"Sale_Order_Summary_{summary.name}.pdf",
                    'type': 'binary',
                    'datas': pdf_base64,
                    'res_model': 'gjp.sale.order.summary',
                    'res_id': summary.id,
                })
                attachment_ids.append(attachment.id)
            except Exception as e:
                _logger.error(f"Failed to generate report for GJP Sale Order Summary {summary.name}: {str(e)}")
                raise models.UserError(_('Failed to generate PDF report: %s') % str(e))

            # 发送邮件并附带附件
            template.send_mail(
                summary.id,
                force_send=True,
                email_values={'attachment_ids': [(6, 0, attachment_ids)]}
            )
        return True
