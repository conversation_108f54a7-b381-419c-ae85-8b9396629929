# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.tools import float_compare
from odoo.exceptions import UserError
from odoo.addons.base_galaxy.models.public_func import parse_float


class GalaxyAccountBankMergedStatement(models.Model):
    _inherit = 'galaxy.account.bank.merged.statement'

    advance_payment_line_ids = fields.Many2many('customer.advance.received', relation="advance_payment_galaxy_bank_statment_rel", string='Advance Payment Lines')
    total_advance_payment_amount = fields.Float(string='Total Advance Payment Amount', compute='_compute_total_advance_payment_amount', store=True, digits=(12, 2))

    @api.depends('advance_payment_line_ids', 'advance_payment_line_ids.total_amount')
    def _compute_total_advance_payment_amount(self):
        for record in self:
            total_amount = 0.0
            for line in record.advance_payment_line_ids:
                total_amount += parse_float(line.total_amount)
            record.total_advance_payment_amount = total_amount

    def action_in_progress(self):
        if not self.partner_id:
            raise models.UserError(_("Please select a remittance partner."))
        if self.state != 'to_do':
            raise models.UserError(_("Only to-do statement can be confirmed."))
        if any(advance_payment_line.currency_id.currency_id != self.currency_id for advance_payment_line in self.advance_payment_line_ids):
            raise models.UserError(_("The currency of advance payment line should be same as bank statement."))
        if self.net_deposit_amount != self.total_advance_payment_amount:
            raise models.UserError(_("The total advance payment amount should same as net deposit amount."))
        not_confirm_advance_payment_line = self.advance_payment_line_ids.filtered(lambda x: x.state not in ('confirm', 'done'))
        if not_confirm_advance_payment_line:
            raise models.UserError(_("advance payment line are not confirm or paid, please check."))
        self.write({'state': 'in_progress',
                    'responsible_person_id': self.env.user.id})
        # 创建一个用户活动
        auditor_user = self.env['ir.config_parameter'].sudo().get_param('galaxy_bankstatement_responsible_user')
        if not auditor_user:
            raise models.UserError(_("Please set the auditor user in the settings."))
        else:
            auditor_user = int(auditor_user)
            self.activity_schedule('mail.mail_activity_data_todo', user_id=auditor_user, summary=_("Please Audit the bank statement"), note=_("Please Audit the bank statement"))

    def action_done(self):
        # 同时把用户活动标记为完成
        if self.state != 'in_progress':
            raise models.UserError(_("Only confirmed statement can be audited."))
        if self.net_deposit_amount != self.total_advance_payment_amount:
            raise models.UserError(_("The total advance payment amount should same as net deposit amount."))
        not_confirm_advance_payment_line = self.advance_payment_line_ids.filtered(lambda x: x.state not in ('confirm', 'done'))
        if not_confirm_advance_payment_line:
            raise models.UserError(_("advance payment line are not confirm, please check."))
        auditor_user = self.env['ir.config_parameter'].sudo().get_param('galaxy_bankstatement_responsible_user')
        auditor_user = int(auditor_user)
        activity = self.env['mail.activity'].search([('res_id', '=', self.id), ('res_model', '=', 'galaxy.account.bank.merged.statement'), ('user_id', '=', auditor_user), ('activity_type_id', '=', self.env.ref('mail.mail_activity_data_todo').id)], limit=1)
        activity.action_done()
        self.write({'state': 'done'})
        for advance_payment in self.advance_payment_line_ids:
            if advance_payment.state == 'confirm':
                advance_payment.action_done()

    def action_to_do(self):
        if self.state == 'done':
            raise UserError(_('Only confirmed state can back to to-do!'))
        self.write({'state': 'to_do'})
        # self.advance_payment_line_ids.filtered(lambda m: m.state == 'done').action_back_to_confirm()
        self.advance_payment_line_ids = [(5, 0, 0)]  # 清空已关联的advance payment line

    @api.model
    def create(self, vals):
        """
        创建statment以后，尝试自动匹配advance payment line
        """
        ret = super(GalaxyAccountBankMergedStatement, self).create(vals)
        self.update_related_advance_payment()
        return ret

    def update_related_advance_payment(self):
        """
        更新相关的advance payment line
        """
        confirm_advance_payment_line = self.env['customer.advance.received'].search([('received_payment_type', '=', 'bank'), ('state', '=', 'confirm')])
        need_to_check_settlement = self.env['customer.settlement'].search([('state', 'in', ('confirm', 'audited'))])
        self.update_bank_payer()
        for item in confirm_advance_payment_line:
            if (item.name or '').lower() in (self.remittance_message or '').lower():
                # 如果advance payment的名称在statement的备注中，说明是可以关联的
                # 直接关联到statement上
                self.partner_id = item.payer_id.id
                if item not in self.advance_payment_line_ids:
                    item.write({'bank_statement_line_ids': [(4, self.id)]})
                    if self.state == 'to_do':
                        try:
                            self.action_in_progress()
                        except Exception as e:
                            pass
        if not self.advance_payment_line_ids:
            for item in need_to_check_settlement:
                if (item.name or '').lower() in (self.remittance_message or '').lower():
                    # 如果结算单的名称在statement的备注中，说明是可以关联的
                    # 直接关联到statement上
                    if not self.partner_id:
                        self.partner_id = item.payer_id.id
                    if item.advance_payments_ids:
                        for advance_payment in item.advance_payments_ids:
                            if advance_payment not in self.advance_payment_line_ids:
                                advance_payment.write({'bank_statement_line_ids': [(4, self.id)]})
                                if self.state == 'to_do':
                                    try:
                                        self.action_in_progress()
                                    except Exception as e:
                                        pass

    def update_bank_payer(self):
        """
        获取银行付款人
        """
        if self.remittance_message:
            # 解析银行付款人
            payer_list = self.env['res.partner'].search([]).mapped('bid_user_id')
            for payer in payer_list:
                if payer in self.remittance_message.upper():
                    bank_payer_id = self.env['res.partner'].search([('bid_user_id', '=', payer)])
                    self.partner_id = bank_payer_id.id
                    break
