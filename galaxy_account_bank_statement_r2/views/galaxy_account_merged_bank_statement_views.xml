<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <!-- Tree View for GalaxyAccountBankStatement -->
    
    <!-- Form View for GalaxyAccountBankStatement -->
    <record id="view_galaxy_account_merged_bank_statement_form" model="ir.ui.view">
        <field name="name">galaxy.account.bank.merged.statement.form</field>
        <field name="model">galaxy.account.bank.merged.statement</field>
        <field name="inherit_id" ref="galaxy_account_bank_statement.view_galaxy_account_merged_bank_statement_form"/>
        <field name="arch" type="xml">
            <xpath expr="//header" position="inside">
                <button name="update_related_advance_payment" type="object" string="Update Advance Payments" class="btn-secondary" attrs="{'invisible': [('state', '!=', 'to_do')]}"/>
            </xpath>
            <xpath expr="//page[1]" position="before">
                <page name="advance_payment_line_ids" attrs="{'readonly': [('state', '!=', 'draft')]}" string="Advance Payment Lines">
                    <field domain="[('customer_id', '=', partner_id),
                    ('customer_currency_id', '=', currency_id),
                    ('state','in',('confirm', 'done')),
                    ('received_payment_type','=','bank'),
                    ('bank_statement_line_ids', '=', False)]" context="{'search_view_ref':'galaxy_account_bank_statement_r2.customer_advance_received_filter',
                    'form_view_ref':'galaxy_account_bank_statement_r2.customer_advance_received_form'}" name="advance_payment_line_ids" nolabel="1">
                        <tree create="0" delete="0">
                            <field name="name"/>
                            <field name="customer_id"/>
                            <field name="currency_id"/>
                            <field name="total_amount"/>
                            <field name="note"/>
                            <field name="create_date" widget="date"/>
                        </tree>
                    </field>
                </page>
            </xpath>
            <xpath expr="//field[@name='deposit_amount']" position="after">
                <field name="total_advance_payment_amount" readonly="1"/>
            </xpath>
        </field>
    </record>
</odoo>