<odoo>
    <data>
        <!-- Tree View -->
        <record id="view_res_partner_tree_customer_credit" model="ir.ui.view">
            <field name="name">res.partner.tree.inherit</field>
            <field name="model">res.partner</field>
            <field name="priority" eval="99"/>
            <field name="arch" type="xml">
                <tree create="0" delete="0" edit="0" string="Customer Credit Report" default_order = 'bid_user_id'>
                    <field name="bid_user_id"/>
                    <field name="name"/>
                    <field name="customer_currency_id"/>
                    <field name="customer_credit_amount" readonly="1"/>
                    <field name="locked_customer_credit_amount" readonly="1"/>
                    <field name="available_customer_credit_amount" readonly="1"/>
                    <field name="last_customer_credit_changed_date"/>
                    <field name="id" invisible="1"/>
                    <button name="action_view_credit_details" context="{'customer_id':id}" string="Details" type="object" class="btn btn-secondary oe_stat_button"/>
                </tree>
            </field>
        </record>
        <record id="view_res_partner_customer_credit_form" model="ir.ui.view">
            <field name="name">res.partner.tree.inherit</field>
            <field name="model">res.partner</field>
            <field name="arch" type="xml">
                <form edit="0" create="0" delete="0">
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button name="action_open_advance_receive_data" type="object" class="oe_stat_button" icon="fa-money">
                                Advance Receive
                            </button>
                            <button name="action_open_advance_statement_data" type="object" class="oe_stat_button" icon="fa-money">
                                Advance Statement
                            </button>
                        </div>
                        <group>
                            <group>
                                <field name="bid_user_id" readonly="1"/>
                                <field name="name" readonly="1"/>
                                <field name="customer_currency_id" readonly="1"/>
                            </group>
                            <group>
                                <field name="customer_credit_amount" readonly="1"/>
                                <field name="locked_customer_credit_amount" readonly="1"/>
                                <field name="available_customer_credit_amount" readonly="1"/>
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>
        <record model="ir.ui.view" id="advance_customer_credit_filter">
            <field name="name">Customer advance received</field>
            <field name="model">res.partner</field>
            <field name="arch" type="xml">
                <search>
                    <field name="name" string="Customer" filter_domain="['|',('bid_user_id','ilike',self),('name','ilike',self)]"/>
                    <filter
                        name="locked_amount_not_zero"
                        domain="[('locked_customer_credit_amount','!=', 0)]"
                        string="Has Locked Amount"
                    />
                    <filter
                        name="balance_not_zero"
                        domain="[('available_customer_credit_amount','!=', 0)]"
                        string="Has Balance"
                    />
                    <searchpanel>
                        <field name="customer_currency_id" icon="fa-tag" string="Currency" enable_counters="1"/>
                    </searchpanel>
                </search>
            </field>
        </record>
        <!-- Action to Open Partner Form for advance payment-->
        <record id="action_open_res_partner_form_for_customer_credit" model="ir.actions.act_window">
            <field name="name">Customer Credit Report</field>
            <field name="res_model">res.partner</field>
            <field name="view_mode">tree,form</field>
            <field name="view_ids" eval="[(5, 0, 0), (0, 0, {'view_mode': 'tree', 'view_id': ref('view_res_partner_tree_customer_credit')}), (0, 0, {'view_mode': 'form', 'view_id': ref('view_res_partner_customer_credit_form')})]"/>
            <field name="search_view_id" ref="advance_customer_credit_filter"/>
            <field name="domain">[('customer_credit_amount', '!=', 0)]</field>
        </record>
    </data>
</odoo>