<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Customer Settlement Tree View -->
        <record id="customer_settlement_tree_view" model="ir.ui.view">
            <field name="name">customer.settlement.tree</field>
            <field name="model">customer.settlement</field>
            <field name="arch" type="xml">
                <tree delete="0" string="Customer Settlement">
                    <header>
                        <button name="action_print" string="Print" type="object" class="btn-secondary"/>
                    </header>
                    <field name="name"/>
                    <field name="payer_id"/>
                    <field name="payee_ids" widget="many2many_tags" options="{'no_create': True, 'no_create_edit': True}"/>
                    <field name="customer_currency_id"/>
                    <field name="gjp_sale_order_summary_ids" widget="many2many_tags" options="{'no_create': True, 'no_create_edit': True}"/>
                    <field name="statement_amount"/>
                    <field name="total_allocate_amount"/>
                    <field name="paid_amount"/>
                    <field name="balance_amount"/>
                    <field name="received_fee" optional="hide"/>
                    <field name="total_amount" optional="hide"/>
                    <field name="received_payment_type"/>
                    <field name="settlement_type"/>
                    <field name="lines_received_type" optional="hide"/>
                    <field name="create_date"/>
                    <field name="paid_date" optional="hide"/>
                    <field name="state" decoration-info="state=='draft'" decoration-danger="state == 'cancel'" decoration-success="state == 'paid'" decoration-primary="state == 'partial_paid'" widget="badge"/>
                </tree>
            </field>
        </record>

        <!-- Customer Settlement Form View -->
        <record id="customer_settlement_form_view" model="ir.ui.view">
            <field name="name">customer.settlement.form</field>
            <field name="model">customer.settlement</field>
            <field name="arch" type="xml">
                <form delete="0" duplicate="0" string="Customer Settlement">
                    <header>
                        <button name="action_confirm" confirm="Customer advance balance will be changed, are you sure?" string="Confirm" type="object" states="draft" class="btn-primary"/>
                        <button name="action_send_settlement_sign_email" string="Send Email" type="object" class="btn-primary" confirm="Confirm send the settlement to the Payer?" attrs="{'invisible': [('state', '!=', 'confirm')]}" />
                        <button name="action_audited"  confirm="Are you sure to audit?" groups='galaxy_account_bank_statement_r2.group_advance_payment_account' string="Audit" type="object" states="confirm"/>
                        <button name="action_print" states="confirm,audited,partial_paid,paid" string="Print" type="object" class="btn-secondary"/>
                        <button name="action_cancel" groups='galaxy_account_bank_statement_r2.group_advance_payment_account' confirm="Are you sure to cancel?" string="Cancel" type="object" states="draft,confirm,audited,partial_paid,paid"/>
                         <button name="action_cancel" groups='galaxy_account_bank_statement_r2.group_advance_payment_sale' confirm="Are you sure to cancel?" string="Cancel" type="object" states="draft,confirm,audited"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,confirm,audited,partial_paid,paid"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <field name="gjp_sale_order_summary_counts" invisible="1"/>

                            <button name="preview_settlement"
                                type="object"
                                class="oe_stat_button"
                                icon="fa-globe icon">
                                <div class="o_field_widget o_stat_info">
                                    <span class="o_stat_text">Customer</span>
                                    <span class="o_stat_text">Preview</span>
                                </div>
                            </button>

                            <button name="action_view_summary_orders" type="object" class="oe_stat_button" attrs="{'invisible': [('gjp_sale_order_summary_counts', '=', 0)]}" icon="fa-truck">
                                <field name="gjp_sale_order_summary_counts" widget="statinfo" string="Sale Order Summary"/>
                            </button>
                            <button name="action_view_advance_payments" type="object" class="oe_stat_button" attrs="{'invisible': [('advance_payment_counts', '=', 0)]}" icon="fa-money">
                                <field name="advance_payment_counts" widget="statinfo" string="Advance Payments"/>
                            </button>
                            <button name="action_view_fund_transfers" type="object" class="oe_stat_button" attrs="{'invisible': [('customer_fund_transfer_counts', '=', 0)]}" icon="fa-exchange">
                                <field name="customer_fund_transfer_counts" widget="statinfo" string="Fund Transfers"/>
                            </button>
                        </div>
                        
                        <div class="oe_title">
                            <h1>
                                <field name="name" readonly="1"/>
                            </h1>
                        </div>
                        
                        <group>
                            <group>
                                <field name="payer_id" attrs="{'readonly': [('state', '!=', 'draft')]}"  options="{'no_create': True, 'no_create_edit': True}"/>
                                <field name="customer_currency_id" readonly="1"/>
                                <label for="received_payment_type" />
                                <div class="o_row">
                                    <field  name="received_payment_type" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                    <field name="advance_payment_balance_amount" force_save="1"
                                        attrs="{'invisible': [('received_payment_type', '!=', 'advance')]}" 
                                        nolabel="1"/>
                                    <field name="downpayment_balance_amount" force_save="1"
                                        attrs="{'invisible': [('received_payment_type', '!=', 'downpayment')]}" 
                                        nolabel="1"/>
                                    <field name="credit_balance_amount" force_save="1"
                                        attrs="{'invisible': [('received_payment_type', '!=', 'credit')]}" 
                                        nolabel="1"/>
                                </div>
                                <label for="settlement_type" />
                                <div class="o_row">
                                    <field name="settlement_type" widget="radio" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                    <field name="locked_customer_credit_amount" force_save="1"
                                        attrs="{'invisible': [('settlement_type', '!=', 'credit')]}" 
                                        nolabel="1"/>
                                </div>
                                <field name="make_by" readonly="1"/>
                                <field name="note" placeholder="Add notes here..."/>
                            </group>
                            <group>
                                <field attrs="{'readonly': [('state', '!=', 'draft')]}" readonly="0" name="statement_amount"/>
                                <label for="total_allocate_amount"/>
                                <div class="o_row">
                                    <field name="total_allocate_amount" readonly="1" style="font-size: 20pt; font-weight: bold;"/>
                                    <field name="total_allocate_chinese_amount" readonly="1" style="font-size: 20pt; font-weight: bold;"/>
                                </div>
                                <field readonly="1" name="paid_amount"/>
                                <field name="balance_amount" readonly="1"/>
                                <field name="allocate_balance_amount" force_save="1"/>
                            </group>
                        </group>
                        
                        <notebook>
                            <page string="Statement Lines" name="statement_lines">
                                <!--context="{'default_allocate_amount': allocate_balance_amount }"-->
                                <field context="{'default_received_type': settlement_type, 'default_customer_id':payer_id}" attrs="{'readonly': [('state', '!=', 'draft')]}" name="customer_statement_line_ids">
                                    <tree string="Statement Lines" editable="bottom">
                                        <field name="name" readonly="1"/>
                                        <field name="customer_id" options="{'no_create': True, 'no_create_edit': True}"/>
                                        <field name="customer_currency_id"/>
                                        <field name="received_type" force_save="1" attrs="{'readonly':[('parent.settlement_type', 'in', ('sopayment','credit'))]}"/>
                                        <field name="gjp_sale_order_summary_id" attrs="{'required': [('received_type', '=', 'sopayment')], 'column_invisible':[('parent.settlement_type', '!=', 'sopayment')]}"
                                            domain="[('customer_id', '=', customer_id), ('state', 'in', ('confirm', 'partial_paid'))]"
                                            options="{'no_create': True, 'no_create_edit': True}"/>
                                        <field name="sale_order_unsettlement_amount" attrs="{'column_invisible':[('parent.settlement_type', '!=', 'sopayment')]}"/>
                                        <field name="allocate_amount"/>
                                        <field name="note"/>
                                    </tree>
                                    <form string="Statement Line">
                                        <group>
                                            <group>
                                                <field name="name" readonly="1"/>
                                                <field name="customer_id" options="{'no_create': True, 'no_create_edit': True}"/>
                                                <field name="customer_currency_id"/>
                                                <field name="received_type"/>
                                            </group>
                                            <group>
                                                <field name="allocate_amount"/>
                                            </group>
                                        </group>
                                        <group string="Notes">
                                            <field name="note" nolabel="1"/>
                                        </group>
                                    </form>
                                </field>
                            </page>
                        </notebook>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids" widget="mail_followers"/>
                        <field name="activity_ids" widget="mail_activity"/>
                        <field name="message_ids" widget="mail_thread"/>
                    </div>
                </form>
            </field>
        </record>

        <!-- Customer Settlement Search View -->
        <record id="customer_settlement_search_view" model="ir.ui.view">
            <field name="name">customer.settlement.search</field>
            <field name="model">customer.settlement</field>
            <field name="arch" type="xml">
                <search string="Customer Settlement">
                    <field name="name" string="Number"/>
                    <field name="payer_id" string="Payer"/>
                    <field name="payee_ids" string="Payee" filter_domain="[('payee_ids.name', 'ilike', self)]"/>
                    <field name="create_date" string="Create Date"/>
                    <field name="received_payment_type"/>
                    <field name="customer_statement_line_ids" string="Customer" filter_domain="[('customer_statement_line_ids.customer_id', 'ilike', self)]"/>
                    <field name="gjp_sale_order_summary_ids" string="Sale Summary"/>
                    <separator/>
                    <filter name="received_payment_type_downpayment" string="Down Payment" domain="['|', ('received_payment_type','=','downpayment'),('customer_statement_line_ids.received_type','=','downpayment')]"/>
                    <separator/>
                    <filter name="filter_today" string="Today"  domain="[('create_date','&gt;=', datetime.datetime.combine(context_today(), datetime.time(0,0,0))),
                       ('create_date','&lt;=', datetime.datetime.combine(context_today(), datetime.time(23,59,59)))]"/>
                    <filter name="start_date_filter" string="This Week" domain="[
                        ('create_date', '>=', (datetime.datetime.combine(context_today() + relativedelta(weeks=-1,days=1,weekday=0), datetime.time(0,0,0)).to_utc()).strftime('%Y-%m-%d %H:%M:%S')),
                        ('create_date', '&lt;', (datetime.datetime.combine(context_today() + relativedelta(days=1,weekday=0), datetime.time(0,0,0)).to_utc()).strftime('%Y-%m-%d %H:%M:%S'))]"/>
                    <filter name="this_month" string="This Month" domain="[
                        ('create_date', '>=', (datetime.datetime.combine(context_today().replace(day=1), datetime.time(0,0,0)).to_utc()).strftime('%Y-%m-%d %H:%M:%S')),
                        ('create_date', '&lt;', (datetime.datetime.combine((context_today().replace(day=1) + relativedelta(months=1)), datetime.time(0,0,0)).to_utc()).strftime('%Y-%m-%d %H:%M:%S'))
                    ]"/>
                    <filter name="create_date" string="Create Date" date="create_date"/>
                    <separator/>
                    <filter name="filter_draft" string="Draft" domain="[('state', '=', 'draft')]"/>
                    <filter name="filter_confirm" string="Confirm" domain="[('state', '=', 'confirm')]"/>
                    <filter name="filter_partial_paid" string="Partial Paid" domain="[('state', '=', 'partial_paid')]"/>
                    <filter name="filter_paid" string="Paid" domain="[('state', '=', 'paid')]"/>
                    <filter name="filter_cancel" string="Cancel" domain="[('state', '=', 'cancel')]"/>
                    <filter name="not_cancel" string="Not Cancel" domain="[('state', '!=', 'cancel')]"/>
                    
                    <separator/>
                    <filter name="filter_bank" string="Bank" domain="[('received_payment_type', '=', 'bank')]"/>
                    <filter name="filter_cash" string="Cash" domain="[('received_payment_type', '=', 'cash')]"/>
                    <filter name="filter_advance_payment" string="Advance Payment" domain="[('received_payment_type', '=', 'advance')]"/>
                    <filter name="filter_exchange" string="Exchange" domain="[('received_payment_type', '=', 'exchange')]"/>
                    
                    <group expand="0" string="Group By">
                        <filter name="group_payer" string="Payer" domain="[]" context="{'group_by': 'payer_id'}"/>
                        <filter name="group_state" string="State" domain="[]" context="{'group_by': 'state'}"/>
                        <filter name="group_payment_type" string="Payment Type" domain="[]" context="{'group_by': 'received_payment_type'}"/>
                        <filter name="group_currency" string="Currency" domain="[]" context="{'group_by': 'customer_currency_id'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- Customer Settlement Action -->
        <record id="action_customer_settlement" model="ir.actions.act_window">
            <field name="name">Customer Settlement</field>
            <field name="res_model">customer.settlement</field>
            <field name="view_mode">tree,form</field>
            <field name="search_view_id" ref="customer_settlement_search_view"/>
            <field name="context">{'search_default_not_cancel':1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first Customer Settlement!
                </p>
                <p>
                    Customer settlements help you manage payments and allocations from customers.
                </p>
            </field>
        </record>

        <!-- Customer Settlement Lines Tree View (standalone) -->
        <record id="customer_settlement_lines_tree_view" model="ir.ui.view">
            <field name="name">customer.settlement.lines.tree</field>
            <field name="model">customer.settlement.lines</field>
            <field name="arch" type="xml">
                <tree string="Customer Settlement Lines">
                    <field name="name"/>
                    <field name="customer_settlement_id"/>
                    <field name="customer_id"/>
                    <field name="customer_currency_id"/>
                    <field name="allocate_amount"/>
                    <field name="received_type"/>
                </tree>
            </field>
        </record>

        <!-- Customer Settlement Lines Form View (standalone) -->
        <record id="customer_settlement_lines_form_view" model="ir.ui.view">
            <field name="name">customer.settlement.lines.form</field>
            <field name="model">customer.settlement.lines</field>
            <field name="arch" type="xml">
                <form string="Customer Settlement Line">
                    <sheet>
                        <group>
                            <group>
                                <field name="name" readonly="1"/>
                                <field name="customer_settlement_id" readonly="1"/>
                                <field name="customer_id" options="{'no_create': True, 'no_create_edit': True}"/>
                                <field name="customer_currency_id"/>
                            </group>
                            <group>
                                <field name="gjp_sale_order_summary_id" options="{'no_create': True, 'no_create_edit': True}"/>
                                <field name="allocate_amount"/>
                                <field name="received_type"/>
                            </group>
                        </group>
                        <group string="Notes">
                            <field name="note" nolabel="1"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Customer Settlement Lines Action -->
        <record id="action_customer_settlement_lines" model="ir.actions.act_window">
            <field name="name">Customer Settlement Lines</field>
            <field name="res_model">customer.settlement.lines</field>
            <field name="view_mode">tree,form</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No settlement lines found!
                </p>
            </field>
        </record>

    </data>
</odoo>
