<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Form View for Line Model -->
    <record id="view_gjp_sale_order_summary_line_form" model="ir.ui.view">
        <field name="name">gjp.sale.order.summary.line.form</field>
        <field name="model">gjp.sale.order.summary.line</field>
        <field name="arch" type="xml">
            <form string="GJP Sale Order Summary Line">
                <sheet>
                    <group>
                        <group>
                            <field name="gjp_order_sequence"/>
                            <field name="gjp_sale_order_id"/>
                        </group>
                        <group>
                            <field name="amount"/>
                            <field name="currency_id"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Details">
                            <field name="detail_line_ids" readonly="1" force_save="1">
                                <tree editable="bottom">
                                    <field name="description"/>
                                    <field name="price_unit"/>
                                    <field name="quantity"/>
                                    <field name="amount"/>
                                </tree>
                                <form>
                                    <group>
                                        <field name="description"/>
                                        <field name="price_unit"/>
                                        <field name="quantity"/>
                                        <field name="amount"/>
                                    </group>
                                </form>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Tree View for Line Model -->
    <record id="view_gjp_sale_order_summary_line_tree" model="ir.ui.view">
        <field name="name">gjp.sale.order.summary.line.tree</field>
        <field name="model">gjp.sale.order.summary.line</field>
        <field name="arch" type="xml">
            <tree string="GJP Sale Order Summary Lines">
                <field name="gjp_sale_order_id"/>
                <field name="amount"/>
                <field name="sale_person"/>
                <field name="order_summary"/>
                <field name="currency_id"/>
            </tree>
        </field>
    </record>

</odoo>
