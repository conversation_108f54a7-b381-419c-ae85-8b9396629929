<odoo>
    <data>
        <record id="extent_customer_advance_received_form" model="ir.ui.view">
        <field name="name">extent_customer_advance_received_form</field>
        <field name="model">customer.advance.received</field>
        <field name="inherit_id" ref="galaxy_lot_r2.customer_advance_received_form"/>
        <field name="arch" type="xml">
            <xpath expr="//page[1]" position="after">
                <page name="bank_statement_line_ids" attrs="{'readonly': [('state', '!=', 'draft')]}" string="Bank Statement Lines">
                    <field  name="bank_statement_line_ids" readonly="1">
                        <tree create="0" delete="0">
                            <field name="transaction_date"/>
                            <field name="currency_name" invisible="1"/>
                            <field name="currency_id"/>
                            <field name="deposit_amount"/>
                            <field name="withdrawl_amount" decoration-danger="withdrawl_amount &gt; 0"/>
                            <field name="bank_service_amount" decoration-danger="bank_service_amount &gt; 0"/>
                            <field name="net_withdrawl_amount" decoration-danger="net_withdrawl_amount &gt; 0"/>
                            <field name="net_deposit_amount"/>
                            <field name="remittance_payer"/>
                            <field name="partner_id"/>
                            <field name="remittance_bank"/>
                            <field name="account_id"/>
                            <field name="transaction_description"/>
                            <field name="transaction_id" optional="hide"/>
                            <field name="index" optional="hide"/>
                            <field name="create_date" optional="hide"/>
                            <field name="responsible_person_id"/>
                            <field name="bank_service_statement_id" optional="hide"/>
                            <field name="state" decoration-danger="state == 'to_do'" decoration-success="state == 'done'" widget="badge"/>
                            <field name="note"/>
                        </tree>
                    </field>
                </page>
            </xpath>
            <xpath expr="//field[@name='deposit_amount']" position="after">
                <field name="total_advance_payment_amount" readonly="1"/>
            </xpath>
<!--            <xpath expr="//field[@name='sale_order']" position="replace">-->
<!--                <field name="gjp_order_summary_id" options="{'no_create': True, 'no_create_edit': True, 'no_open': True}" -->
<!--                                            domain="[('customer_id', '=', parent.customer_id), ('state', 'in', ('confirm', 'partial_paid'))]" -->
<!--                                            attrs="{'required':[('received_type', 'in', ('sopayment'))]}"/>-->
<!--            </xpath>-->
        </field>
    </record>
    </data>
</odoo>
