<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Add menu entry -->
    <template id="portal_my_home_menu_settlement" name="Portal layout : settlement menu entries" inherit_id="portal.portal_breadcrumbs" priority="20">
        <xpath expr="//ol[hasclass('o_portal_submenu')]" position="inside">
            <li t-if="page_name == 'settlement'" t-attf-class="breadcrumb-item #{'active ' if not settlement else ''}">
                <a t-if="settlement" t-attf-href="/my/settlements?{{ keep_query() }}">Settlements</a>
                <t t-else="">Settlements</t>
            </li>
            <li t-if="settlement" class="breadcrumb-item active">
                <span>Settlement</span>
                <t t-esc="settlement.name"/>
            </li>
        </xpath>
    </template>

    <!-- Add menu item to home page -->
    <template id="portal_my_home_settlement" name="Show Settlements" customize_show="True" inherit_id="portal.portal_my_home" priority="20">
        <xpath expr="//div[hasclass('o_portal_docs')]" position="inside">
            <t t-call="portal.portal_docs_entry">
                <t t-set="title">Settlements</t>
                <t t-set="url" t-value="'/my/settlements'"/>
                <t t-set="placeholder_count" t-value="'settlement_count'"/>
            </t>
        </xpath>
    </template>

    <!-- Settlement list page -->
    <template id="portal_my_settlements" name="My Settlements">
        <t t-call="portal.portal_layout">
            <t t-set="breadcrumbs_searchbar" t-value="True"/>

            <t t-call="portal.portal_searchbar">
                <t t-set="title">Settlements</t>
            </t>
            <t t-if="not settlements">
                <p>There are currently no settlements for your account.</p>
            </t>
            <t t-if="settlements" t-call="portal.portal_table">
                <thead>
                    <tr class="active">
                        <th>Settlement #</th>
                        <th class="text-right">Create Date</th>
                        <th class="text-center">Status</th>
                        <th class="text-right">Amount</th>
                    </tr>
                </thead>
                <t t-foreach="settlements" t-as="settlement">
                    <tr>
                        <td><a t-att-href="settlement.get_portal_url()"><t t-esc="settlement.name"/></a></td>
                        <td class="text-right"><span t-field="settlement.create_date"/></td>
                        <td class="text-center">
                            <span t-if="settlement.state == 'confirm'" class="badge badge-pill badge-info">
                                <i class="fa fa-fw fa-check"/> Confirmed
                            </span>
                            <span t-if="settlement.state == 'audited'" class="badge badge-pill badge-warning">
                                <i class="fa fa-fw fa-check"/> Audited
                            </span>
                            <span t-if="settlement.state == 'partial_paid'" class="badge badge-pill badge-primary">
                                <i class="fa fa-fw fa-check"/> Partial Paid
                            </span>
                            <span t-if="settlement.state == 'paid'" class="badge badge-pill badge-success">
                                <i class="fa fa-fw fa-check"/> Paid
                            </span>
                        </td>
                        <td class="text-right">
                            <span t-field="settlement.statement_amount"/>
                        </td>
                    </tr>
                </t>
            </t>
        </t>
    </template>

    <!-- Settlement detail page -->
    <template id="portal_settlement_page" name="Settlement Portal Template" inherit_id="portal.portal_sidebar" primary="True">
        <xpath expr="//div[hasclass('o_portal_sidebar')]" position="inside">
            <div class="row mt16 o_portal_sale_sidebar">
                <!-- Page content -->
                <div id="settlement_content" class="col-12 col-lg justify-content-end">
                    <!-- modal relative to the actions sign -->
                    <div role="dialog" class="modal fade" id="modalaccept">
                        <div class="modal-dialog" t-if="settlement.has_to_be_signed()">
                            <form id="accept" method="POST" t-att-data-settlement-id="settlement.id" t-att-data-token="settlement.access_token" class="js_accept_json modal-content js_website_submit_form">
                                <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                                <header class="modal-header">
                                    <h4 class="modal-title">Validate Settlement</h4>
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">×</button>
                                </header>
                                <main class="modal-body" id="sign-dialog">
                                    <!-- <p>
                                        <span>By signing this settlement, I agree to the following terms:</span>
                                        <ul>
                                            <li><span>Accepted on the behalf of:</span> <b t-field="settlement.payer_id.commercial_partner_id"/></li>
                                            <li><span>For an amount of:</span> <b data-id="total_amount" t-field="settlement.statement_amount"/></li>
                                        </ul>
                                    </p> -->
                                    <!-- 详细结算行信息 -->
                                    <div class="mt-3">
                                        <!-- <h5>Payment Details:</h5> -->
                                        <t t-foreach="settlement.customer_statement_line_ids" t-as="line">
                                            <div class="mb-2">
                                                <!-- 中文提示 -->
                                                <p class="mb-1">
                                                    我同意使用 <strong t-esc="settlement.get_payment_type_display_name()"/> 帮 <strong t-field="line.customer_id"/> 支付 <strong t-esc="line.get_received_type_display_name()"/> <strong t-field="line.allocate_amount" t-options='{"widget": "monetary", "display_currency": line.customer_currency_id}'/>。
                                                </p>
                                                <!-- 英文提示 -->
                                                <p class="mb-2 text-muted small">
                                                    I agree to use <strong t-esc="settlement.get_payment_type_display_name()"/> to help <strong t-field="line.customer_id"/> pay <strong t-esc="line.get_received_type_display_name()"/> <strong t-field="line.allocate_amount" t-options='{"widget": "monetary", "display_currency": line.customer_currency_id}'/>.
                                                </p>
                                            </div>
                                        </t>
                                        <!-- 现金付款手续费提示 -->
                                        <t t-if="settlement.received_payment_type == 'cash'">
                                            <div class="mb-2">
                                                <p class="mb-1">
                                                    现金付款手续费：<strong t-esc="'{:,.2f}'.format(settlement.get_cash_handling_fee())"/>
                                                </p>
                                                <p class="mb-1">
                                                    含手续费总额：<strong t-esc="'{:,.2f}'.format(settlement.get_total_amount_with_fee())"/>
                                                </p>
                                            </div>
                                        </t>
                                    </div>
                                    <t t-call="galaxy_account_bank_statement_r2.settlement_signature_form">
                                        <t t-set="call_url" t-value="settlement.get_portal_url(suffix='/accept')"/>
                                        <t t-set="default_name" t-value="settlement.payer_id.name"/>
                                    </t>
                                </main>
                            </form>
                        </div>
                    </div>

                    <!-- status messages -->
                    <div t-if="message == 'sign_ok'" class="alert alert-success alert-dismissable d-print-none" role="status">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">×</button>
                        <strong>Thank You!</strong><br/>
                        Your settlement has been signed.
                    </div>

                    <div t-if="settlement.state == 'cancel'" class="alert alert-danger alert-dismissable d-print-none" role="alert">
                        <button type="button" class="close" data-dismiss="alert" aria-label="close">×</button>
                        <strong>This settlement has been canceled.</strong>
                    </div>

                    <!-- main content -->
                    <div class="card" id="portal_settlement_content">
                        <div class="card-body">
                            <!-- 结算单头部信息 -->
                            <div id="introduction" class="pb-2 pt-3">
                                <h2 class="my-0">
                                    Settlement No(結算號): <em t-esc="settlement.name"/>
                                </h2>
                                <!-- 转账提示语 -->
                                <div class="mt-2">
                                    <div class="alert alert-info mb-0">
                                        如您为转账支付，请務必在轉帳備註欄填寫此編號！<br/>
                                        Please include this number in the transfer remarks. Thank you!
                                    </div>
                                </div>
                            </div>
                            <div id="informations" class="mb-4">
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>Payer(付款人)：</strong>
                                        <span t-esc="settlement.payer_id.name"/>(<span t-esc="settlement.payer_id.bid_user_id"/>)
                                    </div>
                                    <div class="col-md-6">
                                        <strong>Amount(金額)：</strong>
                                        <t t-if="settlement.received_payment_type == 'cash'">
                                            <span class="font-weight-bold" t-esc="'{:,.2f}'.format(settlement.get_total_amount_with_fee())"/>
                                        </t>
                                        <t t-else="">
                                            <span class="font-weight-bold" t-field="settlement.statement_amount"/>
                                        </t>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>Currency(幣種)：</strong>
                                        <span t-field="settlement.customer_currency_id"/>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>Payment Method(付款方式)：</strong>
                                        <span t-esc="settlement.get_payment_type_display_name()"/>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>Date(日期)：</strong>
                                        <span t-field="settlement.create_date"/>
                                    </div>
                                </div>
                            </div>
                            <!-- 结算明细表格 -->
                            <section id="details" class="mt-3 mb-4">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-sm">
                                        <thead class="bg-100">
                                            <tr>
                                                <th>Payee<br/>用款人</th>
                                                <th>Purpose<br/>用途</th>
                                                <t t-if="settlement.settlement_type == 'sopayment'">
                                                    <th>Summary Sales No.<br/>銷售匯總單號</th>
                                                </t>
                                                <th class="text-right">Amount<br/>金額</th>
                                                <th>Note<br/>備註</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <t t-foreach="settlement.customer_statement_line_ids" t-as="line">
                                                <tr>
                                                    <td><span t-esc="line.customer_id.name"/>(<span t-esc="line.customer_id.bid_user_id"/>)</td>
                                                    <td><span t-field="line.received_type"/></td>
                                                    <t t-if="settlement.settlement_type == 'sopayment'">
                                                        <td>
                                                            <a t-if="line.gjp_sale_order_summary_id"
                                                                t-att-href="'/my/settlements/%s/summary/%s?access_token=%s' % (settlement.id, line.gjp_sale_order_summary_id.id, settlement.access_token)"
                                                                target="_blank">
                                                                <span t-field="line.gjp_sale_order_summary_id"/>
                                                            </a>
                                                            <span t-if="not line.gjp_sale_order_summary_id"/>
                                                            <t t-if="line.gjp_sale_order_summary_id">
                                                                (總計<span t-esc="'{:,.2f}'.format(line.gjp_sale_order_summary_id.total_amount)"/>)
                                                            </t>
                                                        </td>
                                                    </t>
                                                    <td class="text-right"><span t-esc="'{:,.2f}'.format(line.allocate_amount)"/></td>
                                                    <td><span t-field="line.note"/></td>
                                                </tr>
                                            </t>
                                            <!-- 现金付款手续费行 -->
                                            <t t-if="settlement.received_payment_type == 'cash'">
                                                <tr>
                                                    <td t-attf-colspan="#{3 if settlement.settlement_type == 'sopayment' else 2}" class="text-right">
                                                        <strong>現金付款需增加手續費0.1%，四捨五入取整數</strong>
                                                    </td>
                                                    <td class="text-right">
                                                        <strong t-esc="'{:,.2f}'.format(settlement.get_cash_handling_fee())"/>
                                                    </td>
                                                    <td></td>
                                                </tr>
                                            </t>
                                        </tbody>
                                    </table>
                                </div>
                            </section>
                            <!-- 底部提示语 -->
                            <div class="mt-4 mb-2" t-if="settlement.has_to_be_signed()">
                                <!-- 中文提示 -->
                                <div>尊敬的客户，您正在办理以上业务，如同意操作，请点击以下按钮进行确认！</div>
                                <!-- 英文提示 -->
                                <div>Dear customer, you are currently processing the above business. If you agree please click the button to confirm!</div>

                                <!-- bottom actions -->
                                <div class="row justify-content-center text-center d-print-none pt-1 pb-4">
                                    <div class="col-sm-auto mt8">
                                        <a role="button" class="btn btn-primary" data-toggle="modal" data-target="#modalaccept" href="#"><i class="fa fa-check"/> Accept &amp; Sign</a>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-4 mb-2" t-if="settlement.signature">
                                <div>
                                    我們已收到您的確認，请及时安排付款。收到款项后，我们将有郵件通知您，感謝您的配合！<br/>
                                    Payment confirmation received. Please proceed with payment. We'll email you once received. Thank you.
                                </div>
                            </div>
                            <!-- 签名部分 -->
                            <section t-if="settlement.signature" id="signature" name="Signature">
                                <div class="row mt-4" name="signature">
                                    <div t-attf-class="#{'col-3' if report_type != 'html' else 'col-sm-7 col-md-4'} ml-auto text-center">
                                        <h5>Signature</h5>
                                        <img t-att-src="image_data_uri(settlement.signature)" style="max-height: 6rem; max-width: 100%;"/>
                                        <p t-field="settlement.signed_by"/>
                                        <p t-field="settlement.signed_on"/>
                                    </div>
                                </div>
                            </section>
                            
                            <!-- 银行信息部分 -->
                            <section id="bank_info" name="Bank Information">
                                <div class="row mt-4">
                                    <div class="col-12">
                                        <h5>Bank Account Information (銀行帳戶資料)</h5>
                                        <div class="mb-3">
                                            <p><strong>銀行戶名：</strong>GALAXY TELECOM (HONG KONG) LIMITED</p>
                                            <p><strong>公司名稱：</strong>銀河電訊(香港)有限公司</p>
                                            <p><strong>開戶銀行：</strong>大新銀行</p>
                                            <p><strong>銀行帳號（多幣種戶口）：</strong>040-774-012-45050-3</p>
                                            <p><strong>銀行編號：</strong>040</p>
                                            <p><strong>分行編號：</strong>774</p>
                                            <p><strong>Swift Code：</strong>DSBAHKHHXXX</p>
                                        </div>
                                    </div>
                                </div>
                            </section>
                        </div>
                    </div>

                    <!-- chatter -->
                    <!-- <div id="settlement_communication" class="mt-4">
                        <h2>History</h2>
                        <t t-call="portal.message_thread">
                            <t t-set="object" t-value="settlement"/>
                        </t>
                    </div> -->
                </div><!-- // #settlement_content -->
            </div>
        </xpath>
    </template>

    <!-- Settlement-specific signature form template -->
    <template id="settlement_signature_form" name="Settlement Signature Form">
        <div class="o_portal_settlement_signature_form"
            t-att-data-call-url="call_url"
            t-att-data-default-name="default_name"
            t-att-data-mode="mode"
            t-att-data-send-label="send_label"
            t-att-data-signature-ratio="signature_ratio"
            t-att-data-signature-type="signature_type"
            t-att-data-font-color="font_color"
            />
    </template>
</odoo>