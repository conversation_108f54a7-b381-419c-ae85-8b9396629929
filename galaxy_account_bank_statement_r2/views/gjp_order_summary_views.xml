<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Form View -->
    <record id="view_gjp_sale_order_summary_form" model="ir.ui.view">
        <field name="name">gjp.sale.order.summary.form</field>
        <field name="model">gjp.sale.order.summary</field>
        <field name="arch" type="xml">
            <form duplicate="0" delete="0" string="GJP Sale Order Summary" js_class="batch_add_gjp_sale_order_summary_form">
                <header>
                    <field name="state" widget="statusbar"/>
                    <button name="action_confirm" confirm="Are yous sure to confirm?" type="object" string="Confirm" class="oe_highlight" states="draft"/>
                    <button name="action_back_to_draft" type="object" string="Back to draft" class="btn btn-secondary" states="confirm"/>
                    <button name="action_send_email" type="object" string="Send Email" class="oe_highlight" states="confirm"/>
                    <button name="action_print_delivery" states="paid" type="object" string="Print Delivery" class="btn btn-secondary"/>
                    <button name="action_cancel" groups='galaxy_account_bank_statement_r2.group_advance_payment_sale' confirm="Are you sure to cancel?"  type="object" string="Cancel" class="oe_link" states="draft, confirm, partial_paid, paid" attrs="{'invisible': [('state', '=', 'cancel')]}"/>
                    <button name="action_sync_gjp_sale_orders" confirm="Are you sure to sync gjp sale order?" type="object" string="Sync GJP Sale Orders" class="oe_link" states="draft"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_advance_payments" type="object" class="oe_stat_button" attrs="{'invisible': [('advance_payment_counts', '=', 0)]}" icon="fa-credit-card">
                                <field name="advance_payment_counts" widget="statinfo" string="Advance Payments"/>
                        </button>
                        <button name="action_view_fund_transfers" type="object" class="oe_stat_button" attrs="{'invisible': [('customer_fund_transfer_counts', '=', 0)]}" icon="fa-exchange">
                                <field name="customer_fund_transfer_counts" widget="statinfo" string="Fund Transfers"/>
                        </button>
                        <button name="action_view_customer_settlements" type="object" class="oe_stat_button" attrs="{'invisible': [('customer_advance_settlement_counts', '=', 0)]}" icon="fa-money">
                                <field name="customer_advance_settlement_counts" widget="statinfo" string="Customer Settlements"/>
                        </button>
                        
                    </div>

                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="customer_id" attrs="{'readonly': [('state', '!=', 'draft')]}" options="{'no_create': True, 'no_create_edit': True, 'no_open': True}"  required="1"/>
                            <field name="glot" readonly="1"/>
                            <field name="gjp_customer_name" readonly="1"/>
                            <field name="customer_currency_id" options="{'no_create': True}"/>
                            <div class="o_row">
                                <label for="customer_security_code"/>
                                <field name="customer_security_code"  required="1"/>
                                <button name="set_delivery_verify_code" type="object" class="btn-link">
                                    <i title="Set Verify Code" role="img" aria-label="Set Verify Code" class="fa fa-lock"></i>
                                </button>
                            </div>
                            <field name="note"/>
                        </group>
                        <group>
                            <field name="total_quantity"/>
                            <field name="total_amount"/>
                            <field name="total_unsettlement_amount"/>
                            <field name="total_paid_amount"/>
                            <field name="balance_amount"/>
                        </group>
                    </group>
                    <notebook>
                        <page name="order_lines" string="Order Lines">
                            <field name="line_ids" attrs="{'readonly': [('state', '!=', 'draft')]}">
                                <tree editable="bottom" js_class="galaxy_batch_add_gjp_sale_orders">
                                    <control>
                                            <create name="single_add_customer_advance_payments_control"
                                                    attrs="{'invisible':[('parent.state','=','done')]}"
                                                    string="Add a line" context="{'add_gjp_sale_order_batch':0}"/>
                                            <create name="batch_add_customer_advance_payments_control"
                                                    attrs="{'invisible':[('parent.state','=','done')]}"
                                                    string="Add Lines" context="{'add_gjp_sale_order_batch':1}"/>
                                        </control>
                                    <field name="gjp_order_sequence"/>
                                    <field name="name" readonl="1"/>
                                    <field name="glot" readonly="1"/>
                                    <field name="customer_id"/>
                                    <field name="gjp_sale_order_id" options="{'no_create': True, 'no_create_edit': True, 'no_open': True}" domain="[('customer_id', '=', parent.customer_id), ('sale_person', 'ilike', 'Yan 阿颜'), ('gjp_order_sequence','=', gjp_order_sequence)]"/>
                                    <field name="quantity"/>
                                    <field name="amount"/>
                                    <field name="paid_amount" optional="hide"/>
                                    <field name="sale_person" optional="hide"/>
                                    <field name="order_summary" optional="hide"/>
                                    <field name="note"/>
                                </tree>
                                <form string="Lines">
                                    <sheet>
                                        <group>
                                            <group>
                                                <field name="name" readonl="1"/>
                                                <field name="customer_id" readonly="1"/>
                                                <field name="gjp_order_sequence"/>
                                                <field name="gjp_sale_order_id" options="{'no_create': True, 'no_create_edit': True, 'no_open': True}"/>
                                            </group>
                                            <group>
                                                <field name="quantity"/>
                                                <field name="amount"/>
                                                <field name="paid_amount" readonly="1"/>
                                                <field name="note"/>
                                            </group>
                                        </group>
                                        <notebook>
                                            <page string="Details">
                                                <field name="detail_line_ids">
                                                    <tree editable="bottom">
                                                        <field name="gjp_sale_order_line_id" invisible="1"/>
                                                        <field name="description"/>
                                                        <field name="quantity"/>
                                                        <field name="price_unit"/>
                                                        <field name="amount"/>
                                                        <field name="line_summary"/>
                                                        <field name="line_comment"/>
                                                    </tree>
                                                    <form>
                                                        <group>
                                                            <field name="gjp_sale_order_line_id" invisible="1"/>
                                                            <field name="description"/>
                                                            <field name="quantity"/>
                                                            <field name="price_unit"/>
                                                            <field name="amount"/>
                                                            <field name="line_summary"/>
                                                            <field name="line_comment"/>
                                                        </group>
                                                    </form>
                                                </field>
                                            </page>
                                        </notebook>
                                    </sheet>
                                </form>
                            </field>
                        </page>
                        <page name="settlement lines" string="Settlement Lines">
                            <field name="customer_advance_settlement_line_ids" readonly="1">
                                <tree>
                                    <field name="name"/>
                                    <field name="advance_payment_id"/>
                                    <field name="customer_fund_transfer_ids" widget="many2many_tags" options="{'no_create': True, 'no_create_edit': True, 'no_open': True}"/>
                                    <field name="received_payment_type"/>
                                    <field name="received_type" optional="hide"/>
                                    <field name="payer_id"/>
                                    <field name="allocate_amount"/>
                                    <field name="customer_settlement_state"/>
                                    <field name="note"/>
                                </tree>
                                <form string="Lines">
                                    <header>
                                        <field name="customer_settlement_state" widget="statusbar" statusbar_visible="unpaid,partial_paid,paid"/>
                                    </header>
                                    <sheet>
                                        <group>
                                            <group>
                                                <field name="name" readonl="1"/>
                                                <field name="advance_payment_id"/>
                                                <field name="received_type" readonly="1"/>
                                            </group>
                                            <group>
                                                <field name="allocate_amount"/>
                                                <field name="customer_fund_transfer_ids" widget="many2many_tags" options="{'no_create': True, 'no_create_edit': True, 'no_open': True}"/>
                                                <field name="note"/>
                                            </group>
                                        </group>
                                    </sheet>
                                </form>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Tree View -->
    <record id="view_gjp_sale_order_summary_tree" model="ir.ui.view">
        <field name="name">gjp.sale.order.summary.tree</field>
        <field name="model">gjp.sale.order.summary</field>
        <field name="arch" type="xml">
            <tree delete="0" string="GJP Sale Order Summary">
                <field name="name"/>
                <field name="customer_id"/>
                <field name="gjp_customer_name"/>
                <field name="customer_currency_id"/>
                <field name="total_quantity"/>
                <field name="total_amount"/>
                <field name="total_unsettlement_amount"/>
                <field name="total_paid_amount"/>
                <field name="balance_amount"/>
                <field name="state" decoration-info="state=='draft'" decoration-danger="state == 'cancel'" decoration-success="state == 'paid'" decoration-primary="state == 'partial_paid'" widget="badge"/>
                <field name="create_date"/>
                <field name="glot" optional="hide"/>
            </tree>
        </field>
    </record>

    <!-- Search View -->
    <record id="view_gjp_sale_order_summary_search" model="ir.ui.view">
        <field name="name">gjp.sale.order.summary.search</field>
        <field name="model">gjp.sale.order.summary</field>
        <field name="arch" type="xml">
            <search string="GJP Sale Order Summary">
                <field name="name" string="Order No." placeholder="Search by Order No."/>
                <field name="glot"/>
                <field name="customer_id"/>
                <field name="line_ids" string="GJP Order" filter_domain="[('line_ids.gjp_sale_order_id.gjp_order_no','ilike', self)]"/>
                <field name="gjp_customer_name"/>
                <separator/>
                <filter name="unpaid" string="Unpaid" domain="[('state', '=', 'unpaid')]"/>
                <filter name="partial_paid" string="Partial Paid" domain="[('state', '=', 'partial_paid')]"/>
                <filter name="paid" string="Paid" domain="[('state', '=', 'paid')]"/>
                <filter name="not_cancel" string="Not Cancel" domain="[('state', '!=', 'cancel')]"/>
                <group expand="0" string="Group By">
                    <filter name="group_by_customer" string="Customer" domain="[]" context="{'group_by': 'customer_id'}"/>
                    <filter name="group_by_state" string="Status" domain="[]" context="{'group_by': 'state'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action -->
    <record id="action_gjp_sale_order_summary" model="ir.actions.act_window">
        <field name="name">GJP Sale Order Summary</field>
        <field name="res_model">gjp.sale.order.summary</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'search_default_not_cancel':1}</field>
        <field name="search_view_id" ref="view_gjp_sale_order_summary_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first GJP sale order summary
            </p>
        </field>
    </record>
</odoo>
