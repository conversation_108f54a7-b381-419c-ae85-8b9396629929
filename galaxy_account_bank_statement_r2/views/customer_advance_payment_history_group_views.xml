<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Tree View For dashborad-->
    <record id="view_customer_advance_payment_history_group_tree" model="ir.ui.view">
        <field name="name">customer.advance.payment.history.group.tree</field>
        <field name="model">customer.advance.payment.history.group</field>
        <field name="arch" type="xml">
            <tree decoration-danger="business_type == 'reversal'" js_class="GalaxyPaymentHistoryListDashboardView" string="Customer Advance Payment History" default_order = 'id desc'  duplicate="0" edit="0" delete="0" create="0">
                <field name="transaction_date" optional="hide"/>
                <field name="document_number"/>
                <field name="related_sale_summary_order_ids" widget="many2many_tags" optional="hide"/>
                <field name="related_customer_id"/>
                <field name="document_type" optional="hide"/>
                <field name="business_type"/>
                <field name="funding_source" optional="hide"/>
                <field name="fund_type"/>
                <!--<field name="merged_bank_statement_ids" widget="many2many_tags"/>-->
                <!--<field name="customer_received_statement_id"/>-->
                <!--<field name="reconciliation_status"/>-->
                <field name="advance_amount" optional="hide"/>
                <field name="downpayment_amount" optional="hide"/>
                <field name="transaction_amount" optional="hide"/>
                <field name="advance_balance_amount" optional="hide"/>
                <field name="downpayment_balance_amount" optional="hide"/>
                <field name="operator" optional="hide"/>
                <field name="notes"/>
            </tree>
        </field>
    </record>

    <!-- Tree View For Normal-->
    <record id="view_customer_advance_payment_history_group_normal_tree" model="ir.ui.view">
        <field name="name">customer.advance.payment.history.group.normal.tree</field>
        <field name="model">customer.advance.payment.history.group</field>
        <field name="arch" type="xml">
            <tree decoration-danger="business_type == 'reversal'" string="Customer Advance Payment History" default_order = 'id desc'  duplicate="0" edit="0" delete="0" create="0">
                <field name="transaction_date"/>
                <field name="document_number"/>
                <field name="related_sale_summary_order_ids" widget="many2many_tags" optional="hide"/>
                <field name="related_customer_id"/>
                <field name="document_type"/>
                <field name="business_type"/>
                <field name="funding_source"/>
                <field name="fund_type"/>
                <!--<field name="merged_bank_statement_ids" widget="many2many_tags"/>-->
                <!--<field name="customer_received_statement_id"/>-->
                <!--<field name="reconciliation_status"/>-->
                <field name="advance_amount"/>
                <field name="downpayment_amount"/>
                <field name="transaction_amount" optional="hide"/>
                <field name="advance_balance_amount"/>
                <field name="downpayment_balance_amount"/>
                <field name="operator" optional="hide"/>
                <field name="notes"/>
            </tree>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_customer_advance_payment_history_group_form" model="ir.ui.view">
        <field name="name">customer.advance.payment.history.group.form</field>
        <field name="model">customer.advance.payment.history.group</field>
        <field name="arch" type="xml">
            <form edit="0" create="0" delete="0" duplicate="0" string="Customer Advance Payment History">
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_statement" type="object" class="oe_stat_button" attrs="{'invisible': [('statement_counts', '=', 0)]}" icon="fa-list-alt">
                            <field name="statement_counts" widget="statinfo" string="Bank Statements"/>
                        </button>
                    </div>
                    <group>
                        <group>
                            <field name="transaction_date"/>
                            <field name="document_number"/>
                            <field name="related_customer_id"/>
                            <field name="document_type"/>
                            <field name="business_type"/>
                            <field name="funding_source"/>
                            <field name="fund_type"/>
                            <!--<field name="merged_bank_statement_ids" widget="many2many_tags"/>-->
                        </group>
                        <group>
                            <!--<field name="customer_received_statement_id"/>
                            <field name="reconciliation_status"/>-->
                            <field name="advance_amount"/>
                            <field name="downpayment_amount"/>
                            <field name="transaction_amount"/>
                            <field name="advance_balance_amount"/>
                            <field name="downpayment_balance_amount"/>
                            <field name="operator"/>
                            <field name="notes"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Search View -->
    <record id="view_customer_advance_payment_history_group_search" model="ir.ui.view">
        <field name="name">customer.advance.payment.history.group.search</field>
        <field name="model">customer.advance.payment.history.group</field>
        <field name="arch" type="xml">
            <search string="Customer Advance Payment History">
                <field name="transaction_date"/>
                <field name="customer_id"/>
                <field name="customer_advance_received_line_id" string="Customer Advance Payment Line"/>
                <field name="business_type"/>
                <field name="funding_source"/>
                <field name="fund_type"/>
                <field name="merged_bank_statement_ids" string="Merged Bank Statement"/>
                <field name="customer_received_statement_id" string="Customer Received Statement"/>
                <field name="operator"/>
                <filter string="Created Today" name="created_today" domain="[('create_date','&gt;', (context_today() - datetime.timedelta(days=1)).strftime('%%Y-%%m-%%d'))]"/>
                <separator/>
                <filter string="Unreconciled" name="unreconciled" domain="[('reconciliation_status', '=', 'unreconciled')]"/>
                <filter string="Reconciled" name="reconciled" domain="[('reconciliation_status', '=', 'reconciled')]"/>
                <filter string="Partially Reconciled" name="partially_reconciled" domain="[('reconciliation_status', '=', 'partially_reconciled')]"/>
                <separator/>
                <filter string="Receipt" name="receipt" domain="[('business_type', '=', 'receipt')]"/>
                <filter string="Reconciliation" name="reconciliation" domain="[('business_type', '=', 'reconciliation')]"/>
                <filter string="Reversal" name="reversal" domain="[('business_type', '=', 'reversal')]"/>
                <separator/>
                <filter string="Advance Payment" name="advance" domain="[('fund_type', '=', 'advance')]"/>
                <filter string="Down Payment" name="downpayment" domain="[('fund_type', '=', 'downpayment')]"/>
                <filter string="SO Payment" name="sopayment" domain="[('fund_type', '=', 'sopayment')]"/>
                <group expand="0" string="Group By">
                    <filter string="Transaction Date" name="group_by_date" domain="[]" context="{'group_by': 'transaction_date'}"/>
                    <filter string="Business Type" name="group_by_business_type" domain="[]" context="{'group_by': 'business_type'}"/>
                    <filter string="Customer" name="group_by_customer" domain="[]" context="{'group_by': 'customer_id'}"/>
                    <filter string="Reconciliation Status" name="group_by_status" domain="[]" context="{'group_by': 'reconciliation_status'}"/>
                    <filter string="Operator" name="group_by_operator" domain="[]" context="{'group_by': 'operator'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action-->
    <record id="action_customer_advance_payment_history_group" model="ir.actions.act_window">
        <field name="name">Customer Advance Payment Detail</field>
        <field name="res_model">customer.advance.payment.history.group</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_customer_advance_payment_history_group_search"/>
        <field name="context">{"tree_view_ref": "galaxy_account_bank_statement_r2.view_customer_advance_payment_history_group_tree"}</field>
    </record>

    <!-- Action-->
    <record id="action_customer_advance_payment_normal_history_group" model="ir.actions.act_window">
        <field name="name">Customer Advance Payment Detail</field>
        <field name="res_model">customer.advance.payment.history.group</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_customer_advance_payment_history_group_search"/>
        <field name="context">{'tree_view_ref': 'galaxy_account_bank_statement_r2.view_customer_advance_payment_history_group_normal_tree',
        'search_default_created_today': 1}</field>
    </record>
</odoo>
