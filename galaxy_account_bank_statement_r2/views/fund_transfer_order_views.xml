<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="fund_transfer_order_search" model="ir.ui.view">
        <field name="name">fund.transfer.order.search</field>
        <field name="model">fund.transfer.order</field>
        <field name="arch" type="xml">
            <search string="Fund Transfer Order Search">
                <field name="name"/>
                <field name="transfer_date"/>
                <field name="payer_id"/>
                <field name="payee_id"/>
                <field name="fund_source"/>
                <field name="fund_to"/>
                <field name="state"/>
                <field name="transfer_type"/>
                <filter string="Draft" name="filter_draft" domain="[('state','=','draft')]"/>
                <filter string="Confirmed" name="filter_confirm" domain="[('state','=','confirm')]"/>
                <filter string="Done" name="filter_done" domain="[('state','=','done')]"/>
            </search>
        </field>
    </record>
    <record id="fund_transfer_order_action" model="ir.actions.act_window">
        <field name="name">Fund Transfer Order</field>
        <field name="res_model">fund.transfer.order</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first fund transfer order
            </p>
            <p>
                Add fund transfer orders to keep track of all the fund transfer orders.
            </p>
        </field>
    </record>

    <record id="fund_transfer_order_tree" model="ir.ui.view">
        <field name="name">fund.transfer.order.tree</field>
        <field name="model">fund.transfer.order</field>
        <field name="arch" type="xml">
            <tree string="Fund Transfer Order" delete='1' create='0'>
                <field name="name"/>
                <field name="transfer_date"/>
                <field name="payer_id"/>
                <field name="currency_id"/>
                <field name="payee_id"/>
                <field name="fund_source"/>
                <field name="amount"/>
                <field name="applied_amount"/>
                <field name="balance_amount"/>
                <field name="fund_to"/>
                <field name="customer_settlement_id"/>
                <field name="state"/>
                <field name="transfer_type"/>
                <field name="note"/>
                <field name="create_uid" optional="hide"/>
                <field name="create_date" optional="hide"/>
            </tree>
        </field>
    </record>

    <record id="fund_transfer_order_form" model="ir.ui.view">
        <field name="name">fund.transfer.order.form</field>
        <field name="model">fund.transfer.order</field>
        <field name="arch" type="xml">
            <form duplicate="0" string="Fund Transfer Order" delete='0' create='0' edit='1'>
                <header>
                    <button string="Confirm" name="action_confirm" type="object"
                            attrs="{'invisible': [('state', '!=', 'draft')]}"
                            class="oe_highlight"/>
                    <button string="Done" name="action_done" type="object"
                            attrs="{'invisible': [('state', '!=', 'confirm')]}"
                            class="oe_highlight"/>
                    <!-- <button  confirm="Are you sure to cancel?" string="Cancel" name="action_cancel" type="object"
                            attrs="{'invisible': [('state', '=', 'cancel')]}"
                            class="btn btn_secondary"/> -->
                    <field name="state" widget='statusbar'/>
                </header>
                <sheet>
                    <!-- <div class="oe_button_box" name="button_box">
                        <button name="action_to_account_move"
                                string="Account Move" type="object"
                                class="oe_stat_button"
                                icon="fa-bars"/>
                        <button name="action_to_customer_receipt"
                                icon="fa-bell"
                                string="Settlement Statement"
                                type="object"
                                class="oe_stat_button"/>
                    </div> -->
                    <div class='oe_title'>
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="payer_id" readonly="1"/>
                            <field name="payee_id" readonly="1"/>
                            <field name="fund_source" readonly="1"/>
                            <field name="transfer_type" readonly="1"/>
                            <field name="applied_fund_transfer_order_ids" widget="many2many_tags" options="{'no_create': True}" readonly="1"/>                           
                            <field name="applied_amount" readonly="1"/>
                            <field name="applied_fund_transfer_amount_details" groups="base.group_system" readonly="1"/>
                            <field name="note"/>
                        </group>
                        <group>
                            <field name="currency_id" readonly="1"/>
                            <field name="amount" readonly="1"/>
                            <field name="fund_to" readonly="1"/>
                            <field name="transfer_date" readonly="1"/>
                            <field name="applied_fund_transfer_amount" readonly="1"/>
                            <field name="balance_amount" readonly="1"/>
                            <field name="customer_settlement_id" readonly="1"/>
                        </group>
                    </group>
                </sheet>
                <div class="oe_chatter">
                        <field name="message_follower_ids" groups="base.group_user"/>
                        <field name="activity_ids"/>
                        <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>
</odoo>
