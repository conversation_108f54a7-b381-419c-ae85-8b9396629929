<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="res_config_settings_galaxy_account_bank_statement_r2" model="ir.ui.view">
        <field name="name">res.config.settings.view.account.action_account_config</field>
        <field name="model">res.config.settings</field>
        <field name="priority" eval="50"/>
        <field name="inherit_id" ref="base.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[hasclass('app_settings_block') and @data-key='account']" position="inside">
                <h2>Customer Advance Report</h2>
                <div class="row mt16 o_settings_container" name="   ">
                    <div class="col-12 col-lg-6 o_setting_box" id="advance_payment_report_send_time">
                        <div class="o_setting_left_pane"/>
                        <div class="o_setting_right_pane">
                            <span class="o_form_label">Send Time</span>
                            <div class="content-group">
                                <div class="row mt16">
                                    <label for="advance_payment_report_send_time_hour" string="Hour" class="col-lg-3 o_light_label"/>
                                    <field name="advance_payment_report_send_time_hour" class="oe_inline"/>
                                </div>
                                <div class="row mt16">
                                    <label for="advance_payment_report_send_time_minute" string="Minute" class="col-lg-3 o_light_label"/>
                                    <field name="advance_payment_report_send_time_minute" class="oe_inline"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </xpath>
        </field>
    </record>  
</odoo>
