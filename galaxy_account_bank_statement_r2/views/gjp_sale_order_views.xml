<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Form View -->
    <record id="view_gjp_sale_order_form" model="ir.ui.view">
        <field name="name">gjp.sale.order.form</field>
        <field name="model">gjp.sale.order</field>
        <field name="arch" type="xml">
            <form create="0" delete="0" edit="1" string="GJP Sale Order">
                <header>
                    <button name="action_sync_gjp_order"
                        string="Sync GJP Order"
                        type="object"
                        class="btn-primary"
                    />
                    <button name="action_print_invoice"
                        string="Print Invoice"
                        type="object"
                        class="btn-secondary"
                    />
                    <field name="state" widget="statusbar" statusbar_visible="draft,not_paid,partial_paid,paid"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="gjp_order_no" attrs="{'readonly':[('state', '!=', 'not_paid')]}" placeholder="GJP Order Number"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="customer_id" attrs="{'readonly':[('state', '!=', 'not_paid')]}" options="{'no_create': True, 'no_create_edit': True}"/>
                            <field name="gjp_order_sequence" readonly="1"/>
                            <field name="audit_level" readonly="1"/>
                            <field name="glot" readonly="1"/>
                            <field name="dept" readonly="1"/>
                            <field name="notes"/>
                        </group>
                        <group>
                            <field name="user_code" readonly="1"/>
                            <field name="sale_person" readonly="1"/>
                            <field name="odoo_so_number" readonly="1"/>
                            <field name="order_summary" readonly="1"/>
                            <field name="customer_security_code" readonly="1"/>
                            <!-- <label for="customer_security_code" string="Delivery Verify Code"/> -->
                            <!-- <div class="o_row">
                                <field name="customer_security_code"/>
                                <button name="set_delivery_verify_code" type="object" class="btn-link">
                                    <i title="Set Verify Code" role="img" aria-label="Set Verify Code" class="fa fa-lock"></i>
                                </button>
                            <div> -->
                            <field name="invoice_res_partner_id" options="{'no_create': True, 'no_create_edit': True}"/>
                        </group>
                    </group>
                    <group>
                        <group>
                            <field name="quantity" readonly="1"/>
                        </group>
                        <group>
                            <field name="amount" readonly="1"/>
                        </group>
                    </group>
                    <notebook>
                        <page name="detail_lines" string="Detail Lines">
                            <field name="detail_line_ids" readonly="1">
                                <tree editable="bottom">
                                    <field name="product_code"/>
                                    <field name="description"/>
                                    <field name="quantity"/>
                                    <field name="price_unit"/>
                                    <field name="amount"/>
                                    <field name="line_summary"/>
                                    <field name="line_comment"/>
                                </tree>
                                <form string="Detail Line">
                                    <group>
                                        <group>
                                            <field name="product_code"/>
                                            <field name="description"/>
                                            <field name="quantity"/>
                                        </group>
                                        <group>
                                            <field name="price_unit"/>
                                            <field name="amount"/>
                                            <field name="line_summary"/>
                                            <field name="line_comment"/>
                                        </group>
                                    </group>
                                </form>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Tree View -->
    <record id="view_gjp_sale_order_tree" model="ir.ui.view">
        <field name="name">gjp.sale.order.tree</field>
        <field name="model">gjp.sale.order</field>
        <field name="arch" type="xml">
            <tree create="0" delete="0" edit="0" string="GJP Sale Orders">
                <header>
                    <button name="batch_action_print_invoice"
                        string="Print Invoice"
                        type="object"
                        class="btn btn-primary"
                    />
                </header>
                <field name="gjp_order_no"/>
                <field name="glot"/>
                <field name="sale_summary_order_id"/>
                <field name="audit_level"/>
                <field name="gjp_order_sequence"/>
                <field name="customer_id"/>
                <field name="quantity"/>
                <field name="amount"/>
                <!--<field name="paid_amount"/>-->
                <field name="sale_person"/>
                <field name="odoo_so_number"/>
                <field name="paid_date" optional="hide"/>
                <field name="create_date"/>
                <field name="state"/>
            </tree>
        </field>
    </record>

    <record id="view_gjp_sale_order_tree_for_select" model="ir.ui.view">
        <field name="name">gjp.sale.order.tree</field>
        <field name="model">gjp.sale.order</field>
        <field name="arch" type="xml">
            <tree default_order="gjp_order_no desc"  create="0" delete="0" edit="0" string="GJP Sale Orders">
                <header>
                        <button name="action_select_gjp_sale_orders"
                            string="Select"
                            type="object"
                            class="btn-primary"
                        />
                    </header>
                <field name="gjp_order_no"/>
                <field name="glot"/>
                <field name="audit_level"/>
                <field name="gjp_order_sequence"/>
                <field name="customer_id"/>
                <field name="quantity"/>
                <field name="amount"/>
                <field name="paid_amount"/>
                <field name="sale_person"/>
                <field name="odoo_so_number"/>
                <field name="state"/>
            </tree>
        </field>
    </record>

    <!-- Search View -->
    <record id="view_gjp_sale_order_search" model="ir.ui.view">
        <field name="name">gjp.sale.order.search</field>
        <field name="model">gjp.sale.order</field>
        <field name="arch" type="xml">
            <search string="GJP Sale Orders">
                <field name="gjp_order_no"/>
                <field name="sale_summary_order_id"/>
                <field name="glot"/>
                <field name="gjp_order_sequence"/>
                <field name="customer_id"/>
                <field name="sale_person"/>
                <field name="odoo_so_number"/>
                <filter string="Not Paid" name="not_paid" domain="[('state', '=', 'not_paid')]"/>
                <filter string="Partial Paid" name="paid" domain="[('state', '=', 'partial_paid')]"/>
                <filter string="Paid" name="paid" domain="[('state', '=', 'paid')]"/>
                <filter string="Cancelled" name="cancelled" domain="[('state', '=', 'cancelled')]"/>
                <group expand="0" string="Group By">
                    <filter string="Customer" name="customer" domain="[]" context="{'group_by': 'customer_id'}"/>
                    <filter string="Salesperson" name="salesperson" domain="[]" context="{'group_by': 'sale_person'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action -->
    <record id="action_gjp_sale_order" model="ir.actions.act_window">
        <field name="name">GJP Sale Orders</field>
        <field name="res_model">gjp.sale.order</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new GJP Sale Order
            </p>
        </field>
    </record>

    <record id="action_batch_get_gjp_sale_orders" model="ir.actions.act_window">
        <field name="name">Batch Add GJP Sale Orders</field>
        <field name="res_model">gjp.sale.order</field>
        <field name="view_mode">tree</field>
        <field name="domain">[
                                ('audit_level', '=', '1'), 
                                ('sale_summary_order_id', '=', False),
                                ('sale_person', 'ilike', 'Yan 阿颜'),
                                ('customer_id','=',context.get('customer_id')),
                                ('id','not in',context.get('selected_gjp_order_ids')),
                            ]</field>
        <field name="view_ids" 
                    eval="[(5, 0, 0),
                            (0, 0, {'view_mode': 'tree','view_id': ref('view_gjp_sale_order_tree_for_select')})]"/>
        <field name="target">new</field>
    </record>
</odoo>
