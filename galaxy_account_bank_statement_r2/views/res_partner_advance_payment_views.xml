<odoo>
    <data>
        <!-- Tree View -->
        <record id="view_res_partner_tree_advance_payment" model="ir.ui.view">
            <field name="name">res.partner.tree.inherit</field>
            <field name="model">res.partner</field>
            <field name="priority" eval="99"/>
            <field name="arch" type="xml">
                <tree create="0" delete="0" edit="0" string="Customer Advance Payment Report" default_order = 'bid_user_id'>
                    <header>
                        <button string="Send Balance Report" name="batch_send_advance_balance_report_email" type="object" class="btn btn-primary"/>
                    </header>
                    <field name="bid_user_id"/>
                    <field name="name"/>
                    <!-- <field name="gjp_customer_name"/> -->
                    <field name="customer_currency_id"/>
                    <field name="total_advance_payment_balance" readonly="1"/>
                    <!-- <field name="allocate_advance_payment_amount" readonly="1"/>
                    <field name="allocate_downpayment_amount" readonly="1"/>
                    <field name="applied_advance_payment_amount" readonly="1" optional="hide"/>
                    <field name="applied_downpayment_amount" readonly="1" optional="hide"/>
                    <field name="transfered_out_advance_payment_amount" optional="hide" readonly="1"/>
                    <field name="transfered_out_downpayment_amount" optional="hide" readonly="1"/> -->
                    <field name="advance_payment_balance_amount" readonly="1"/>
                    <field name="downpayment_balance_amount" readonly="1"/>
                    <field name="last_advance_payment_changed_date"/>
                    <field name="id" invisible="1"/>
                    <button name="action_view_details_tech" context="{'customer_id':id}" string="Details_tech" groups="base.group_system"  type="object" class="btn btn-secondary oe_stat_button"/>
                    <button name="action_view_details" context="{'customer_id':id}" string="Details" type="object" class="btn btn-secondary oe_stat_button"/>
                </tree>
            </field>
        </record>
        <record id="view_res_partner_advance_report_form" model="ir.ui.view">
            <field name="name">res.partner.tree.inherit</field>
            <field name="model">res.partner</field>
            <field name="arch" type="xml">
                <form edit="0" create="0" delete="0">
                    <header>
                        <button string="Send Balance Report" name="action_advance_balance_send" type="object" class="btn btn-primary"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button name="action_open_advance_receive_data" type="object" class="oe_stat_button" icon="fa-money">
                                Advance Receive
                            </button>
                            <button name="action_open_advance_statement_data" type="object" class="oe_stat_button" icon="fa-money">
                                Advance Statement
                            </button>
                        </div>
                        <group>
                            <group>
                                <field name="bid_user_id" readonly="1"/>
                                <field name="name" readonly="1"/>
                                <field name="gjp_customer_name"/>
                                <field name="customer_currency_id" readonly="1"/>
                            </group>
                            <group>
                                <field name="total_advance_payment_amount" readonly="1"/>
                                <field name="applied_advance_payment_amount" readonly="1"/>
                                <field name="advance_payment_balance_amount" readonly="1"/>
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>
        <record model="ir.ui.view" id="advance_payment_report_filter">
            <field name="name">Customer advance received</field>
            <field name="model">res.partner</field>
            <field name="arch" type="xml">
                <search>
                    <field name="name" string="Customer" filter_domain="['|',('bid_user_id','ilike',self),('name','ilike',self)]"/>
                    <filter name="balance_not_zero" domain="['|', ('advance_payment_balance_amount','!=', 0),('downpayment_balance_amount','!=', 0)]" string="Has Balance"/>
                    <separator/>
                    <filter name="filter_today" string="Changed Today"  domain="[
                       ('last_advance_payment_changed_date','&gt;=', datetime.datetime.combine(context_today(), datetime.time(0,0,0))),
                       ('last_advance_payment_changed_date','&lt;=', datetime.datetime.combine(context_today(), datetime.time(23,59,59)))]"/>
                    <filter name="start_date_filter" string="Changed This Week" domain="[
                        ('last_advance_payment_changed_date', '>=', (datetime.datetime.combine(context_today() + relativedelta(weeks=-1,days=1,weekday=0), datetime.time(0,0,0)).to_utc()).strftime('%Y-%m-%d %H:%M:%S')),
                        ('last_advance_payment_changed_date', '&lt;', (datetime.datetime.combine(context_today() + relativedelta(days=1,weekday=0), datetime.time(0,0,0)).to_utc()).strftime('%Y-%m-%d %H:%M:%S'))]"/>
                    <filter name="this_month" string="Changed This Month" domain="[
                        ('last_advance_payment_changed_date', '>=', (datetime.datetime.combine(context_today().replace(day=1), datetime.time(0,0,0)).to_utc()).strftime('%Y-%m-%d %H:%M:%S')),
                        ('last_advance_payment_changed_date', '&lt;', (datetime.datetime.combine((context_today().replace(day=1) + relativedelta(months=1)), datetime.time(0,0,0)).to_utc()).strftime('%Y-%m-%d %H:%M:%S'))
                    ]"/>
                    <searchpanel>
                        <field name="customer_currency_id" icon="fa-tag" string="Currency" enable_counters="1"/>
                    </searchpanel>
                </search>
            </field>
        </record>
        <!-- Action to Open Partner Form for advance payment-->
        <record id="action_open_res_partner_form" model="ir.actions.act_window">
            <field name="name">Advance Receive Report</field>
            <field name="res_model">res.partner</field>
            <field name="view_mode">tree,form</field>
            <field name="view_ids" eval="[(5, 0, 0), (0, 0, {'view_mode': 'tree', 'view_id': ref('view_res_partner_tree_advance_payment')}), (0, 0, {'view_mode': 'form', 'view_id': ref('view_res_partner_advance_report_form')})]"/>
            <field name="search_view_id" ref="advance_payment_report_filter"/>
            <!-- <field name="domain">['|', ('total_advance_payment_amount','!=', 0), ('total_downpayment_amount','!=', 0)]</field> -->
        </record>
    </data>
</odoo>