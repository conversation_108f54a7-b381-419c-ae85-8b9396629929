<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="action_customer_settlement_report" model="ir.actions.report">
        <field name="name">Customer Settlement</field>
        <field name="model">customer.settlement</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">galaxy_account_bank_statement_r2.report_customer_settlement_template</field>
        <field name="report_file">galaxy_account_bank_statement_r2.report_customer_settlement_template</field>
        <field name="print_report_name">'Customer Settlement %s-%s' % (object.payer_id.display_name,object.name)</field>
        <!--<field name="paperformat_id" ref="sale_invoice_downpayment.paperformat_delivery_plan"/>-->
        <!--<field name="binding_model_id" ref="model_customer_settlement"/>-->
        <field name="binding_type">report</field>
        <field name="property_printing_action">open_print_dialog</field>
    </record>

    <template id="report_customer_settlement_template">
        <t t-call="web.html_container">
            <t t-foreach="docs" t-as="o">
                <t t-call="web.external_layout">
                    <t>
                        <div class="page">
                            <div margin-top="10px">
                                <table style="width:100%; text-align: center; table-layout: fixed;">
                                    <tr>
                                        <td style="width:100%;font-size:40px; font-weight: bold;">
                                            <div>结算单</div>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            
                            <div style="margin-top:40px; margin-bottom:40px">
                                <t t-set='customer_currency' t-value="o.payer_id.customer_currency_id.name"/>
                                <table style="width: 100%; margin-bottom: 5px; border-spacing: 0 10px;">
                                    <tr>
                                        <td style="width: 50%; font-size: 22px; font-weight: bold; text-align: left;">
                                            单號：<span t-field="o.name"/>
                                            <div t-if="o.received_payment_type == 'bank'" >
                                                <span style="font-weight:bold;color:red">
                                                    <p>請您轉賬時務必在備註欄填寫此編號</p>
                                                </span>
                                            </div>
                                        </td>
                                        <td style="width: 50%; font-size: 22px; font-weight: bold; text-align: right;">
                                            付款人：<span t-field="o.payer_id.display_name" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="width: 50%; font-size: 22px; font-weight: bold; text-align: left;">
                                            創建日期: <span><t t-esc="o.create_date.strftime('%Y-%m-%d') if o.create_date else ''"/></span>
                                        </td>
                                        <td style="width: 50%; font-size: 22px; font-weight: bold; text-align: right;">
                                            付款方式: <span>
                                                <t t-if="o.received_payment_type == 'bank'">銀行轉賬</t>
                                                <t t-elif="o.received_payment_type == 'cash'">現金</t>
                                                <t t-elif="o.received_payment_type == 'exchange'">結匯</t>
                                                <t t-elif="o.received_payment_type == 'advance'">預收款</t>
                                                <t t-elif="o.received_payment_type == 'downpayment'">首付款</t>
                                                <t t-elif="o.received_payment_type == 'credit'">信用額度</t>
                                                <t t-else=""></t>
                                            </span> 
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="width: 50%; font-size: 22px; font-weight: bold; text-align: left;">
                                            結算類型: <span>
                                                <t t-if="o.settlement_type == 'advance'">預收款</t>
                                                <t t-elif="o.settlement_type == 'sopayment'">貨款尾款</t>
                                                <t t-elif="o.settlement_type == 'credit'">還款結算</t>
                                                <t t-else=""></t>
                                            </span> 
                                        </td>
                                        <td style="width: 50%; font-size: 22px; font-weight: bold; text-align: right;">
                                            
                                        </td>
                                    </tr>
                                
                                    <!-- <tr>
                                        <td style="width: 50%; font-size: 22px; font-weight: bold; text-align: left;">
                                            創建人: <span t-field="o.create_uid"/>
                                        </td>
                                        <td style="width: 50%; font-size: 22px; font-weight: bold; text-align: right;">
                                            结算金額：<span t-field="o.statement_amount"/>
                                        </td>
                                    </tr> -->
                                </table>
                            </div>
                            <div style="margin-top:160px">
                                <table style="width: 100%; border: 1px solid black;border-collapse:collapse">
                                    <!-- In case we want to repeat the header, remove "display: table-row-group" -->
                                    <thead style="color:black !important">
                                        <tr>
                                            <th name="the_seq" class="text-center" style="width:10%;padding:15px;border: 1px solid black;">
                                                <div>行號</div>
                                            </th>

                                            <th style="width:20%;padding:15px;text-align:center;border: 1px solid black;">
                                                <div>用款人</div>
                                            </th>

                                            <th style="width:10%;padding:15px;text-align:center;border: 1px solid black;">
                                                <div>用途</div>
                                            </th>

                                            <th style="width:20%;padding:15px;text-align:center;border: 1px solid black;">
                                                <div>銷售匯總單號</div>
                                            </th>
                                             <th style="width:20%;padding:15px; text-align: center;border: 1px solid black;">
                                                <div>備註</div>
                                            </th>

                                            <th style="width:20%;padding:15px;text-align:center;border: 1px solid black;">
                                                <div>小計</div>
                                            </th>

                                        </tr>
                                    </thead>
                                    <tbody class="sale_tbody">
                                        <t t-set='counter' t-value="0"/>
                                        <t t-foreach="o.customer_statement_line_ids" t-as="line">
                                            <t t-set='counter' t-value="counter+1"/>
                                            <tr>
                                                <td style="width:10%; text-align: center;border: 1px solid black;">
                                                    <t t-esc='counter'/>
                                                </td>
                                                <td style="width:20%; text-align: center;border: 1px solid black;">
                                                    <span t-field="line.customer_id.display_name"/>
                                                </td>
                                                <td style="width:10%; text-align: center; border: 1px solid black;">
                                                    <t t-if="line.received_type == 'downpayment'">首付款</t>
                                                    <t t-elif="line.received_type == 'sopayment'">貨款</t>
                                                    <t t-elif="line.received_type == 'advance'">預付款</t>
                                                    <t t-elif="line.received_type == 'credit'">額度還款</t>
                                                    <t t-else=""></t>
                                                </td>
                                                <td style="width:20%; text-align: center;border: 1px solid black;">
                                                    <span t-field="line.gjp_sale_order_summary_id"/>
                                                </td>
                                                 <td style="width:20%; text-align: center; border: 1px solid black;">
                                                    <span t-field="line.note"/>
                                                </td>
                                                <td style="width:20%; text-align: right; border: 1px solid black;">
                                                    <span t-field="line.allocate_amount"/>
                                                </td>
                                            </tr>
                                        </t>
                                        <tr t-if="o.received_payment_type == 'cash'" style="border-bottom:1px solid black;">
                                            <td style="text-align: right; border: 1px solid black;" colspan="5">
                                                <p><b>現金交易收取交易額0.1%手續費</b></p>
                                            </td> 
                                            <td style="text-align: right;border: 1px solid black;">
                                                <b><t t-esc='customer_currency'/><span>&#160;</span><span t-field="o.received_fee"/></b>
                                            </td>
                                        </tr>
                                        <tr style="border-bottom:1px solid black;">
                                            <td  style="text-align: right; border: 1px solid black;" colspan="5">
                                                <p><b>總金額</b></p>
                                            </td> 
                                            <td style="text-align: right;border: 1px solid black;">
                                                <b><t t-esc='customer_currency'/><span>&#160;</span><span t-field="o.total_amount"/></b>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div t-if="o.received_payment_type == 'bank'" style="margin-top: 10px;">
                                <p>銀行賬戶資料</p>
                                <p>銀行戶名：GALAXY TELECOM (HONG KONG) LIMITED</p>
                                <p>公司名稱：銀河電訊(香港)有限公司</p>
                                <p>開戶銀行：大新銀行</p>
                                <p>銀行帳號（多幣種戶口）:040-774-012-45050-3</p>
                                <p>銀行編號：040</p>
                                <p>分行编號：774</p>
                                <p>Swift Code :DSBAHKHHXXX</p>
                            </div>
                        </div>
                    </t>
                </t>
            </t>
        </t>
        <style>
            table, th, td {
            border: 1px solid black;
            }
        </style>
    </template>

</odoo>
