# -*- coding: utf-8 -*-
import base64
from io import BytesIO
import qrcode
from collections import defaultdict
from odoo import api, models, fields
from odoo.tools import DEFAULT_SERVER_DATE_FORMAT
from datetime import datetime
import pytz


class GJPDeliveryOrder(models.AbstractModel):
    _name = "report.galaxy_account_bank_statement_r2.gjp_delivery_order"
    _description = "GJP Delivery Report"

    def _format_float(self, value):
        """
        Format float number with thousand separators and 2 decimal places
        :param value: float number to format
        :return: formatted string
        """
        if value is None:
            return "0.00"
        return "{:,.2f}".format(float(value))

    @api.model
    def _get_report_values(self, docids, data=None):
        docs = self.env["gjp.sale.order.summary"].browse(docids)

        page_vals_list = []

        for doc in docs:
            for line in doc.line_ids:
                page_vals = {}
                gjp_order = line.gjp_sale_order_id

                qr = qrcode.QRCode(
                    version=1,
                    error_correction=qrcode.constants.ERROR_CORRECT_L,
                    box_size=10,
                    border=4,
                )
                qr.add_data(gjp_order.gjp_order_no)
                qr.make(fit=True)

                img = qr.make_image(fill='black', back_color='white')
                temp = BytesIO()
                img.save(temp, format="PNG")
                qr_img = base64.b64encode(temp.getvalue())
                page_vals['qr_img'] = qr_img
                page_vals['doc_no'] = doc.name

                page_vals['customer'] = f"{gjp_order.customer_id.name}({gjp_order.customer_id.bid_user_id})"
                page_vals['customer_code'] = gjp_order.customer_id.bid_user_id
                page_vals['customer_name'] = gjp_order.customer_id.name
                page_vals['stock_name'] = gjp_order.stock_name
                page_vals['order_no'] = gjp_order.gjp_order_no
                page_vals['summary'] = gjp_order.order_summary
                # Convert to China timezone
                today = fields.Datetime.now()
                china_tz = pytz.timezone('Asia/Shanghai')
                today_china = today.astimezone(china_tz)
                page_vals['date'] = today_china.strftime("%Y-%m-%d")

                total_qty = 0
                total_amount = 0
                detail_val_list = []
                for detail in gjp_order.detail_line_ids:
                    detail_vals = {
                        'product': detail.description,
                        'qty': detail.quantity,
                        'price_unit': self._format_float(detail.price_unit),
                        'amount': self._format_float(detail.amount),
                        'line_summary': detail.line_summary,
                        'line_comment': detail.line_comment,
                    }
                    total_qty += detail.quantity
                    total_amount += detail.amount
                    detail_val_list.append(detail_vals)
                page_vals['lines'] = detail_val_list
                page_vals['total_qty'] = total_qty
                page_vals['total_amount'] = self._format_float(total_amount)
                page_vals['sale_person'] = gjp_order.sale_person
                page_vals['dept'] = gjp_order.dept

                page_vals_list.append(page_vals)
        return {
            "doc_ids": docids,
            "doc_model": "gjp.sale.order.summary",
            "docs": docs,
            "pages": page_vals_list,
        }
