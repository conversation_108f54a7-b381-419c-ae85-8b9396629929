<odoo>
    <template id="gjp_delivery_order">
        <t t-call="web.html_container">
            <!--每个page是一个订单-->
            <t t-foreach="pages" t-as="page" t-foreach-index="page_index">
                <t t-set="items_size" t-value="len(pages)"/>
                <t t-set="lines_count" t-value="len(page['lines'])"/>
                <t t-set="max_lines_per_page" t-value="14"/>
                <t t-set="total_pages" t-value="(lines_count // max_lines_per_page) + (1 if lines_count % max_lines_per_page != 0 else 0)"/>
                <t t-set="total_pages" t-value="1 if total_pages == 0 else total_pages"/>
                
                <!-- 拆分行并按每页14行进行处理 -->
                <!--t-attf-style="padding: 10px; {{ 'page-break-after: always;' if not is_last_order else '' }}"-->
                <t t-foreach="range(total_pages)" t-as="page_num">
                    <t t-set="start_idx" t-value="page_num * max_lines_per_page"/>
                    <t t-set="end_idx" t-value="min((page_num + 1) * max_lines_per_page, lines_count)"/>
                    <t t-set="current_page_lines" t-value="page['lines'][start_idx:end_idx]"/>
                    <t t-set="is_last_page" t-value="page_num == total_pages - 1"/>
                    <t t-set="is_last_order" t-value="page_index == items_size - 1"/>

                    <div class="page">
                        <!-- 标题和二维码布局 -->
                        <table style="width: 100%; margin-bottom: 5px;">
                            <tr>
                                <td style="width: 80px;"></td>
                                <td style="text-align: center;">
                                    <h2 style="font-size: 30px; font-weight: bold; font-weight: bold; margin: 0;">
                                        提貨單
                                    </h2>
                                </td>
                                <td style="width: 80px; text-align: center;">
                                    <img t-att-src="'data:image/png;base64,%s' % page['qr_img'].decode('utf-8')"
                                        style="width: 80px; height: 80px;"/>
                                </td>
                            </tr>
                        </table>
                        <!-- 单号、客户、日期、备注 -->
                        <div style="width: 100%; margin-bottom: 10px;">
                            <!-- 单号行 -->
                            <div class="row" style="width: 100%; margin-bottom: 5px;">
                                <div style="width: 100%; display: inline-block; text-align: left;">
                                    <span style="float: left; font-size: 30px; font-weight: bold; font-weight: bold;">
                                        仓库: <span><t t-esc="page['stock_name']"/></span>
                                    </span>
                                    <span style="float: right;font-size: 30px; font-weight: bold;">单号: <t t-esc="page['order_no']"/></span>
                                </div>
                            </div>
                            <!-- 客户和日期行（使用table布局） -->
                            <table style="width: 100%; margin-bottom: 5px;">
                                <tr>
                                    <td style="width: 70%;font-size: 22px; font-weight: bold;">
                                        客户: <span style="width: 70%; font-size: 30px; font-weight: bold; text-align: left;"><t t-esc="page['customer_code']"/>
                                        <span style="font-size: 18px; font-weight: bold; text-align: left;"><t t-esc="page['customer_name']"/></span></span>
                                    </td>
                                    <td style="width: 30%; font-size: 22px; font-weight: bold; font-weight: bold; text-align: right;">
                                        日期: <span><t t-esc="page['date']"/></span>
                                    </td>
                                </tr>
                            </table>
                            <!-- 备注行 -->
                            <div class="row" style="width: 100%; margin-bottom: 5px;">
                                <div style="width: 100%; display: inline-block;font-weight: bold; font-weight: bold; text-align: left;">
                                    摘要: <span><t t-esc="page['summary']"/></span>
                                </div>
                            </div>
                        </div>
                        <!-- 表头 -->
                        <table style="width: 100%; border: 1px solid black; border-collapse: collapse; margin-bottom: 10px;">
                            <thead>
                                <tr>
                                    <th style="border: 1px solid black; padding: 4px;">商品名称</th>
                                    <th style="border: 1px solid black; padding: 4px;">数量</th>
                                    <th style="border: 1px solid black; padding: 4px;">单价</th>
                                    <th style="border: 1px solid black; padding: 4px;">金额</th>
                                    <th style="border: 1px solid black; padding: 4px;">行摘要</th>
                                    <th style="border: 1px solid black; padding: 4px;">备注</th>
                                </tr>
                            </thead>
                            <tbody>
                                <t t-foreach="current_page_lines" t-as="line">
                                    <tr style="min-height: 50px; height: 50px;">
                                        <td style="border: 1px solid black; padding: 4px; word-wrap: break-word; word-break: break-all; max-width: 270px;">
                                            <t t-esc="line['product']"/>
                                        </td>
                                        <td style="border: 1px solid black; padding: 4px; text-align: right;">
                                            <t t-esc="line['qty']"/>
                                        </td>
                                        <td style="border: 1px solid black; padding: 4px; text-align: right;">
                                            <t t-esc="line['price_unit']"/>
                                        </td>
                                        <td style="border: 1px solid black; padding: 4px; text-align: right;">
                                            <t t-esc="line['amount']"/>
                                        </td>
                                        <td style="border: 1px solid black; padding: 4px;">
                                            <t t-esc="line['line_summary']"/>
                                        </td>
                                        <td style="border: 1px solid black; padding: 4px;">
                                            <t t-esc="line['line_comment']"/>
                                        </td>
                                    </tr>
                                </t>

                                <t t-set="empty_rows" t-value="max_lines_per_page - len(current_page_lines)"/>
                                <t t-if="empty_rows > 0">
                                    <t t-foreach="range(empty_rows)" t-as="i">
                                            <tr style="min-height: 50px; height: 50px;">
                                            <td style="border: 1px solid black; padding: 4px;"></td>
                                            <td style="border: 1px solid black; padding: 4px;"></td>
                                            <td style="border: 1px solid black; padding: 4px;"></td>
                                            <td style="border: 1px solid black; padding: 4px;"></td>
                                            <td style="border: 1px solid black; padding: 4px;"></td>
                                            <td style="border: 1px solid black; padding: 4px;"></td>
                                        </tr>
                                    </t>
                                </t>

                                <!-- 只在最后一页显示合计行 -->
                                <t t-if="is_last_page">
                                    <tr>
                                        <td style="min-height: 40px; border: 1px solid black; padding: 4px; text-align: left; font-size: 25px; font-weight: bold; vertical-align: middle;">
                                            合计:
                                        </td>
                                        <td style="min-height: 40px; border: 1px solid black; padding: 4px; text-align: right; font-size: 25px; font-weight: bold; vertical-align: middle;">
                                            <t t-esc="page['total_qty']"/>
                                        </td>
                                        <td style="min-height: 40px; border: 1px solid black; padding: 4px; vertical-align: middle;"></td>
                                        <td style="min-height: 40px; border: 1px solid black; padding: 4px; text-align: right; font-size: 25px; font-weight: bold; vertical-align: middle;">
                                            <t t-esc="page['total_amount']"/>
                                        </td>
                                        <td style="min-height: 40px; border: 1px solid black; padding: 4px; vertical-align: middle;"></td>
                                        <td style="min-height: 40px; border: 1px solid black; padding: 4px; vertical-align: middle;"></td>
                                    </tr>
                                </t>
                            </tbody>
                        </table>

                        <!-- 底部签名区域 - 始终显示空间 -->
                        <table style="width: 100%; margin-top: 15px;">
                            <tr style="min-height: 30px; height: 30px;">
                                <td style="width: 50%;"></td>
                                <td style="text-align: right; white-space: nowrap;">
                                    <t t-if="is_last_page">
                                        <span style="font-size: 22px; font-weight: bold; margin-right: 50px;">销售: <t t-esc="page['sale_person']"/></span>
                                        <span style="font-size: 22px; font-weight: bold;">部门: <t t-esc="page['dept']"/></span>
                                    </t>
                                </td>
                            </tr>
                        </table>
                        <!-- 添加页脚显示页码 -->
                        <div style="width: 100%; text-align: center; margin-top: 5px; font-size: 18px;">
                            第 <t t-esc="page_num + 1"/> / <t t-esc="total_pages"/> 页
                            <div t-if="not is_last_order" style="page-break-after: always;"></div>
                            <div t-if="is_last_order and not is_last_page" style="page-break-after: always;"></div>
                        </div>
                    </div>
                </t>
            </t>
        </t>
    </template>
</odoo>