<odoo>
    <template id="order_summary">
        <t t-call="web.html_container">
            <t t-foreach="docs" t-as="page" t-foreach-index="page_index">
                <t t-set="items_size" t-value="len(page)"/>
                <t t-set="lines_count" t-value="len(page.line_ids)"/>
                <t t-set="max_lines_per_page" t-value="15"/>
                <t t-set="total_pages" t-value="(lines_count // max_lines_per_page) + (1 if lines_count % max_lines_per_page != 0 else 0)"/>
                <t t-set="total_pages" t-value="1 if total_pages == 0 else total_pages"/>
                
                <!-- 拆分行并按每页15行进行处理 -->
                <t t-foreach="range(total_pages)" t-as="page_num">
                    <t t-set="start_idx" t-value="page_num * max_lines_per_page"/>
                    <t t-set="end_idx" t-value="min((page_num + 1) * max_lines_per_page, lines_count)"/>
                    <t t-set="is_last_page" t-value="page_num == total_pages - 1"/>
                    <t t-set="is_last_order" t-value="page_index == items_size - 1"/>
                    <t t-set="current_page_lines" t-value="page.line_ids[start_idx:end_idx]"/>
                    <div class="page">
                        <!-- 标题和二维码布局 -->
                        <table style="width: 100%; margin-bottom: 5px;">
                            <tr>
                                <td style="width: 200px;"/>
                                <td style="text-align: center;">
                                    <h2 style="font-size: 25px; font-weight: bold; margin: 0;">
                                        銷售匯總單
                                    </h2>
                                </td>
                                <td style="width: 200px; text-align: right; font-size: 16px; font-weight: bold;">
                                    <!--<img t-att-src="'data:image/png;base64,%s' % page.qr_img.decode('utf-8')" style="width: 80px; height: 80px;"/>-->
                                    <span style="font-size: 18 pt">No.: <t t-esc="page.name"/></span>
                                </td>
                            </tr>
                        </table>
                        <!-- 单号、客户、日期、备注 -->
                        <div style="width: 100%; margin-bottom: 10px;">
                            <!-- 客户和日期行（使用table布局） -->
                            <table style="width: 100%; margin-bottom: 5px; border-spacing: 0 10px;">
                                <tr>
                                    <td style="width: 50%; font-size: 22px; font-weight: bold; text-align: left;">
                                        客户: <span><t t-esc="page.customer_id.display_name"/></span>
                                    </td>
                                    <td style="width: 50%; font-size: 22px; font-weight: bold; text-align: right;">
                                        應付金額：
                                        <t t-if="page.customer_currency_id.name == 'HKD'">
                                            <span t-esc="'{:,}'.format(int(page.total_amount))"/>
                                        </t>
                                        <t t-if="page.customer_currency_id.name == 'USD'">
                                            <span t-esc="'{:,.2f}'.format(page.total_amount)"/>
                                        </t>
                                        <t t-if="page.customer_currency_id.name not in ['HKD', 'USD']">
                                            <span t-esc="'{:,}'.format(page.total_amount)"/>
                                        </t>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 50%; font-size: 22px; font-weight: bold; text-align: left;">
                                        創建日期: <span><t t-esc="page.create_date.strftime('%Y-%m-%d') if page.create_date else ''"/></span>
                                    </td>
                                    <td style="width: 50%; font-size: 22px; font-weight: bold; text-align: right;">
                                        結算幣種: <span><t t-esc="page.customer_currency_id.name"/></span>
                                    </td>
                                </tr>
                               
                                <!-- <tr>
                                    <td style="width: 50%; font-size: 22px; font-weight: bold; text-align: left;">
                                        創建人: <span t-field="page.create_uid"/>
                                    </td>
                                    <td style="width: 50%; font-size: 22px; font-weight: bold; text-align: right;">
                                        未付金額：<span t-field="page.balance_amount"/>
                                    </td>
                                </tr> -->
                                <tr>
                                    <td style="width: 50%; font-size: 22px; font-weight: bold; text-align: left;">
                                        備註: <span><t t-esc="page.note"/></span>
                                    </td>
                                    <td style="width: 50%; font-size: 22px; font-weight: bold; text-align: right;">
                                        總數量：<span t-field="page.total_quantity"/>
                                    </td>
                                </tr>
                            </table>
                            <!-- 备注行 -->
                            <!-- <div class="row" style="width: 100%; margin-bottom: 5px;">
                                <div style="width: 100%; display: inline-block; font-size: 22px; font-weight: bold;  text-align: left;">
                                    備註: <span><t t-esc="page.note"/></span>
                                </div>
                            </div> -->
                        </div>
                        <!-- 表头 -->
                        <table style="width: 100%; border: 1px solid black; border-collapse: collapse; margin-bottom: 10px;">
                            <thead>
                                <tr>
                                    <th style="border: 1px solid black; padding: 4px;">行號</th>
                                    <th style="border: 1px solid black; padding: 4px;">SO單號</th>
                                    <th style="border: 1px solid black; padding: 4px;">數量</th>
                                    <th style="border: 1px solid black; padding: 4px;">金額</th>
                                    <!-- <th style="border: 1px solid black; padding: 4px;">備註</th> -->
                                </tr>
                            </thead>
                            <tbody>
                                <t t-set='counter' t-value="0"/>
                                <t t-foreach="current_page_lines" t-as="line">
                                    <t t-set='counter' t-value="counter+1"/>
                                    <tr style="min-height: 50px; height: 50px;">
                                        <td style="border: 1px solid black; padding: 4px; text-align: center;">
                                            <t t-esc="counter"/>
                                        </td>
                                        <td style="border: 1px solid black; padding: 4px; text-align: center;">
                                            <t t-esc="line.gjp_sale_order_id.gjp_order_no"/>
                                        </td>
                                        <td style="border: 1px solid black; padding: 4px; text-align: right;">
                                            <t t-esc="line.quantity"/>
                                        </td>
                                        <td style="border: 1px solid black; padding: 4px; text-align: right;">
                                            <t t-if="page.customer_currency_id.name == 'HKD'">
                                                <span><t t-esc="'%d' % line.amount"/></span>
                                            </t>
                                            <t t-if="page.customer_currency_id.name == 'USD'">
                                                <span><t t-esc="'%.2f' % line.amount"/></span>
                                            </t>
                                            <t t-if="page.customer_currency_id.name not in ['HKD', 'USD']">
                                                <span><t t-esc="line.amount"/></span>
                                            </t>
                                        </td>
                                        <!-- <td style="border: 1px solid black; padding: 4px; text-align: center;">
                                            <t t-esc="line.order_summary"/>
                                        </td> -->
                                    </tr>
                                </t>

                                <t t-set="empty_rows" t-value="max_lines_per_page - len(current_page_lines)"/>
                                <t t-if="empty_rows > 0">
                                    <t t-foreach="range(empty_rows)" t-as="i">
                                        <tr style="min-height: 50px; height: 50px;">
                                            <td style="border: 1px solid black; padding: 4px;"></td>
                                            <td style="border: 1px solid black; padding: 4px;"></td>
                                            <td style="border: 1px solid black; padding: 4px;"></td>
                                            <td style="border: 1px solid black; padding: 4px;"></td>
                                            <!-- <td style="border: 1px solid black; padding: 4px;"></td> -->
                                        </tr>
                                    </t>
                                </t>
                            </tbody>
                        </table>
                        <!-- 添加页脚显示页码 -->
                        <div style="width: 100%; text-align: center; margin-top: 5px; font-size: 18px;">
                            第 <t t-esc="page_num + 1"/> / <t t-esc="total_pages"/> 页
                            <div t-if="not is_last_order" style="page-break-after: always;"></div>
                            <div t-if="is_last_order and not is_last_page" style="page-break-after: always;"></div>
                        </div>
                    </div>
                </t>
            </t>
        </t>
    </template>
</odoo>