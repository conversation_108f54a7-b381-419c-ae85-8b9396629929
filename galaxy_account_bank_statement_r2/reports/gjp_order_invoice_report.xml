<odoo>
    <template id="gjp_order_invoice_report">
        <t t-call="web.html_container">
            <!--每个page是一个订单-->
            <t t-foreach="docs" t-as="page" t-foreach-index="page_index">
                <t t-call="web.external_layout">
                    <t t-set="items_size" t-value="len(docs)"/>
                    <t t-set="lines_count" t-value="len(page.detail_line_ids)"/>
                    <t t-set="max_lines_per_page" t-value="12"/>
                    <t t-set="total_pages" t-value="(lines_count // max_lines_per_page) + (1 if lines_count % max_lines_per_page != 0 else 0)"/>
                    <t t-set="total_pages" t-value="1 if total_pages == 0 else total_pages"/>
                    
                    <!-- 拆分行并按每页12行进行处理 -->
                    <!--t-attf-style="padding: 10px; {{ 'page-break-after: always;' if not is_last_order else '' }}"-->
                    <t t-foreach="range(total_pages)" t-as="page_num">
                        <t t-set="start_idx" t-value="page_num * max_lines_per_page"/>
                        <t t-set="end_idx" t-value="min((page_num + 1) * max_lines_per_page, lines_count)"/>
                        <t t-set="current_page_lines" t-value="page.detail_line_ids[start_idx:end_idx]"/>
                        <t t-set="is_last_page" t-value="page_num == total_pages - 1"/>
                        <t t-set="is_last_order" t-value="page_index == items_size - 1"/>

                        <div t-attf-class="page" t-attf-style="page-break-after: {{ 'always' if not is_last_page else 'auto' }}">
                            <!-- 标题和二维码布局 -->
                            <table style="width: 100%; margin-bottom: 5px;">
                                <tr>
                                    <td style="width: 80px;"></td>
                                    <td style="text-align: center;">
                                        <h2 style="font-size: 30px; font-weight: bold; font-weight: bold; margin: 0;">
                                            <div>INVOICE</div>
                                            <div>發票</div>
                                        </h2>
                                    </td>
                                    <td style="width: 80px; text-align: center;">
                                        <img t-att-src="'data:image/png;base64,%s' % page.invoice_qr_img.decode('utf-8')"
                                            style="width: 80px; height: 80px;"/>
                                        <span style="float: center;font-size: 20px; font-weight: bold;"><t t-esc="page.invoice_number"/></span>
                                    </td>
                                </tr>
                            </table>
                            <!-- 单号、客户、日期、备注 -->
                            <div style="width: 100%;">
                                <!-- 客户和日期行（使用table布局） -->
                                <table style="width: 100%">
                                    <tr>
                                        <td style="width: 70%;font-size: 22px; font-weight: bold;">
                                            客户: <span style="width: 70%; font-size: 30px; font-weight: bold; text-align: left;">
                                            <t t-if="page.invoice_res_partner_id">
                                                <t t-esc="page.invoice_res_partner_id.bid_user_id"/>
                                            </t>
                                            <t t-else="">
                                                <t t-esc="page.customer_id.bid_user_id"/>
                                            </t>
                                            </span>
                                            <span style="font-size: 18px; font-weight: bold; text-align: left;">
                                            <t t-if="page.invoice_res_partner_id">
                                                <t t-esc="page.invoice_res_partner_id.name"/>
                                            </t>
                                            <t t-else="">
                                                <t t-esc="page.customer_id.name"/>
                                            </t>
                                            </span>
                                        </td>
                                        <td style="width: 30%; font-size: 22px; font-weight: bold; font-weight: bold; text-align: right;">
                                            日期: <span><t t-esc="page.invoice_date"/></span>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <!-- 表头 -->
                            <table style="width: 100%; border: 1px solid black">
                                <thead style="font-weight: bold; font-size: 18px;">
                                    <tr>
                                        <th style="border: 1px solid black; padding: 4px;"><font color="black">商品名称</font></th>
                                        <th style="border: 1px solid black; padding: 4px;"><font color="black">数量</font></th>
                                        <th style="border: 1px solid black; padding: 4px;"><font color="black">单价</font></th>
                                        <th style="border: 1px solid black; padding: 4px;"><font color="black">金额</font></th>
                                        <th style="border: 1px solid black; padding: 4px;"><font color="black">行摘要</font></th>
                                        <th style="border: 1px solid black; padding: 4px;"><font color="black">备注</font></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <t t-foreach="current_page_lines" t-as="line">
                                        <tr style="min-height: 50px; height: 50px;">
                                            <td style="border: 1px solid black; padding: 4px; word-wrap: break-word; word-break: break-all; max-width: 270px;">
                                                <t t-esc="line.description"/>
                                            </td>
                                            <td style="border: 1px solid black; padding: 4px; text-align: right;">
                                                <t t-esc="line.quantity"/>
                                            </td>
                                            <td style="border: 1px solid black; padding: 4px; text-align: right;">
                                                <t t-esc="'{:,.2f}'.format(line.price_unit)"/>
                                            </td>
                                            <td style="border: 1px solid black; padding: 4px; text-align: right;">
                                                <t t-esc="'{:,.2f}'.format(line.amount)"/>
                                            </td>
                                            <td style="border: 1px solid black; padding: 4px;">
                                                <t t-esc="line.line_summary"/>
                                            </td>
                                            <td style="border: 1px solid black; padding: 4px;">
                                                <t t-esc="line.line_comment"/>
                                            </td>
                                        </tr>
                                    </t>

                                    <t t-set="empty_rows" t-value="max_lines_per_page - len(current_page_lines)"/>
                                    <t t-if="empty_rows > 0">
                                        <t t-foreach="range(empty_rows)" t-as="i">
                                                <tr style="min-height: 50px; height: 50px;">
                                                <td style="border: 1px solid black; padding: 4px;"></td>
                                                <td style="border: 1px solid black; padding: 4px;"></td>
                                                <td style="border: 1px solid black; padding: 4px;"></td>
                                                <td style="border: 1px solid black; padding: 4px;"></td>
                                                <td style="border: 1px solid black; padding: 4px;"></td>
                                                <td style="border: 1px solid black; padding: 4px;"></td>
                                            </tr>
                                        </t>
                                    </t>

                                    <!-- 只在最后一页显示合计行 -->
                                    <t t-if="is_last_page">
                                        <tr>
                                            <td style="min-height: 40px; border: 1px solid black; padding: 4px; text-align: left; font-size: 25px; font-weight: bold; vertical-align: middle;">
                                                合计:
                                            </td>
                                            <td style="min-height: 40px; border: 1px solid black; padding: 4px; text-align: right; font-size: 25px; font-weight: bold; vertical-align: middle;">
                                                <t t-esc="page.quantity"/>
                                            </td>
                                            <td style="min-height: 40px; border: 1px solid black; padding: 4px; vertical-align: middle;"></td>
                                            <td style="min-height: 40px; border: 1px solid black; padding: 4px; text-align: right; font-size: 25px; font-weight: bold; vertical-align: middle;">
                                                <t t-esc="'{:,.2f}'.format(page.amount)"/>
                                            </td>
                                            <td style="min-height: 40px; border: 1px solid black; padding: 4px; vertical-align: middle;"></td>
                                            <td style="min-height: 40px; border: 1px solid black; padding: 4px; vertical-align: middle;"></td>
                                        </tr>
                                    </t>
                                </tbody>
                            </table>
                            <!--增加公司银行信息，打印格式-->
                            <div t-if="is_last_page" style="width: 100%; margin-top: 10px; font-size: 15px; font-weight: bold;">
                                Bank Account Information (銀行帳戶資料)<br></br>
                                銀行戶名：GALAXY TELECOM (HONG KONG) LIMITED<br></br>
                                公司名稱：銀河電訊(香港)有限公司<br></br>
                                開戶銀行：大新銀行<br></br>
                                銀行帳號（多幣種戶口）：040-774-012-45050-3<br></br>
                                銀行編號：040	分行編號：774<br></br>
                                Swift Code：DSBAHKHHXXX<br></br>
                                FPS ID : *********<br></br>
                                <img src="/galaxy_account_bank_statement_r2/static/src/img/fps_qr_code.png" style="width: 150px; height: 150px; margin-top: 10px;"/>
                            </div>
                            <!-- 底部签名区域 - 始终显示空间 -->
                            <table t-if="is_last_page" style="width: 100%; margin-top: 15px;">
                                <tr style="min-height: 30px; height: 30px;">
                                    <td style="width: 50%;"></td>
                                    <td style="text-align: right; white-space: nowrap;">
                                        <t t-if="is_last_page">
                                            <span style="font-size: 22px; font-weight: bold; margin-right: 50px;">CUSTOMERS: _________________</span>
                                            <span style="font-size: 22px; font-weight: bold;">COMPANY CHOP:________________________</span>
                                        </t>
                                    </td>
                                </tr>
                            </table>
                            <!-- 备注信息 -->
                            <div t-if="is_last_page" style="width: 100%; margin-top: 10px; font-size: 15px; font-weight: bold;">
                                <span>REMARKS : GOODS SOLD ARE NOT RETURNABLE </span><br></br>
                                <span>備註: 所有售出貨品恕不退換</span>
                            </div>
                        </div>
                    </t>
                </t>
            </t>
        </t>
    </template>
</odoo>