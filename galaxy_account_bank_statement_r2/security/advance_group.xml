<?xml version="1.0" encoding="utf-8"?>
<odoo>
<data noupdate="0">
    <record id="group_advance_payment_sale" model="res.groups">
        <field name="name">Advance Paymnent Sale</field>
        <field name="category_id" ref="base.module_category_hidden"/>
        <field name="implied_ids" eval="[(4, ref('sales_team.group_sale_manager'))]"/>
    </record>
    <record id="group_advance_payment_account" model="res.groups">
        <field name="name">Advance Paymnent Account</field>
        <field name="category_id" ref="base.module_category_hidden"/>
        <field name="implied_ids" eval="[(4, ref('sales_team.group_sale_manager'))]"/>
    </record>
    <record id="group_advance_payment_admin" model="res.groups">
        <field name="name">Advance Paymnent Admin</field>
        <field name="category_id" ref="base.module_category_hidden"/>
        <field name="implied_ids" eval="[(4, ref('sales_team.group_sale_manager')),
        (4, ref('galaxy_account_bank_statement_r2.group_advance_payment_sale')),
        (4, ref('galaxy_account_bank_statement_r2.group_advance_payment_account'))]"/>
    </record>
    <record id="group_advance_payment_report" model="res.groups">
        <field name="name">Advance Paymnent Report</field>
        <field name="category_id" ref="base.module_category_hidden"/>
    </record>
</data>
</odoo>
