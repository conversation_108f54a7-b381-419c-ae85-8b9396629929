# -*- coding: utf-8 -*-
{
    'name': "galaxy_account_bank_statement_r2",

    'summary': """
        extend bank statement to select advance payment""",

    'description': """
         extend bank statement to select advance payment
    """,

    'author': "JackyRen",
    'website': "",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/14.0/odoo/addons/base/data/ir_module_category_data.xml
    # for the full list
    'category': 'Galaxy_ERP/Galaxy_ERP',
    'version': '0.1',
    # any module necessary for this one to work correctly
    'depends': ['galaxy_account_bank_statement', 'galaxy_lot_r2'],

    # always loaded
    'data': [
        'security/advance_group.xml',
        'security/ir.model.access.csv',
        'views/galaxy_account_merged_bank_statement_views.xml',
        'views/customer_advance_received_views.xml',
        'views/customer_settlement_views.xml',
        'views/customer_credit_history_views.xml',
        'views/res_partner_advance_payment_views.xml',
        'views/res_partner_customer_credit_views.xml',
        'views/customer_advance_payment_history_views.xml',
        'views/customer_advance_payment_history_group_views.xml',
        'views/assets.xml',
        'views/gjp_order_summary_views.xml',
        'views/gjp_order_summary_line_views.xml',
        'views/fund_transfer_order_views.xml',
        'views/gjp_sale_order_views.xml',
        'views/portal_templates.xml',
        'views/res_config_settings_views.xml',
        'data/advance_balance_mail_template_views.xml',
        'wizard/galaxy_audit_scan.xml',
        'data/sequence.xml',
        'data/cron.xml',
        'data/mail.xml',
        'reports/settlement_report_views.xml',
        'reports/gjp_order_summary_report_action.xml',
        'reports/gjp_order_summary_report.xml',
        'reports/gjp_delivery_order_report_action.xml',
        'reports/gjp_delivery_order_report.xml',
        'reports/fund_transfer_order.xml',
        'reports/gjp_order_invoice_report.xml',
        'reports/gjp_order_invoice_report_action.xml',
        'reports/report_payment_receipt_templates.xml',
        'views/menu.xml',
    ],
    'qweb': [
        "static/src/xml/payment_history_dashboard.xml",
        'static/src/xml/credit_history_dashboard.xml'
    ],
    # demo data
    'demo': [
        'demo/customer_settlement_demo.xml',
    ],
    # only loaded in demonstration mode
}
