<?xml version="1.0" encoding="UTF-8"?>
<templates>
    <!-- This template is for a table at the top of bank statement views that shows balance -->
    <t t-name="galaxy_account_bank_statement_r2.BalanceDashboard">
        <div class="o_purchase_dashboard container">
            <div class="row">
                <div style="width: 100%;">
                    <table class="table table-sm" style="width: 100%;">
                        <!-- thead needed to avoid list view rendering error for some reason -->
                        <thead style="width: 100%;">
                            <tr style="width: 100%;">
                                <!-- can't use th tag due to list rendering error when no values in list... -->
                                <td class="o_text" style="font-size: 1.5em; font-weight: bold; padding: 2px 8px;"><t t-esc="values['name']"/></td>
                                <td class="o_text" style="font-size: 1.5em; font-weight: bold; padding: 2px 8px;">Advance Balance (<t t-esc="values['currency']"/>)</td>
                                <td class="o_main" style="font-size: 1.5em; font-weight: bold; padding: 2px 8px;"><a href="javascript:void(0);" role="button" >$<t t-esc="values['advance_balance']"/></a></td>
                                <td class="o_text" style="font-size: 1.5em; font-weight: bold; padding: 2px 8px;">Down Payment Balance (<t t-esc="values['currency']"/>)</td>
                                <td class="o_main" style="font-size: 1.5em; font-weight: bold; padding: 2px 8px;"><a href="javascript:void(0);" role="button" >$<t t-esc="values['downpayment_balance']"/></a></td>
                                <td class="o_text" style="font-size: 1.5em; font-weight: bold; padding: 2px 8px;">Total Balance (<t t-esc="values['currency']"/>)</td>
                                <td class="o_main" style="font-size: 1.5em; font-weight: bold; padding: 2px 8px;"><a href="javascript:void(0);" role="button" >$<t t-esc="values['balance']"/></a></td>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    </t>
</templates>
