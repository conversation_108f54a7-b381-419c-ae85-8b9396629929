odoo.define('batch_add_gjp_sale_order_summary_form', function (require) {
'use strict';

const FormController = require('web.FormController');
const viewRegistry = require('web.view_registry');
const FormView = require('web.FormView');
const LotsFormController = FormController.extend({
    custom_events: Object.assign({}, FormController.prototype.custom_events,{
        save_lots: '_save_lots',
    }),
    async _save_lots(ev) {
        var self = this;
        var changes = []
        ev.data.lot_ids.forEach(function (item,index){
            changes.push(
                { operation: 'CREATE', 
                    context: [{
                        default_gjp_sale_order_id: item
                    }]
                }
            )
        })
        var customValuesCommands ={
            line_ids:{
                operation: 'MULTI',
                commands: changes
            },
        }
        self.trigger_up('field_changed', {
            dataPointID: self.handle,
            changes: customValuesCommands,
        });
    },
});
const LotsFormView = FormView.extend({
    config: Object.assign({}, FormView.prototype.config, {
        Controller: LotsFormController,     
    }),
    
});
viewRegistry.add('batch_add_gjp_sale_order_summary_form', LotsFormView);
return LotsFormController;
});
