odoo.define('barcode_field_audit_customer_security_code', function (require) {
    /*
      定义一个widget字段，捕捉key enter事件
    
    */
    "use strict";
    var fieldRegistry = require('web.field_registry');
    var FieldChar = require('web.basic_fields').FieldChar;
    var CustomFieldChar = FieldChar.extend({ 
        _onKeydown: async function (ev) {
            if (ev.which === $.ui.keyCode.ENTER) {
                var self=this;
                var res_id = self.res_id
                var delivery_order_name = $("input.barcode_field_audit_delivery").val()
                var sale_order = $("input.barcode_field_audit_so_number").val()
                var customer_security_code = $("input.audit_customer_security_code").val()
                var error_tips = ''
                var elementId = this.attrs.next_widget;
                $("span.galaxy_success").text('')
                self._rpc({
                    model: 'galaxy.audit.scan',
                    method: 'action_check_customer_security_code',
                    args: [res_id, delivery_order_name, sale_order, customer_security_code],
                }).then(function (result) {
                    error_tips = result
                    if(error_tips == ''){
                        if (elementId != undefined) {
                            $("input."+ elementId).select()
                        }
                    }
                    else{
                        var audio = new Audio('/galaxy_lot_r2/static/src/audio/error.mp3')
                        $("span.customer_security_code_scan_tips").text(error_tips)
                        $("input.audit_customer_security_code").val('')
                        audio.play()
                    }
                })
            }
        },
    });
    fieldRegistry.add('audit_customer_security_code', CustomFieldChar);
});