odoo.define('galaxy_batch_add_gjp_sale_orders',function(require){
    'use strict';
    var ListRender = require('web.ListRenderer');
    var ListView = require('web.ListView');
    var viewRegistry = require('web.view_registry');
    var Dialog = require('web.Dialog');
    var core = require('web.core')
    var _t = core._t
    var DataListRender = ListRender.extend({
        _onAddRecord: function (ev) {
            // we don't want the browser to navigate to a the # url
            ev.preventDefault();
    
            // we don't want the click to cause other effects, such as unselecting
            // the row that we are creating, because it counts as a click on a tr
            ev.stopPropagation();
            // but we do want to unselect current row
            var self = this;
            this.unselectRow().then(function () {
                var customer_id = self.__parentedParent.recordData.customer_id.res_id
                var res_id = self.__parentedParent.recordData.id
                var gjp_order_line_ids = self.__parentedParent.recordData.line_ids
                var context = ev.currentTarget.dataset.context
                if(!customer_id){
                    Dialog.alert(self,_t('Please select customer firstly'),{confirm_callback:function(){return}})
                    return
                }
                if(!res_id){
                    res_id=0
                }
                if(context){
                    context = JSON.parse(context.replace(/'/g, '"'))
                }
                var selected_gjp_order_ids =[]
                if(context && context.add_gjp_sale_order_batch){
                    gjp_order_line_ids.data.forEach(function (item,index){
                        selected_gjp_order_ids.push(item.data.gjp_sale_order_id.res_id)
                    })
                    self.do_action('galaxy_account_bank_statement_r2.action_batch_get_gjp_sale_orders',{
                        additional_context:{'customer_id':customer_id,
                                            'settlement_res_id':res_id,
                                            'selected_gjp_order_ids':selected_gjp_order_ids,
                                            },
                        on_close: function (result) {
                            if(!result || !result.lot_ids) return;
                            var LotsCount = result.lot_ids.length
                            if(LotsCount > 0){
                                self.trigger_up('save_lots',{lot_ids:result.lot_ids})
                            }
                        }   
                    })
                }else{
                    //下面是原生的按行添加记录的代码
                    self.trigger_up('add_record', {context: ev.currentTarget.dataset.context && [ev.currentTarget.dataset.context]});
                }
            })
        },
    });

    var DataListView = ListView.extend({
        config: _.extend({}, ListView.prototype.config, {
            Renderer: DataListRender,
        }),
    });
    viewRegistry.add('galaxy_batch_add_gjp_sale_orders', DataListView);
    return DataListRender;
});

