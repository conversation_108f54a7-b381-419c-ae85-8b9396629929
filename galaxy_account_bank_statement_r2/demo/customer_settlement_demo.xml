<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Demo Customer Settlement -->
        <record id="demo_customer_settlement_1" model="customer.settlement">
            <field name="name">ST001</field>
            <field name="state">draft</field>
            <field name="statement_amount">1000.00</field>
            <field name="paid_amount">0.00</field>
            <field name="received_type">advance</field>
            <field name="received_payment_type">bank</field>
            <field name="note">Demo settlement record for testing</field>
        </record>

        <!-- Demo Customer Settlement Line -->
        <record id="demo_customer_settlement_line_1" model="customer.settlement.lines">
            <field name="customer_settlement_id" ref="demo_customer_settlement_1"/>
            <field name="allocate_amount">500.00</field>
            <field name="received_type">advance</field>
            <field name="note">Demo settlement line 1</field>
        </record>

        <!-- Demo Customer Settlement Line 2 -->
        <record id="demo_customer_settlement_line_2" model="customer.settlement.lines">
            <field name="customer_settlement_id" ref="demo_customer_settlement_1"/>
            <field name="allocate_amount">300.00</field>
            <field name="received_type">downpayment</field>
            <field name="note">Demo settlement line 2</field>
        </record>

    </data>
</odoo>
