# -*- coding: utf-8 -*-
import logging
from odoo.exceptions import UserError, ValidationError
from odoo import models, fields, api, _

_logger = logging.getLogger(__name__)


class GalaxyAuditScan(models.TransientModel):

    _inherit = "galaxy.audit.scan"

    customer_security_code = fields.Char(
        string='Delivery Security Code',
        help='This is the 6-digit security code for the customer, used for delivery verification purposes.')
    customer_security_code_scan_tips = fields.Char(string='Tips')

    def action_done(self, delivery_order, so_order, customer_security_code=''):
        """
        出货审核完成的时候，修改管家婆订单状态为3
        """
        check_delivery_verify_refult = self.check_customer_security_code(delivery_order, so_order, customer_security_code)
        if check_delivery_verify_refult:
            raise UserError(check_delivery_verify_refult)
        action, tips = super(GalaxyAuditScan, self).action_done(delivery_order, so_order)
        gjp_sale_order = self.env['gjp.sale.order'].search([('gjp_order_no', '=', so_order),
                                                            ('state', '=', 'paid')])
        if not gjp_sale_order:
            raise UserError(_('GJP Sale Order not found or not paid: %s') % so_order)
        gjp_sale_order.audit_level = 3  # 设置订单状态为3
        return action, tips

    @api.model
    def get_customer_security_code(self, gjp_sale_order_no):
        """
        获取客户安全码
        """
        gjp_sale_order = self.env['gjp.sale.order'].search([('gjp_order_no', '=', gjp_sale_order_no)])
        if not gjp_sale_order:
            raise UserError(_('GJP Sale Order not found: %s') % gjp_sale_order_no)
        if gjp_sale_order.state != 'paid':
            raise ValidationError(_('GJP Sale Order must be in paid state: %s') % gjp_sale_order_no)
        return gjp_sale_order.customer_security_code or ''

    def action_check_customer_security_code(self, delivery_order, so_order, customer_security_code):
        """
        Check customer security code
        """
        return ''

    def check_customer_security_code(self, delivery_order, so_order, customer_security_code):
        """
        Check customer security code
        """
        delivery_order_rec = self.env['galaxy.delivery'].search([('name', '=', delivery_order)])
        if not delivery_order_rec:
            return _('Delivery order not found')
        if not customer_security_code:
            return _('Delivery verify code not setup in sale order')
        if delivery_order_rec.delivery_type in ('xh', 'to_trade_stk'):
            return ''
        audit_customer_security_code = self.get_customer_security_code(so_order)
        if audit_customer_security_code != customer_security_code:
            return _('Delivery verify code does not match') + ' ' + customer_security_code
        return ''
