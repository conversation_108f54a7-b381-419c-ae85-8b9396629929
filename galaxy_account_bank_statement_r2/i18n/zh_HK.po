# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* galaxy_account_bank_statement_r2
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0+e-********\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-26 05:14+0000\n"
"PO-Revision-Date: 2025-06-26 05:14+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: galaxy_account_bank_statement_r2
#: model:mail.template,body_html:galaxy_account_bank_statement_r2.email_template_customer_settlement
msgid ""
"\n"
"                <div style=\"font-family: Arial, sans-serif; background: #f7f7f7; padding: 30px;\">\n"
"                  <div style=\"background: #fff; padding: 30px; border-radius: 8px; max-width: 600px; margin: auto;\">\n"
"                    <h3>Settlement No.<br/><span style=\"font-size: 2em;\">${object.name}</span></h3>\n"
"                    <hr/>\n"
"                    <p>Hello,</p>\n"
"                    <p><b>Please check your settlement on the link below(請點擊鏈接確認結算信息).</b></p>\n"
"                    <p style=\"color: red; font-weight: bold;\">\n"
"                      Don't reply to this email. This email is sent by the system(此郵件是系統自動發送,請勿回復).\n"
"                    </p>\n"
"                    <div style=\"text-align: center; margin: 30px 0;\">\n"
"                      <a href=\"${object.get_portal_url()}\" style=\"background: #8b5c7e; color: #fff; padding: 12px 40px; border-radius: 8px; text-decoration: none; font-size: 1.2em;\">click</a>\n"
"                    </div>\n"
"                    <hr/>\n"
"                    <p>GALAXY TELECOM HK LTD</p>\n"
"                  </div>\n"
"                </div>\n"
"            "
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:mail.template,body_html:galaxy_account_bank_statement_r2.mail_template_sale_order_summary
msgid ""
"\n"
"                <div style=\"font-family: Arial, sans-serif; background: #f7f7f7; padding: 30px;\">\n"
"                <div style=\"background: #fff; padding: 30px; border-radius: 8px; max-width: 600px; margin: auto;\">\n"
"                    <h3>Galaxy 銷售匯總單<br/><span style=\"font-size: 2em;\">${object.name}</span></h3>\n"
"                    <hr/>\n"
"                    <p>尊敬的客戶：</p>\n"
"                    <p style=\"margin-left:2em;\">\n"
"                        附件是您本次準備結算的銷售匯總單明細，如有疑問請聯繫客服經理。\n"
"                    </p>\n"
"                    <p>\n"
"                        Telephone聯繫電話：+852 6288 9822<br/>\n"
"                        Whatsapp：+852 6288 9822\n"
"                    </p>\n"
"                    <hr/>\n"
"                    <p>GALAXY TELECOM HK LTD</p>\n"
"                </div>\n"
"                </div>\n"
"            "
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:mail.template,body_html:galaxy_account_bank_statement_r2.email_template_fund_transfer_done
msgid ""
"\n"
"                <div style=\"font-family: Arial, sans-serif; background: #fff; padding: 30px;\">\n"
"                    <div style=\"max-width: 700px; margin: auto; border: 1px solid #eee; border-radius: 8px; box-shadow: 0 2px 8px #eee;\">\n"
"                        <div style=\"padding: 30px;\">\n"
"                            <div style=\"text-align: center;\">\n"
"                                <h2 style=\"margin: 0 0 10px 0;\">轉帳成功!<br/>Transfer successful!</h2>\n"
"                            </div>\n"
"                            <table style=\"width:100%; margin: 20px 0; font-size: 1.1em;\">\n"
"                                <tr>\n"
"                                    <td style=\"width:60%;\"><b>Payer(轉出人)：</b> ${object.payer_id.display_name}</td>\n"
"                                    <td style=\"width:40%;\"><b>No.(單號)：</b> <span style=\"font-weight:bold;\">${object.name}</span></td>\n"
"                                </tr>\n"
"                                <tr>\n"
"                                    <td><b>Date(日期)：</b> ${object.transfer_date.strftime('%d/%m/%Y') if object.transfer_date else ''}</td>\n"
"                                    <td><b>Payment Method(付款方式)：</b> ${object.get_fund_source_display() or ''}</td>\n"
"                                </tr>\n"
"                            </table>\n"
"                            <hr/>\n"
"                            <table style=\"width:100%; margin: 20px 0; font-size: 1.1em;\">\n"
"                                <tr>\n"
"                                    <td><b>Payee(收款人)：</b> ${object.payee_id.display_name}</td>\n"
"                                </tr>\n"
"                                <tr>\n"
"                                    <td><b>Currency(幣種)：</b> ${object.currency_id.name}</td>\n"
"                                </tr>\n"
"                                <tr>\n"
"                                    <td><b>Amount(金額)：</b> ${'%0.2f' % object.amount}</td>\n"
"                                </tr>\n"
"                                <tr>\n"
"                                    <td><b>Purpose(用途)：</b> ${object.get_fund_to_display() or ''}</td>\n"
"                                </tr>\n"
"                                <tr>\n"
"                                    <td><b>Type(類型)：</b> ${object.get_transfer_type_display() or ''}</td>\n"
"                                </tr>\n"
"                                <tr>\n"
"                                    <td><b>Note(備註)：</b> ${object.note or ''}</td>\n"
"                                </tr>\n"
"                            </table>\n"
"                            <hr/>\n"
"                            <div style=\"margin: 20px 0; font-size: 1em;\">\n"
"                                <p>\n"
"                                    尊敬的客户，當您收到此單說明轉帳已完成，<br/>\n"
"                                    您當前預收餘額為${'%0.2f' % (object.payer_id.advance_payment_balance_amount or 0.0)}，首付款餘額為${'%0.2f' % (object.payer_id.downpayment_balance_amount or 0.0)}，請知悉！<br/>\n"
"                                    Dear customer, Your current pre-collected balance is ${'%0.2f' % (object.payer_id.advance_payment_balance_amount or 0.0)} and the down payment balance is ${'%0.2f' % (object.payer_id.downpayment_balance_amount or 0.0)}. Please be informed!\n"
"                                </p>\n"
"                                <p style=\"color: #888;\">\n"
"                                    Please do not reply to this email. If you need help, contact customer service manager.<br/>\n"
"                                    Telephone聯繫電話: <br/>\n"
"                                    Whatsapp:\n"
"                                </p>\n"
"                            </div>\n"
"                            <hr/>\n"
"                            <div style=\"text-align:center; color:#888; font-size:1em;\">GALAXY TELECOM HK LTD</div>\n"
"                        </div>\n"
"                    </div>\n"
"                </div>\n"
"            "
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.actions.report,print_report_name:galaxy_account_bank_statement_r2.action_customer_settlement_report
msgid ""
"'Customer Settlement %s-%s' % (object.payer_id.display_name,object.name)"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.actions.report,print_report_name:galaxy_account_bank_statement_r2.action_report_delivery_order
msgid "'Delivery Order - %s' % (object.name)"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.actions.report,print_report_name:galaxy_account_bank_statement_r2.action_fund_transfer_order_report
msgid ""
"'Fund Transfer Order %s-%s' % (object.payer_id.display_name,object.name)"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.actions.report,print_report_name:galaxy_account_bank_statement_r2.action_report_gjp_order_invoice
msgid "'Invoice - %s' % (object.name)"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.actions.report,print_report_name:galaxy_account_bank_statement_r2.action_report_sale_order_summary
msgid ""
"'Sale Order Summary %s-%s' % (object.customer_id.display_name,object.name)"
msgstr "'銷售匯總單 %s-%s' % (object.name,object.customer_id.display_name)"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_settlement_page
msgid "(總計"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_customer_settlement_template
msgid "<b>現金交易收取交易額0.1%手續費</b>"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_customer_settlement_template
msgid "<b>總金額</b>"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.gjp_order_invoice_report
msgid "<font color=\"black\">单价</font>"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.gjp_order_invoice_report
msgid "<font color=\"black\">商品名称</font>"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.gjp_order_invoice_report
msgid "<font color=\"black\">备注</font>"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.gjp_order_invoice_report
msgid "<font color=\"black\">数量</font>"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.gjp_order_invoice_report
msgid "<font color=\"black\">行摘要</font>"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.gjp_order_invoice_report
msgid "<font color=\"black\">金额</font>"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_settlement_page
msgid "<i class=\"fa fa-check\"/> Accept &amp; Sign"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_my_settlements
msgid "<i class=\"fa fa-fw fa-check\"/> Audited"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_my_settlements
msgid "<i class=\"fa fa-fw fa-check\"/> Confirmed"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_my_settlements
msgid "<i class=\"fa fa-fw fa-check\"/> Paid"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_my_settlements
msgid "<i class=\"fa fa-fw fa-check\"/> Partial Paid"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_summary_form
msgid ""
"<i title=\"Set Verify Code\" role=\"img\" aria-label=\"Set Verify Code\" "
"class=\"fa fa-lock\"/>"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.customer_settlement_form_view
msgid ""
"<span class=\"o_stat_text\">Customer</span>\n"
"                                    <span class=\"o_stat_text\">Preview</span>"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.gjp_order_invoice_report
msgid ""
"<span style=\"font-size: 22px; font-weight: bold; margin-right: 50px;\">CUSTOMERS: _________________</span>\n"
"                                            <span style=\"font-size: 22px; font-weight: bold;\">COMPANY CHOP:________________________</span>"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.gjp_order_invoice_report
msgid ""
"<span>REMARKS : GOODS SOLD ARE NOT RETURNABLE </span><br/>\n"
"                                <span>備註: 所有售出貨品恕不退換</span>"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_my_home_menu_settlement
msgid "<span>Settlement</span>"
msgstr "<span>結算單</span>"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_settlement_page
msgid "<strong>Amount(金額)：</strong>"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_settlement_page
msgid "<strong>Currency(幣種)：</strong>"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_settlement_page
msgid "<strong>Date(日期)：</strong>"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_settlement_page
msgid "<strong>Payer(付款人)：</strong>"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_settlement_page
msgid "<strong>Payment Method(付款方式)：</strong>"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_settlement_page
msgid "<strong>Swift Code：</strong>DSBAHKHHXXX"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_settlement_page
msgid ""
"<strong>Thank You!</strong><br/>\n"
"                        Your settlement has been signed."
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_settlement_page
msgid "<strong>This settlement has been canceled.</strong>"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_settlement_page
msgid "<strong>公司名稱：</strong>銀河電訊(香港)有限公司"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_settlement_page
msgid "<strong>分行編號：</strong>774"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_settlement_page
msgid "<strong>現金付款需增加手續費0.1%，四捨五入取整數</strong>"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_settlement_page
msgid "<strong>銀行帳號（多幣種戶口）：</strong>040-774-012-45050-3"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_settlement_page
msgid "<strong>銀行戶名：</strong>GALAXY TELECOM (HONG KONG) LIMITED"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_settlement_page
msgid "<strong>銀行編號：</strong>040"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_settlement_page
msgid "<strong>開戶銀行：</strong>大新銀行"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#. openerp-web
#: code:addons/galaxy_account_bank_statement_r2/static/src/js/portal_settlement_signature.js:0
#, python-format
msgid "Accept & Sign"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__access_warning
msgid "Access warning"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__message_needaction
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__message_needaction
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__message_needaction
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__message_needaction
msgid "Action Needed"
msgstr "需要採取行動"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__activity_ids
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__activity_ids
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__activity_ids
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__activity_ids
msgid "Activities"
msgstr "活動"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__activity_exception_decoration
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__activity_exception_decoration
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__activity_exception_decoration
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "活動異常裝飾"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__activity_state
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__activity_state
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__activity_state
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__activity_state
msgid "Activity State"
msgstr "活動狀態"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__activity_type_icon
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__activity_type_icon
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__activity_type_icon
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__activity_type_icon
msgid "Activity Type Icon"
msgstr "活動類型圖示"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_summary_form
msgid "Add Lines"
msgstr "批量添加"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_summary_form
msgid "Add a line"
msgstr "添加單行"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.actions.act_window,help:galaxy_account_bank_statement_r2.fund_transfer_order_action
msgid ""
"Add fund transfer orders to keep track of all the fund transfer orders."
msgstr "新增互轉單，以便追蹤互轉單。"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.customer_settlement_form_view
msgid "Add notes here..."
msgstr "在此處新增註釋..."

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_advance_payment_history__funding_source__advance
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_credit_history__funding_source__advance
msgid "Advance"
msgstr "預收"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__advance_amount
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_credit_history__advance_amount
msgid "Advance Amount"
msgstr "預收金額"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__advance_balance_amount
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_credit_history__advance_balance_amount
msgid "Advance Balance"
msgstr "預收余額"

#. module: galaxy_account_bank_statement_r2
#. openerp-web
#: code:addons/galaxy_account_bank_statement_r2/static/src/xml/payment_history_dashboard.xml:0
#, python-format
msgid "Advance Balance ("
msgstr "預收余額 ("

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement_lines__advance_payment_id
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_advance_payment_history__fund_type__advance
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_credit_history__fund_type__advance
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_settlement__received_payment_type__advance
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_settlement_lines__received_type__advance
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__fund_transfer_order__fund_source__advance
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.customer_settlement_search_view
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_customer_advance_payment_history_search
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_customer_credit_history_search
msgid "Advance Payment"
msgstr "預收款"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_res_partner__advance_payment_balance_amount
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_res_users__advance_payment_balance_amount
msgid "Advance Payment Balance"
msgstr "預收款余額"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__advance_payment_balance_amount
msgid "Advance Payment Balance Amount"
msgstr "預收余額"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__advance_payment_counts
msgid "Advance Payment Count"
msgstr "收款單數量"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__advance_payment_counts
msgid "Advance Payment Counts"
msgstr "預收數量"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_galaxy_account_bank_merged_statement__advance_payment_line_ids
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_galaxy_account_merged_bank_statement_form
msgid "Advance Payment Lines"
msgstr "預收行"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/customer_settlement.py:0
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__advance_payments_ids
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__advance_payment_ids
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.customer_settlement_form_view
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_summary_form
#, python-format
msgid "Advance Payments"
msgstr "收款單"

#. module: galaxy_account_bank_statement_r2
#: model:res.groups,name:galaxy_account_bank_statement_r2.group_advance_payment_account
msgid "Advance Paymnent Account"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:res.groups,name:galaxy_account_bank_statement_r2.group_advance_payment_admin
msgid "Advance Paymnent Admin"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:res.groups,name:galaxy_account_bank_statement_r2.group_advance_payment_sale
msgid "Advance Paymnent Sale"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.ui.menu,name:galaxy_account_bank_statement_r2.menu_action_account_customer_advance_received_for_sales_action
#: model:ir.ui.menu,name:galaxy_account_bank_statement_r2.menu_action_account_customer_advance_received_for_sales_action_account
msgid "Advance Receipt"
msgstr "收款單"

#. module: galaxy_account_bank_statement_r2
#: model:ir.ui.menu,name:galaxy_account_bank_statement_r2.menu_action_account_customer_advance_received_for_sales_statements_action_account
msgid "Advance Receipt Allocation"
msgstr "預收分配"

#. module: galaxy_account_bank_statement_r2
#: model:ir.ui.menu,name:galaxy_account_bank_statement_r2.sale_menu_account_customer_advance_received_detail_report_account
msgid "Advance Receipt Details Report"
msgstr "預收詳情報告"

#. module: galaxy_account_bank_statement_r2
#: model:ir.ui.menu,name:galaxy_account_bank_statement_r2.menu_action_account_customer_advance_received_for_account
#: model:ir.ui.menu,name:galaxy_account_bank_statement_r2.menu_action_account_customer_advance_received_for_sales
msgid "Advance Receipt Management"
msgstr "預收管理"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__advance_receipt_number
msgid "Advance Receipt Number"
msgstr "收款單號"

#. module: galaxy_account_bank_statement_r2
#: model:ir.ui.menu,name:galaxy_account_bank_statement_r2.sale_menu_account_customer_advance_received_report
#: model:ir.ui.menu,name:galaxy_account_bank_statement_r2.sale_menu_account_customer_advance_received_report_account
msgid "Advance Receipt Report"
msgstr "預收報告"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_res_partner_advance_report_form
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_res_partner_customer_credit_form
msgid "Advance Receive"
msgstr "預收"

#. module: galaxy_account_bank_statement_r2
#: model:ir.actions.act_window,name:galaxy_account_bank_statement_r2.action_open_res_partner_form
msgid "Advance Receive Report"
msgstr "預收報告"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_settlement__settlement_type__advance
msgid "Advance Settlement"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_res_partner_advance_report_form
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_res_partner_customer_credit_form
msgid "Advance Statement"
msgstr "預收分配"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__advance_payment_ids
msgid "Advance payments linked to this summary"
msgstr "關聯的收款單"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/customer_advance_received.py:0
#, python-format
msgid "All selected bank statements must be in 'Audited' state."
msgstr "所有選中的銀行流水必須處於“已審核”狀態。"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/customer_settlement.py:0
#, python-format
msgid "All statement lines must have the same customer currency."
msgstr "所有行貨幣需一致，請修改！"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_res_partner__allocate_advance_payment_amount
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_res_users__allocate_advance_payment_amount
msgid "Allocate Advance Payment Amount"
msgstr "預收款分配金額"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement_lines__allocate_amount
msgid "Allocate Amount"
msgstr "分配金額"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__allocate_balance_amount
msgid "Allocate Balance Amount"
msgstr "分配餘額"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_res_partner__allocate_credit_amount
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_res_users__allocate_credit_amount
msgid "Allocate Credit Amount"
msgstr "已分配信用額度"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_res_partner__allocate_downpayment_amount
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_res_users__allocate_downpayment_amount
msgid "Allocate Down Payment Amount"
msgstr "首付款分配金額"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__amount
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__amount
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_line__amount
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary_line__amount
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary_line_detail__amount
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_my_settlements
msgid "Amount"
msgstr "應付金額"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_settlement_page
msgid "Amount<br/>金額"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/customer_settlement.py:0
#, python-format
msgid "An advance payment for this customer already exists."
msgstr "該客戶的預付款已存在！"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/customer_advance_received.py:0
#: code:addons/galaxy_account_bank_statement_r2/models/customer_advance_received.py:0
#: code:addons/galaxy_account_bank_statement_r2/models/customer_advance_received.py:0
#, python-format
msgid "An error occurred while importing the Excel file: %s"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/customer_settlement.py:0
#: code:addons/galaxy_account_bank_statement_r2/models/customer_settlement.py:0
#, python-format
msgid "An fund transfer order for this customer already exists."
msgstr "該客戶的互轉單已存在！"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_res_partner__applied_advance_payment_amount
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_res_users__applied_advance_payment_amount
msgid "Applied Advance Payment Amount"
msgstr "已核銷預收款金額"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__applied_amount
msgid "Applied Amount"
msgstr "核銷金額"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_res_partner__applied_downpayment_amount
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_res_users__applied_downpayment_amount
msgid "Applied Down Payment Amount"
msgstr "已核銷首付款金額"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__applied_fund_transfer_amount
msgid "Applied Fund Transfer Amount"
msgstr "已核銷互轉金額"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__applied_fund_transfer_order_ids
msgid "Applied Fund Transfer Orders"
msgstr "已核銷互轉單金額"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.customer_settlement_form_view
msgid "Are you sure to audit?"
msgstr "確認審核嗎"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.customer_settlement_form_view
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_summary_form
msgid "Are you sure to cancel?"
msgstr "確認要取消嗎？"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_summary_form
msgid "Are you sure to sync gjp sale order?"
msgstr "確認同步訂單嗎"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_summary_form
msgid "Are yous sure to confirm?"
msgstr "确认吗？"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__message_attachment_count
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__message_attachment_count
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__message_attachment_count
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__message_attachment_count
msgid "Attachment Count"
msgstr "附件數量"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.customer_settlement_form_view
msgid "Audit"
msgstr "審核"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__audit_level
msgid "Audit Level"
msgstr "審核級別"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_settlement__state__audited
msgid "Audited"
msgstr "已審核"

#. module: galaxy_account_bank_statement_r2
#. openerp-web
#: code:addons/galaxy_account_bank_statement_r2/static/src/xml/credit_history_dashboard.xml:0
#, python-format
msgid "Available Credit ("
msgstr "可用信用額度"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_summary_form
msgid "Back to draft"
msgstr "回退到草稿"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__balance_amount
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__balance_amount
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__balance_amount
msgid "Balance Amount"
msgstr "剩余應付"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_advance_payment_history__funding_source__bank
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_credit_history__funding_source__bank
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_settlement__received_payment_type__bank
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.customer_settlement_search_view
msgid "Bank"
msgstr "銀行"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_settlement_page
msgid "Bank Account Information (銀行帳戶資料)"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.gjp_order_invoice_report
msgid ""
"Bank Account Information (銀行帳戶資料)<br/>\n"
"                                銀行戶名：GALAXY TELECOM (HONG KONG) LIMITED<br/>\n"
"                                公司名稱：銀河電訊(香港)有限公司<br/>\n"
"                                開戶銀行：大新銀行<br/>\n"
"                                銀行帳號（多幣種戶口）：040-774-012-45050-3<br/>\n"
"                                銀行編號：040\t分行編號：774<br/>\n"
"                                Swift Code：DSBAHKHHXXX<br/>\n"
"                                FPS ID : *********<br/>"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_advance_received__bank_statement_line_ids
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.extent_customer_advance_received_form
msgid "Bank Statement Lines"
msgstr "銀行流水行"

#. module: galaxy_account_bank_statement_r2
#: model:ir.ui.menu,name:galaxy_account_bank_statement_r2.menu_galaxy_account_merged_bank_statement_for_sales
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_customer_advance_payment_history_form
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_customer_credit_history_form
msgid "Bank Statements"
msgstr "銀行流水匹配"

#. module: galaxy_account_bank_statement_r2
#: model:ir.actions.act_window,name:galaxy_account_bank_statement_r2.action_batch_get_gjp_sale_orders
msgid "Batch Add GJP Sale Orders"
msgstr "批量添加管家婆訂單"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__business_type
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_credit_history__business_type
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_customer_advance_payment_history_search
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_customer_credit_history_search
msgid "Business Type"
msgstr "業務類型"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_settlement__state__cancel
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__fund_transfer_order__state__cancel
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.customer_settlement_form_view
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.customer_settlement_search_view
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_summary_form
msgid "Cancel"
msgstr "取消"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__gjp_sale_order__state__cancelled
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__gjp_sale_order_summary__state__cancel
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_search
msgid "Cancelled"
msgstr "已取消"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_advance_payment_history__funding_source__cash
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_credit_history__funding_source__cash
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_settlement__received_payment_type__cash
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.customer_settlement_search_view
msgid "Cash"
msgstr "現金"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.advance_payment_report_filter
msgid "Changed This Month"
msgstr "本月有變動"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.advance_payment_report_filter
msgid "Changed This Week"
msgstr "本周有變動"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.advance_payment_report_filter
msgid "Changed Today"
msgstr "今天有變動"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_settlement_page
msgid "Close"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_settlement__state__confirm
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__fund_transfer_order__state__confirm
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.customer_settlement_form_view
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.customer_settlement_search_view
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.fund_transfer_order_form
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_summary_form
msgid "Confirm"
msgstr "確認"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.customer_settlement_form_view
msgid "Confirm send the settlement to the Payer?"
msgstr "確認發送結算單？"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__gjp_sale_order_summary__state__confirm
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.fund_transfer_order_search
msgid "Confirmed"
msgstr "已確認"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model,name:galaxy_account_bank_statement_r2.model_res_partner
msgid "Contact"
msgstr "聯繫人"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/controllers/protal.py:0
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.customer_settlement_search_view
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_my_settlements
#, python-format
msgid "Create Date"
msgstr "創建日期"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.actions.act_window,help:galaxy_account_bank_statement_r2.action_gjp_sale_order
msgid "Create a new GJP Sale Order"
msgstr "建立新的管家婆銷售訂單！"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.actions.act_window,help:galaxy_account_bank_statement_r2.action_open_res_partner_form
msgid "Create new document"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.actions.act_window,help:galaxy_account_bank_statement_r2.action_customer_settlement
msgid "Create your first Customer Settlement!"
msgstr "創建您的第一個客戶結算"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.actions.act_window,help:galaxy_account_bank_statement_r2.action_gjp_sale_order_summary
msgid "Create your first GJP sale order summary"
msgstr "創建銷售匯總單"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.actions.act_window,help:galaxy_account_bank_statement_r2.fund_transfer_order_action
msgid "Create your first fund transfer order"
msgstr "建立您的第一個互轉單"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_customer_advance_payment_history_search
msgid "Created Today"
msgstr "今日創建"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__create_uid
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_credit_history__create_uid
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__create_uid
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement_lines__create_uid
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__create_uid
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__create_uid
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_line__create_uid
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__create_uid
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary_line__create_uid
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary_line_detail__create_uid
msgid "Created by"
msgstr "創建人"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__create_date
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_credit_history__create_date
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__create_date
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement_lines__create_date
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__create_date
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__create_date
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_line__create_date
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__create_date
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary_line__create_date
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary_line_detail__create_date
msgid "Created on"
msgstr "創建時間"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_advance_payment_history__fund_type__credit
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_advance_payment_history__funding_source__credit
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_credit_history__fund_type__credit
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_credit_history__funding_source__credit
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_settlement__received_payment_type__credit
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_settlement_lines__received_type__credit
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__fund_transfer_order__fund_source__credit
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__fund_transfer_order__fund_to__credit
msgid "Credit"
msgstr "信用額度"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_credit_history__credit_amount
msgid "Credit Amount"
msgstr "額度金額"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_credit_history__credit_balance_amount
msgid "Credit Balance"
msgstr "可用额度"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__credit_balance_amount
msgid "Credit Balance Amount"
msgstr "可用額度"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement_lines__customer_currency_id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__currency_id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary_line__currency_id
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.advance_customer_credit_filter
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.advance_payment_report_filter
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.customer_settlement_search_view
msgid "Currency"
msgstr "幣別"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/customer_advance_received.py:0
#: code:addons/galaxy_account_bank_statement_r2/models/customer_advance_received.py:0
#: code:addons/galaxy_account_bank_statement_r2/models/customer_advance_received.py:0
#, python-format
msgid "Currency mismatch for customer %s: expected %s, got %s"
msgstr "客戶幣種不壹致 %s: 應是 %s, 當前是 %s"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__customer_id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_credit_history__customer_id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement_lines__customer_id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__customer_id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__customer_id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary_line__customer_id
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.advance_customer_credit_filter
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.advance_payment_report_filter
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.customer_settlement_search_view
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_customer_advance_payment_history_search
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_search
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_summary_search
msgid "Customer"
msgstr "客戶"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/customer_advance_received.py:0
#: code:addons/galaxy_account_bank_statement_r2/models/customer_advance_received.py:0
#: code:addons/galaxy_account_bank_statement_r2/models/customer_advance_received.py:0
#, python-format
msgid "Customer %s not found."
msgstr "客戶 %s 客戶."

#. module: galaxy_account_bank_statement_r2
#: model:ir.actions.act_window,name:galaxy_account_bank_statement_r2.action_customer_advance_payment_history
#: model:ir.actions.act_window,name:galaxy_account_bank_statement_r2.action_customer_advance_payment_normal_history
msgid "Customer Advance Payment Detail"
msgstr "預收明細表"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model,name:galaxy_account_bank_statement_r2.model_customer_advance_payment_history
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_customer_advance_payment_history_form
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_customer_advance_payment_history_normal_tree
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_customer_advance_payment_history_search
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_customer_advance_payment_history_tree
msgid "Customer Advance Payment History"
msgstr "預收記錄"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_customer_advance_payment_history_search
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_customer_credit_history_search
msgid "Customer Advance Payment Line"
msgstr "預收款行"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_res_partner_tree_advance_payment
msgid "Customer Advance Payment Report"
msgstr "客戶預收報告"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement_lines__customer_settlement_id
msgid "Customer Advance Received"
msgstr "預收款"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model,name:galaxy_account_bank_statement_r2.model_customer_advance_received_lines
msgid "Customer Advance Received Lines"
msgstr "預收款行"

#. module: galaxy_account_bank_statement_r2
#: model:ir.actions.act_window,name:galaxy_account_bank_statement_r2.action_customer_credit_history
msgid "Customer Credit Detail"
msgstr "信用額度明細"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_customer_credit_history_form
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_customer_credit_history_search
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_customer_credit_history_tree
msgid "Customer Credit History"
msgstr "信用額度報告"

#. module: galaxy_account_bank_statement_r2
#: model:ir.actions.act_window,name:galaxy_account_bank_statement_r2.action_open_res_partner_form_for_customer_credit
#: model:ir.ui.menu,name:galaxy_account_bank_statement_r2.sale_menu_account_customer_credit_report_account
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_res_partner_tree_customer_credit
msgid "Customer Credit Report"
msgstr "信用額度報告"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__customer_currency_id
msgid "Customer Currency"
msgstr "幣種"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__customer_fund_transfer_ids
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement_lines__customer_fund_transfer_ids
msgid "Customer Fund Transfer"
msgstr "互轉單"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__customer_fund_transfer_counts
msgid "Customer Fund Transfer Counts"
msgstr "互轉單計數"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/customer_settlement.py:0
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__customer_fund_transfer_ids
#, python-format
msgid "Customer Fund Transfers"
msgstr "互轉單"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_settlement__access_url
msgid "Customer Portal URL"
msgstr "門戶網址"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/customer_settlement.py:0
#: model:ir.model,name:galaxy_account_bank_statement_r2.model_customer_received_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__customer_received_statement_id
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_customer_advance_payment_history_search
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_customer_credit_history_search
#, python-format
msgid "Customer Received Statement"
msgstr "預收款分配"

#. module: galaxy_account_bank_statement_r2
#: model:ir.actions.act_window,name:galaxy_account_bank_statement_r2.action_customer_settlement
#: model:ir.actions.report,name:galaxy_account_bank_statement_r2.action_customer_settlement_report
#: model:ir.model,name:galaxy_account_bank_statement_r2.model_customer_settlement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_advance_received__customer_settlement_id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__customer_settlement_id
#: model:ir.ui.menu,name:galaxy_account_bank_statement_r2.menu_action_customer_settlement_for_sales
#: model:ir.ui.menu,name:galaxy_account_bank_statement_r2.menu_action_customer_settlement_for_sales_account
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.customer_settlement_form_view
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.customer_settlement_search_view
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.customer_settlement_tree_view
msgid "Customer Settlement"
msgstr "結算單"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_advance_received_lines__customer_settlement_line_id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__customer_settlement_line_id
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_advance_received_lines__customer_settlement_line_id
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.customer_settlement_lines_form_view
msgid "Customer Settlement Line"
msgstr "結算單行"

#. module: galaxy_account_bank_statement_r2
#: model:ir.actions.act_window,name:galaxy_account_bank_statement_r2.action_customer_settlement_lines
#: model:ir.model,name:galaxy_account_bank_statement_r2.model_customer_settlement_lines
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__customer_advance_settlement_line_ids
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.customer_settlement_lines_tree_view
msgid "Customer Settlement Lines"
msgstr "結算單行"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__customer_advance_settlement_ids
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_summary_form
msgid "Customer Settlements"
msgstr "結算單"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.customer_settlement_form_view
msgid "Customer advance balance will be changed, are you sure?"
msgstr "客戶預收款余額將要變更，確認嗎？"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model,name:galaxy_account_bank_statement_r2.model_customer_advance_received
msgid "Customer advance received"
msgstr "收款單"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model,name:galaxy_account_bank_statement_r2.model_customer_credit_history
msgid "Customer credit History"
msgstr "额度歷史"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_res_partner__gjp_customer_name
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_res_users__gjp_customer_name
msgid "Customer name in GJP system"
msgstr "管家婆客戶名稱"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.actions.act_window,help:galaxy_account_bank_statement_r2.action_customer_settlement
msgid ""
"Customer settlements help you manage payments and allocations from "
"customers."
msgstr "結算單可協助您管理客戶的付款和分配。"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/gjp_sale_order.py:0
#: code:addons/galaxy_account_bank_statement_r2/models/gjp_sale_order.py:0
#, python-format
msgid "Customer with user code %s not found."
msgstr "客戶代碼 %s 未找到."

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_settlement__signed_on
msgid "Date of the signature."
msgstr "簽名日期"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_settlement_page
msgid ""
"Dear customer, you are currently processing the above business. If you agree"
" please click the button to confirm!"
msgstr "請確認"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__delivery_number
msgid "Delivery Number"
msgstr "發貨計劃號"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_galaxy_audit_scan__customer_security_code
msgid "Delivery Security Code"
msgstr "發貨驗證碼"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__customer_security_code
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__customer_security_code
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.galaxy_audit_scan_so_form
msgid "Delivery Verify Code"
msgstr "發貨驗證碼"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/wizard/galaxy_audit_scan.py:0
#, python-format
msgid "Delivery order not found"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/wizard/galaxy_audit_scan.py:0
#, python-format
msgid "Delivery verify code does not match"
msgstr "發貨驗證碼不匹配"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/wizard/galaxy_audit_scan.py:0
#, python-format
msgid "Delivery verify code not setup in sale order"
msgstr "發貨驗證碼在銷售單沒有設置"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__dept
msgid "Department"
msgstr "部門"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_form
msgid "Detail Line"
msgstr "明細行"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__detail_line_ids
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary_line__detail_line_ids
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_form
msgid "Detail Lines"
msgstr "明細"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_summary_form
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_summary_line_form
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_res_partner_tree_advance_payment
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_res_partner_tree_customer_credit
msgid "Details"
msgstr "明細"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__display_name
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_advance_received__display_name
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_advance_received_lines__display_name
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_credit_history__display_name
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_received_statement__display_name
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__display_name
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement_lines__display_name
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__display_name
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_galaxy_account_bank_merged_statement__display_name
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_galaxy_audit_scan__display_name
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_galaxy_delivery__display_name
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_galaxy_gjp_customer__display_name
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__display_name
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_line__display_name
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__display_name
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary_line__display_name
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary_line_detail__display_name
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_report_galaxy_account_bank_statement_r2_gjp_delivery_order__display_name
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_res_partner__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__document_number
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_credit_history__document_number
msgid "Document No."
msgstr "單據號碼"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__document_number
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_credit_history__document_number
msgid "Document Number"
msgstr "單據號碼"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__document_type
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_credit_history__document_type
msgid "Document Type"
msgstr "單據類型"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__fund_transfer_order__state__done
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.fund_transfer_order_form
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.fund_transfer_order_search
msgid "Done"
msgstr "完成"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_advance_payment_history__fund_type__downpayment
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_advance_payment_history__funding_source__downpayment
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_credit_history__fund_type__downpayment
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_credit_history__funding_source__downpayment
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_settlement_lines__received_type__downpayment
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__fund_transfer_order__fund_source__downpayment
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_customer_advance_payment_history_search
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_customer_credit_history_search
msgid "Down Payment"
msgstr "首付款"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__downpayment_amount
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_credit_history__downpayment_amount
msgid "Down Payment Amount"
msgstr "首付款金額"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__downpayment_balance_amount
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_credit_history__downpayment_balance_amount
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_res_partner__downpayment_balance_amount
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_res_users__downpayment_balance_amount
msgid "Down Payment Balance"
msgstr "首付款余額"

#. module: galaxy_account_bank_statement_r2
#. openerp-web
#: code:addons/galaxy_account_bank_statement_r2/static/src/xml/payment_history_dashboard.xml:0
#, python-format
msgid "Down Payment Balance ("
msgstr "首付款余額 ("

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__downpayment_balance_amount
msgid "Down Payment Balance Amount"
msgstr "首付款余額"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_settlement__received_payment_type__downpayment
msgid "Downpayment"
msgstr "首付款"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_settlement__state__draft
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__fund_transfer_order__state__draft
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__gjp_sale_order_summary__state__draft
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.customer_settlement_search_view
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.fund_transfer_order_search
msgid "Draft"
msgstr "草稿"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_advance_payment_history__funding_source__exchange
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_credit_history__funding_source__exchange
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_settlement__received_payment_type__exchange
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.customer_settlement_search_view
msgid "Exchange"
msgstr "結匯"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__fund_transfer_order__transfer_type__external
msgid "External Transfer"
msgstr "他人互轉"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_advance_payment_history__business_type__external_transfer_in
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_credit_history__business_type__external_transfer_in
msgid "External Transfer In"
msgstr "他人互轉-轉入"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_advance_payment_history__business_type__external_transfer_out
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_credit_history__business_type__external_transfer_out
msgid "External Transfer Out"
msgstr "他人互轉-轉出"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/gjp_sale_order.py:0
#: code:addons/galaxy_account_bank_statement_r2/models/gjp_sale_order.py:0
#: code:addons/galaxy_account_bank_statement_r2/models/gjp_sale_order.py:0
#: code:addons/galaxy_account_bank_statement_r2/models/gjp_sale_order_summary.py:0
#, python-format
msgid "Failed to get GJP Sale Orders: %s"
msgstr "獲取管家婆訂單失敗: %s"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__message_follower_ids
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__message_follower_ids
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__message_follower_ids
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__message_follower_ids
msgid "Followers"
msgstr "追蹤者"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__message_channel_ids
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__message_channel_ids
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__message_channel_ids
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__message_channel_ids
msgid "Followers (Channels)"
msgstr "追蹤者（頻道）"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__message_partner_ids
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__message_partner_ids
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__message_partner_ids
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__message_partner_ids
msgid "Followers (Partners)"
msgstr "追隨者（合作夥伴）"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_settlement__activity_type_icon
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_fund_transfer_order__activity_type_icon
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_gjp_sale_order__activity_type_icon
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome 圖示例如fa-任務"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_advance_payment_history__reconciliation_status__fully_applied
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_credit_history__reconciliation_status__fully_applied
msgid "Fully Applied"
msgstr "完全核銷"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__fund_source
msgid "Fund Source"
msgstr "資金來源"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__fund_to
msgid "Fund To"
msgstr "轉給"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_advance_payment_history__document_type__fund_transfer
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_credit_history__document_type__fund_transfer
msgid "Fund Transfer"
msgstr "互轉"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__customer_fund_transfer_counts
msgid "Fund Transfer Count"
msgstr "互轉單數量"

#. module: galaxy_account_bank_statement_r2
#: model:ir.actions.act_window,name:galaxy_account_bank_statement_r2.fund_transfer_order_action
#: model:ir.model,name:galaxy_account_bank_statement_r2.model_fund_transfer_order
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__fund_transfer_order_id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_credit_history__fund_transfer_order_id
#: model:ir.ui.menu,name:galaxy_account_bank_statement_r2.fund_transfer_order_menu_account
#: model:ir.ui.menu,name:galaxy_account_bank_statement_r2.fund_transfer_order_menu_for_sales
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.fund_transfer_order_form
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.fund_transfer_order_tree
msgid "Fund Transfer Order"
msgstr "互轉單"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.fund_transfer_order_search
msgid "Fund Transfer Order Search"
msgstr "搜索互轉單"

#. module: galaxy_account_bank_statement_r2
#: model:ir.actions.report,name:galaxy_account_bank_statement_r2.action_fund_transfer_order_report
msgid "Fund Transfer Ordrer"
msgstr "互轉單"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.customer_settlement_form_view
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_summary_form
msgid "Fund Transfers"
msgstr "互轉單"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__fund_type
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_credit_history__fund_type
msgid "Fund Type"
msgstr "資金用途"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__customer_fund_transfer_ids
msgid "Fund transfers linked to this summary"
msgstr "關聯的互轉單"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__funding_source
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_credit_history__funding_source
msgid "Funding Source"
msgstr "資金來源"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__gjp_customer_name
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_res_partner__gjp_customer_name
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_res_users__gjp_customer_name
msgid "GJP Customer Name"
msgstr "管家婆客戶名稱"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model,name:galaxy_account_bank_statement_r2.model_report_galaxy_account_bank_statement_r2_gjp_delivery_order
msgid "GJP Delivery Report"
msgstr "發貨單"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_summary_search
msgid "GJP Order"
msgstr "管家婆訂單"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__gjp_order_no
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_form
msgid "GJP Order Number"
msgstr "管家婆訂單號"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__gjp_order_sequence
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary_line__gjp_order_sequence
msgid "GJP Order Sequence"
msgstr "管家婆訂單序號"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_advance_received_lines__gjp_order_summary_id
msgid "GJP Order Summary"
msgstr "管家婆訂單匯總"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.constraint,message:galaxy_account_bank_statement_r2.constraint_gjp_sale_order_gjp_order_unique
msgid ""
"GJP Order no must be unique(ignoring case, ignoring leading and trailing "
"spaces)"
msgstr "管家婆訂單號必須唯一"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/gjp_sale_order.py:0
#, python-format
msgid "GJP Order: %s,  Dept %s with user code %s not found."
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model,name:galaxy_account_bank_statement_r2.model_gjp_sale_order
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_line__gjp_sale_order_id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary_line__gjp_sale_order_id
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_form
msgid "GJP Sale Order"
msgstr "管家婆訂單號"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model,name:galaxy_account_bank_statement_r2.model_gjp_sale_order_line
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary_line_detail__gjp_sale_order_line_id
msgid "GJP Sale Order Line"
msgstr "管家婆訂單行"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/customer_settlement.py:0
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__gjp_sale_order_summary_ids
#, python-format
msgid "GJP Sale Order Summaries"
msgstr "管家婆銷售訂單摘要"

#. module: galaxy_account_bank_statement_r2
#: model:ir.actions.act_window,name:galaxy_account_bank_statement_r2.action_gjp_sale_order_summary
#: model:ir.model,name:galaxy_account_bank_statement_r2.model_gjp_sale_order_summary
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_advance_received_lines__gjp_order_summary_id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement_lines__gjp_sale_order_summary_id
#: model:ir.ui.menu,name:galaxy_account_bank_statement_r2.menu_gjp_sale_order_summary
#: model:ir.ui.menu,name:galaxy_account_bank_statement_r2.menu_gjp_sale_order_summary_account
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_summary_form
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_summary_search
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_summary_tree
msgid "GJP Sale Order Summary"
msgstr "銷售匯總單"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__gjp_sale_order_summary_counts
msgid "GJP Sale Order Summary Counts"
msgstr "管家婆銷售訂單總計計數"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model,name:galaxy_account_bank_statement_r2.model_gjp_sale_order_summary_line
#: model:ir.model,name:galaxy_account_bank_statement_r2.model_gjp_sale_order_summary_line_detail
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_summary_line_form
msgid "GJP Sale Order Summary Line"
msgstr "銷售明細"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_summary_line_tree
msgid "GJP Sale Order Summary Lines"
msgstr "銷售明細"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/wizard/galaxy_audit_scan.py:0
#, python-format
msgid "GJP Sale Order must be in paid state: %s"
msgstr "管家婆銷售單必須是已付款狀態: %s"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/galaxy_delivery.py:0
#: code:addons/galaxy_account_bank_statement_r2/wizard/galaxy_audit_scan.py:0
#, python-format
msgid "GJP Sale Order not found or not paid: %s"
msgstr "管家婆銷售單未找到或者未付款: %s"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/wizard/galaxy_audit_scan.py:0
#, python-format
msgid "GJP Sale Order not found: %s"
msgstr "管家婆銷售單未找到或者: %s"

#. module: galaxy_account_bank_statement_r2
#: model:ir.actions.act_window,name:galaxy_account_bank_statement_r2.action_gjp_sale_order
#: model:ir.ui.menu,name:galaxy_account_bank_statement_r2.menu_gjp_sale_order
#: model:ir.ui.menu,name:galaxy_account_bank_statement_r2.menu_gjp_sale_order_account
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_search
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_tree
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_tree_for_select
msgid "GJP Sale Orders"
msgstr "管家婆銷售訂單"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__glot
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__glot
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary_line__glot
msgid "GLOT"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model,name:galaxy_account_bank_statement_r2.model_galaxy_delivery
msgid "Galaxy Delivery"
msgstr "出庫單"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model,name:galaxy_account_bank_statement_r2.model_galaxy_gjp_customer
msgid "Galaxy GJP Customer"
msgstr "管家婆客戶"

#. module: galaxy_account_bank_statement_r2
#: model:mail.template,subject:galaxy_account_bank_statement_r2.email_template_customer_settlement
msgid "Galaxy Settlement Notice（结算通知） ${object.name}"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:mail.template,subject:galaxy_account_bank_statement_r2.email_template_fund_transfer_done
msgid "Galaxy Transfer Successful Notice（轉賬成功通知） ${object.name}"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model,name:galaxy_account_bank_statement_r2.model_galaxy_audit_scan
msgid "Galaxy audit scan"
msgstr "掃碼審核"

#. module: galaxy_account_bank_statement_r2
#: model:mail.template,subject:galaxy_account_bank_statement_r2.mail_template_sale_order_summary
msgid "Galaxy 銷售匯總單 ${object.name}"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_advance_payment_history__business_type__goods_payment
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_credit_history__business_type__goods_payment
msgid "Goods Payment"
msgstr "貨款尾款"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.customer_settlement_search_view
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_customer_advance_payment_history_search
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_customer_credit_history_search
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_search
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_summary_search
msgid "Group By"
msgstr "分組"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.advance_customer_credit_filter
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.advance_payment_report_filter
msgid "Has Balance"
msgstr "有餘額"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.advance_customer_credit_filter
msgid "Has Locked Amount"
msgstr "有鎖定額度"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_settlement_page
msgid "I agree to use"
msgstr "同意使用"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_advance_received__id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_advance_received_lines__id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_credit_history__id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_received_statement__id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement_lines__id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_galaxy_account_bank_merged_statement__id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_galaxy_audit_scan__id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_galaxy_delivery__id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_galaxy_gjp_customer__id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_line__id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary_line__id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary_line_detail__id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_report_galaxy_account_bank_statement_r2_gjp_delivery_order__id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_res_partner__id
msgid "ID"
msgstr "編號"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.gjp_order_invoice_report
msgid "INVOICE"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__activity_exception_icon
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__activity_exception_icon
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__activity_exception_icon
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__activity_exception_icon
msgid "Icon"
msgstr "圖示"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_settlement__activity_exception_icon
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_fund_transfer_order__activity_exception_icon
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_gjp_sale_order__activity_exception_icon
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "表示異常活動的圖示。"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_settlement__message_needaction
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_settlement__message_unread
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_fund_transfer_order__message_needaction
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_fund_transfer_order__message_unread
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_gjp_sale_order__message_needaction
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_gjp_sale_order__message_unread
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__message_needaction
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__message_unread
msgid "If checked, new messages require your attention."
msgstr "如果選中，則表示有新訊息需要您注意。"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_settlement__message_has_error
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_settlement__message_has_sms_error
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_fund_transfer_order__message_has_error
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_fund_transfer_order__message_has_sms_error
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_gjp_sale_order__message_has_error
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_gjp_sale_order__message_has_sms_error
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__message_has_error
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "如果選中，則某些訊息會出現傳遞錯誤。"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__fund_transfer_order__transfer_type__internal
msgid "Internal Transfer"
msgstr "內部轉帳"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_advance_payment_history__business_type__internal_transfer_in
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_credit_history__business_type__internal_transfer_in
msgid "Internal Transfer In"
msgstr "內部轉入"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_advance_payment_history__business_type__internal_transfer_out
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_credit_history__business_type__internal_transfer_out
msgid "Internal Transfer Out"
msgstr "內部轉出"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/customer_advance_received.py:0
#: code:addons/galaxy_account_bank_statement_r2/models/customer_advance_received.py:0
#: code:addons/galaxy_account_bank_statement_r2/models/customer_advance_received.py:0
#, python-format
msgid "Invalid amount for customer %s: %s"
msgstr "客戶錯誤的金額"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/controllers/protal.py:0
#, python-format
msgid "Invalid settlement."
msgstr "錯誤的結算單"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/controllers/protal.py:0
#, python-format
msgid "Invalid signature data."
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__invoice_date
msgid "Invoice Date"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__invoice_number
msgid "Invoice Number"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__invoice_res_partner_id
msgid "Invoice Partner"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__message_is_follower
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__message_is_follower
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__message_is_follower
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__message_is_follower
msgid "Is Follower"
msgstr "是追隨者"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__account_move_id
msgid "Journal Entry"
msgstr "日記帳分錄"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_res_partner__last_advance_payment_changed_date
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_res_users__last_advance_payment_changed_date
msgid "Last Advanche payment Changed Date"
msgstr "上次預收款變動日期"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_advance_payment_history____last_update
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_advance_received____last_update
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_advance_received_lines____last_update
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_credit_history____last_update
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_received_statement____last_update
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement____last_update
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement_lines____last_update
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order____last_update
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_galaxy_account_bank_merged_statement____last_update
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_galaxy_audit_scan____last_update
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_galaxy_delivery____last_update
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_galaxy_gjp_customer____last_update
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order____last_update
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_line____last_update
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary____last_update
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary_line____last_update
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary_line_detail____last_update
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_report_galaxy_account_bank_statement_r2_gjp_delivery_order____last_update
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_res_partner____last_update
msgid "Last Modified on"
msgstr "修改時間"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__write_uid
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_credit_history__write_uid
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__write_uid
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement_lines__write_uid
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__write_uid
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__write_uid
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_line__write_uid
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__write_uid
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary_line__write_uid
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary_line_detail__write_uid
msgid "Last Updated by"
msgstr "最後更新用戶"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__write_date
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_credit_history__write_date
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__write_date
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement_lines__write_date
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__write_date
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__write_date
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_line__write_date
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__write_date
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary_line__write_date
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary_line_detail__write_date
msgid "Last Updated on"
msgstr "最後更新時間"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_line__line_comment
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary_line_detail__line_comment
msgid "Line Comment"
msgstr "行備註"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement_lines__name
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary_line__name
msgid "Line No"
msgstr "行號"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_line__line_summary
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary_line_detail__line_summary
msgid "Line Summary"
msgstr "行摘要"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_summary_form
msgid "Lines"
msgstr "行明細"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__customer_advance_settlement_line_ids
msgid "Lines from customer settlements linked to this summary"
msgstr "存在關聯的客戶結算單"

#. module: galaxy_account_bank_statement_r2
#. openerp-web
#: code:addons/galaxy_account_bank_statement_r2/static/src/xml/credit_history_dashboard.xml:0
#, python-format
msgid "Locked Credit ("
msgstr "鎖定額度 ("

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__locked_customer_credit_amount
msgid "Locked Customer Credit Amount"
msgstr "鎖定額度"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__message_main_attachment_id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__message_main_attachment_id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__message_main_attachment_id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__message_main_attachment_id
msgid "Main Attachment"
msgstr "主要附件"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__make_by
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__make_by
msgid "Make By"
msgstr "製造者"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model,name:galaxy_account_bank_statement_r2.model_galaxy_account_bank_merged_statement
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_customer_advance_payment_history_search
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_customer_credit_history_search
msgid "Merged Bank Statement"
msgstr "銀行流水"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__message_has_error
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__message_has_error
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__message_has_error
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__message_has_error
msgid "Message Delivery error"
msgstr "訊息傳遞錯誤"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__message_ids
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__message_ids
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__message_ids
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__message_ids
msgid "Messages"
msgstr "訊息"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__my_activity_date_deadline
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__my_activity_date_deadline
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__my_activity_date_deadline
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "我的活動截止日期"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_settlement__signed_by
msgid "Name of the person that signed the settlement."
msgstr "結算單簽名客戶"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/customer_settlement.py:0
#: code:addons/galaxy_account_bank_statement_r2/models/customer_settlement.py:0
#: code:addons/galaxy_account_bank_statement_r2/models/customer_settlement.py:0
#: code:addons/galaxy_account_bank_statement_r2/models/customer_settlement.py:0
#: code:addons/galaxy_account_bank_statement_r2/models/fund_transfer_order.py:0
#: code:addons/galaxy_account_bank_statement_r2/models/fund_transfer_order.py:0
#: code:addons/galaxy_account_bank_statement_r2/models/fund_transfer_order.py:0
#: code:addons/galaxy_account_bank_statement_r2/models/fund_transfer_order.py:0
#: code:addons/galaxy_account_bank_statement_r2/models/gjp_sale_order_summary.py:0
#, python-format
msgid "New"
msgstr "新建"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__activity_date_deadline
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__activity_date_deadline
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__activity_date_deadline
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "下一個活動截止日期"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__activity_summary
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__activity_summary
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__activity_summary
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__activity_summary
msgid "Next Activity Summary"
msgstr "下一活動摘要"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__activity_type_id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__activity_type_id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__activity_type_id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__activity_type_id
msgid "Next Activity Type"
msgstr "下一個活動類型"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/customer_advance_received.py:0
#, python-format
msgid "No bank statement lines selected."
msgstr "請選擇銀行流水"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.actions.act_window,help:galaxy_account_bank_statement_r2.action_customer_settlement_lines
msgid "No settlement lines found!"
msgstr "未找到結算行！"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_advance_payment_history__reconciliation_status__not_applied
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_credit_history__reconciliation_status__not_applied
msgid "Not Applied"
msgstr "未核銷"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.customer_settlement_search_view
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_summary_search
msgid "Not Cancel"
msgstr "未取消"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__gjp_sale_order__state__not_paid
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_search
msgid "Not Paid"
msgstr "未付款"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__note
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement_lines__note
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__note
msgid "Note"
msgstr "備註"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_settlement_page
msgid "Note<br/>備註"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__notes
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_credit_history__notes
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__notes
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__note
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary_line__note
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_gjp_sale_order__notes
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__note
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary_line__note
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.customer_settlement_form_view
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.customer_settlement_lines_form_view
msgid "Notes"
msgstr "備註"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__name
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__name
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__name
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.customer_settlement_search_view
msgid "Number"
msgstr "單據編號"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__message_needaction_counter
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__message_needaction_counter
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__message_needaction_counter
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__message_needaction_counter
msgid "Number of Actions"
msgstr "行動次數"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__advance_payment_counts
msgid "Number of advance payments linked to this summary"
msgstr "收款單數量"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__customer_advance_settlement_counts
msgid "Number of customer settlements linked to this summary"
msgstr "結算單數量"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__message_has_error_counter
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__message_has_error_counter
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__message_has_error_counter
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__message_has_error_counter
msgid "Number of errors"
msgstr "錯誤數量"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__customer_fund_transfer_counts
msgid "Number of fund transfers linked to this summary"
msgstr "互轉單數量"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_settlement__message_needaction_counter
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_fund_transfer_order__message_needaction_counter
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_gjp_sale_order__message_needaction_counter
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "需要採取行動的訊息數量"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_settlement__message_has_error_counter
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_fund_transfer_order__message_has_error_counter
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_gjp_sale_order__message_has_error_counter
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "傳遞錯誤的訊息數量"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_settlement__message_unread_counter
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_fund_transfer_order__message_unread_counter
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_gjp_sale_order__message_unread_counter
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__message_unread_counter
msgid "Number of unread messages"
msgstr "未讀訊息數"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__odoo_so_number
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary_line__odoo_so_number
msgid "Odoo SO Number"
msgstr "odoo訂單號"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__require_signature
msgid "Online Signature"
msgstr "在線簽名"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/customer_advance_received.py:0
#, python-format
msgid "Only confirm state can be done."
msgstr "單據不是確認狀態，無法完成"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/customer_settlement.py:0
#, python-format
msgid "Only confirmed settlements can be audited."
msgstr "單據不是確認狀態，無法審核"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/galaxy_account_bank_merged_statement.py:0
#, python-format
msgid "Only confirmed state can back to to-do!"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/galaxy_account_bank_merged_statement.py:0
#, python-format
msgid "Only confirmed statement can be audited."
msgstr "衹有確認的銀行流水可以被讅核"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/customer_advance_received.py:0
#, python-format
msgid "Only done state can be back to confirm."
msgstr "只有已完成的收款單可以回退到確認狀態"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/customer_settlement.py:0
#: code:addons/galaxy_account_bank_statement_r2/models/customer_settlement.py:0
#, python-format
msgid "Only one statement line is allowed for SO Payment settlement type."
msgstr "货款类型的结算单只允许有一行"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/galaxy_account_bank_merged_statement.py:0
#, python-format
msgid "Only to-do statement can be confirmed."
msgstr "衹有草稿的銀行流水可以被確認"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__operator
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_credit_history__operator
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_customer_advance_payment_history_search
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_customer_credit_history_search
msgid "Operator"
msgstr "操作人"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__line_ids
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_summary_form
msgid "Order Lines"
msgstr "行明細"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_summary_search
msgid "Order No."
msgstr "單號"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__order_summary
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary_line__order_summary
msgid "Order Summary"
msgstr "行摘要"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_settlement__state__paid
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__gjp_sale_order__state__paid
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__gjp_sale_order_summary__state__paid
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.customer_settlement_search_view
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_search
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_summary_search
msgid "Paid"
msgstr "已付款"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__paid_amount
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__paid_amount
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__total_paid_amount
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary_line__paid_amount
msgid "Paid Amount"
msgstr "已付金額"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_advance_payment_history__reconciliation_status__partial_applied
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_credit_history__reconciliation_status__partial_applied
msgid "Partial Applied"
msgstr "部分核銷"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_settlement__state__partial_paid
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__gjp_sale_order__state__partial_paid
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__gjp_sale_order_summary__state__partial_paid
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.customer_settlement_search_view
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_search
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_summary_search
msgid "Partial Paid"
msgstr "部分付款"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_customer_advance_payment_history_search
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_customer_credit_history_search
msgid "Partially Reconciled"
msgstr "部分核銷"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__payee_id
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.customer_settlement_search_view
msgid "Payee"
msgstr "收款人"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_settlement_page
msgid "Payee<br/>用款人"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__payee_ids
msgid "Payees"
msgstr "收款人"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__payer_id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement_lines__payer_id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__payer_id
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.customer_settlement_search_view
msgid "Payer"
msgstr "付款人"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__customer_currency_id
msgid "Payer Currency"
msgstr "幣種"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_settlement__settlement_type__sopayment
msgid "Payment Settlement"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__state
msgid "Payment Status"
msgstr "付款狀態"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__received_payment_type
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement_lines__received_payment_type
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.customer_settlement_search_view
msgid "Payment Type"
msgstr "付款方式"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_advance_payment_history__document_type__payment_receipt
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_credit_history__document_type__payment_receipt
msgid "Payment receipt"
msgstr "收款"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/galaxy_account_bank_merged_statement.py:0
#: code:addons/galaxy_account_bank_statement_r2/models/galaxy_account_bank_merged_statement.py:0
#, python-format
msgid "Please Audit the bank statement"
msgstr "請讅核銀行流水"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/customer_settlement.py:0
#, python-format
msgid "Please Check the applied transfer order and cancel related settlement"
msgstr "存在已核銷的互轉單"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/galaxy_account_bank_merged_statement.py:0
#, python-format
msgid "Please select a remittance partner."
msgstr "請選擇付款人"

#. module: galaxy_account_bank_statement_r2
#. openerp-web
#: code:addons/galaxy_account_bank_statement_r2/static/src/js/batch_selec_gjp_sale_orders.js:0
#, python-format
msgid "Please select customer firstly"
msgstr "先選擇客戶"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/galaxy_account_bank_merged_statement.py:0
#, python-format
msgid "Please set the auditor user in the settings."
msgstr "請設置銀行流水讅核人"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__access_url
msgid "Portal Access URL"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.customer_settlement_form_view
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.customer_settlement_tree_view
msgid "Print"
msgstr "打印"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_summary_form
msgid "Print Delivery"
msgstr "打印提貨單"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_form
msgid "Print Invoice"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_line__product_code
msgid "Product Code"
msgstr "產品編碼"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_line__description
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary_line_detail__description
msgid "Product Description"
msgstr "產品名稱"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_settlement_page
msgid "Purpose<br/>用途"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__invoice_qr_img
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__qr_img
msgid "QR Code"
msgstr "二維碼"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_gjp_sale_order__invoice_qr_img
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__qr_img
msgid "QR Code for the GJP Order Number"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__quantity
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_line__quantity
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary_line__quantity
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary_line_detail__quantity
msgid "Quantity"
msgstr "數量"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_advance_payment_history__business_type__receipt
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_credit_history__business_type__receipt
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_customer_advance_payment_history_search
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_customer_credit_history_search
msgid "Receipt"
msgstr "收款"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__received_fee
msgid "Received Fee"
msgstr "手續費"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__lines_received_type
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement_lines__received_type
msgid "Received Type"
msgstr "用途"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/customer_advance_received.py:0
#: code:addons/galaxy_account_bank_statement_r2/models/customer_advance_received.py:0
#: code:addons/galaxy_account_bank_statement_r2/models/customer_advance_received.py:0
#, python-format
msgid "Received type %s is not recognized."
msgstr "不能识别的收款類型 %s"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_customer_advance_payment_history_search
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_customer_credit_history_search
msgid "Reconciled"
msgstr "核銷"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_customer_advance_payment_history_search
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_customer_credit_history_search
msgid "Reconciliation"
msgstr "核銷"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__customer_received_statement_line_id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_credit_history__customer_received_statement_line_id
msgid "Reconciliation Line Number"
msgstr "核銷行號"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__customer_received_statement_id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_credit_history__customer_received_statement_id
msgid "Reconciliation Number"
msgstr "核銷號"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__reconciliation_status
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_credit_history__reconciliation_status
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_customer_advance_payment_history_search
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_customer_credit_history_search
msgid "Reconciliation Status"
msgstr "核销狀態"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/controllers/protal.py:0
#, python-format
msgid "Reference"
msgstr "參考"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_advance_payment_history__document_type__refund
msgid "Refund"
msgstr "退款"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__related_customer_id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_credit_history__related_customer_id
msgid "Related Customer"
msgstr "相關客戶"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__related_sale_summary_order_ids
msgid "Related Sale Summary Orders"
msgstr "關聯的銷售匯總"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_settlement__settlement_type__credit
msgid "Repayment Settlement"
msgstr "還款結算"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_settlement__require_signature
msgid ""
"Request a online signature to the customer in order to confirm settlements "
"automatically."
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__activity_user_id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__activity_user_id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__activity_user_id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__activity_user_id
msgid "Responsible User"
msgstr "責任人"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_advance_payment_history__business_type__reversal
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_credit_history__business_type__reversal
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_customer_advance_payment_history_search
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_customer_credit_history_search
msgid "Reversal"
msgstr "沖銷"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_res_partner__reversal_advance_payment_amount
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_res_partner__reversal_downpayment_amount
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_res_users__reversal_advance_payment_amount
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_res_users__reversal_downpayment_amount
msgid "Reversal Advance Payment Amount"
msgstr "沖銷金額"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__message_has_sms_error
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__message_has_sms_error
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__message_has_sms_error
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__message_has_sms_error
msgid "SMS Delivery error"
msgstr "簡訊發送錯誤"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_advance_payment_history__fund_type__sopayment
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_credit_history__fund_type__sopayment
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__customer_settlement_lines__received_type__sopayment
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_customer_advance_payment_history_search
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_customer_credit_history_search
msgid "SO Payment"
msgstr "貨款尾款"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.order_summary
msgid "SO單號"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.customer_settlement_form_view
msgid "Sale Order Summary"
msgstr "銷售匯總單"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.customer_settlement_search_view
msgid "Sale Summary"
msgstr "銷售匯總"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__sale_summary_order_id
msgid "Sale Summary Order"
msgstr "銷售匯總"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__related_sale_summary_order_ids
msgid "Sale Summary Orders"
msgstr "銷售匯總"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__sale_person
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary_line__sale_person
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_search
msgid "Salesperson"
msgstr "銷售員"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_summary_search
msgid "Search by Order No."
msgstr "按單號搜索"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__access_token
msgid "Security Token"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_tree_for_select
msgid "Select"
msgstr "選擇"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.customer_settlement_form_view
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_summary_form
msgid "Send Email"
msgstr "發送郵件"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_my_settlements
msgid "Settlement #"
msgstr "結算單 #"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__customer_advance_settlement_counts
msgid "Settlement Count"
msgstr "結算單數量"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__customer_advance_received_line_id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_credit_history__customer_advance_received_line_id
msgid "Settlement Line Number"
msgstr "預收结算单行号"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_summary_form
msgid "Settlement Lines"
msgstr "結算單行"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_settlement_page
msgid "Settlement No(結算號):"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__customer_advance_received_id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_credit_history__customer_advance_received_id
msgid "Settlement Number"
msgstr "預收结算单号"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement_lines__customer_settlement_state
msgid "Settlement State"
msgstr "結算單狀態"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__settlement_type
msgid "Settlement Type"
msgstr "結算類型"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/controllers/protal.py:0
#, python-format
msgid "Settlement signed by %s"
msgstr "簽名結算單 %s"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/controllers/protal.py:0
#, python-format
msgid "Settlement viewed by customer %s"
msgstr "查看結算單 %s"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_my_home_menu_settlement
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_my_home_settlement
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_my_settlements
msgid "Settlements"
msgstr "結算單"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__signature
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_settlement_page
msgid "Signature"
msgstr "簽名"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/controllers/protal.py:0
#, python-format
msgid "Signature is missing."
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_settlement__signature
msgid "Signature received through the portal."
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__signed_by
msgid "Signed By"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__signed_on
msgid "Signed On"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__state
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__state
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.customer_settlement_search_view
msgid "State"
msgstr "狀態"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__statement_amount
msgid "Statement Amount"
msgstr "結算金額"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__statement_counts
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_credit_history__statement_counts
msgid "Statement Counts"
msgstr "銀行流水條數"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.customer_settlement_form_view
msgid "Statement Line"
msgstr "聲明行"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__customer_statement_line_ids
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.customer_settlement_form_view
msgid "Statement Lines"
msgstr "聲明行"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/customer_settlement.py:0
#, python-format
msgid "Statement amount should not exceed the locked customer credit amount."
msgstr "結算金額不能大於欠款金額"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/controllers/protal.py:0
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__state
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_my_settlements
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_summary_search
#, python-format
msgid "Status"
msgstr "狀態"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_settlement__activity_state
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_fund_transfer_order__activity_state
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_gjp_sale_order__activity_state
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"基於活動的狀態\n"
"逾期：已超過截止日期\n"
"今天：活動日期是今天\n"
"計劃：未來的活動。"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__stock_name
msgid "Stock Name"
msgstr "倉庫"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary_line__summary_id
msgid "Summary"
msgstr "銷售匯總單"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary_line_detail__summary_line_id
msgid "Summary Line"
msgstr "銷售匯總單行"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_settlement_page
msgid "Summary Sales No.<br/>銷售匯總單號"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_customer_settlement_template
msgid "Swift Code :DSBAHKHHXXX"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_form
msgid "Sync GJP Order"
msgstr "同步管家婆訂單"

#. module: galaxy_account_bank_statement_r2
#: model:ir.actions.server,name:galaxy_account_bank_statement_r2.sync_gjp_sale_order_data_ir_actions_server
#: model:ir.cron,cron_name:galaxy_account_bank_statement_r2.sync_gjp_sale_order_data
#: model:ir.cron,name:galaxy_account_bank_statement_r2.sync_gjp_sale_order_data
msgid "Sync GJP Sale Order Data"
msgstr "同步管家婆訂單"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_summary_form
msgid "Sync GJP Sale Orders"
msgstr "同步管家婆訂單"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/customer_settlement.py:0
#, python-format
msgid "The advance payment balance amount is less than the paid amount."
msgstr "預付款余額小於結算金額"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/fund_transfer_order.py:0
#, python-format
msgid ""
"The advance payment is not done yet, please check the advance payment first."
msgstr "收款單還未完成，請先完成收款單"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_settlement__advance_receipt_number
msgid "The advance receipt number for this settlement."
msgstr "結算單關聯的收款單"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/customer_settlement.py:0
#: code:addons/galaxy_account_bank_statement_r2/models/customer_settlement.py:0
#, python-format
msgid ""
"The allocated amount cannot exceed the unsettled amount of the GJP Sale "
"Order Summary."
msgstr "分配金额不能大于管家婆訂單未結算金額"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/customer_received_statement.py:0
#, python-format
msgid ""
"The allocated amount is greater than the balance amount of the advance "
"received."
msgstr "分配金額大於客戶可用余額，請修改"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/fund_transfer_order.py:0
#, python-format
msgid ""
"The amount of the fund transfer order is greater than the balance amount of "
"the advance payment."
msgstr "轉帳金額大於可用預付款餘額，請修改"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/fund_transfer_order.py:0
#: code:addons/galaxy_account_bank_statement_r2/models/fund_transfer_order.py:0
#: code:addons/galaxy_account_bank_statement_r2/models/fund_transfer_order.py:0
#, python-format
msgid "The applied fund_transfer_amount is not correct."
msgstr "核銷金額不正確"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_settlement__advance_payment_balance_amount
msgid "The balance amount of advance payment for the payer."
msgstr "付款人預付款餘額。"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_settlement__credit_balance_amount
msgid "The balance amount of credit amount for the payer."
msgstr "可用額度"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_settlement__downpayment_balance_amount
msgid "The balance amount of down payment for the payer."
msgstr "付款人首付款餘額。"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/customer_received_statement.py:0
#, python-format
msgid "The balance amount shoudl be 0"
msgstr "余額應該是0"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/galaxy_account_bank_merged_statement.py:0
#, python-format
msgid "The currency of advance payment line should be same as bank statement."
msgstr "收款單幣種必須跟銀行轉賬幣種相同"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/customer_settlement.py:0
#, python-format
msgid ""
"The customer in the statement line must be the same as the payer for Credit "
"settlement."
msgstr "結算行客戶必須跟付款人相同"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/customer_settlement.py:0
#, python-format
msgid ""
"The customer in the statement line must match the customer in the GJP Sale "
"Order Summary."
msgstr "報表行中的客戶必須與管家婆銷售訂單摘要中的客戶相符。"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_settlement__received_fee
msgid "The fee received for this settlement."
msgstr "現金手續費"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/fund_transfer_order.py:0
#, python-format
msgid "The fund transfer order must be confirmed before it can be done."
msgstr "完成訂單之前先要確認訂單"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/fund_transfer_order.py:0
#, python-format
msgid ""
"The fund transfer order must be in draft state before it can be confirmed."
msgstr "只有草稿的互轉單可以被確認"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/customer_advance_received.py:0
#, python-format
msgid "The payer balance is not enough."
msgstr "客戶余額不夠"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/customer_settlement.py:0
#, python-format
msgid ""
"The payer cannot be the same as the customer in the statement line with the "
"same received type."
msgstr "相同的客戶，互轉類型不能相同"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/customer_settlement.py:0
#, python-format
msgid ""
"The payer must be the same as the customer in the statement line when the "
"payment type is downpayment."
msgstr "使用首付款的時候，付款人和客戶必須相同"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/customer_settlement.py:0
#, python-format
msgid ""
"The received type of the statement line can not be SO Payment for "
"Downpayment and Advance settlement."
msgstr "結算類型是預付款的結算單不能結算貨款"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/customer_settlement.py:0
#, python-format
msgid ""
"The received type of the statement line must be Credit for Credit "
"settlement."
msgstr "不能用付款方式是信用額度類型的結算單還款"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/customer_settlement.py:0
#, python-format
msgid ""
"The received type of the statement line must be SO Payment for SO Payment "
"settlement."
msgstr "結算類型是貨款的結算單只能結算貨款"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/controllers/protal.py:0
#, python-format
msgid "The settlement is not in a state requiring customer signature."
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/customer_settlement.py:0
#: code:addons/galaxy_account_bank_statement_r2/models/customer_settlement.py:0
#, python-format
msgid ""
"The settlement type cannot be Credit when the received payment type is "
"Credit."
msgstr "付款類型是信用額度的結算單不能選擇還款的結算類型"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/customer_advance_received.py:0
#: code:addons/galaxy_account_bank_statement_r2/models/customer_advance_received.py:0
#: code:addons/galaxy_account_bank_statement_r2/models/customer_advance_received.py:0
#, python-format
msgid "The specified Excel file was not found: %s"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/customer_settlement.py:0
#, python-format
msgid "The statement amount cannot be zero."
msgstr "結算金額必須大於零"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/customer_settlement.py:0
#, python-format
msgid "The statement amount cannot exceed the advance payment balance amount."
msgstr "帳單金額不能超過預收款餘額。"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/customer_settlement.py:0
#, python-format
msgid "The statement amount cannot exceed the credit balance amount."
msgstr "结算金額不能大於可用額度"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/customer_settlement.py:0
#, python-format
msgid "The statement amount cannot exceed the downpayment balance amount."
msgstr "結算金額不能超過首付餘額。"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/customer_settlement.py:0
#, python-format
msgid "The statement amount must equal to the total allocate amount."
msgstr "結算金額必須等於總分配金額。"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/customer_advance_received.py:0
#, python-format
msgid "The statement has been confirmed, you cannot cancel it."
msgstr "結算單已經被確認，無法取消！"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/fund_transfer_order.py:0
#, python-format
msgid "The tansfer out partner and transfer in partner have different account"
msgstr "轉出合夥人和轉入合夥人有不同的帳戶"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/galaxy_account_bank_merged_statement.py:0
#: code:addons/galaxy_account_bank_statement_r2/models/galaxy_account_bank_merged_statement.py:0
#, python-format
msgid "The total advance payment amount should same as net deposit amount."
msgstr "銀行轉賬金額必須與選擇的收款單金額一致"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/customer_settlement.py:0
#, python-format
msgid ""
"The total allocation amount cannot exceed the advance payment balance "
"amount."
msgstr "分配金額不能大於結算金額"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/customer_settlement.py:0
#, python-format
msgid ""
"The total allocation amount cannot exceed the down payment balance amount."
msgstr "分配金額不能大於首付款可用余額"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_settlement__total_amount
msgid ""
"The total amount of the settlement, including statement amount and received "
"fee."
msgstr "結算單總額"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_settlement__settlement_type
msgid "The type of settlement, either normal or advance payment."
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_settlement__make_by
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_fund_transfer_order__make_by
msgid "The user who created the AD Payment."
msgstr "建立 AD 付款的用戶。"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_my_settlements
msgid "There are currently no settlements for your account."
msgstr "沒有結算單"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.customer_settlement_search_view
msgid "This Month"
msgstr "本月"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.customer_settlement_search_view
msgid "This Week"
msgstr "本周"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_settlement__gjp_sale_order_summary_ids
msgid ""
"This field contains the GJP Sale Order Summaries related to this settlement."
msgstr "此欄位包含與此結算相關的 GJP 銷售訂單摘要。"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_settlement_lines__advance_payment_id
msgid ""
"This field contains the advance payment related to this settlement line."
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_settlement__advance_payments_ids
msgid "This field contains the advance payments related to this settlement."
msgstr "此欄位包含與本次結算相關的預付款。"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_settlement__payee_ids
msgid "This field contains the payees related to this settlement."
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_gjp_sale_order__invoice_date
msgid ""
"This field is used to store the invoice date associated with the GJP Sale "
"Order."
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_gjp_sale_order__invoice_number
msgid ""
"This field is used to store the invoice number associated with the GJP Sale "
"Order."
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_gjp_sale_order__invoice_res_partner_id
msgid ""
"This field is used to store the partner associated with the invoice for the "
"GJP Sale Order."
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_gjp_sale_order__stock_name
msgid ""
"This field is used to store the stock name associated with the GJP Sale "
"Order."
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_galaxy_audit_scan__customer_security_code
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_gjp_sale_order__customer_security_code
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__customer_security_code
msgid ""
"This is the 6-digit security code for the customer, used for delivery "
"verification purposes."
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_galaxy_audit_scan__customer_security_code_scan_tips
msgid "Tips"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__fund_transfer_order__fund_to__advance
msgid "To Advance Payment"
msgstr "轉預收款"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__fund_transfer_order__fund_to__downpayment
msgid "To Down Payment"
msgstr "轉首付款"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement_r2.selection__fund_transfer_order__fund_to__sopayment
msgid "To SO Payment"
msgstr "轉貨款"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.customer_settlement_search_view
msgid "Today"
msgstr "當天"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__total_advance_balance_amount
msgid "Total Advance Balance Amount"
msgstr "總預收餘額"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_galaxy_account_bank_merged_statement__total_advance_payment_amount
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_res_partner__total_advance_payment_amount
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_res_users__total_advance_payment_amount
msgid "Total Advance Payment Amount"
msgstr "總預收金額"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__total_allocate_amount
msgid "Total Allocate Amount"
msgstr "總分配金額"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__total_amount
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__total_amount
msgid "Total Amount"
msgstr "總金額"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_res_partner__total_advance_payment_balance
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_res_users__total_advance_payment_balance
msgid "Total Balance"
msgstr "總餘額"

#. module: galaxy_account_bank_statement_r2
#. openerp-web
#: code:addons/galaxy_account_bank_statement_r2/static/src/xml/payment_history_dashboard.xml:0
#, python-format
msgid "Total Balance ("
msgstr "總余額 ("

#. module: galaxy_account_bank_statement_r2
#. openerp-web
#: code:addons/galaxy_account_bank_statement_r2/static/src/xml/credit_history_dashboard.xml:0
#, python-format
msgid "Total Credit ("
msgstr "總額度 ("

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_res_partner__total_downpayment_amount
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_res_users__total_downpayment_amount
msgid "Total Down Payment Amount"
msgstr "總首付金額"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__total_quantity
msgid "Total Quantity"
msgstr "數量"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__transaction_amount
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_credit_history__transaction_amount
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__transaction_amount
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_credit_history__transaction_amount
msgid "Transaction Amount"
msgstr "金額"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__transaction_date
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_credit_history__transaction_date
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_customer_advance_payment_history_search
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_customer_credit_history_search
msgid "Transaction Date"
msgstr "交易日期"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__merged_bank_statement_ids
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_credit_history__merged_bank_statement_ids
msgid "Transaction Number"
msgstr "流水號"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__transfer_date
msgid "Transfer Date"
msgstr "轉帳日期"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__account_id
msgid "Transfer Out Account"
msgstr "轉出帳戶"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__transfer_type
msgid "Transfer Type"
msgstr "轉帳類型"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/fund_transfer_order.py:0
#, python-format
msgid "Transfer out partner has not enough amount"
msgstr "付款人餘額不足"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_res_partner__transfered_out_advance_payment_amount
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_res_users__transfered_out_advance_payment_amount
msgid "Transfered Out Advance Payment Amount"
msgstr "預收款轉出金額"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_res_partner__transfered_out_downpayment_amount
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_res_users__transfered_out_downpayment_amount
msgid "Transfered Out Down Payment Amount"
msgstr "首付款轉出金額"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_settlement__activity_exception_decoration
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_fund_transfer_order__activity_exception_decoration
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_gjp_sale_order__activity_exception_decoration
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "異常活動記錄的類型。"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_line__price_unit
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary_line_detail__price_unit
msgid "Unit Price"
msgstr "單價"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_gjp_sale_order_summary_search
msgid "Unpaid"
msgstr "未付款"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__message_unread
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__message_unread
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__message_unread
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__message_unread
msgid "Unread Messages"
msgstr "未讀訊息"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__message_unread_counter
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__message_unread_counter
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__message_unread_counter
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__message_unread_counter
msgid "Unread Messages Counter"
msgstr "未讀訊息計數器"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_customer_advance_payment_history_search
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_customer_credit_history_search
msgid "Unreconciled"
msgstr "未核銷"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement_lines__sale_order_unsettlement_amount
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__total_unsettlement_amount
msgid "Unsettlement Amount"
msgstr "未結算金額"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.view_galaxy_account_merged_bank_statement_form
msgid "Update Advance Payments"
msgstr "更新預付款"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__user_code
msgid "User Code"
msgstr "客戶編碼"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_settlement_page
msgid "Validate Settlement"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_customer_settlement__website_message_ids
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_fund_transfer_order__website_message_ids
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order__website_message_ids
#: model:ir.model.fields,field_description:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__website_message_ids
msgid "Website Messages"
msgstr "網站訊息"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_settlement__website_message_ids
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_fund_transfer_order__website_message_ids
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_gjp_sale_order__website_message_ids
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_gjp_sale_order_summary__website_message_ids
msgid "Website communication history"
msgstr "網站通訊歷史記錄"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/customer_advance_received.py:0
#, python-format
msgid "You need cancel the customer settlement firstly."
msgstr "必須先取消結算單"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/customer_settlement.py:0
#, python-format
msgid "You need input Sale Summary order for SO Payment line"
msgstr "貨款結算行必須輸入銷售匯總單"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/customer_advance_received.py:0
#, python-format
msgid "You need remove link to bank statment firstly."
msgstr "需要選移除與銀行流水的關聯"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/galaxy_account_bank_merged_statement.py:0
#, python-format
msgid "advance payment line are not confirm or paid, please check."
msgstr "預收行不是確認或者已到賬狀態"

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/galaxy_account_bank_merged_statement.py:0
#, python-format
msgid "advance payment line are not confirm, please check."
msgstr "預收行不是確認状态"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_settlement_page
msgid "close"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_settlement_page
msgid "pay"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/fund_transfer_order.py:0
#, python-format
msgid "please set journal first"
msgstr "請先設定日誌"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_settlement_page
msgid "to help"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: code:addons/galaxy_account_bank_statement_r2/models/customer_settlement.py:0
#: code:addons/galaxy_account_bank_statement_r2/models/fund_transfer_order.py:0
#, python-format
msgid "you can only delete draft record"
msgstr "只有草稿的單據可以刪除"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__business_type
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_credit_history__business_type
msgid "业务类型"
msgstr "業務類型"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__transaction_date
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_credit_history__transaction_date
msgid "交易日期"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.gjp_delivery_order
msgid "仓库:"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_customer_settlement_template
msgid "付款人："
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_customer_settlement_template
msgid "付款方式:"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_customer_settlement_template
msgid "信用額度"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_credit_history__credit_amount
msgid "信用额度金额"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__notes
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_credit_history__notes
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_customer_settlement_template
msgid "備註"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.order_summary
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_fund_transfer_order_document
msgid "備註:"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_customer_settlement_template
msgid "公司名稱：銀河電訊(香港)有限公司"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_customer_settlement_template
msgid "分行编號：774"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.order_summary
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_customer_settlement_template
msgid "創建日期:"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.gjp_delivery_order
msgid "单价"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.gjp_delivery_order
msgid "单号:"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_customer_settlement_template
msgid "单號："
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.actions.report,name:galaxy_account_bank_statement_r2.action_report_gjp_order_invoice
msgid "发票"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.gjp_delivery_order
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.gjp_order_invoice_report
msgid "合计:"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_settlement_page
msgid "含手续费总额："
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.gjp_delivery_order
msgid "商品名称"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.gjp_delivery_order
msgid "备注"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_settlement_page
msgid ""
"如您为转账支付，请務必在轉帳備註欄填寫此編號！<br/>\n"
"                                        Please include this number in the transfer remarks. Thank you!"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__customer_id
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__related_customer_id
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_credit_history__customer_id
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_credit_history__related_customer_id
msgid "客户"
msgstr "客戶"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.gjp_delivery_order
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.gjp_order_invoice_report
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.order_summary
msgid "客户:"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__customer_advance_received_id
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_credit_history__customer_advance_received_id
msgid "客户预收款"
msgstr "客戶預收款項"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__statement_counts
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_credit_history__statement_counts
msgid "对账单行数"
msgstr "對帳單行數"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_settlement_page
msgid "尊敬的客户，您正在办理以上业务，如同意操作，请点击以下按钮进行确认！"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_customer_settlement_template
msgid "小計"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_fund_transfer_order_document
msgid "幣種:"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.order_summary
msgid "應付金額："
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_settlement_page
msgid ""
"我們已收到您的確認，请及时安排付款。收到款项后，我们将有郵件通知您，感謝您的配合！<br/>\n"
"                                    Payment confirmation received. Please proceed with payment. We'll email you once received. Thank you."
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_settlement_page
msgid "我同意使用"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.actions.report,name:galaxy_account_bank_statement_r2.action_report_delivery_order
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.gjp_delivery_order
msgid "提貨單"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.gjp_delivery_order
msgid "摘要:"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__operator
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_credit_history__operator
msgid "操作人"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_settlement_page
msgid "支付"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_fund_transfer_order_document
msgid "收款人："
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.gjp_delivery_order
msgid "数量"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.order_summary
msgid "數量"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.gjp_delivery_order
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.gjp_order_invoice_report
msgid "日期:"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__customer_received_statement_id
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_credit_history__customer_received_statement_id
msgid "核銷號"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__customer_received_statement_line_id
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_credit_history__customer_received_statement_line_id
msgid "核銷行號"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__reconciliation_status
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_credit_history__reconciliation_status
msgid "核销狀態"
msgstr "核狀態銷"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.portal_settlement_page
msgid "现金付款手续费："
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_customer_settlement_template
msgid "現金"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_customer_settlement_template
msgid "用款人"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_customer_settlement_template
msgid "用途"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_fund_transfer_order_document
msgid "用途:"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_fund_transfer_order_document
msgid "當您收到此單說明轉賬已完成，您當前預收余額為："
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.gjp_order_invoice_report
msgid "發票"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_customer_settlement_template
msgid "結匯"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.order_summary
msgid "結算幣種:"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_customer_settlement_template
msgid "結算類型:"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.order_summary
msgid "總數量："
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_fund_transfer_order_document
msgid "经办人："
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_customer_settlement_template
msgid "结算单"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.gjp_delivery_order
msgid "行摘要"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.order_summary
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_customer_settlement_template
msgid "行號"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_customer_settlement_template
msgid "請您轉賬時務必在備註欄填寫此編號"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_customer_settlement_template
msgid "貨款"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_customer_settlement_template
msgid "貨款尾款"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_fund_transfer_order_document
msgid "資金來源:"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__fund_transfer_order_id
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_credit_history__fund_transfer_order_id
msgid "资金划拨单"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__funding_source
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_credit_history__funding_source
msgid "资金来源"
msgstr "資金來源"

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__fund_type
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_credit_history__fund_type
msgid "资金用途"
msgstr "資金用途"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_fund_transfer_order_document
msgid "轉出人："
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_fund_transfer_order_document
msgid "轉賬單號："
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_fund_transfer_order_document
msgid "轉賬日期:"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_fund_transfer_order_document
msgid "轉賬確認單"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_customer_settlement_template
msgid "還款結算"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.gjp_delivery_order
msgid "部门:"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.order_summary
msgid "金額"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_fund_transfer_order_document
msgid "金額："
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.gjp_delivery_order
msgid "金额"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_customer_settlement_template
msgid "銀行帳號（多幣種戶口）:040-774-012-45050-3"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_customer_settlement_template
msgid "銀行戶名：GALAXY TELECOM (HONG KONG) LIMITED"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_customer_settlement_template
msgid "銀行編號：040"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_customer_settlement_template
msgid "銀行賬戶資料"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_customer_settlement_template
msgid "銀行轉賬"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.actions.report,name:galaxy_account_bank_statement_r2.action_report_sale_order_summary
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.order_summary
msgid "銷售匯總單"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_customer_settlement_template
msgid "銷售匯總單號"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__merged_bank_statement_ids
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_credit_history__merged_bank_statement_ids
msgid "银行对账单"
msgstr "銀行對帳單"

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.gjp_delivery_order
msgid "销售:"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_customer_settlement_template
msgid "開戶銀行：大新銀行"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_customer_settlement_template
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_fund_transfer_order_document
msgid "預付款"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_customer_settlement_template
msgid "預收款"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__customer_advance_received_line_id
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_credit_history__customer_advance_received_line_id
msgid "預收结算单行号"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_customer_settlement_template
msgid "額度還款"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_fund_transfer_order_document
msgid "類型："
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__advance_balance_amount
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__downpayment_balance_amount
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_credit_history__advance_balance_amount
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_credit_history__credit_balance_amount
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_credit_history__downpayment_balance_amount
msgid "预收款余额"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__advance_amount
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_credit_history__advance_amount
msgid "预收款金额"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_customer_settlement_template
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_fund_transfer_order_document
msgid "首付款"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_advance_payment_history__downpayment_amount
#: model:ir.model.fields,help:galaxy_account_bank_statement_r2.field_customer_credit_history__downpayment_amount
msgid "首付款金额"
msgstr ""

#. module: galaxy_account_bank_statement_r2
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement_r2.report_fund_transfer_order_document
msgid "，請知悉。"
msgstr ""
