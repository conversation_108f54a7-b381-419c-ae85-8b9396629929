# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * date_range
#
# Translators:
# <AUTHOR> <EMAIL>, 2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-11-26 03:36+0000\n"
"PO-Revision-Date: 2016-11-26 03:36+0000\n"
"Last-Translator: OCA Transbot <<EMAIL>>, 2016\n"
"Language-Team: Galician (Spain) (https://www.transifex.com/oca/teams/23907/"
"gl_ES/)\n"
"Language: gl_ES\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: date_range
#: code:addons/date_range/models/date_range.py:0
#, python-format
msgid "%s is not a valid range (%s > %s)"
msgstr ""

#. module: date_range
#: code:addons/date_range/models/date_range.py:0
#, python-format
msgid "%s overlaps %s"
msgstr ""

#. module: date_range
#: model_terms:ir.ui.view,arch_db:date_range.date_range_generator_view_form
#: model_terms:ir.ui.view,arch_db:date_range.view_date_range_type_form_view
msgid "&amp;nbsp;"
msgstr ""

#. module: date_range
#: model_terms:ir.ui.view,arch_db:date_range.date_range_generator_view_form
msgid ""
",\n"
"                                or generate"
msgstr ""

#. module: date_range
#: model:ir.model.constraint,message:date_range.constraint_date_range_date_range_uniq
msgid "A date range must be unique per company !"
msgstr ""

#. module: date_range
#: model:ir.model.constraint,message:date_range.constraint_date_range_type_date_range_type_uniq
msgid "A date range type must be unique per company !"
msgstr ""

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range__active
#: model:ir.model.fields,field_description:date_range.field_date_range_type__active
msgid "Active"
msgstr ""

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range_type__allow_overlap
msgid "Allow Overlap"
msgstr ""

#. module: date_range
#: model:ir.actions.server,name:date_range.ir_cron_autocreate_ir_actions_server
#: model:ir.cron,cron_name:date_range.ir_cron_autocreate
#: model:ir.cron,name:date_range.ir_cron_autocreate
msgid "Auto-generate date ranges"
msgstr ""

#. module: date_range
#: model_terms:ir.ui.view,arch_db:date_range.view_date_range_type_form_view
msgid "Auto-generation settings"
msgstr ""

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range_type__autogeneration_count
msgid "Autogeneration Count"
msgstr ""

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range_type__autogeneration_date_start
msgid "Autogeneration Start Date"
msgstr ""

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range_type__autogeneration_unit
msgid "Autogeneration Unit"
msgstr ""

#. module: date_range
#: model_terms:ir.ui.view,arch_db:date_range.date_range_generator_view_form
msgid "Cancel"
msgstr ""

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range__company_id
#: model:ir.model.fields,field_description:date_range.field_date_range_generator__company_id
#: model:ir.model.fields,field_description:date_range.field_date_range_type__company_id
msgid "Company"
msgstr ""

#. module: date_range
#: model_terms:ir.ui.view,arch_db:date_range.view_date_range_type_form_view
msgid "Configuration"
msgstr ""

#. module: date_range
#: model_terms:ir.ui.view,arch_db:date_range.view_date_range_type_form_view
msgid "Create"
msgstr ""

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range__create_uid
#: model:ir.model.fields,field_description:date_range.field_date_range_generator__create_uid
#: model:ir.model.fields,field_description:date_range.field_date_range_type__create_uid
msgid "Created by"
msgstr ""

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range__create_date
#: model:ir.model.fields,field_description:date_range.field_date_range_generator__create_date
#: model:ir.model.fields,field_description:date_range.field_date_range_type__create_date
msgid "Created on"
msgstr ""

#. module: date_range
#: model:ir.model,name:date_range.model_date_range
msgid "Date Range"
msgstr ""

#. module: date_range
#: model:ir.model,name:date_range.model_date_range_generator
msgid "Date Range Generator"
msgstr ""

#. module: date_range
#: model:ir.model,name:date_range.model_date_range_type
#: model_terms:ir.ui.view,arch_db:date_range.view_date_range_type_form_view
msgid "Date Range Type"
msgstr ""

#. module: date_range
#: model:ir.actions.act_window,name:date_range.date_range_type_action
#: model:ir.ui.menu,name:date_range.menu_date_range_type_action
msgid "Date Range Types"
msgstr ""

#. module: date_range
#: model:ir.actions.act_window,name:date_range.date_range_action
#: model:ir.ui.menu,name:date_range.menu_date_range_action
msgid "Date Ranges"
msgstr ""

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range_type__date_ranges_exist
msgid "Date Ranges Exist"
msgstr ""

#. module: date_range
#: model:ir.ui.menu,name:date_range.menu_date_range
msgid "Date ranges"
msgstr ""

#. module: date_range
#: model_terms:ir.ui.view,arch_db:date_range.view_date_range_type_form_view
msgid "Defaults for generating date ranges"
msgstr ""

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range__display_name
#: model:ir.model.fields,field_description:date_range.field_date_range_generator__display_name
#: model:ir.model.fields,field_description:date_range.field_date_range_search_mixin__display_name
#: model:ir.model.fields,field_description:date_range.field_date_range_type__display_name
msgid "Display Name"
msgstr ""

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range_generator__duration_count
#: model:ir.model.fields,field_description:date_range.field_date_range_type__duration_count
msgid "Duration"
msgstr ""

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range__date_end
#: model:ir.model.fields,field_description:date_range.field_date_range_generator__date_end
msgid "End date"
msgstr ""

#. module: date_range
#: model:ir.model.fields,help:date_range.field_date_range_generator__name_expr
#: model:ir.model.fields,help:date_range.field_date_range_type__name_expr
msgid ""
"Evaluated expression. E.g. \"'FY%s' % date_start.strftime('%Y%m%d')\"\n"
"You can use the Date types 'date_end' and 'date_start', as well as the "
"'index' variable."
msgstr ""

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range_search_mixin__date_range_search_id
msgid "Filter by period (technical field)"
msgstr ""

#. module: date_range
#: model:ir.actions.act_window,name:date_range.date_range_generator_action
#: model:ir.ui.menu,name:date_range.menu_date_range_generator_action
#: model_terms:ir.ui.view,arch_db:date_range.date_range_generator_view_form
msgid "Generate Date Ranges"
msgstr ""

#. module: date_range
#: model_terms:ir.ui.view,arch_db:date_range.view_date_range_type_form_view
msgid "Generation"
msgstr ""

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range__id
#: model:ir.model.fields,field_description:date_range.field_date_range_generator__id
#: model:ir.model.fields,field_description:date_range.field_date_range_search_mixin__id
#: model:ir.model.fields,field_description:date_range.field_date_range_type__id
msgid "ID"
msgstr "ID"

#. module: date_range
#: model:ir.model.fields,help:date_range.field_date_range_type__allow_overlap
msgid "If sets date range of same type must not overlap."
msgstr ""

#. module: date_range
#: code:addons/date_range/wizard/date_range_generator.py:0
#, python-format
msgid "Invalid name expression: %s"
msgstr ""

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range__archive_uid
#: model:ir.model.fields,field_description:date_range.field_date_range_type__archive_uid
msgid "Last Archived by"
msgstr ""

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range__archive_date
#: model:ir.model.fields,field_description:date_range.field_date_range_type__archive_date
msgid "Last Archived on"
msgstr ""

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range____last_update
#: model:ir.model.fields,field_description:date_range.field_date_range_generator____last_update
#: model:ir.model.fields,field_description:date_range.field_date_range_search_mixin____last_update
#: model:ir.model.fields,field_description:date_range.field_date_range_type____last_update
msgid "Last Modified on"
msgstr ""

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range__write_uid
#: model:ir.model.fields,field_description:date_range.field_date_range_generator__write_uid
#: model:ir.model.fields,field_description:date_range.field_date_range_type__write_uid
msgid "Last Updated by"
msgstr ""

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range__write_date
#: model:ir.model.fields,field_description:date_range.field_date_range_generator__write_date
#: model:ir.model.fields,field_description:date_range.field_date_range_type__write_date
msgid "Last Updated on"
msgstr ""

#. module: date_range
#: model:ir.model,name:date_range.model_date_range_search_mixin
msgid "Mixin class to add a Many2one style period search field"
msgstr ""

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range__name
#: model:ir.model.fields,field_description:date_range.field_date_range_type__name
msgid "Name"
msgstr ""

#. module: date_range
#: code:addons/date_range/wizard/date_range_generator.py:0
#, python-format
msgid "No ranges to generate with these settings"
msgstr ""

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range_generator__count
msgid "Number of ranges to generate"
msgstr ""

#. module: date_range
#: model:ir.model.fields,help:date_range.field_date_range_type__autogeneration_date_start
msgid "Only applies when there are no date ranges of this type yet"
msgstr ""

#. module: date_range
#: model_terms:ir.ui.view,arch_db:date_range.date_range_generator_view_form
msgid "Parameters"
msgstr ""

#. module: date_range
#: code:addons/date_range/models/date_range_search_mixin.py:0
#, python-format
msgid "Period"
msgstr ""

#. module: date_range
#: code:addons/date_range/wizard/date_range_generator.py:0
#, python-format
msgid "Please enter an end date, or the number of ranges to generate."
msgstr ""

#. module: date_range
#: code:addons/date_range/wizard/date_range_generator.py:0
#, python-format
msgid "Please set a prefix or an expression to generate the range names."
msgstr ""

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range_generator__range_name_preview
#: model:ir.model.fields,field_description:date_range.field_date_range_type__range_name_preview
msgid "Range Name Preview"
msgstr ""

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range_generator__name_expr
#: model:ir.model.fields,field_description:date_range.field_date_range_type__name_expr
msgid "Range name expression"
msgstr ""

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range_generator__name_prefix
#: model:ir.model.fields,field_description:date_range.field_date_range_type__name_prefix
msgid "Range name prefix"
msgstr ""

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range_type__date_range_ids
msgid "Ranges"
msgstr ""

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range__date_start
#: model:ir.model.fields,field_description:date_range.field_date_range_generator__date_start
msgid "Start date"
msgstr ""

#. module: date_range
#: model_terms:ir.ui.view,arch_db:date_range.date_range_generator_view_form
msgid "Submit"
msgstr ""

#. module: date_range
#: code:addons/date_range/wizard/date_range_generator.py:0
#, python-format
msgid ""
"The Company in the Date Range Generator and in Date Range Type must be the "
"same."
msgstr ""

#. module: date_range
#: code:addons/date_range/models/date_range.py:0
#, python-format
msgid "The Company in the Date Range and in Date Range Type must be the same."
msgstr ""

#. module: date_range
#: model:ir.model.fields,help:date_range.field_date_range_type__active
msgid ""
"The active field allows you to hide the date range type without removing it."
msgstr ""

#. module: date_range
#: model:ir.model.fields,help:date_range.field_date_range__active
msgid "The active field allows you to hide the date range without removing it."
msgstr ""

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range__type_id
#: model:ir.model.fields,field_description:date_range.field_date_range_generator__type_id
msgid "Type"
msgstr ""

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range__type_name
msgid "Type Name"
msgstr ""

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range_generator__unit_of_time
#: model:ir.model.fields,field_description:date_range.field_date_range_type__unit_of_time
msgid "Unit Of Time"
msgstr ""

#. module: date_range
#: model_terms:ir.ui.view,arch_db:date_range.date_range_generator_view_form
msgid "Until"
msgstr ""

#. module: date_range
#: code:addons/date_range/models/date_range_type.py:0
#, python-format
msgid ""
"You cannot change the company, as this Date Range Type is  assigned to Date "
"Range (%s)."
msgstr ""

#. module: date_range
#: model:ir.model.fields.selection,name:date_range.selection__date_range_generator__unit_of_time__3
#: model:ir.model.fields.selection,name:date_range.selection__date_range_type__autogeneration_unit__3
#: model:ir.model.fields.selection,name:date_range.selection__date_range_type__unit_of_time__3
msgid "days"
msgstr ""

#. module: date_range
#: model_terms:ir.ui.view,arch_db:date_range.date_range_generator_view_form
msgid "entries."
msgstr ""

#. module: date_range
#: model_terms:ir.ui.view,arch_db:date_range.view_date_range_type_form_view
msgid "in advance"
msgstr ""

#. module: date_range
#: model:ir.model.fields.selection,name:date_range.selection__date_range_generator__unit_of_time__1
#: model:ir.model.fields.selection,name:date_range.selection__date_range_type__autogeneration_unit__1
#: model:ir.model.fields.selection,name:date_range.selection__date_range_type__unit_of_time__1
msgid "months"
msgstr ""

#. module: date_range
#: model_terms:ir.ui.view,arch_db:date_range.date_range_generator_view_form
#: model_terms:ir.ui.view,arch_db:date_range.view_date_range_type_form_view
msgid "or enter a complex expression below"
msgstr ""

#. module: date_range
#: model:ir.model.fields.selection,name:date_range.selection__date_range_generator__unit_of_time__2
#: model:ir.model.fields.selection,name:date_range.selection__date_range_type__autogeneration_unit__2
#: model:ir.model.fields.selection,name:date_range.selection__date_range_type__unit_of_time__2
msgid "weeks"
msgstr ""

#. module: date_range
#: model:ir.model.fields.selection,name:date_range.selection__date_range_generator__unit_of_time__0
#: model:ir.model.fields.selection,name:date_range.selection__date_range_type__autogeneration_unit__0
#: model:ir.model.fields.selection,name:date_range.selection__date_range_type__unit_of_time__0
msgid "years"
msgstr ""
