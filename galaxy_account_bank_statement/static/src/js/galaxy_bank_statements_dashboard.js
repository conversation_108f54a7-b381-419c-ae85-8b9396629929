odoo.define('GalaxyBankStatementListDashboard', function (require) {
"use strict";

/**
 * This file defines the Purchase Dashboard view (alongside its renderer, model
 * and controller). This Dashboard is added to the top of list and kanban Purchase
 * views, it extends both views with essentially the same code except for
 * _onDashboardActionClicked function so we can apply filters without changing our
 * current view.
 */

var core = require('web.core');
var ListController = require('web.ListController');
var ListModel = require('web.ListModel');
var ListRenderer = require('web.ListRenderer');
var ListView = require('web.ListView');
var SampleServer = require('web.SampleServer');
var view_registry = require('web.view_registry');

var QWeb = core.qweb;

//--------------------------------------------------------------------------
// List View
//--------------------------------------------------------------------------

var BankStatementListDashboardRenderer = ListRenderer.extend({
    /**
     * @override
     * @private
     * @returns {Promise}
     */
    _renderView: function () {
        var self = this;
        var context=self.state.getContext();
        var account_balance_info = self.state.getContext().account_balance_info;
        var bank_id = self.state.domain[0][2];
        return this._super.apply(this, arguments).then(function () {
            if(bank_id){
                self._rpc({
                    model: 'galaxy.bank',
                    method: 'get_account_balance',
                    args: [bank_id],
                }).then(function(result){
                    account_balance_info = result;
                    var purchase_dashboard = QWeb.render('galaxy_account_bank_statement.BalanceDashboard', {
                        values: account_balance_info,
                    });
                    self.$el.prepend(purchase_dashboard);
                });
            }
        });
    },
});

var GalaxyBankStatementListDashboardView = ListView.extend({
    config: _.extend({}, ListView.prototype.config, {
        Renderer: BankStatementListDashboardRenderer,
    }),
});

//--------------------------------------------------------------------------
// Kanban View
//--------------------------------------------------------------------------


view_registry.add('GalaxyBankStatementListDashboardView', GalaxyBankStatementListDashboardView);
});
