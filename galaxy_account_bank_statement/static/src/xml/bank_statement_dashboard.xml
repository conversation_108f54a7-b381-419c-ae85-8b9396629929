<?xml version="1.0" encoding="UTF-8"?>
<templates>
    <!-- This template is for a table at the top of bank statement views that shows balance -->
    <t t-name="galaxy_account_bank_statement.BalanceDashboard">
        <div class="o_purchase_dashboard container">
            <div class="row">
                <div class="col-sm-7">
                    <table class="table table-sm">
                        <!-- thead needed to avoid list view rendering error for some reason -->
                        <thead>
                            <tr>
                                <!-- can't use th tag due to list rendering error when no values in list... -->
                                <td class="o_text">Current Balance (HKD)</td>
                                <td class="o_main"><a href="javascript:void(0);" role="button" >$<t t-esc="values['HKD']['current_balance']"/></a></td>
                                <td class="o_text">Current Balance (USD)</td>
                                <td class="o_main"><a href="javascript:void(0);" role="button" >$<t t-esc="values['USD']['current_balance']"/></a></td>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="o_text">Available Balance (HKD)</td>
                                <td class="o_main"><a href="javascript:void(0);" role="button" >$<t t-esc="values['HKD']['available_balance']"/></a></td>
                                <td class="o_text">Available Balance (USD)</td>
                                <td class="o_main"><a href="javascript:void(0);" role="button" >$<t t-esc="values['USD']['available_balance']"/></a></td>
                            </tr>
                            <tr>
                                <td class="o_text">Last Update Time</td>
                                <td class="o_text"><span><t t-esc="values['HKD']['last_update_time']"/></span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </t>
</templates>
