# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* galaxy_account_bank_statement
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0+e-********\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-20 08:47+0000\n"
"PO-Revision-Date: 2025-05-20 08:47+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: galaxy_account_bank_statement
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Bank Statment Auditor</span>"
msgstr ""

#. module: galaxy_account_bank_statement
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_account_merged_bank_statement_search
msgid "Account"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_bank_account__account_holder_company_id
msgid "Account Holder Company"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_bank_account__account_number
msgid "Account Number"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__message_needaction
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__message_needaction
msgid "Action Needed"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_bank__active
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_bank_account__active
msgid "Active"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__activity_ids
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__activity_ids
msgid "Activities"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__activity_exception_decoration
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__activity_state
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__activity_state
msgid "Activity State"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__activity_type_icon
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__activity_type_icon
msgid "Activity Type Icon"
msgstr ""

#. module: galaxy_account_bank_statement
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_account_merged_bank_statement_form
msgid "Are you sure make the statement as to-do?"
msgstr "確認要把銀行流水狀態脩改爲待辦嗎？"

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__message_attachment_count
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement.selection__galaxy_account_bank_merged_statement__state__done
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_account_merged_bank_statement_form
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_account_merged_bank_statement_search
msgid "Audited"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_bank_account__available_balance
msgid "Available Balance"
msgstr ""

#. module: galaxy_account_bank_statement
#. openerp-web
#: code:addons/galaxy_account_bank_statement/static/src/xml/bank_statement_dashboard.xml:0
#, python-format
msgid "Available Balance (HKD)"
msgstr ""

#. module: galaxy_account_bank_statement
#. openerp-web
#: code:addons/galaxy_account_bank_statement/static/src/xml/bank_statement_dashboard.xml:0
#, python-format
msgid "Available Balance (USD)"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__bank_id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement_line__bank_id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__bank_id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement_line__bank_id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_bank_account__bank_id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_refresh_bank_statement__bank_id
msgid "Bank"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__account_id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement_line__account_id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__account_id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement_line__account_id
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_account_bank_statement_search
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_account_merged_bank_statement_search
msgid "Bank Account"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_bank__bank_account_ids
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_bank_form
msgid "Bank Accounts"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__bank_code
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__bank_code
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_bank__bank_code
msgid "Bank Code"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__bank_service_amount
msgid "Bank Service Amount"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model,name:galaxy_account_bank_statement.model_galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement_line__account_bank_statement_id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement_line__account_bank_statement_id
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_account_bank_merged_statement_tree
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_account_bank_statement_form
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_account_bank_statement_search
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_account_bank_statement_tree
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_account_merged_bank_statement_form
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_account_merged_bank_statement_search
msgid "Bank Statement"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_res_config_settings__galaxy_bankstatement_responsible_user
msgid "Bank Statement Auditor"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model,name:galaxy_account_bank_statement.model_galaxy_account_bank_merged_statement_line
#: model:ir.model,name:galaxy_account_bank_statement.model_galaxy_account_bank_statement_line
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_account_bank_statement_line_form
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_account_bank_statement_line_search
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_account_bank_statement_line_tree
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_account_merged_bank_statement_line_form
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_account_merged_bank_statement_line_search
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_account_merged_bank_statement_line_tree
msgid "Bank Statement Line"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.actions.act_window,name:galaxy_account_bank_statement.action_galaxy_account_bank_statement_lines
#: model:ir.actions.act_window,name:galaxy_account_bank_statement.action_galaxy_account_merged_bank_statement_lines
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__line_ids
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__line_ids
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_account_bank_statement_form
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_account_merged_bank_statement_form
msgid "Bank Statement Lines"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.actions.act_window,name:galaxy_account_bank_statement.action_galaxy_account_bank_statement
#: model:ir.actions.act_window,name:galaxy_account_bank_statement.action_galaxy_account_merged_bank_statement
#: model:ir.ui.menu,name:galaxy_account_bank_statement.menu_galaxy_account_merged_bank_statement
msgid "Bank Statements"
msgstr "銀行流水"

#. module: galaxy_account_bank_statement
#: code:addons/galaxy_account_bank_statement/wizard/galaxy_refresh_bank_statement.py:0
#, python-format
msgid ""
"Bank statement refresh successfully! please check the bank statement after 5"
" minutes."
msgstr ""

#. module: galaxy_account_bank_statement
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_refresh_bank_statement_form
msgid "Cancel"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement_line__channel
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement_line__channel
msgid "Channel"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement_line__charge_amount
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement_line__charge_amount
msgid "Charge Amount"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement_line__charge_currency
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement_line__charge_currency_id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement_line__charge_currency
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement_line__charge_currency_id
msgid "Charge Currency"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement_line__company_name
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement_line__company_name
msgid "Company Name"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model,name:galaxy_account_bank_statement.model_res_config_settings
msgid "Config Settings"
msgstr "配置設定"

#. module: galaxy_account_bank_statement
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement.selection__galaxy_account_bank_merged_statement__state__in_progress
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_account_merged_bank_statement_form
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_account_merged_bank_statement_search
msgid "Confirm"
msgstr ""

#. module: galaxy_account_bank_statement
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_account_merged_bank_statement_search
msgid "Created Today"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__create_uid
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement_line__create_uid
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__create_uid
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement_line__create_uid
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_bank__create_uid
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_bank_account__create_uid
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_refresh_bank_statement__create_uid
msgid "Created by"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__create_date
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement_line__create_date
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__create_date
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement_line__create_date
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_bank__create_date
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_bank_account__create_date
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_refresh_bank_statement__create_date
msgid "Created on"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__currency_id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__currency_id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_bank_account__currency_id
msgid "Currency"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__currency_name
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_statement__currency_name
msgid "Currency Code (ISO 4217)"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__currency_name
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__currency_name
msgid "Currency Name"
msgstr ""

#. module: galaxy_account_bank_statement
#: code:addons/galaxy_account_bank_statement/models/online_bid_data.py:0
#, python-format
msgid "Currency type error"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_bank_account__current_balance
msgid "Current Balance"
msgstr ""

#. module: galaxy_account_bank_statement
#. openerp-web
#: code:addons/galaxy_account_bank_statement/static/src/xml/bank_statement_dashboard.xml:0
#, python-format
msgid "Current Balance (HKD)"
msgstr ""

#. module: galaxy_account_bank_statement
#. openerp-web
#: code:addons/galaxy_account_bank_statement/static/src/xml/bank_statement_dashboard.xml:0
#, python-format
msgid "Current Balance (USD)"
msgstr ""

#. module: galaxy_account_bank_statement
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_account_bank_statement_search
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_account_merged_bank_statement_search
msgid "Deposit"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__deposit_amount
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement_line__deposit_amount
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__deposit_amount
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement_line__deposit_amount
msgid "Deposit Amount"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement_line__deposit_currency_id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement_line__deposit_currency_id
msgid "Deposit Currency"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement_line__deposit_currency_and_amount
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement_line__deposit_currency_and_amount
msgid "Deposit Currency And Amount"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__display_name
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement_line__display_name
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__display_name
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement_line__display_name
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_bank__display_name
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_bank_account__display_name
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_bank_service__display_name
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_refresh_bank_statement__display_name
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_online_bid_data__display_name
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_res_config_settings__display_name
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_rpc_bank_statement__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: galaxy_account_bank_statement
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement.selection__galaxy_account_bank_statement__state__done
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_account_bank_statement_search
msgid "Done"
msgstr ""

#. module: galaxy_account_bank_statement
#: code:addons/galaxy_account_bank_statement/wizard/galaxy_refresh_bank_statement.py:0
#: code:addons/galaxy_account_bank_statement/wizard/galaxy_refresh_bank_statement.py:0
#, python-format
msgid "Failed to refresh bank statement!"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__message_follower_ids
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__message_follower_ids
msgid "Followers"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__message_channel_ids
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__message_channel_ids
msgid "Followers (Channels)"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__message_partner_ids
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__activity_type_icon
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_statement__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.actions.act_window,name:galaxy_account_bank_statement.action_galaxy_bank
#: model:ir.model,name:galaxy_account_bank_statement.model_galaxy_bank
#: model:ir.ui.menu,name:galaxy_account_bank_statement.menu_galaxy_bank
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_bank_search
msgid "Galaxy Bank"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.actions.act_window,name:galaxy_account_bank_statement.action_galaxy_bank_account
#: model:ir.model,name:galaxy_account_bank_statement.model_galaxy_bank_account
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_bank_account_form
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_bank_account_search
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_bank_account_tree
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_bank_form
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_bank_tree
msgid "Galaxy Bank Account"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model,name:galaxy_account_bank_statement.model_galaxy_bank_service
msgid "Galaxy Bank Service"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.actions.act_window,name:galaxy_account_bank_statement.action_galaxy_refresh_bank_statement
#: model:ir.model,name:galaxy_account_bank_statement.model_galaxy_refresh_bank_statement
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_refresh_bank_statement_form
msgid "Galaxy Refresh Bank Statement"
msgstr ""

#. module: galaxy_account_bank_statement
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_account_bank_statement_search
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_account_merged_bank_statement_search
msgid "HKD"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement_line__id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement_line__id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_bank__id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_bank_account__id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_bank_service__id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_refresh_bank_statement__id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_online_bid_data__id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_res_config_settings__id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_rpc_bank_statement__id
msgid "ID"
msgstr "編號"

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__activity_exception_icon
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__activity_exception_icon
msgid "Icon"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__activity_exception_icon
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_statement__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__message_needaction
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__message_unread
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_statement__message_needaction
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_statement__message_unread
msgid "If checked, new messages require your attention."
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__message_has_error
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__message_has_sms_error
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_statement__message_has_error
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_statement__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: galaxy_account_bank_statement
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_account_bank_statement_search
msgid "In Progress"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement.selection__galaxy_account_bank_statement__state__in_progress
msgid "In progress"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__index
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__index
msgid "Index"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__message_is_follower
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement____last_update
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement_line____last_update
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement____last_update
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement_line____last_update
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_bank____last_update
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_bank_account____last_update
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_bank_service____last_update
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_refresh_bank_statement____last_update
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_online_bid_data____last_update
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_res_config_settings____last_update
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_rpc_bank_statement____last_update
msgid "Last Modified on"
msgstr "修改時間"

#. module: galaxy_account_bank_statement
#. openerp-web
#: code:addons/galaxy_account_bank_statement/static/src/xml/bank_statement_dashboard.xml:0
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_bank_account__last_update_time
#, python-format
msgid "Last Update Time"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__write_uid
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement_line__write_uid
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__write_uid
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement_line__write_uid
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_bank__write_uid
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_bank_account__write_uid
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_refresh_bank_statement__write_uid
msgid "Last Updated by"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__write_date
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement_line__write_date
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__write_date
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement_line__write_date
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_bank__write_date
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_bank_account__write_date
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_refresh_bank_statement__write_date
msgid "Last Updated on"
msgstr ""

#. module: galaxy_account_bank_statement
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_account_bank_statement_form
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_account_merged_bank_statement_form
msgid "Lines"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__message_main_attachment_id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model,name:galaxy_account_bank_statement.model_galaxy_account_bank_merged_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement_line__account_bank_merged_statement_id
msgid "Merged Bank Statement"
msgstr "銀行流水"

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement_line__message
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement_line__message
msgid "Message"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__message_has_error
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__message_ids
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__message_ids
msgid "Messages"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__my_activity_date_deadline
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__name
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement_line__name
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__name
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement_line__name
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_bank__name
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_bank_account__name
msgid "Name"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__net_deposit_amount
msgid "Net Deposit Amount"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__net_withdrawl_amount
msgid "Net Withdrawl Amount"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__activity_date_deadline
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__activity_summary
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__activity_summary
msgid "Next Activity Summary"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__activity_type_id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__note
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__note
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_bank__note
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_bank_account__note
msgid "Note"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__message_needaction_counter
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__message_has_error_counter
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__message_needaction_counter
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_statement__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__message_has_error_counter
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_statement__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__message_unread_counter
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_statement__message_unread_counter
msgid "Number of unread messages"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model,name:galaxy_account_bank_statement.model_online_bid_data
msgid "Online Bid Data"
msgstr ""

#. module: galaxy_account_bank_statement
#: code:addons/galaxy_account_bank_statement/models/online_bid_data.py:0
#, python-format
msgid "Process data failed"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model,name:galaxy_account_bank_statement.model_rpc_bank_statement
msgid "RPC Bank Statement"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement_line__reference_number
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement_line__reference_number
msgid "Reference Number"
msgstr ""

#. module: galaxy_account_bank_statement
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_refresh_bank_statement_form
msgid "Refresh"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement_line__remittance_amount
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement_line__remittance_amount
msgid "Remittance Amount"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__remittance_bank
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement_line__remittance_bank
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__remittance_bank
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement_line__remittance_bank
msgid "Remittance Bank"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement_line__remittance_currency
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement_line__remittance_currency_id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement_line__remittance_currency
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement_line__remittance_currency_id
msgid "Remittance Currency"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__remittance_message
msgid "Remittance Message"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__partner_id
msgid "Remittance Partner"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__remittance_payer
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement_line__remittance_payer
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__remittance_payer
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement_line__remittance_payer
msgid "Remittance Payer"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__responsible_person_id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__responsible_person_id
msgid "Responsible Person"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__activity_user_id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__activity_user_id
msgid "Responsible User"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__message_has_sms_error
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__message_has_sms_error
msgid "SMS Delivery error"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_refresh_bank_statement__transaction_date
msgid "Start Date"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__state
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__state
msgid "State"
msgstr ""

#. module: galaxy_account_bank_statement
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_account_bank_statement_form
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_account_merged_bank_statement_form
msgid "Statement Line"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__activity_state
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_statement__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: galaxy_account_bank_statement
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_account_bank_statement_search
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_account_merged_bank_statement_search
msgid "To Do"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement.selection__galaxy_account_bank_merged_statement__state__to_do
#: model:ir.model.fields.selection,name:galaxy_account_bank_statement.selection__galaxy_account_bank_statement__state__to_do
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_account_merged_bank_statement_form
msgid "To do"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__transaction_id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__transaction_id
msgid "Transaction"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__transaction_date
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement_line__transaction_date
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__transaction_date
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement_line__transaction_date
msgid "Transaction Date"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__transaction_description
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__transaction_description
msgid "Transaction Description"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement_line__transaction_detail_id
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement_line__transaction_detail_id
msgid "Transaction Detail"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement_line__transaction_status
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement_line__transaction_status
msgid "Transaction Status"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__activity_exception_decoration
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_statement__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr ""

#. module: galaxy_account_bank_statement
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_account_bank_statement_search
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_account_merged_bank_statement_search
msgid "USD"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__message_unread
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__message_unread
msgid "Unread Messages"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__message_unread_counter
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__message_unread_counter
msgid "Unread Messages Counter"
msgstr ""

#. module: galaxy_account_bank_statement
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_bank_tree
msgid "View Bank Balance"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__website_message_ids
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__website_message_ids
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_statement__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: galaxy_account_bank_statement
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.res_config_settings_view_form
msgid "Who can audit bank statements"
msgstr ""

#. module: galaxy_account_bank_statement
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_account_bank_statement_search
#: model_terms:ir.ui.view,arch_db:galaxy_account_bank_statement.view_galaxy_account_merged_bank_statement_search
msgid "Withdrawl"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__withdrawl_amount
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_statement__withdrawl_amount
msgid "Withdrawl Amount"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,field_description:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__bank_service_statement_id
msgid "bank_service_statement"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__withdrawl_amount
msgid "去掉手续费之前的取款金额"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__deposit_amount
msgid "去掉手续费之前的存款金额"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__net_withdrawl_amount
msgid "去掉手续费以后的取款金额"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__net_deposit_amount
msgid "去掉手续费以后的存款金额"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_statement__withdrawl_amount
msgid "取款金额"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_statement__deposit_amount
msgid "存款金额"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__remittance_payer
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement_line__remittance_payer
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_statement__remittance_payer
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_statement_line__remittance_payer
msgid "汇款人"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement_line__remittance_currency
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_statement_line__remittance_currency
msgid "汇款币种"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement_line__remittance_amount
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_statement_line__remittance_amount
msgid "汇款金额"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__remittance_bank
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement_line__remittance_bank
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_statement__remittance_bank
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_statement_line__remittance_bank
msgid "汇款银行"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__transaction_id
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_statement__transaction_id
msgid "用于与交易明细ID匹配的唯一识别ID"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement_line__transaction_detail_id
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_statement_line__transaction_detail_id
msgid "用于与交易记录ID匹配的唯一识别ID"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_merged_statement__index
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_account_bank_statement__index
msgid "用于采集信息时的识别"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_bank__bank_code
msgid "银行代码"
msgstr ""

#. module: galaxy_account_bank_statement
#: model:ir.model.fields,help:galaxy_account_bank_statement.field_galaxy_bank__name
msgid "银行名称"
msgstr ""
