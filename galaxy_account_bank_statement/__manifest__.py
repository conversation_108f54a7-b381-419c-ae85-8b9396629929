# -*- coding: utf-8 -*-
{
    'name': "galaxy_account_bank_statement",

    'summary': """
        bank statement used to make easy to query the bank statement""",

    'description': """
         bank statement used to make easy to query the bank statement
    """,

    'author': "JackyRen",
    'website': "",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/14.0/odoo/addons/base/data/ir_module_category_data.xml
    # for the full list
    'category': 'Galaxy_ERP/Galaxy_ERP',
    'version': '0.1',
    # any module necessary for this one to work correctly
    'depends': ['base', 'account', 'proxy_bid', 'zpower_webhook_api'],

    # always loaded
    'data': [
        'security/ir.model.access.csv',
        'views/galaxy_bank_account_views.xml',
        'views/galaxy_bank_views.xml',
        'views/galaxy_account_bank_statement_views.xml',
        'views/galaxy_account_merged_bank_statement_views.xml',
        'wizard/galaxy_refresh_bank_statement.xml',
        'views/res_config_settings_views.xml',
        'views/assets.xml',
        'views/menu.xml',
    ],
    # only loaded in demonstration mode
    'qweb': [
        "static/src/xml/bank_statement_dashboard.xml",
    ],
    'demo': [
        'demo/demo.xml',
    ],
}
