<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <!-- Tree View for GalaxyBankAccount -->
    <record id="view_galaxy_bank_account_tree" model="ir.ui.view">
        <field name="name">galaxy.bank.account.tree</field>
        <field name="model">galaxy.bank.account</field>
        <field name="arch" type="xml">
            <tree string="Galaxy Bank Account">
                <field name="name"/>
                <field name="bank_id"/>
                <field name="account_number"/>
                <field name="account_holder_company_id"/>
                <field name="current_balance"/>
                <field name="available_balance"/>
                <field name="currency_id"/>
            </tree>
        </field>
    </record>

    <!-- Form View for GalaxyBankAccount -->
    <record id="view_galaxy_bank_account_form" model="ir.ui.view">
        <field name="name">galaxy.bank.account.form</field>
        <field name="model">galaxy.bank.account</field>
        <field name="arch" type="xml">
            <form string="Galaxy Bank Account">
                <sheet>
                    <group>
                        <field name="name"/>
                        <field name="bank_id"/>
                        <field name="account_number"/>
                        <field name="account_holder_company_id"/>
                        <field name="current_balance" readonly="1" force_save="1"/>
                        <field name="available_balance" readonly="1" force_save="1"/>
                        <field name="currency_id"/>
                        <field name="note"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Search View for GalaxyBankAccount -->
    <record id="view_galaxy_bank_account_search" model="ir.ui.view">
        <field name="name">galaxy.bank.account.search</field>
        <field name="model">galaxy.bank.account</field>
        <field name="arch" type="xml">
            <search string="Galaxy Bank Account">
                <field name="name"/>
                <field name="account_number"/>
                <field name="account_holder_company_id"/>
                <field name="currency_id"/>
            </search>
        </field>
    </record>

    <!-- Action for GalaxyBankAccount -->
    <record id="action_galaxy_bank_account" model="ir.actions.act_window">
        <field name="name">Galaxy Bank Account</field>
        <field name="res_model">galaxy.bank.account</field>
        <field name="view_mode">tree,form</field>
        <field name="view_id" ref="view_galaxy_bank_account_tree"/>
    </record>
</odoo>