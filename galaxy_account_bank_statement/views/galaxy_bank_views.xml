<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <!-- Tree View for GalaxyBankAccount -->
    <record id="view_galaxy_bank_tree" model="ir.ui.view">
        <field name="name">galaxy.bank.tree</field>
        <field name="model">galaxy.bank</field>
        <field name="arch" type="xml">
            <tree string="Galaxy Bank Account">
                <field name="name"/>
                <field name="bank_code"/>
                <field name="note"/>
                <button name="action_view_bank_balance" icon="fa-money" type="object" string="View Bank Balance"/>
                <!--<button name="action_refresh_bank_balance" icon="fa-cloud-download" type="object" string="Refresh Bank Balance"/>-->
            </tree>
        </field>
    </record>

    <!-- Form View for GalaxyBankAccount -->
    <record id="view_galaxy_bank_form" model="ir.ui.view">
        <field name="name">galaxy.bank.form</field>
        <field name="model">galaxy.bank</field>
        <field name="arch" type="xml">
            <form string="Galaxy Bank Account">
                <sheet>
                    <group>
                        <field name="name"/>
                        <field name="bank_code"/>
                        <field name="note"/>
                    </group>
                    <notebook>
                        <page string="Bank Accounts" name="bank_accounts">
                            <field name="bank_account_ids" widget="section_and_note_one2many" mode="tree,form">
                                <tree string="Bank Accounts">
                                    <field name="account_number"/>
                                    <field name="currency_id"/>
                                    <field name="account_holder_company_id"/>
                                    <field name="current_balance"/>
                                    <field name="available_balance"/>
                                    <field name="last_update_time"/>
                                </tree>
                                <form string="Bank Accounts">
                                    <group>
                                        <field name="account_number"/>
                                        <field name="currency_id"/>
                                        <field name="account_holder_company_id"/>
                                        <field name="current_balance"/>
                                        <field name="available_balance"/>
                                        <field name="last_update_time"/>
                                    </group>
                                </form>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Search View for GalaxyBankAccount -->
    <record id="view_galaxy_bank_search" model="ir.ui.view">
        <field name="name">galaxy.bank.search</field>
        <field name="model">galaxy.bank</field>
        <field name="arch" type="xml">
            <search string="Galaxy Bank">
                <field name="name"/>
                <field name="bank_code"/>
            </search>
        </field>
    </record>

    <!-- Action for GalaxyBankAccount -->
    <record id="action_galaxy_bank" model="ir.actions.act_window">
        <field name="name">Galaxy Bank</field>
        <field name="res_model">galaxy.bank</field>
        <field name="view_mode">tree,form</field>
        <field name="view_id" ref="view_galaxy_bank_tree"/>
    </record>
</odoo>