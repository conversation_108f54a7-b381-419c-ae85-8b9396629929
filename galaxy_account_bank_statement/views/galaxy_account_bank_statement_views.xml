<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <!-- Tree View for GalaxyAccountBankStatement -->
    <record id="view_galaxy_account_bank_statement_tree" model="ir.ui.view">
        <field name="name">galaxy.account.bank.statement.tree</field>
        <field name="model">galaxy.account.bank.statement</field>
        <field name="arch" type="xml">
            <tree string="Bank Statement" decoration-success="currency_name == 'USD'" create="0" edit="1" default_order="transaction_date desc, index, account_id">
                <field name="transaction_date"/>
                <field name="currency_name" invisible="1"/>
                <field name="currency_id" />
                <field name="deposit_amount"/>
                <field name="withdrawl_amount" decoration-danger="withdrawl_amount &gt; 0"/>
                <field name="remittance_payer"/>
                <field name="remittance_bank"/>
                <field name="account_id"/>
                <field name="transaction_description"/>
                <field name="transaction_id" optional="hide"/>
                <field name="index" optional="hide"/>
                <field name="create_date" optional="hide"/>
            </tree>
        </field>
    </record>

    <!-- Form View for GalaxyAccountBankStatement -->
    <record id="view_galaxy_account_bank_statement_form" model="ir.ui.view">
        <field name="name">galaxy.account.bank.statement.form</field>
        <field name="model">galaxy.account.bank.statement</field>
        <field name="arch" type="xml">
            <form string="Bank Statement" edit="0" create="0">
                <sheet>
                    <group>
                        <field name="transaction_date"/>
                        <field name="currency_id"/>
                        <field name="deposit_amount"/>
                        <field name="withdrawl_amount"/>
                        <field name="remittance_payer"/>
                        <field name="remittance_bank"/>
                        <field name="account_id"/>
                        <field name="transaction_description"/>
                        <field name="account_id"/>
                        <field name="transaction_id"/>
                    </group>
                    <notebook>
                        <page string="Bank Statement Lines" name="bank_statement_lines">
                            <field name="line_ids" readonly="1" widget="section_and_note_one2many" mode="tree,form">
                                <tree string="Lines">
                                    <field name="transaction_date"/>
                                    <field name="company_name"/>
                                    <field name="deposit_amount"/>
                                    <field name="deposit_currency_id"/>
                                    <field name="remittance_amount"/>
                                    <field name="remittance_currency_id"/>
                                    <field name="remittance_bank"/>
                                    <field name="remittance_payer"/>
                                    <field name="reference_number"/>
                                    <field name="message"/>
                                    <field name="transaction_status" optional="hide"/>
                                    <field name="transaction_detail_id" optional="hide"/>
                                </tree>
                                <form string="Statement Line">
                                    <sheet>
                                        <group>
                                            <field name="transaction_date"/>
                                            <field name="company_name"/>
                                            <field name="deposit_amount"/>
                                            <field name="deposit_currency_id"/>
                                            <field name="remittance_amount"/>
                                            <field name="remittance_currency_id"/>
                                            <field name="remittance_bank"/>
                                            <field name="remittance_payer"/>
                                            <field name="reference_number"/>
                                            <field name="message"/>
                                            <field name="transaction_status"/>
                                            <field name="transaction_detail_id"/>
                                        </group>
                                    </sheet>
                                </form>
                            </field>
                        </page>
                    </notebook>
                    <div class="oe_chatter">
                        <field name="message_follower_ids"/>
                        <field name="activity_ids"/>
                        <field name="message_ids"/>
                    </div>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Search View for GalaxyAccountBankStatement -->
    <record id="view_galaxy_account_bank_statement_search" model="ir.ui.view">
        <field name="name">galaxy.account.bank.statement.search</field>
        <field name="model">galaxy.account.bank.statement</field>
        <field name="arch" type="xml">
            <search string="Bank Statement">
                <field name="responsible_person_id"/>
                <field name="account_id"/>
                <field name="remittance_payer"/>
                <field name="transaction_description"/>
                <field name="transaction_id"/>
                <separator/>
                <filter name="deposit" string="Deposit" domain="[('deposit_amount', '&gt;', 0)]"/>
                <filter name="withdraw" string="Withdrawl" domain="[('withdrawl_amount', '&gt;', 0)]"/>
                <separator/>
                <filter name="USD" string="USD" domain="[('currency_name', '=', 'USD')]"/>
                <filter name="HKD" string="HKD" domain="[('currency_name', '=', 'HKD')]"/>
                <separator/>
                <filter name="action_to_do" string="To Do" domain="[('state', '=', 'to_do')]"/>
                <filter name="action_in_progress" string="In Progress" domain="[('state', '=', 'in_progress')]"/>
                <filter name="action_done" string="Done" domain="[('state', '=', 'done')]"/>
                <searchpanel>
                    <field name="account_id"  icon="fa-tag" string="Bank Account" enable_counters="1"/>
                </searchpanel>
            </search>
        </field>
    </record>

    <!-- Action for GalaxyAccountBankStatement -->
    <record id="action_galaxy_account_bank_statement" model="ir.actions.act_window">
        <field name="name">Bank Statements</field>
        <field name="res_model">galaxy.account.bank.statement</field>
        <field name="view_mode">tree,form,kanban</field>
        <field name="view_id" ref="view_galaxy_account_bank_statement_tree"/>
    </record>

    <!-- Tree View for GalaxyAccountBankStatementLine -->
    <record id="view_galaxy_account_bank_statement_line_tree" model="ir.ui.view">
        <field name="name">galaxy.account.bank.statement.line.tree</field>
        <field name="model">galaxy.account.bank.statement.line</field>
        <field name="arch"  type="xml">
            <tree create="0" edit="0" default_order="transaction_date desc, account_id" string="Bank Statement Line">
                <field name="bank_id"/>
                <field name="account_id"/>
                <field name="transaction_date"/>
                <field name="remittance_payer"/>
                <field name="remittance_bank"/>
                <field name="remittance_currency_id"/>
                <field name="remittance_amount"/>
                <field name="account_bank_statement_id"/>
                <field name="reference_number"/>
            </tree>
        </field>
    </record>

    <!-- Form View for GalaxyAccountBankStatementLine -->
    <record id="view_galaxy_account_bank_statement_line_form" model="ir.ui.view">
        <field name="name">galaxy.account.bank.statement.line.form</field>
        <field name="model">galaxy.account.bank.statement.line</field>
        <field name="arch" type="xml">
            <form string="Bank Statement Line" create="0" edit="0">
                <sheet>
                    <group>
                        <field name="bank_id"/>
                        <field name="account_id"/>
                        <field name="transaction_date"/>
                        <field name="remittance_payer"/>
                        <field name="remittance_bank"/>
                        <field name="remittance_currency"/>
                        <field name="remittance_amount"/>
                        <field name="account_bank_statement_id"/>
                        <field name="reference_number"/>
                        <field name="remittance_currency_id"/>
                        <field name="deposit_amount"/>
                        <field name="deposit_currency_id"/>
                        <field name="charge_currency_id"/>
                        <field name="channel"/>
                        <field name="message"/>
                        <field name="company_name"/>
                        <field name="transaction_status"/>
                        <field name="transaction_detail_id"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Search View for GalaxyAccountBankStatementLine -->
    <record id="view_galaxy_account_bank_statement_line_search" model="ir.ui.view">
        <field name="name">galaxy.account.bank.statement.line.search</field>
        <field name="model">galaxy.account.bank.statement.line</field>
        <field name="arch" type="xml">
            <search string="Bank Statement Line">
                <field name="name"/>
                <field name="account_bank_statement_id"/>
                <field name="account_id"/>
                <field name="transaction_date"/>
                <field name="transaction_detail_id"/>
            </search>
        </field>
    </record>

    <!-- Action for GalaxyAccountBankStatementLine -->
    <record id="action_galaxy_account_bank_statement_lines" model="ir.actions.act_window">
        <field name="name">Bank Statement Lines</field>
        <field name="res_model">galaxy.account.bank.statement.line</field>
        <field name="view_mode">tree,form</field>
    </record>



</odoo>