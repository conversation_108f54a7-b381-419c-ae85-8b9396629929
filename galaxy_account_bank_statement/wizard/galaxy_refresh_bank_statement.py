
import logging

from odoo import models, fields, api, _
from odoo.exceptions import UserError
from odoo.addons.zpower_webhook_api.models.web_hook import zpower_webhook

_logger = logging.getLogger(__name__)


class GalaxyRefreshBankStatement(models.TransientModel):
    _name = 'galaxy.refresh.bank.statement'
    _description = 'Galaxy Refresh Bank Statement'

    bank_id = fields.Many2one('galaxy.bank', string='Bank', required=True)
    transaction_date = fields.Date(string='Start Date', required=True)

    def refresh_bank_statement(self):
        """
        刷新银行对账单
        """
        transactin_date = self.transaction_date.strftime('%d%m%Y')
        try:
            resp = zpower_webhook.send_webhook_run_bank_statement({"date": transactin_date})
            if resp.status_code != 200:
                raise UserError(_('Failed to refresh bank statement!'))
        except Exception as e:
            _logger.error(f"Failed to refresh bank statement! {e}")
            raise UserError(_('Failed to refresh bank statement!'))
        self.env.user.notify_info(message=_('Bank statement refresh successfully! please check the bank statement after 5 minutes.'))
