# -*- coding: utf-8 -*-

import base64
import logging
import traceback
import ast
from datetime import datetime as DT
from importlib import reload

from odoo import models, _
from odoo.tools import config
from odoo.exceptions import UserError
from odoo.addons.base_galaxy.models.public_func import parse_float

_logger = logging.getLogger(__name__)


# =========================== tender lot ===========================
class OnlineBid(models.Model):
    _inherit = 'online.bid.data'

    def create_bank_statement_detail(self, bank_code, trade_detail_list):
        """
        创建银行交易明细
        """
        bank_accounts = self.env['galaxy.bank.account'].search(
            [('bank_id.bank_code', '=', bank_code)])
        bank_accounts_dict = {
            bank_account.currency_id.name: bank_account for bank_account in bank_accounts}
        trade_rec_detail_list = []
        for trade_detail in trade_detail_list:
            account_currency = trade_detail['remittance_currency']
            odoo_currency = self.env['galaxy.bank.service'].get_odoo_currency(
                account_currency)
            bank_account = bank_accounts_dict.get(odoo_currency)
            reference_number = trade_detail['reference_number']
            channel = trade_detail['channel']
            company_name = trade_detail['company_name']
            deposit_currency_and_amount = trade_detail['deposit_currency_and_amount']
            charge_currency = trade_detail['charge_currency']
            charge_amount = parse_float(trade_detail['charge_amount'])
            remittance_bank = trade_detail['remittance_bank']
            remittance_payer = trade_detail['remittance_payer']
            remittance_currency = trade_detail['remittance_currency']
            remittance_amount = parse_float(trade_detail['remittance_amount'])
            message = trade_detail['message']
            try:
                transaction_date = DT.strptime(
                    trade_detail['transaction_date'], '%d/%m/%Y')
            except ValueError:
                transaction_date = DT.strptime(
                    trade_detail['transaction_date'], '%Y-%m-%d')
            transaction_status = trade_detail['transaction_status']
            transaction_detail_id = trade_detail['transaction_detail_id']

            trade_detail_rec = self.env['galaxy.account.bank.statement.line'].search(
                [('account_id', '=', bank_account.id), ('transaction_date', '=', transaction_date),
                    ('transaction_detail_id', '=', transaction_detail_id)])
            if not trade_detail_rec:
                trade_detail_rec = self.env['galaxy.account.bank.statement.line'].create({
                    'reference_number': reference_number,
                    'channel': channel,
                    'company_name': company_name,
                    'deposit_currency_and_amount': deposit_currency_and_amount,
                    'charge_currency': charge_currency,
                    'charge_amount': charge_amount,
                    'remittance_bank': remittance_bank,
                    'remittance_payer': remittance_payer,
                    'remittance_currency': remittance_currency,
                    'remittance_amount': remittance_amount,
                    'message': message,
                    'transaction_date': transaction_date,
                    'transaction_status': transaction_status,
                    'transaction_detail_id': transaction_detail_id,
                    'account_id': bank_account.id,
                })
            else:
                trade_detail_rec.write({
                    'reference_number': reference_number,
                    'channel': channel,
                    'company_name': company_name,
                    'deposit_currency_and_amount': deposit_currency_and_amount,
                    'charge_currency': charge_currency,
                    'charge_amount': charge_amount,
                    'remittance_bank': remittance_bank,
                    'remittance_payer': remittance_payer,
                    'remittance_currency': remittance_currency,
                    'remittance_amount': remittance_amount,
                    'message': message,
                    'transaction_date': transaction_date,
                    'transaction_status': transaction_status,
                    'transaction_detail_id': transaction_detail_id,
                    'account_id': bank_account.id,
                })
            trade_rec_detail_list.append(trade_detail_rec)
        return trade_rec_detail_list

    def preprocess_bank_statement(self, trade_rec_list):
        """
        预处理银行对账单数据
        """
        transaction_ids_count = {}
        # 预处理一下，返回按照transaction_id分组的交易记录
        transaction_ids = []
        for trade in trade_rec_list:
            transaction_id = trade['transaction_id']
            transaction_ids.append(transaction_id)
            transaction_ids_count[transaction_id] = transaction_ids.count(
                transaction_id)
        return transaction_ids_count

    def create_bank_statement(self, bank_code, trade_list):
        """
        创建银行对账单
        """
        trade_rec_list = []
        # 预处理一下，找到transaction id相同且不是手续费的交易记录
        transaction_ids_count = self.preprocess_bank_statement(trade_list)
        for trade in trade_list:
            index = trade['index']
            if index == 0:
                continue
            account_number = trade['account_number']
            currency_type = self.env['galaxy.bank.service'].get_odoo_currency(
                trade['currency_type'])
            bank_account = self.env['galaxy.bank.account'].search([('bank_id.bank_code', '=', bank_code),
                                                                   ('account_number',
                                                                    '=', account_number),
                                                                   ('currency_id.name', '=', currency_type)])
            try:
                transaction_date = DT.strptime(
                    trade['transaction_date'], '%d/%m/%Y')
            except ValueError:
                transaction_date = DT.strptime(
                    trade['transaction_date'], '%Y-%m-%d')
            transaction_description = trade['transaction_description'].replace(
                '\n', ' ')
            withdrawal_amount = parse_float(trade['withdrawal_amount'])
            deposit_amount = parse_float(trade['deposit_amount'])
            transaction_id = trade['transaction_id']
            related_statement_transaction_id = ''
            is_service_charge = False
            if transaction_description.find('CHARGE') != -1:
                # 如果是手续费,更新手续费
                # 查找跟手续费相关的交易 trade_rec.index - 1 同一天的下一笔交易
                is_service_charge = True
                main_bank_statement = self.env['galaxy.account.bank.statement'].search(
                    [('bank_code', '=', bank_code), ('account_id', '=', bank_account.id),
                     ('index', '=', index - 1), ('transaction_date', '=', transaction_date)])
                if main_bank_statement:
                    related_statement_transaction_id = main_bank_statement.transaction_id
            force_create_bank_statement = False
            # 对于一些transaction_id相同的非手续费交易记录（交易金额相同），需要强制创建银行对账单
            if not is_service_charge:
                # 非手续费的银行流水
                bank_transaction_ids_count = transaction_ids_count.get(
                    transaction_id)
                trade_rec = self.env['galaxy.account.bank.statement'].search(
                    [('bank_code', '=', bank_code), ('account_id', '=', bank_account.id),
                        ('transaction_id', '=', transaction_id), ('transaction_date', '=', transaction_date)])
                if bank_transaction_ids_count > 1 and bank_transaction_ids_count > len(trade_rec):
                    force_create_bank_statement = True
            else:
                # 手续费交易记录, 通过related_statement_transaction_id与合并银行对账单的交易记录ID匹配
                trade_rec = self.env['galaxy.account.bank.statement'].search(
                    [('bank_code', '=', bank_code), ('account_id', '=', bank_account.id),
                        ('transaction_id', '=', transaction_id),
                        ('related_statement_transaction_id',
                         '=', related_statement_transaction_id),
                        ('transaction_date', '=', transaction_date)])
            if not trade_rec or force_create_bank_statement:
                trade_rec = self.env['galaxy.account.bank.statement'].create({
                    'name': transaction_description,
                    'index': index,
                    'bank_code': bank_code,
                    'account_id': bank_account.id,
                    'withdrawl_amount': withdrawal_amount,
                    'deposit_amount': deposit_amount,
                    'currency_id': bank_account.currency_id.id,
                    'transaction_date': transaction_date,
                    'transaction_description': transaction_description,
                    'transaction_id': transaction_id,
                    'related_statement_transaction_id': related_statement_transaction_id,
                })
            else:
                trade_rec.write({
                    'name': transaction_description,
                    'index': index,
                    'account_id': bank_account.id,
                    'withdrawl_amount': withdrawal_amount,
                    'deposit_amount': deposit_amount,
                    'currency_id': bank_account.currency_id.id,
                    'transaction_date': transaction_date,
                    'transaction_description': transaction_description,
                    'transaction_id': transaction_id
                })
            trade_rec_list.append(trade_rec)
        return trade_rec_list

    def create_bank_statement_dahsing(self, bank_code):
        """
        通过银行文件创建银行对账单
        """
        parsed_json = ast.literal_eval(self.data)['data']
        # parsed_json = ast.literal_eval(parsed_json)
        trade_list = parsed_json['info']['trade']
        trade_detail_list = parsed_json['info']['txn']
        account_info = parsed_json['info']['account']
        # 更新银行账户可用余额信息
        for account in account_info:
            currency_type = self.env['galaxy.bank.service'].get_odoo_currency(
                account['currency_type'])
            account_number = account['account_number']
            current_balance = parse_float(account['current_balance'])
            available_balance = parse_float(account['available_balance'])
            last_update_time = DT.now()
            if not currency_type:
                raise UserError(_('Currency type error'))
            bank_account = self.env['galaxy.bank.account'].search([('bank_id.bank_code', '=', bank_code),
                                                                   ('account_number',
                                                                    '=', account_number),
                                                                   ('currency_id.name', '=', currency_type)])
            if bank_account:
                bank_account.write({'current_balance': current_balance, 'available_balance': available_balance,
                                    'last_update_time': last_update_time})
        # 更新trade信息
        trade_rec_list = self.create_bank_statement(bank_code, trade_list)
        # 更新交易明细
        trade_rec_detail_list = self.create_bank_statement_detail(
            bank_code, trade_detail_list)
        # 合并银行对账单
        self.merge_bank_statement(trade_rec_list, trade_rec_detail_list)
        self.done = True

    def create_merged_bank_statement(self, trade_rec):
        # ARM022-BANK SERVICES CHARGE为我司应承担的手续费,不应该算入客户费用
        force_create_bank_statement = self.env.context.get(
            'force_create_bank_statement', False)
        merged_bank_statement = self.env['galaxy.account.bank.merged.statement'].search(
            [('bank_code', '=', trade_rec.bank_code), ('account_id', '=', trade_rec.account_id.id),
             ('transaction_id', '=', trade_rec.transaction_id), ('transaction_date', '=', trade_rec.transaction_date)])
        if not merged_bank_statement or force_create_bank_statement:
            merged_bank_statement = self.env['galaxy.account.bank.merged.statement'].create({
                'currency_id': trade_rec.currency_id.id,
                'name': trade_rec.name,
                'responsible_person_id': trade_rec.responsible_person_id.id,
                'index': trade_rec.index,
                'bank_code': trade_rec.bank_id.bank_code,
                'account_id': trade_rec.account_id.id,
                'bank_id': trade_rec.bank_id.id,
                'withdrawl_amount': trade_rec.withdrawl_amount,
                'deposit_amount': trade_rec.deposit_amount,
                'transaction_date': trade_rec.transaction_date,
                'transaction_description': trade_rec.transaction_description,
                'transaction_id': trade_rec.transaction_id
            })
        else:
            merged_bank_statement.write({
                'currency_id': trade_rec.currency_id.id,
                'name': trade_rec.name,
                'index': trade_rec.index,
                'account_id': trade_rec.account_id.id,
                'bank_id': trade_rec.bank_id.id,
                'withdrawl_amount': trade_rec.withdrawl_amount,
                'deposit_amount': trade_rec.deposit_amount,
                'transaction_date': trade_rec.transaction_date,
                'transaction_description': trade_rec.transaction_description,
                'transaction_id': trade_rec.transaction_id
            })
        return merged_bank_statement

    def merge_bank_statement(self, trade_rec_list, trade_rec_detail_list):
        """
        合并银行对账单
        """
        trade_rec_list = sorted(trade_rec_list, key=lambda m: m.index)
        transaction_ids_count = self.preprocess_bank_statement(trade_rec_list)
        merged_bank_statement = self.env['galaxy.account.bank.merged.statement']
        for trade_rec in trade_rec_list:
            bank_code = trade_rec.bank_id.bank_code
            bank_account = trade_rec.account_id
            transaction_id = trade_rec.transaction_id
            transaction_date = trade_rec.transaction_date
            force_create_bank_statement = False
            if trade_rec.transaction_description.find('CHARGE') != -1 and \
                    trade_rec.transaction_description.find('ARM022-BANK  SERVICES CHARGE') == -1:
                # 如果是手续费,更新手续费
                # 查找跟手续费相关的交易 trade_rec.index + 1 同一天的下一笔交易
                # 'ARM022-BANK  SERVICES CHARGE'为我司应承担的手续费,不应该算入客户费用
                main_bank_statement = self.env['galaxy.account.bank.merged.statement'].search(
                    [('bank_code', '=', trade_rec.bank_code), ('account_id', '=', trade_rec.account_id.id),
                     ('index', '=', trade_rec.index - 1), ('transaction_date', '=', trade_rec.transaction_date)])
                if main_bank_statement:
                    main_bank_statement.write({'bank_service_amount': trade_rec.withdrawl_amount,
                                               'bank_service_statement_id': trade_rec.id})
                continue
            else:
                merged_trade_rec = self.env['galaxy.account.bank.merged.statement'].search(
                    [('bank_code', '=', bank_code), ('account_id', '=', bank_account.id),
                        ('transaction_id', '=', transaction_id), ('transaction_date', '=', transaction_date)])
                bank_transaction_ids_count = transaction_ids_count.get(
                    transaction_id)
                if bank_transaction_ids_count > 1 and bank_transaction_ids_count > len(merged_trade_rec):
                    force_create_bank_statement = True
                merged_bank_statement = self.with_context(force_create_bank_statement=force_create_bank_statement) \
                    .create_merged_bank_statement(trade_rec)
            trade_transaction_date = trade_rec.transaction_date
            trade_deposit_amount = trade_rec.deposit_amount
            trade_transaction_description = trade_rec.transaction_description
            trade_currency_id = trade_rec.currency_id
            for trade_detail_rec in trade_rec_detail_list:
                # 获取关键字段
                if trade_detail_rec.account_bank_merged_statement_id:
                    continue
                trade_detail_transaction_date = trade_detail_rec.transaction_date
                trade_detail_deposit_amount = trade_detail_rec.remittance_amount
                trade_detail_transaction_description = trade_detail_rec.reference_number
                trade_detail_currency_id = trade_detail_rec.remittance_currency_id
                remittance_payer = trade_detail_rec.remittance_payer
                remittance_bank = trade_detail_rec.remittance_bank
                if trade_detail_currency_id == trade_currency_id and trade_detail_transaction_date == trade_transaction_date and trade_detail_deposit_amount == trade_deposit_amount:
                    if trade_detail_transaction_description.upper() in trade_transaction_description.upper():
                        merged_bank_statement.write(
                            {'remittance_payer': remittance_payer, 'remittance_bank': remittance_bank})
                        trade_detail_rec.write({'account_bank_statement_id': trade_rec.id,
                                                'account_bank_merged_statement_id': merged_bank_statement.id})
                        break
                    if trade_detail_transaction_description[-11:].upper() in trade_transaction_description.upper():
                        merged_bank_statement.write(
                            {'remittance_payer': remittance_payer, 'remittance_bank': remittance_bank})
                        trade_detail_rec.write({'account_bank_statement_id': trade_rec.id,
                                                'account_bank_merged_statement_id': merged_bank_statement.id})
                        break

    def _process_bid_data(self, auto=False):
        """
        处理银行对账单数据
        """
        super(OnlineBid, self)._process_bid_data(auto)
        for rec in self:
            try:
                if rec.type == 'bank_statement':
                    bank_code = self.name
                    if hasattr(self, f"create_bank_statement_{bank_code}"):
                        special_bankstatement_action = getattr(
                            self, f"create_bank_statement_{bank_code}")
                        special_bankstatement_action(bank_code)
            except Exception as e:
                _logger.error('process_bid_data error ' +
                              str(e) + traceback.format_exc())
                if not auto:
                    raise UserError(_('Process data failed') + str(e))
