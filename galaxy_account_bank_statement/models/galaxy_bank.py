# -*- coding: utf-8 -*-
import json
from odoo import models, fields, api
from datetime import timedelta
import pytz


class GalaxyBank(models.Model):
    _name = 'galaxy.bank'
    _description = 'Galaxy Bank'

    name = fields.Char(help="银行名称")
    bank_code = fields.Char(help="银行代码")
    active = fields.Boolean(default=True)
    note = fields.Text()
    bank_account_ids = fields.One2many('galaxy.bank.account', 'bank_id', string='Bank Accounts')

    def action_view_bank_balance(self):
        """
        查看银行余额
        """
        action = self.sudo().env.ref('galaxy_account_bank_statement.action_galaxy_account_merged_bank_statement').sudo().read()[0]
        account_balance_info = self.get_account_balance()
        context_dict = json.loads(action['context'])
        context_dict.update({"account_balance_info": account_balance_info})
        action["context"] = context_dict
        return action

    def action_refresh_bank_balance(self):
        """
        刷新银行余额
        """
        action = self.sudo().env.ref('galaxy_account_bank_statement.action_galaxy_refresh_bank_statement').sudo().read()[0]
        action['res_id'] = self.env['galaxy.refresh.bank.statement'].sudo().create({
            'bank_id': self.id,
            'transaction_date': fields.Date.today(),
        }).id
        return action

    def get_account_balance(self):
        """
        获取银行余额
        """
        user_tz = self.env.user.tz or 'UTC'  # 如果没有设置时区，默认为UTC
        tz = pytz.timezone(user_tz)
        account_balance_info = {}
        for account in self.bank_account_ids:
            account_balance_info[account.currency_id.name] = {
                "current_balance": f"{account.current_balance:,.2f}",
                "available_balance": f"{account.available_balance:,.2f}",
                "last_update_time": account.last_update_time.astimezone(tz).strftime('%Y-%m-%d %H:%M:%S'),
            }
        return account_balance_info
