from odoo import models, api
import json


class RPCBankStatement(models.AbstractModel):
    _name = "rpc.bank.statement"
    _description = 'RPC Bank Statement'

    """供应商物流单和发票的数据"""

    @api.model
    def create_bank_statement(self, data):
        """
        创建银行对账单
        """
        dict_data = json.loads(data)
        vendor = dict_data.get('data', {}).get('bank_code')
        record = self.env['online.bid.data'].create(
            {'name': vendor, 'type': 'bank_statement', 'data': data})
        record._process_bid_data()
        return {'code': 1, 'msg': 'success'}
