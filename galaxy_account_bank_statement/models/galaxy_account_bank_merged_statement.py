# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.addons.base_galaxy.models.public_func import parse_float


class GalaxyAccountBankMergedStatement(models.Model):
    _name = 'galaxy.account.bank.merged.statement'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Merged Bank Statement'
    _rec_name = 'transaction_id'
    _order = 'id desc'
    name = fields.Char()
    partner_id = fields.Many2one('res.partner', string='Remittance Partner')
    bank_service_statement_id = fields.Many2one('galaxy.account.bank.statement', string="bank_service_statement")
    responsible_person_id = fields.Many2one('res.users', string='Responsible Person')
    index = fields.Integer(help="用于采集信息时的识别", readonly=True)
    account_id = fields.Many2one('galaxy.bank.account', string='Bank Account', readonly=True, index=True)
    bank_id = fields.Many2one('galaxy.bank', related='account_id.bank_id', string='Bank', store=True, readonly=True, index=True)
    bank_code = fields.Char(index=True)
    withdrawl_amount = fields.Float(readonly=True, help="去掉手续费之前的取款金额", digits=(12, 2),)
    net_withdrawl_amount = fields.Float(readonly=True, help="去掉手续费以后的取款金额", compute="_compute_get_net_withdrawl_amount", store=True, digits=(12, 2),)
    deposit_amount = fields.Float(readonly=True, digits=(12, 2), help="去掉手续费之前的存款金额")
    net_deposit_amount = fields.Float(readonly=True, digits=(12, 2), help="去掉手续费以后的存款金额", compute="_compute_get_net_deposit_amount", store=True)
    bank_service_amount = fields.Float(digits=(12, 2),)
    remittance_payer = fields.Char(help="汇款人", readonly=True)
    remittance_bank = fields.Char(help="汇款银行", readonly=True)
    currency_id = fields.Many2one('res.currency', string='Currency', readonly=True)
    currency_name = fields.Char(related='currency_id.name', string='Currency Name', store=True, readonly=True)
    transaction_date = fields.Date(readonly=True, index=True)
    transaction_description = fields.Char(readonly=True)
    transaction_id = fields.Char(help="用于与交易明细ID匹配的唯一识别ID", readonly=True, index=True)
    line_ids = fields.One2many('galaxy.account.bank.statement.line', 'account_bank_merged_statement_id', string='Bank Statement Lines')
    state = fields.Selection([('to_do', 'To do'), ('in_progress', 'Confirm'), ('done', 'Audited')], default='to_do', tracking=True)
    remittance_message = fields.Text(compute="_compute_remittance_message")
    note = fields.Text()

    @api.depends('line_ids')
    def _compute_remittance_message(self):
        for rec in self:
            message = ' '.join(rec.line_ids.mapped("message"))
            rec.remittance_message = message

    @api.depends('withdrawl_amount', 'bank_service_amount')
    def _compute_get_net_withdrawl_amount(self):
        for rec in self:
            if not rec.deposit_amount and rec.withdrawl_amount:
                rec.net_withdrawl_amount = rec.withdrawl_amount - rec.bank_service_amount

    @api.depends('deposit_amount', 'bank_service_amount')
    def _compute_get_net_deposit_amount(self):
        for rec in self:
            if not rec.withdrawl_amount and rec.deposit_amount:
                rec.net_deposit_amount = rec.deposit_amount - rec.bank_service_amount

    def action_in_progress(self):
        """
        后续继承实现
        """
        pass

    def action_done(self):
        """
        后续继承实现
        """
        pass

    def action_to_do(self):
        """
        后续继承实现
        """
        pass

class GalaxyAccountBankMergedStatementLine(models.Model):
    _name = 'galaxy.account.bank.merged.statement.line'
    _description = 'Bank Statement Line'

    name = fields.Char()
    account_bank_statement_id = fields.Many2one('galaxy.account.bank.merged.statement', string='Bank Statement', ondelete='cascade')
    account_id = fields.Many2one('galaxy.bank.account', string='Bank Account')
    bank_id = fields.Many2one('galaxy.bank', string='Bank', related='account_id.bank_id', store=True)
    remittance_currency_id = fields.Many2one('res.currency', string='Remittance Currency', compute="_compute_remittance_currency_id", store=True)
    deposit_amount = fields.Float(compute="_compute_deposit_amount", store=True, digits=(12, 2),)
    deposit_currency_id = fields.Many2one('res.currency', string='Deposit Currency', compute="_compute_deposit_currency_id", store=True)
    charge_currency_id = fields.Many2one('res.currency', string='Charge Currency', compute="_compute_charge_currency_id", store=True)
    # 以下字段用于交易明细是原始数据
    reference_number = fields.Char()
    channel = fields.Char()
    company_name = fields.Char()
    deposit_currency_and_amount = fields.Char()
    charge_currency = fields.Char()
    charge_amount = fields.Float(digits=(12, 2),)
    remittance_bank = fields.Char(help="汇款银行")
    remittance_payer = fields.Char(help="汇款人")
    remittance_currency = fields.Char(help="汇款币种")
    remittance_amount = fields.Float(help="汇款金额", digits=(12, 2),)
    message = fields.Text()
    transaction_date = fields.Date()
    transaction_status = fields.Char()
    transaction_detail_id = fields.Char(help="用于与交易记录ID匹配的唯一识别ID")

    @api.depends('remittance_currency')
    def _compute_remittance_currency_id(self):
        for rec in self:
            currency_name = self.env['galaxy.bank.service'].get_odoo_currency(rec.remittance_currency)
            rec.remittance_currency_id = self.env['res.currency'].search([('name', '=', currency_name)], limit=1).id

    @api.depends('deposit_currency_and_amount')
    def _compute_deposit_amount(self):
        for rec in self:
            rec.deposit_amount = parse_float(rec.deposit_currency_and_amount.split(' ')[-1])

    @api.depends('deposit_currency_and_amount')
    def _compute_deposit_currency_id(self):
        for rec in self:
            currency_name = self.env['galaxy.bank.service'].get_odoo_currency(rec.deposit_currency_and_amount.split('  ')[0].replace(' ',''))
            rec.deposit_currency_id = self.env['res.currency'].search([('name', '=', currency_name)], limit=1).id

    @api.depends('charge_currency')
    def _compute_charge_currency_id(self):
        for rec in self:
            currency_name = self.env['galaxy.bank.service'].get_odoo_currency(rec.charge_currency)
            rec.charge_currency_id = self.env['res.currency'].search([('name', '=', currency_name)], limit=1).id


