
from odoo import models, api

class GalaxyBankService(models.AbstractModel):
    _name = 'galaxy.bank.service'
    _description = 'Galaxy Bank Service'

    @api.model
    def get_odoo_currency(self, currency_name):
        """
        获取Odoo货币
        """
        currency_type_dict = {'港元': "HKD", '美元': "USD"}
        odoo_currency_name = currency_type_dict.get(currency_name, '')
        return odoo_currency_name