# -*- coding: utf-8 -*-
from odoo import models, fields, api


class GalaxyBankAccount(models.Model):
    _name = 'galaxy.bank.account'
    _description = 'Galaxy Bank Account'

    name = fields.Char(compute='_compute_name', store=True)
    bank_id = fields.Many2one('galaxy.bank', string='Bank')
    account_number = fields.Char()
    account_holder_company_id = fields.Many2one('res.company', string='Account Holder Company')
    current_balance = fields.Float(digits=(12, 2))
    available_balance = fields.Float(digits=(12, 2))
    currency_id = fields.Many2one('res.currency', string='Currency')
    active = fields.Boolean(default=True)
    note = fields.Text()
    last_update_time = fields.Datetime()

    @api.depends('bank_id', 'account_number')
    def _compute_name(self):
        for rec in self:
            rec.name = f'{rec.bank_id.name} - {rec.account_number}'
