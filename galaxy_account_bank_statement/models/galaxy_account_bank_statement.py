# -*- coding: utf-8 -*-

from odoo import models, fields, api
from odoo.addons.base_galaxy.models.public_func import parse_float


class GalaxyAccountBankStatement(models.Model):
    _name = 'galaxy.account.bank.statement'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Bank Statement'

    name = fields.Char()
    responsible_person_id = fields.Many2one('res.users', string='Responsible Person')
    index = fields.Integer(help="用于采集信息时的识别", readonly=True)
    account_id = fields.Many2one('galaxy.bank.account', string='Bank Account', readonly=True, index=True)
    bank_id = fields.Many2one('galaxy.bank', related='account_id.bank_id', string='Bank', store=True, readonly=True, index=True)
    bank_code = fields.Char(index=True)
    withdrawl_amount = fields.Float(help="取款金额", readonly=True, digits=(12, 2),)
    deposit_amount = fields.Float(help="存款金额", readonly=True, digits=(12, 2),)
    remittance_payer = fields.Char(help="汇款人", readonly=True)
    remittance_bank = fields.Char(help="汇款银行", readonly=True)
    currency_id = fields.Many2one('res.currency', string='Currency', readonly=True)
    currency_name = fields.Char(related='currency_id.name', string='Currency Name', store=True, readonly=True)
    transaction_date = fields.Date(readonly=True, index=True)
    transaction_description = fields.Char(readonly=True)
    transaction_id = fields.Char(help="用于与交易明细ID匹配的唯一识别ID", readonly=True, index=True)
    line_ids = fields.One2many('galaxy.account.bank.statement.line', 'account_bank_statement_id', string='Bank Statement Lines')
    state = fields.Selection([('to_do', 'To do'), ('in_progress', 'In progress'), ('done', 'Done')], default='to_do', tracking=True)
    note = fields.Text()
    related_statement_transaction_id = fields.Char(help="用于与合并银行对账单的交易记录ID匹配的唯一识别ID, 仅仅是手续费会有这个id，识别主交易id", readonly=True, index=True)

    def action_in_progress(self):
        self.write({'state': 'in_progress'})
    
    def action_done(self):
        self.write({'state': 'done'})
    
    def action_to_do(self):
        self.write({'state': 'to_do'})


class GalaxyAccountBankStatementLine(models.Model):
    _name = 'galaxy.account.bank.statement.line'
    _description = 'Bank Statement Line'

    name = fields.Char()
    account_bank_statement_id = fields.Many2one('galaxy.account.bank.statement', string='Bank Statement', ondelete='cascade')
    account_bank_merged_statement_id = fields.Many2one('galaxy.account.bank.merged.statement', string='Merged Bank Statement', ondelete='cascade')
    account_id = fields.Many2one('galaxy.bank.account', string='Bank Account')
    bank_id = fields.Many2one('galaxy.bank', string='Bank', related='account_id.bank_id', store=True)
    remittance_currency_id = fields.Many2one('res.currency', string='Remittance Currency', compute="_compute_remittance_currency_id", store=True)
    deposit_amount = fields.Float(compute="_compute_deposit_amount", store=True, digits=(12, 2),)
    deposit_currency_id = fields.Many2one('res.currency', string='Deposit Currency', compute="_compute_deposit_currency_id", store=True)
    charge_currency_id = fields.Many2one('res.currency', string='Charge Currency', compute="_compute_charge_currency_id", store=True)
    # 以下字段用于交易明细是原始数据
    reference_number = fields.Char()
    channel = fields.Char()
    company_name = fields.Char()
    deposit_currency_and_amount = fields.Char()
    charge_currency = fields.Char()
    charge_amount = fields.Float(digits=(12, 2),)
    remittance_bank = fields.Char(help="汇款银行")
    remittance_payer = fields.Char(help="汇款人")
    remittance_currency = fields.Char(help="汇款币种")
    remittance_amount = fields.Float(help="汇款金额", digits=(12, 2),)
    message = fields.Text()
    transaction_date = fields.Date()
    transaction_status = fields.Char()
    transaction_detail_id = fields.Char(help="用于与交易记录ID匹配的唯一识别ID")

    @api.depends('remittance_currency')
    def _compute_remittance_currency_id(self):
        for rec in self:
            currency_name = self.env['galaxy.bank.service'].get_odoo_currency(rec.remittance_currency)
            rec.remittance_currency_id = self.env['res.currency'].search([('name', '=', currency_name)], limit=1).id

    @api.depends('deposit_currency_and_amount')
    def _compute_deposit_amount(self):
        for rec in self:
            rec.deposit_amount = parse_float(rec.deposit_currency_and_amount.split(' ')[-1])

    @api.depends('deposit_currency_and_amount')
    def _compute_deposit_currency_id(self):
        for rec in self:
            currency_name = self.env['galaxy.bank.service'].get_odoo_currency(rec.deposit_currency_and_amount.split('  ')[0].replace(' ',''))
            rec.deposit_currency_id = self.env['res.currency'].search([('name', '=', currency_name)], limit=1).id

    @api.depends('charge_currency')
    def _compute_charge_currency_id(self):
        for rec in self:
            currency_name = self.env['galaxy.bank.service'].get_odoo_currency(rec.charge_currency)
            rec.charge_currency_id = self.env['res.currency'].search([('name', '=', currency_name)], limit=1).id
