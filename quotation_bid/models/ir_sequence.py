from odoo import models


getNextIds = """
    
        CREATE OR REPLACE FUNCTION get_next_ids (VARCHAR,INTEGER) RETURNS VARCHAR AS $body$
        DECLARE str VARCHAR ;
        DECLARE nextid VARCHAR ;
        BEGIN

            str = '';
            FOR i in 1..$2 loop
                nextid = (SELECT NEXTVAL($1));
                str = str || nextid || ',' ;
            END loop;

            RETURN str ;
        END ; 
        $body$ 
        LANGUAGE 'plpgsql';
    """

class IrSequence(models.Model):
    _inherit = 'ir.sequence'

    def _next_do(self):
        batch_bid = self._context.get('batch_bid')
        if self.implementation == 'standard' and batch_bid:
            self._cr.execute("select oid from pg_proc where proname='get_next_ids'")
            func_of_getNextIds = self._cr.fetchone()
            if not func_of_getNextIds:
                self._cr.execute(getNextIds)      
            self._cr.execute("select get_next_ids('ir_sequence_%03d',%d)" % (self.id,batch_bid))
            sequences = self._cr.fetchone()
            return ''.join(sequences[0])
        else:
            return super(IrSequence,self)._next_do()
