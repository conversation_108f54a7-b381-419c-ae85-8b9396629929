odoo.define('quotation_bid.vendor_rfq_publish_lot', function (require) {
    "use strict";
    
    var core = require('web.core');    
    var _t = core._t;
    
    async function rpc_batch_publish_lot(parent,bid_order_ids,lots,bid_order_id){
        await parent._rpc({
            model: 'generate.bid.information',
            method: 'batch_write_bid_order',
            args: ["",bid_order_id,bid_order_ids],
        }).then(function(result){
          if(result =='ok'){
                var action= {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title':_t('Publish'),
                        'message': _t('The number of published lots is')+String(lots),
                        'sticky': false,
                        'type':'info',
                    }
                }
                parent.do_action(action);

            }
            else{
                alert(_t('error occured ')+ result)
            }  
        })
    }
    
    async function __batch_publish_lot(parent, action) {
        var bid_order_ids = action.params.bid_order_ids;
        var times = action.params.times;
        var threshold = action.params.threshold;
        var bid_order_id = action.params.bid_order_id;
        var lots = threshold
        var endsign = false
        for (var loop_times=1;loop_times<=times;loop_times++){
            var start = threshold *(loop_times - 1)
            var end = threshold*loop_times
            var bid_order_lots = bid_order_ids.slice(start,end)
            lots = lots + bid_order_lots.length
            if (loop_times === times){
                endsign = true
            }
            await rpc_batch_publish_lot(parent,bid_order_lots,lots,bid_order_id);
         
        }
        if(endsign){
            parent.do_action({type:'ir.actions.act_window_close'})
        }

        
    } 
    
    function vendor_rfq_publish_lot(parent, action){
        __batch_publish_lot(parent, action)

    };
    core.action_registry.add("vendor_rfq_publish_lot", vendor_rfq_publish_lot);
    
    });