from odoo import models, fields


class CustomerQuotationTrans(models.TransientModel):
    _name = 'customer.quotation.trans'
    _description = 'update customer quotation trans'

    bid_order_lot_line_id = fields.Many2one('bid.order.lot.line')
    make = fields.Char()
    model = fields.Char()
    capacity = fields.Char()
    color = fields.Char()
    version = fields.Char(string='Carrier')
    carrier_lock = fields.Char()
    vendor_grade = fields.Char()
    quantity = fields.Integer()
    vendor_sku_reference = fields.Char()
    partner_id = fields.Many2one('res.partner')
    currency_id = fields.Many2one('res.currency')
    before_price = fields.Float(string='Previous Quote')
    after_price = fields.Float(string="Latest Quote")


    def create_cutomer_quotation(self):
        pass

    # @api.onchange('bid_order_lot_id')
    # def _onchange_bid_order_lot_id(self):
    #     self.line_ids = [(5, 0, 0)]
    #     lines_to_add = []
    #     if self.bid_order_lot_id.order_id.bid_type == 'lot':
    #         lines_to_add.append((0, 0, {'bid_order_lot_id': self.bid_order_lot_id.id,
    #                                     'before_price': self.bid_order_lot_id.customer_bid.bid_price,
    #                                     'after_price': self.bid_order_lot_id.customer_bid.bid_price,
    #                                     'partner_id': self.bid_order_lot_id.customer_bid.name.id}))
    #     else:
    #         for item in self.bid_order_lot_id.bid_order_lot_line_ids:
    #             lines_to_add.append((0, 0, {'bid_order_lot_line_id': item.id,
    #                                         'before_price': item.customer_bid.bid_price,
    #                                         'after_price': item.customer_bid.bid_price,
    #                                         'partner_id': item.customer_bid.name.id}))
    #     self.line_ids = lines_to_add

    # def to_update_customer_quotation(self):
    #     update_quotation = []
    #     for line in self.line_ids:
    #         if line.before_price != line.after_price:
    #             if line.bid_order_lot_id:
    #                 update_quotation.append({'bid_order_lot_id': line.bid_order_lot_id,
    #                                          'before_price': line.before_price,
    #                                          'after_price': line.after_price,
    #                                          'partner_id': line.partner_id})
    #             else:
    #                 update_quotation.append({'bid_order_lot_line_id': line.bid_order_lot_line_id,
    #                                          'before_price': line.before_price,
    #                                          'after_price': line.after_price,
    #                                          'partner_id': line.partner_id})
    #     if update_quotation:
    #         self.env['update.customer.quotation'].create(update_quotation)
