# -*- coding: utf-8 -*-
{
    'name': "java_bid_server",

    'summary': """
        only for java bid server api
       """,

    'description': """
      only for java bid server api
    """,

    'author': "<PERSON><PERSON>",
    'website': "http://www.yourcompany.com",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/14.0/odoo/addons/base/data/ir_module_category_data.xml
    # for the full list
    'category': 'Galaxy_ERP/Galaxy_ERP',
    'version': '0.1',

    # any module necessary for this one to work correctly
    'depends': ['base'],

    # always loaded
    'data': [
     
    ],
    # only loaded in demonstration mode
 
}
