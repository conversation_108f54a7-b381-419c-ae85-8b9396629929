FROM postgres:12

# 安装必要的构建依赖
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        build-essential \
        postgresql-server-dev-12 \
        postgresql-contrib \
    && rm -rf /var/lib/apt/lists/*

# 启用必要的扩展
RUN echo "shared_preload_libraries = 'pg_stat_statements'" >> /usr/share/postgresql/postgresql.conf.sample

# 创建扩展初始化脚本
COPY ./init-postgres-extensions.sql /docker-entrypoint-initdb.d/ 