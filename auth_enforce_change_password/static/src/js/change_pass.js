odoo.define('base_calendar.base_calendar', function (require) {
    "use strict";
    var session = require('web.session');
    var WebClient = require('web.WebClient'); 
    WebClient.include({
        show_application: async function () {
            // await this._super(...arguments);
            var self=this;
            const _super = this._super.bind(this);
            self._rpc({
                model: 'res.users',
                method: 'read',
                args: [session.uid, ["enforce_change_pwd"]],
            }).then(function (result){
                if(result[0].enforce_change_pwd){
                    return self.do_action({
                        'type': 'ir.actions.client',
                        'tag': 'change_password',
                        'target': 'new',
                    },{on_close:function(){
                        self.do_action({
                            'type': 'ir.actions.client',
                            'tag': 'logout',
                        })
                    }})
                }else{
                    return _super(...arguments);
                }
            })
        },
    });
});
