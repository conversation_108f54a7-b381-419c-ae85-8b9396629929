# (c) 2015 ACSONE SA/NV, Dhinesh D

# License AGPL-3.0 or later (http://www.gnu.org/licenses/agpl.html).

import logging
from odoo import models, fields


_logger = logging.getLogger(__name__)


class ResUsers(models.Model):
    _inherit = "res.users"

    enforce_change_pwd = fields.Boolean('Enforce Change password next login', default=False)

    def change_password(self, old_passwd, new_passwd):
        result = super(ResUsers, self).change_password(old_passwd, new_passwd)
        self.env.user.write({'enforce_change_pwd': False})
        return result
