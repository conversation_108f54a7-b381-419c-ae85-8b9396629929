from odoo import models, fields, api

class GalaxyHylaCustomerBid(models.Model):
    _name = 'galaxy.hyla.customer.bid'
    _description = 'Galaxy Hyla Customer Bid'

    product_id = fields.Many2one('galaxy.hyla.product.list', string='Product', required=True)
    customer_id = fields.Many2one('res.partner', string='Customer', required=True)
    bid_price = fields.Monetary(string='Bid Price', currency_field='currency_id', required=True)
    currency_id = fields.Many2one('res.currency', string='Currency', required=True)
    bid_date = fields.Datetime(string='Bid Date',required=True)
    state = fields.Selection([('bid', 'Bidding'), ('won', 'Won'), ('failed', 'Lost')], string='Status', default='bid')
