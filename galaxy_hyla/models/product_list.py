from odoo import models, fields, api


class GalaxyHylaProductList(models.Model):
    """
    产品清单
    """
    _name = 'galaxy.hyla.product.list'
    _description = 'This table is used to record the HYLA product list'

    name = fields.Char(
        related='sku_id.name',
        string='SKU Name',
        store=True,
        readonly=True
    )
    supplier_id = fields.Many2one('res.partner', string='Supplier', required=True)
    currency_id = fields.Many2one('res.currency', string='Currency', required=True)
    # 标价
    list_price = fields.Monetary(string='List Price', currency_field='currency_id', required=True)
    # 还价
    counter_price = fields.Monetary(string='Counter Price', currency_field='currency_id')
    # 是否处于还价中
    is_countering = fields.Boolean(string='Is Countering')
    # 还价限时
    counter_end_time = fields.Datetime(string='Counter End Time')
    # 出价
    offer_price = fields.Monetary(string='Offer Price', currency_field='currency_id')
    # 意向价（最高客户的出价）
    indicative_price = fields.Monetary(string='Indicative Price', currency_field='currency_id')
    # 运费
    shipping_cost = fields.Float(string='Shipping')
    # 仓库
    warehouse = fields.Char(string="Warehouse")
    sku_id = fields.Many2one('product.product', string='SKU', required=True)
    # 数量
    qty = fields.Integer(string='Quantity', required=True)
    # 库存更新时间
    stock_update_date = fields.Datetime(string='Stock Update Date')
    # 备注
    note = fields.Text(string='Note')
    # 外部产品编号
    external_product_number = fields.Char(string='External Product Number')
    # 发布状态：待发布、已发布、不发布
    publish_state = fields.Selection([('draft', 'Draft'), ('published', 'Published'), ('not_publish', 'Not Publish')],
                                     string='Publish State', default='draft')

    @api.onchange('sku_id', 'supplier_id')
    def _onchange_sku_supplier(self):
        for rec in self:
            if rec.sku_id and rec.supplier_id:
                rule = self.env['supplier.shipping'].search([
                    ('supplier', '=', rec.supplier_id.id),
                    ('erp_category', '=', rec.sku_id.categ_id.id)
                ], limit=1)
                rec.shipping_cost = rule.shipping if rule else 0

    def get_attr_values_by_attr_key(self, attr_key) -> str:
        """
        指定属性名称，搜索属性值sku_id关联的所有属性值
        """
        data = self.sku_id.product_template_attribute_value_ids1.filtered(
            lambda m: m.attribute_id.attribute_key == attr_key
        ).mapped('name')

        return ",".join(data)