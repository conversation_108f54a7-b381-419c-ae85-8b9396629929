# -*- coding: utf-8 -*-

import json
from odoo import http
from odoo.http import request


class HylaSpaController(http.Controller):

    @http.route(['/hyla/spa'], type='http', auth="public", website=True)
    def hyla_spa_app(self, **kw):
        """Hyla单页应用入口"""
        return request.render("galaxy_hyla.hyla_spa_app", {})

    @http.route(['/hyla/api/products'], type='json', auth="public", csrf=False)
    def get_products(self, **kw):
        """获取产品列表API"""
        try:
            products = request.env['galaxy.hyla.product.list'].sudo().search_read(
                [], 
                ['id', 'name', 'external_product_number', 'qty', 'list_price', 'currency_id']
            )
            
            # 处理货币信息
            for product in products:
                if product.get('currency_id'):
                    currency = request.env['res.currency'].sudo().browse(product['currency_id'][0])
                    product['currency_symbol'] = currency.symbol
                else:
                    product['currency_symbol'] = 'HKD'
            
            return {
                'success': True,
                'products': products
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    @http.route(['/hyla/api/login'], type='json', auth="public", csrf=False)
    def spa_login(self, mobile, password, **kw):
        """SPA登录API"""
        try:
            # 这里调用你的java_login方法
            from odoo.addons.galaxy_hyla.services.login import java_login
            java_token = java_login(mobile, password)
            
            if java_token:
                # 创建会话token
                import time
                import hashlib
                session_token = hashlib.md5(f"{mobile}-{java_token}-{time.time()}".encode()).hexdigest()
                
                # 设置到session中
                request.session['hyla_token'] = session_token
                request.session['hyla_mobile'] = mobile
                
                return {
                    'success': True,
                    'token': session_token,
                    'mobile': mobile
                }
            else:
                return {
                    'success': False,
                    'error': 'Invalid credentials'
                }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    @http.route(['/hyla/api/buy'], type='json', auth="public", csrf=False)
    def create_order(self, product_id, quantity, **kw):
        """创建订单API"""
        try:
            # 检查登录状态
            if not request.session.get('hyla_token'):
                return {
                    'success': False,
                    'error': 'Not logged in'
                }
            
            # 获取产品信息
            product = request.env['galaxy.hyla.product.list'].sudo().browse(product_id)
            if not product.exists():
                return {
                    'success': False,
                    'error': 'Product not found'
                }
            
            # 检查库存
            if quantity > product.qty:
                return {
                    'success': False,
                    'error': 'Insufficient stock'
                }
            
            # 创建订单数据
            order_data = {
                'product_id': product_id,
                'product_name': product.name,
                'quantity': quantity,
                'unit_price': product.list_price,
                'total_price': quantity * product.list_price,
                'mobile': request.session.get('hyla_mobile'),
                'create_time': time.time()
            }
            
            # 这里可以保存到数据库或调用其他业务逻辑
            
            return {
                'success': True,
                'order': order_data,
                'message': 'Order created successfully'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
