<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- 一口价购买弹窗 -->
    <template id="hyla_buy_modal" name="<PERSON>yla Buy Modal" t-translation="on">
        <style>
            .hyla-modal-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: none;
                justify-content: center;
                align-items: center;
                z-index: 1000;
            }
            
            .hyla-modal-content {
                background: white;
                border-radius: 8px;
                width: 90%;
                max-width: 500px;
                padding: 0;
                position: relative;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            }
            
            .hyla-modal-header {
                padding: 20px 20px 15px 20px;
                border-bottom: 1px solid #eee;
                position: relative;
            }
            
            .hyla-modal-title {
                font-size: 18px;
                font-weight: 600;
                color: #333;
                margin: 0;
            }
            
            .hyla-modal-close {
                position: absolute;
                top: 15px;
                right: 15px;
                background: none;
                border: none;
                font-size: 24px;
                cursor: pointer;
                color: #666;
                padding: 5px;
                line-height: 1;
            }
            
            .hyla-modal-close:hover {
                color: #333;
            }
            
            .hyla-modal-body {
                padding: 20px;
            }
            
            .hyla-product-info {
                background: #f8f9fa;
                padding: 15px;
                border-radius: 6px;
                margin-bottom: 20px;
            }
            
            .hyla-product-id {
                color: #999;
                font-size: 12px;
                text-align: right;
                margin-bottom: 5px;
            }
            
            .hyla-product-desc {
                color: #666;
                font-size: 13px;
                line-height: 1.4;
            }
            
            .hyla-buy-form {
                display: flex;
                gap: 20px;
                align-items: flex-start;
            }
            
            .hyla-form-left {
                flex: 1;
            }
            
            .hyla-form-right {
                flex: 1;
            }
            
            .hyla-form-group {
                margin-bottom: 15px;
            }
            
            .hyla-form-label {
                display: block;
                margin-bottom: 8px;
                color: #333;
                font-weight: 500;
                font-size: 14px;
            }
            
            .hyla-stock-info {
                color: #666;
                font-size: 14px;
                margin-bottom: 8px;
            }
            
            .hyla-quantity-input {
                width: 100%;
                padding: 10px 12px;
                border: 2px solid #ddd;
                border-radius: 4px;
                font-size: 16px;
                text-align: center;
                box-sizing: border-box;
            }
            
            .hyla-quantity-input:focus {
                outline: none;
                border-color: #007bff;
            }
            
            .hyla-quantity-warning {
                background: #fff3cd;
                color: #856404;
                padding: 8px 12px;
                border-radius: 4px;
                font-size: 12px;
                margin-top: 8px;
                display: flex;
                align-items: center;
            }
            
            .hyla-quantity-warning::before {
                content: "⚠";
                margin-right: 6px;
                color: #f0ad4e;
            }
            
            .hyla-price-info {
                text-align: center;
            }
            
            .hyla-unit-price {
                color: #666;
                font-size: 14px;
                margin-bottom: 8px;
            }
            
            .hyla-total-price {
                font-size: 20px;
                font-weight: 600;
                color: #333;
                margin-bottom: 20px;
            }
            
            .hyla-buy-btn {
                width: 100%;
                padding: 12px;
                background: #17a2b8;
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 16px;
                font-weight: 600;
                cursor: pointer;
                transition: background-color 0.2s;
            }
            
            .hyla-buy-btn:hover {
                background: #138496;
            }
            
            .hyla-buy-btn:disabled {
                background: #ccc;
                cursor: not-allowed;
            }
            
            /* 响应式设计 */
            @media (max-width: 600px) {
                .hyla-modal-content {
                    width: 95%;
                    margin: 20px;
                }
                
                .hyla-buy-form {
                    flex-direction: column;
                    gap: 15px;
                }
                
                .hyla-modal-header {
                    padding: 15px;
                }
                
                .hyla-modal-body {
                    padding: 15px;
                }
            }
        </style>

        <!-- 弹窗遮罩层 -->
        <div id="hyla-buy-modal" class="hyla-modal-overlay">
            <div class="hyla-modal-content">
                <!-- 弹窗头部 -->
                <div class="hyla-modal-header">
                    <h3 class="hyla-modal-title" id="modal-product-name">Apple iPhone 12 Pro</h3>
                    <button class="hyla-modal-close" onclick="closeBuyModal()">&times;</button>
                </div>
                
                <!-- 弹窗内容 -->
                <div class="hyla-modal-body">
                    <!-- 产品信息 -->
                    <div class="hyla-product-info">
                        <div class="hyla-product-id" id="modal-product-id">Item#20241813</div>
                        <div class="hyla-product-desc" id="modal-product-desc">
                            00000004 Apple iPhone 5s 64GB Gold Verizon Unlocked ID OFF HYLA DLS B+ Phone
                        </div>
                    </div>
                    
                    <!-- 购买表单 -->
                    <div class="hyla-buy-form">
                        <!-- 左侧：数量选择 -->
                        <div class="hyla-form-left">
                            <div class="hyla-form-group">
                                <label class="hyla-form-label">库存数量：</label>
                                <div class="hyla-stock-info" id="modal-stock-qty">100</div>
                            </div>
                            
                            <div class="hyla-form-group">
                                <label class="hyla-form-label" for="buy-quantity">购买数量</label>
                                <input 
                                    type="number" 
                                    id="buy-quantity" 
                                    class="hyla-quantity-input" 
                                    value="100" 
                                    min="1" 
                                    max="100"
                                    onchange="updateTotalPrice()"
                                    oninput="validateQuantity()"
                                />
                                <div id="quantity-warning" class="hyla-quantity-warning" style="display: none;">
                                    购买数量不能超过库存数量
                                </div>
                            </div>
                        </div>
                        
                        <!-- 右侧：价格和购买按钮 -->
                        <div class="hyla-form-right">
                            <div class="hyla-price-info">
                                <div class="hyla-unit-price">
                                    标价：<span id="modal-unit-price">1500</span> 币种：<span id="modal-currency">HKD</span>
                                </div>
                                <div class="hyla-total-price">
                                    总价：<span id="modal-total-price">150,000</span>
                                </div>
                                <button class="hyla-buy-btn" onclick="submitBuyOrder()">
                                    购买
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- JavaScript -->
        <script type="text/javascript">
            let currentProduct = null;

            function openBuyModal(product) {
                currentProduct = product;
                
                // 更新弹窗内容
                document.getElementById('modal-product-name').textContent = product.name || '';
                document.getElementById('modal-product-id').textContent = product.external_product_number || '';
                document.getElementById('modal-product-desc').textContent = product.description || product.name || '';
                document.getElementById('modal-stock-qty').textContent = product.qty || 0;
                document.getElementById('modal-unit-price').textContent = product.list_price || 0;
                document.getElementById('modal-currency').textContent = product.currency_symbol || 'HKD';
                
                // 设置数量输入框
                const quantityInput = document.getElementById('buy-quantity');
                quantityInput.max = product.qty || 0;
                quantityInput.value = Math.min(100, product.qty || 0);
                
                // 更新总价
                updateTotalPrice();
                
                // 显示弹窗
                document.getElementById('hyla-buy-modal').style.display = 'flex';
                document.body.style.overflow = 'hidden';
            }

            function closeBuyModal() {
                document.getElementById('hyla-buy-modal').style.display = 'none';
                document.body.style.overflow = 'auto';
                currentProduct = null;
            }

            function validateQuantity() {
                const quantityInput = document.getElementById('buy-quantity');
                const warning = document.getElementById('quantity-warning');
                const buyBtn = document.querySelector('.hyla-buy-btn');
                
                const quantity = parseInt(quantityInput.value) || 0;
                const maxQty = parseInt(quantityInput.max) || 0;
                
                if (quantity > maxQty) {
                    warning.style.display = 'block';
                    buyBtn.disabled = true;
                } else {
                    warning.style.display = 'none';
                    buyBtn.disabled = false;
                }
                
                updateTotalPrice();
            }

            function updateTotalPrice() {
                if (!currentProduct) return;
                
                const quantity = parseInt(document.getElementById('buy-quantity').value) || 0;
                const unitPrice = parseFloat(currentProduct.list_price) || 0;
                const totalPrice = quantity * unitPrice;
                
                document.getElementById('modal-total-price').textContent = totalPrice.toLocaleString();
            }

            function submitBuyOrder() {
                if (!currentProduct) return;
                
                const quantity = parseInt(document.getElementById('buy-quantity').value) || 0;
                
                if (quantity <= 0) {
                    alert('请输入有效的购买数量');
                    return;
                }
                
                if (quantity > currentProduct.qty) {
                    alert('购买数量不能超过库存数量');
                    return;
                }
                
                // 这里添加提交订单的逻辑
                const orderData = {
                    product_id: currentProduct.id,
                    quantity: quantity,
                    unit_price: currentProduct.list_price,
                    total_price: quantity * currentProduct.list_price
                };
                
                console.log('提交订单:', orderData);
                alert('订单提交成功！');
                closeBuyModal();
            }

            // 点击遮罩层关闭弹窗
            document.addEventListener('click', function(e) {
                if (e.target.id === 'hyla-buy-modal') {
                    closeBuyModal();
                }
            });

            // ESC键关闭弹窗
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    closeBuyModal();
                }
            });
        </script>
    </template>
</odoo>
