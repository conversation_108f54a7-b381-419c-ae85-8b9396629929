<odoo>
    <template id="hyla_product_card" name="Hyla Product Card" t-translation="on">
        <style>
            .hyla-product-title {
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 15px;
            }

            .hyla-product-card {
                border: 1px solid #ddd;
                border-radius: 8px;
                padding: 20px;
                margin-bottom: 15px;
                background: white;
                position: relative;
            }

            .hyla-product-specs {
                display: flex;
                flex-direction: column;
                gap: 8px;
                margin-bottom: 15px;
            }

            .hyla-spec-item {
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .hyla-spec-icon {
                width: 16px;
                height: 16px;
                border: 1px solid #666;
                border-radius: 50%;
                display: inline-block;
            }

            .hyla-product-footer {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-top: 15px;
                padding-top: 15px;
                border-top: 1px solid #eee;
            }

            .hyla-price-section {
                display: flex;
                align-items: center;
                gap: 15px;
            }

            .hyla-quantity {
                font-size: 24px;
                font-weight: bold;
            }

            .hyla-price {
                font-size: 18px;
                color: #333;
            }

            .hyla-bid-btn {
                background-color: #17a2b8;
                color: white;
                border: none;
                padding: 8px 20px;
                border-radius: 5px;
                cursor: pointer;
            }

            .hyla-favorite {
                font-size: 20px;
                color: #ccc;
                cursor: pointer;
            }

            .hyla-favorite.active {
                color: #ffc107;
            }

            .hyla-product-id {
                color: #999;
                font-size: 12px;
            }

            .hyla-rank-badge {
                position: absolute;
                top: 10px;
                right: 10px;
                background-color: #ffc107;
                color: black;
                padding: 2px 8px;
                border-radius: 3px;
                font-size: 12px;
                font-weight: bold;
            }
        </style>
        <div class="hyla-product-card">
            <div class="row">
                <div class="col-md-6">
                    <div class="hyla-product-title" t-esc="product.get_short_name()"/>
                    <div class="hyla-product-specs">
                        <t t-set="capacity" t-value="product.get_attr_values_by_attr_key('capacity')"/>
                        <t t-set="color" t-value="product.get_attr_values_by_attr_key('color')"/>
                        <t t-set="carrier" t-value="product.get_attr_values_by_attr_key('carrier')"/>
                        <t t-set="carrier_lock" t-value="product.get_attr_values_by_attr_key('carrier_lock')"/>
                        <t t-set="fmip_frp_lock" t-value="product.get_attr_values_by_attr_key('fmip/frp_lock')"/>

                        <t t-if="capacity">
                            <div class="hyla-spec-item">
                                <span class="hyla-spec-icon"></span>
                                <span t-esc="capacity"/>
                            </div>
                        </t>
                        <t t-if="color">
                            <div class="hyla-spec-item">
                                <span class="hyla-spec-icon"></span>
                                <span t-esc="color"/>
                            </div>
                        </t>
                        <t t-if="carrier">
                            <div class="hyla-spec-item">
                                <span class="hyla-spec-icon"></span>
                                <span t-esc="carrier"/>
                            </div>
                        </t>
                        <t t-if="carrier_lock">
                            <div class="hyla-spec-item">
                                <span class="hyla-spec-icon"></span>
                                <span t-esc="carrier_lock"/>
                            </div>
                        </t>
                        <t t-if="fmip_frp_lock">
                            <div class="hyla-spec-item">
                                <span class="hyla-spec-icon"></span>
                                <span t-esc="fmip_frp_lock"/>
                            </div>
                        </t>
                        <div class="hyla-spec-item">
                            <span class="hyla-spec-icon"></span>
                            <span t-esc="product.sku_id.id"/>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 text-end">
                    <div class="hyla-price-section">
                        <div>
                            <div class="hyla-quantity"><t t-esc="product.qty"/>台</div>
                        </div>
                        <div>
                            <div class="hyla-price">港幣 1650</div>
                        </div>
                        <div>
                            <button class="hyla-bid-btn">BUY</button>
                        </div>
                        <div>
                            <span class="hyla-favorite">★</span>
                        </div>
                    </div>

                    <div style="margin-top: 15px;">
                        <span>意向价</span>
                        <input type="text" value="1600" style="width: 80px; margin-left: 10px;"/>
                    </div>
                </div>
            </div>

            <div class="hyla-product-footer">
                <span class="hyla-product-id" t-esc="product.sku_id.default_code"/>
                <span class="hyla-product-id" t-esc="product.name"/>
                <span class="hyla-product-id" t-esc="product.external_product_number"/>
            </div>
        </div>
    </template>
</odoo>