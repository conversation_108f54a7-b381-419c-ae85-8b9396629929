<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- <PERSON><PERSON> Page -->
    <template id="hyla_login_page" name="<PERSON><PERSON> Login Page">
        <t t-call="galaxy_hyla.empty_layout">
            <style>
                .hyla-login-container {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100vw;
                    height: 100vh;
                    min-height: 100vh;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    margin: 0;
                    padding: 0;
                }

                .hyla-login-card {
                    background: white;
                    border-radius: 15px;
                    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
                    padding: 40px;
                    width: 100%;
                    max-width: 400px;
                    margin: 20px;
                }

                .hyla-login-header {
                    text-align: center;
                    margin-bottom: 30px;
                }

                .hyla-login-logo {
                    font-size: 2.5rem;
                    font-weight: bold;
                    color: #667eea;
                    margin-bottom: 10px;
                }

                .hyla-login-subtitle {
                    color: #666;
                    font-size: 1rem;
                    margin-bottom: 0;
                }

                .hyla-form-group {
                    margin-bottom: 20px;
                }

                .hyla-form-label {
                    display: block;
                    margin-bottom: 8px;
                    color: #333;
                    font-weight: 500;
                    font-size: 0.9rem;
                }

                .hyla-form-input {
                    width: 100%;
                    padding: 12px 16px;
                    border: 2px solid #e1e5e9;
                    border-radius: 8px;
                    font-size: 1rem;
                    transition: border-color 0.3s ease;
                    box-sizing: border-box;
                }

                .hyla-form-input:focus {
                    outline: none;
                    border-color: #667eea;
                    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                }

                .hyla-login-btn {
                    width: 100%;
                    padding: 12px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    border: none;
                    border-radius: 8px;
                    font-size: 1rem;
                    font-weight: 600;
                    cursor: pointer;
                    transition: transform 0.2s ease, box-shadow 0.2s ease;
                    margin-top: 10px;
                }

                .hyla-login-btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
                }

                .hyla-login-btn:active {
                    transform: translateY(0);
                }

                .hyla-login-btn:disabled {
                    opacity: 0.6;
                    cursor: not-allowed;
                    transform: none;
                }

                .hyla-error-message {
                    background: #fee;
                    color: #c33;
                    padding: 12px;
                    border-radius: 8px;
                    margin-bottom: 20px;
                    border-left: 4px solid #c33;
                    font-size: 0.9rem;
                }



                .hyla-footer {
                    text-align: center;
                    margin-top: 30px;
                    color: #999;
                    font-size: 0.8rem;
                }

                /* 响应式设计 */
                @media (max-width: 480px) {
                    .hyla-login-card {
                        padding: 30px 20px;
                        margin: 10px;
                    }

                    .hyla-login-logo {
                        font-size: 2rem;
                    }
                }
            </style>

            <div class="hyla-login-container">
                <div class="hyla-login-card">
                    <!-- Header -->
                    <div class="hyla-login-header">
                        <div class="hyla-login-logo">EBID</div>
                        <p class="hyla-login-subtitle">登录您的账户</p>
                    </div>

                    <!-- Error Message -->
                    <div t-if="error" class="hyla-error-message">
                        <span t-if="error == 'missing_credentials'">请输入手机号和密码</span>
                        <span t-elif="error == 'login_failed'">登录失败，请检查手机号和密码</span>
                        <span t-elif="error == 'expired'">登录已过期，请重新登录</span>
                        <span t-else="">登录出现错误，请重试</span>
                    </div>

                    <!-- Login Form -->
                    <form id="hyla-login-form" action="/hyla/login/submit" method="post">
                        <div class="hyla-form-group">
                            <label for="mobile" class="hyla-form-label">手机号</label>
                            <input
                                type="tel"
                                id="mobile"
                                name="mobile"
                                class="hyla-form-input"
                                placeholder="请输入手机号"
                                required="required"
                                maxlength="11"
                                pattern="[0-9]{11}"
                                autocomplete="username"
                            />
                        </div>

                        <div class="hyla-form-group">
                            <label for="password" class="hyla-form-label">密码</label>
                            <input
                                type="password"
                                id="password"
                                name="password"
                                class="hyla-form-input"
                                placeholder="请输入密码"
                                required="required"
                                autocomplete="current-password"
                            />
                        </div>

                        <button type="submit" id="login-btn" class="hyla-login-btn">
                            登录
                        </button>
                        <div style="text-align: center;padding-top: 3px;">
                            <p>没有账号?请下载
                                <a href="https://apps.apple.com/cn/app">EBID</a>
                                注册
                            </p>
                        </div>
                    </form>

                    <!-- Footer -->
                    <div class="hyla-footer">
                        <p>© 2025 GALAXY TELECOM (HK) LTD. All rights reserved.</p>
                    </div>
                </div>
            </div>

            <!-- JavaScript -->
            <script type="text/javascript">
                document.addEventListener('DOMContentLoaded', function() {
                    const mobileInput = document.getElementById('mobile');

                    // 手机号格式化
                    mobileInput.addEventListener('input', function(e) {
                        let value = e.target.value.replace(/\D/g, '');
                        if (value.length > 11) {
                            value = value.slice(0, 11);
                        }
                        e.target.value = value;
                    });
                });
            </script>
        </t>
    </template>
</odoo>
