<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Hyla SPA Application -->
    <template id="hyla_spa_app" name="Hyla SPA App">
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8"/>
            <meta name="viewport" content="width=device-width, initial-scale=1"/>
            <title>Hyla - Single Page Application</title>

            <!-- Odoo Web Client Assets -->
            <link rel="stylesheet" href="/web/static/lib/bootstrap/css/bootstrap.css"/>
            <link rel="stylesheet" href="/web/static/src/css/base.css"/>

            <!-- Hyla SPA Styles -->
            <link rel="stylesheet" href="/galaxy_hyla/static/src/css/spa.css"/>
        </head>
        <body>
            <div id="hyla-spa-root" class="hyla-spa-container">
                <!-- Navigation Bar -->
                <div class="hyla-navbar">
                    <div class="container">
                        <div class="d-flex justify-content-between align-items-center">
                            <h1>HYLA</h1>
                            <div id="user-info" class="d-none">
                                <span id="user-mobile"></span>
                                <button class="hyla-btn hyla-btn-primary ml-2" onclick="HylaApp.logout()">登出</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Main Content -->
                <div class="container hyla-content">
                    <!-- Login Form -->
                    <div id="login-view" class="hyla-login-form">
                        <h2 class="text-center mb-4">登录您的账户</h2>
                        
                        <div id="login-error" class="hyla-alert hyla-alert-danger d-none"></div>
                        
                        <form id="login-form">
                            <div class="hyla-form-group">
                                <label for="mobile">手机号</label>
                                <input type="tel" id="mobile" class="hyla-form-control" placeholder="请输入手机号" required/>
                            </div>
                            
                            <div class="hyla-form-group">
                                <label for="password">密码</label>
                                <input type="password" id="password" class="hyla-form-control" placeholder="请输入密码" required/>
                            </div>
                            
                            <button type="submit" class="hyla-btn hyla-btn-primary w-100">
                                <span id="login-btn-text">登录</span>
                                <div id="login-spinner" class="hyla-spinner d-none" style="width: 20px; height: 20px; margin: 0;"></div>
                            </button>
                        </form>
                    </div>
                    
                    <!-- Product List View -->
                    <div id="product-view" class="d-none">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h2>在售商品</h2>
                            <button class="hyla-btn hyla-btn-primary" onclick="HylaApp.loadProducts()">刷新</button>
                        </div>
                        
                        <div id="products-loading" class="hyla-loading d-none">
                            <div class="hyla-spinner"></div>
                            <p>加载中...</p>
                        </div>
                        
                        <div id="products-error" class="hyla-alert hyla-alert-danger d-none"></div>
                        
                        <div id="products-grid" class="hyla-product-grid"></div>
                    </div>
                </div>
            </div>
            
            <!-- Odoo Web Client Core -->
            <script type="text/javascript" src="/web/static/lib/jquery/jquery.js"></script>
            <script type="text/javascript" src="/web/static/src/js/boot.js"></script>
            <script type="text/javascript" src="/web/static/src/js/core/class.js"></script>
            <script type="text/javascript" src="/web/static/src/js/core/mixins.js"></script>
            <script type="text/javascript" src="/web/static/src/js/core/widget.js"></script>
            
            <!-- Hyla SPA JavaScript Files -->
            <script type="text/javascript" src="/galaxy_hyla/static/src/js/spa/api-service.js"></script>
            <script type="text/javascript" src="/galaxy_hyla/static/src/js/spa/ui-manager.js"></script>
            <script type="text/javascript" src="/galaxy_hyla/static/src/js/spa/app.js"></script>

        </body>
        </html>
    </template>
</odoo>
