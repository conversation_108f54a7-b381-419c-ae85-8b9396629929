<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- <PERSON>yla SPA Application -->
    <template id="hyla_spa_app" name="Hyla SPA App">
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8"/>
            <meta name="viewport" content="width=device-width, initial-scale=1"/>
            <title>Hyla - Single Page Application</title>
            
            <!-- Odoo Web Client Assets -->
            <link rel="stylesheet" href="/web/static/lib/bootstrap/css/bootstrap.css"/>
            <link rel="stylesheet" href="/web/static/src/css/base.css"/>
            
            <!-- Custom CSS -->
            <style>
                .hyla-spa-container {
                    min-height: 100vh;
                    background: #f8f9fa;
                }
                
                .hyla-navbar {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 1rem 0;
                    margin-bottom: 2rem;
                }
                
                .hyla-navbar h1 {
                    margin: 0;
                    font-size: 1.5rem;
                    font-weight: bold;
                }
                
                .hyla-content {
                    padding: 0 1rem;
                }
                
                .hyla-login-form {
                    max-width: 400px;
                    margin: 2rem auto;
                    background: white;
                    padding: 2rem;
                    border-radius: 8px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }
                
                .hyla-product-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
                    gap: 1rem;
                    margin-top: 1rem;
                }
                
                .hyla-product-card {
                    background: white;
                    border-radius: 8px;
                    padding: 1rem;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                    transition: transform 0.2s;
                }
                
                .hyla-product-card:hover {
                    transform: translateY(-2px);
                }
                
                .hyla-btn {
                    padding: 0.5rem 1rem;
                    border: none;
                    border-radius: 4px;
                    cursor: pointer;
                    font-weight: 500;
                    transition: all 0.2s;
                }
                
                .hyla-btn-primary {
                    background: #007bff;
                    color: white;
                }
                
                .hyla-btn-primary:hover {
                    background: #0056b3;
                }
                
                .hyla-btn-success {
                    background: #28a745;
                    color: white;
                }
                
                .hyla-btn-success:hover {
                    background: #1e7e34;
                }
                
                .hyla-form-group {
                    margin-bottom: 1rem;
                }
                
                .hyla-form-control {
                    width: 100%;
                    padding: 0.5rem;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    font-size: 1rem;
                }
                
                .hyla-alert {
                    padding: 0.75rem 1rem;
                    border-radius: 4px;
                    margin-bottom: 1rem;
                }
                
                .hyla-alert-danger {
                    background: #f8d7da;
                    color: #721c24;
                    border: 1px solid #f5c6cb;
                }
                
                .hyla-alert-success {
                    background: #d4edda;
                    color: #155724;
                    border: 1px solid #c3e6cb;
                }
                
                .hyla-loading {
                    text-align: center;
                    padding: 2rem;
                }
                
                .hyla-spinner {
                    border: 3px solid #f3f3f3;
                    border-top: 3px solid #007bff;
                    border-radius: 50%;
                    width: 30px;
                    height: 30px;
                    animation: spin 1s linear infinite;
                    margin: 0 auto 1rem;
                }
                
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
                
                .d-none { display: none !important; }
                .d-block { display: block !important; }
                .text-center { text-align: center; }
                .mt-2 { margin-top: 0.5rem; }
                .mb-2 { margin-bottom: 0.5rem; }
            </style>
        </head>
        <body>
            <div id="hyla-spa-root" class="hyla-spa-container">
                <!-- Navigation Bar -->
                <div class="hyla-navbar">
                    <div class="container">
                        <div class="d-flex justify-content-between align-items-center">
                            <h1>HYLA</h1>
                            <div id="user-info" class="d-none">
                                <span id="user-mobile"></span>
                                <button class="hyla-btn hyla-btn-primary ml-2" onclick="HylaApp.logout()">登出</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Main Content -->
                <div class="container hyla-content">
                    <!-- Login Form -->
                    <div id="login-view" class="hyla-login-form">
                        <h2 class="text-center mb-4">登录您的账户</h2>
                        
                        <div id="login-error" class="hyla-alert hyla-alert-danger d-none"></div>
                        
                        <form id="login-form">
                            <div class="hyla-form-group">
                                <label for="mobile">手机号</label>
                                <input type="tel" id="mobile" class="hyla-form-control" placeholder="请输入手机号" required/>
                            </div>
                            
                            <div class="hyla-form-group">
                                <label for="password">密码</label>
                                <input type="password" id="password" class="hyla-form-control" placeholder="请输入密码" required/>
                            </div>
                            
                            <button type="submit" class="hyla-btn hyla-btn-primary w-100">
                                <span id="login-btn-text">登录</span>
                                <div id="login-spinner" class="hyla-spinner d-none" style="width: 20px; height: 20px; margin: 0;"></div>
                            </button>
                        </form>
                    </div>
                    
                    <!-- Product List View -->
                    <div id="product-view" class="d-none">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h2>在售商品</h2>
                            <button class="hyla-btn hyla-btn-primary" onclick="HylaApp.loadProducts()">刷新</button>
                        </div>
                        
                        <div id="products-loading" class="hyla-loading d-none">
                            <div class="hyla-spinner"></div>
                            <p>加载中...</p>
                        </div>
                        
                        <div id="products-error" class="hyla-alert hyla-alert-danger d-none"></div>
                        
                        <div id="products-grid" class="hyla-product-grid"></div>
                    </div>
                </div>
            </div>
            
            <!-- Odoo Web Client Core -->
            <script type="text/javascript" src="/web/static/lib/jquery/jquery.js"></script>
            <script type="text/javascript" src="/web/static/src/js/boot.js"></script>
            <script type="text/javascript" src="/web/static/src/js/core/class.js"></script>
            <script type="text/javascript" src="/web/static/src/js/core/mixins.js"></script>
            <script type="text/javascript" src="/web/static/src/js/core/widget.js"></script>
            
            <!-- Hyla SPA Application Script -->
            <script type="text/javascript">
                // Hyla Single Page Application
                class HylaApp {
                    constructor() {
                        this.currentUser = null;
                        this.products = [];
                        this.init();
                    }
                    
                    init() {
                        this.bindEvents();
                        this.checkLoginStatus();
                    }
                    
                    bindEvents() {
                        // Login form
                        document.getElementById('login-form').addEventListener('submit', (e) => {
                            e.preventDefault();
                            this.handleLogin();
                        });
                    }
                    
                    async handleLogin() {
                        const mobile = document.getElementById('mobile').value.trim();
                        const password = document.getElementById('password').value.trim();
                        
                        if (!mobile || !password) {
                            this.showLoginError('请输入手机号和密码');
                            return;
                        }
                        
                        this.setLoginLoading(true);
                        
                        try {
                            const response = await this.apiCall('/hyla/api/login', {
                                mobile: mobile,
                                password: password
                            });
                            
                            if (response.success) {
                                this.currentUser = {
                                    mobile: response.mobile,
                                    token: response.token
                                };
                                this.showProductView();
                                this.loadProducts();
                            } else {
                                this.showLoginError(response.error || '登录失败');
                            }
                        } catch (error) {
                            this.showLoginError('网络错误，请重试');
                        } finally {
                            this.setLoginLoading(false);
                        }
                    }
                    
                    async loadProducts() {
                        this.setProductsLoading(true);
                        
                        try {
                            const response = await this.apiCall('/hyla/api/products', {});
                            
                            if (response.success) {
                                this.products = response.products;
                                this.renderProducts();
                            } else {
                                this.showProductsError(response.error || '加载产品失败');
                            }
                        } catch (error) {
                            this.showProductsError('网络错误，请重试');
                        } finally {
                            this.setProductsLoading(false);
                        }
                    }
                    
                    renderProducts() {
                        const grid = document.getElementById('products-grid');
                        grid.innerHTML = '';
                        
                        this.products.forEach(product => {
                            const card = this.createProductCard(product);
                            grid.appendChild(card);
                        });
                    }
                    
                    createProductCard(product) {
                        const card = document.createElement('div');
                        card.className = 'hyla-product-card';
                        card.innerHTML = `
                            <h5>${product.name}</h5>
                            <p class="text-muted">${product.external_product_number || ''}</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>${product.list_price} ${product.currency_symbol}</strong><br>
                                    <small>库存: ${product.qty}</small>
                                </div>
                                <button class="hyla-btn hyla-btn-success" onclick="HylaApp.buyProduct(${product.id})">
                                    购买
                                </button>
                            </div>
                        `;
                        return card;
                    }
                    
                    async buyProduct(productId) {
                        const product = this.products.find(p => p.id === productId);
                        if (!product) return;
                        
                        const quantity = prompt(`购买数量 (库存: ${product.qty})`, '1');
                        if (!quantity || quantity <= 0) return;
                        
                        if (parseInt(quantity) > product.qty) {
                            alert('购买数量不能超过库存');
                            return;
                        }
                        
                        try {
                            const response = await this.apiCall('/hyla/api/buy', {
                                product_id: productId,
                                quantity: parseInt(quantity)
                            });
                            
                            if (response.success) {
                                alert('订单创建成功！');
                                this.loadProducts(); // 刷新产品列表
                            } else {
                                alert('订单创建失败: ' + response.error);
                            }
                        } catch (error) {
                            alert('网络错误，请重试');
                        }
                    }
                    
                    logout() {
                        this.currentUser = null;
                        this.showLoginView();
                        document.getElementById('login-form').reset();
                    }
                    
                    showLoginView() {
                        document.getElementById('login-view').classList.remove('d-none');
                        document.getElementById('product-view').classList.add('d-none');
                        document.getElementById('user-info').classList.add('d-none');
                    }
                    
                    showProductView() {
                        document.getElementById('login-view').classList.add('d-none');
                        document.getElementById('product-view').classList.remove('d-none');
                        document.getElementById('user-info').classList.remove('d-none');
                        document.getElementById('user-mobile').textContent = this.currentUser.mobile;
                    }
                    
                    setLoginLoading(loading) {
                        const btnText = document.getElementById('login-btn-text');
                        const spinner = document.getElementById('login-spinner');
                        
                        if (loading) {
                            btnText.classList.add('d-none');
                            spinner.classList.remove('d-none');
                        } else {
                            btnText.classList.remove('d-none');
                            spinner.classList.add('d-none');
                        }
                    }
                    
                    setProductsLoading(loading) {
                        const loadingEl = document.getElementById('products-loading');
                        if (loading) {
                            loadingEl.classList.remove('d-none');
                        } else {
                            loadingEl.classList.add('d-none');
                        }
                    }
                    
                    showLoginError(message) {
                        const errorEl = document.getElementById('login-error');
                        errorEl.textContent = message;
                        errorEl.classList.remove('d-none');
                    }
                    
                    showProductsError(message) {
                        const errorEl = document.getElementById('products-error');
                        errorEl.textContent = message;
                        errorEl.classList.remove('d-none');
                    }
                    
                    checkLoginStatus() {
                        // 这里可以检查是否已经登录
                        // 暂时默认显示登录页面
                        this.showLoginView();
                    }
                    
                    async apiCall(url, data) {
                        const response = await fetch(url, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                jsonrpc: '2.0',
                                method: 'call',
                                params: data,
                                id: new Date().getTime()
                            })
                        });
                        
                        const result = await response.json();
                        return result.result;
                    }
                }
                
                // 全局实例
                window.HylaApp = new HylaApp();
            </script>
        </body>
        </html>
    </template>
</odoo>
