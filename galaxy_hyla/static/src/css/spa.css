/* Hyla <PERSON> Styles */

.hyla-spa-container {
    min-height: 100vh;
    background: #f8f9fa;
}

.hyla-navbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 0;
    margin-bottom: 2rem;
}

.hyla-navbar h1 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: bold;
}

.hyla-content {
    padding: 0 1rem;
}

.hyla-login-form {
    max-width: 400px;
    margin: 2rem auto;
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.hyla-product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.hyla-product-card {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: transform 0.2s;
}

.hyla-product-card:hover {
    transform: translateY(-2px);
}

.hyla-btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s;
}

.hyla-btn-primary {
    background: #007bff;
    color: white;
}

.hyla-btn-primary:hover {
    background: #0056b3;
}

.hyla-btn-success {
    background: #28a745;
    color: white;
}

.hyla-btn-success:hover {
    background: #1e7e34;
}

.hyla-form-group {
    margin-bottom: 1rem;
}

.hyla-form-control {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
}

.hyla-alert {
    padding: 0.75rem 1rem;
    border-radius: 4px;
    margin-bottom: 1rem;
}

.hyla-alert-danger {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.hyla-alert-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.hyla-loading {
    text-align: center;
    padding: 2rem;
}

.hyla-spinner {
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Utility Classes */
.d-none { display: none !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.justify-content-between { justify-content: space-between !important; }
.align-items-center { align-items: center !important; }
.text-center { text-align: center; }
.text-muted { color: #6c757d; }
.mt-2 { margin-top: 0.5rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }
.ml-2 { margin-left: 0.5rem; }
.w-100 { width: 100%; }

/* Responsive Design */
@media (max-width: 768px) {
    .hyla-content {
        padding: 0 0.5rem;
    }
    
    .hyla-login-form {
        margin: 1rem auto;
        padding: 1.5rem;
    }
    
    .hyla-product-grid {
        grid-template-columns: 1fr;
    }
    
    .hyla-navbar h1 {
        font-size: 1.25rem;
    }
}
