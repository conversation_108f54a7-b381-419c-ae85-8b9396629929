/**
 * Hyla API Service
 * 处理所有与后端的API通信
 */

class HylaApiService {
    constructor() {
        this.baseUrl = '';
    }
    
    /**
     * 通用API调用方法
     * @param {string} url - API端点
     * @param {Object} data - 请求数据
     * @returns {Promise} API响应
     */
    async apiCall(url, data = {}) {
        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    jsonrpc: '2.0',
                    method: 'call',
                    params: data,
                    id: new Date().getTime()
                })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const result = await response.json();
            
            if (result.error) {
                throw new Error(result.error.message || 'API Error');
            }
            
            return result.result;
        } catch (error) {
            console.error('API Call Error:', error);
            throw error;
        }
    }
    
    /**
     * 用户登录
     * @param {string} mobile - 手机号
     * @param {string} password - 密码
     * @returns {Promise} 登录结果
     */
    async login(mobile, password) {
        return this.apiCall('/hyla/api/login', {
            mobile: mobile,
            password: password
        });
    }
    
    /**
     * 获取产品列表
     * @returns {Promise} 产品列表
     */
    async getProducts() {
        return this.apiCall('/hyla/api/products', {});
    }
    
    /**
     * 购买产品
     * @param {number} productId - 产品ID
     * @param {number} quantity - 购买数量
     * @returns {Promise} 购买结果
     */
    async buyProduct(productId, quantity) {
        return this.apiCall('/hyla/api/buy', {
            product_id: productId,
            quantity: quantity
        });
    }
    
    /**
     * 获取用户信息
     * @returns {Promise} 用户信息
     */
    async getUserInfo() {
        return this.apiCall('/hyla/api/user', {});
    }
    
    /**
     * 获取订单列表
     * @returns {Promise} 订单列表
     */
    async getOrders() {
        return this.apiCall('/hyla/api/orders', {});
    }
    
    /**
     * 搜索产品
     * @param {string} keyword - 搜索关键词
     * @returns {Promise} 搜索结果
     */
    async searchProducts(keyword) {
        return this.apiCall('/hyla/api/products/search', {
            keyword: keyword
        });
    }
    
    /**
     * 获取产品详情
     * @param {number} productId - 产品ID
     * @returns {Promise} 产品详情
     */
    async getProductDetail(productId) {
        return this.apiCall('/hyla/api/products/detail', {
            product_id: productId
        });
    }
}
