/**
 * Hyla UI Manager
 * 管理所有UI相关的操作
 */

class HylaUIManager {
    constructor() {
        this.currentView = null;
    }
    
    /**
     * 显示指定视图
     * @param {string} viewName - 视图名称 ('login' | 'product')
     */
    showView(viewName) {
        // 隐藏所有视图
        this.hideAllViews();
        
        // 显示指定视图
        switch (viewName) {
            case 'login':
                this.showLoginView();
                break;
            case 'product':
                this.showProductView();
                break;
            default:
                console.error('Unknown view:', viewName);
        }
        
        this.currentView = viewName;
    }
    
    /**
     * 隐藏所有视图
     */
    hideAllViews() {
        document.getElementById('login-view').classList.add('d-none');
        document.getElementById('product-view').classList.add('d-none');
        document.getElementById('user-info').classList.add('d-none');
    }
    
    /**
     * 显示登录视图
     */
    showLoginView() {
        document.getElementById('login-view').classList.remove('d-none');
        this.clearLoginError();
    }
    
    /**
     * 显示产品视图
     */
    showProductView() {
        document.getElementById('product-view').classList.remove('d-none');
        document.getElementById('user-info').classList.remove('d-none');
        this.clearProductsError();
    }
    
    /**
     * 设置登录加载状态
     * @param {boolean} loading - 是否加载中
     */
    setLoginLoading(loading) {
        const btnText = document.getElementById('login-btn-text');
        const spinner = document.getElementById('login-spinner');
        const submitBtn = document.querySelector('#login-form button[type="submit"]');
        
        if (loading) {
            btnText.classList.add('d-none');
            spinner.classList.remove('d-none');
            submitBtn.disabled = true;
        } else {
            btnText.classList.remove('d-none');
            spinner.classList.add('d-none');
            submitBtn.disabled = false;
        }
    }
    
    /**
     * 设置产品加载状态
     * @param {boolean} loading - 是否加载中
     */
    setProductsLoading(loading) {
        const loadingEl = document.getElementById('products-loading');
        const gridEl = document.getElementById('products-grid');
        
        if (loading) {
            loadingEl.classList.remove('d-none');
            gridEl.style.opacity = '0.5';
        } else {
            loadingEl.classList.add('d-none');
            gridEl.style.opacity = '1';
        }
    }
    
    /**
     * 显示登录错误
     * @param {string} message - 错误信息
     */
    showLoginError(message) {
        const errorEl = document.getElementById('login-error');
        errorEl.textContent = message;
        errorEl.classList.remove('d-none');
        
        // 3秒后自动隐藏
        setTimeout(() => {
            this.clearLoginError();
        }, 3000);
    }
    
    /**
     * 清除登录错误
     */
    clearLoginError() {
        const errorEl = document.getElementById('login-error');
        errorEl.classList.add('d-none');
    }
    
    /**
     * 显示产品错误
     * @param {string} message - 错误信息
     */
    showProductsError(message) {
        const errorEl = document.getElementById('products-error');
        errorEl.textContent = message;
        errorEl.classList.remove('d-none');
        
        // 5秒后自动隐藏
        setTimeout(() => {
            this.clearProductsError();
        }, 5000);
    }
    
    /**
     * 清除产品错误
     */
    clearProductsError() {
        const errorEl = document.getElementById('products-error');
        errorEl.classList.add('d-none');
    }
    
    /**
     * 显示成功消息
     * @param {string} message - 成功信息
     */
    showSuccessMessage(message) {
        // 创建临时的成功提示
        const alertEl = document.createElement('div');
        alertEl.className = 'hyla-alert hyla-alert-success';
        alertEl.textContent = message;
        alertEl.style.position = 'fixed';
        alertEl.style.top = '20px';
        alertEl.style.right = '20px';
        alertEl.style.zIndex = '9999';
        alertEl.style.minWidth = '300px';
        
        document.body.appendChild(alertEl);
        
        // 3秒后移除
        setTimeout(() => {
            document.body.removeChild(alertEl);
        }, 3000);
    }
    
    /**
     * 显示确认对话框
     * @param {string} message - 确认信息
     * @param {Function} onConfirm - 确认回调
     * @param {Function} onCancel - 取消回调
     */
    showConfirmDialog(message, onConfirm, onCancel) {
        if (confirm(message)) {
            if (onConfirm) onConfirm();
        } else {
            if (onCancel) onCancel();
        }
    }
    
    /**
     * 显示输入对话框
     * @param {string} message - 提示信息
     * @param {string} defaultValue - 默认值
     * @returns {string|null} 用户输入的值
     */
    showInputDialog(message, defaultValue = '') {
        return prompt(message, defaultValue);
    }
    
    /**
     * 更新用户信息显示
     * @param {Object} userInfo - 用户信息
     */
    updateUserInfo(userInfo) {
        const mobileEl = document.getElementById('user-mobile');
        if (mobileEl && userInfo.mobile) {
            mobileEl.textContent = userInfo.mobile;
        }
    }
    
    /**
     * 获取当前视图
     * @returns {string} 当前视图名称
     */
    getCurrentView() {
        return this.currentView;
    }
}
