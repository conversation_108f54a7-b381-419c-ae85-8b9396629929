/**
 * Hyla SPA Main Application
 */

class HylaApp {
    constructor() {
        this.currentUser = null;
        this.products = [];
        this.apiService = new HylaApiService();
        this.uiManager = new HylaUIManager();
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.checkLoginStatus();
    }
    
    bindEvents() {
        // Login form
        document.getElementById('login-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin();
        });
    }
    
    async handleLogin() {
        const mobile = document.getElementById('mobile').value.trim();
        const password = document.getElementById('password').value.trim();
        
        if (!mobile || !password) {
            this.uiManager.showLoginError('请输入手机号和密码');
            return;
        }
        
        this.uiManager.setLoginLoading(true);
        
        try {
            const response = await this.apiService.login(mobile, password);
            
            if (response.success) {
                this.currentUser = {
                    mobile: response.mobile,
                    token: response.token
                };
                this.showProductView();
                this.loadProducts();
            } else {
                this.uiManager.showLoginError(response.error || '登录失败');
            }
        } catch (error) {
            this.uiManager.showLoginError('网络错误，请重试');
        } finally {
            this.uiManager.setLoginLoading(false);
        }
    }
    
    async loadProducts() {
        this.uiManager.setProductsLoading(true);
        
        try {
            const response = await this.apiService.getProducts();
            
            if (response.success) {
                this.products = response.products;
                this.renderProducts();
            } else {
                this.uiManager.showProductsError(response.error || '加载产品失败');
            }
        } catch (error) {
            this.uiManager.showProductsError('网络错误，请重试');
        } finally {
            this.uiManager.setProductsLoading(false);
        }
    }
    
    renderProducts() {
        const grid = document.getElementById('products-grid');
        grid.innerHTML = '';
        
        this.products.forEach(product => {
            const card = this.createProductCard(product);
            grid.appendChild(card);
        });
    }
    
    createProductCard(product) {
        const card = document.createElement('div');
        card.className = 'hyla-product-card';
        card.innerHTML = `
            <h5>${product.name}</h5>
            <p class="text-muted">${product.external_product_number || ''}</p>
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <strong>${product.list_price} ${product.currency_symbol}</strong><br>
                    <small>库存: ${product.qty}</small>
                </div>
                <button class="hyla-btn hyla-btn-success" onclick="window.hylaApp.buyProduct(${product.id})">
                    购买
                </button>
            </div>
        `;
        return card;
    }
    
    async buyProduct(productId) {
        const product = this.products.find(p => p.id === productId);
        if (!product) return;
        
        const quantity = prompt(`购买数量 (库存: ${product.qty})`, '1');
        if (!quantity || quantity <= 0) return;
        
        if (parseInt(quantity) > product.qty) {
            alert('购买数量不能超过库存');
            return;
        }
        
        try {
            const response = await this.apiService.buyProduct(productId, parseInt(quantity));
            
            if (response.success) {
                alert('订单创建成功！');
                this.loadProducts(); // 刷新产品列表
            } else {
                alert('订单创建失败: ' + response.error);
            }
        } catch (error) {
            alert('网络错误，请重试');
        }
    }
    
    logout() {
        this.currentUser = null;
        this.showLoginView();
        document.getElementById('login-form').reset();
    }
    
    showLoginView() {
        this.uiManager.showView('login');
        document.getElementById('user-mobile').textContent = '';
    }
    
    showProductView() {
        this.uiManager.showView('product');
        document.getElementById('user-mobile').textContent = this.currentUser.mobile;
    }
    
    checkLoginStatus() {
        // 这里可以检查是否已经登录
        // 暂时默认显示登录页面
        this.showLoginView();
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', function() {
    window.hylaApp = new HylaApp();
});
