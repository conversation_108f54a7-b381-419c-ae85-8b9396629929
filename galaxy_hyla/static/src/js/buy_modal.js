/**
 * Hyla Buy Modal JavaScript
 * 一口价购买弹窗的交互逻辑
 */

class HylaBuyModal {
    constructor() {
        this.currentProduct = null;
        this.init();
    }

    init() {
        // 绑定事件监听器
        this.bindEvents();
    }

    bindEvents() {
        // 点击遮罩层关闭弹窗
        document.addEventListener('click', (e) => {
            if (e.target.id === 'hyla-buy-modal') {
                this.close();
            }
        });

        // ESC键关闭弹窗
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.close();
            }
        });
    }

    /**
     * 打开购买弹窗
     * @param {Object} product - 产品信息对象
     */
    open(product) {
        this.currentProduct = product;
        
        // 更新弹窗内容
        this.updateModalContent(product);
        
        // 显示弹窗
        const modal = document.getElementById('hyla-buy-modal');
        if (modal) {
            modal.style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }
    }

    /**
     * 关闭购买弹窗
     */
    close() {
        const modal = document.getElementById('hyla-buy-modal');
        if (modal) {
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }
        this.currentProduct = null;
    }

    /**
     * 更新弹窗内容
     * @param {Object} product - 产品信息
     */
    updateModalContent(product) {
        // 更新产品信息
        this.updateElement('modal-product-name', product.name || '');
        this.updateElement('modal-product-id', product.external_product_number || '');
        this.updateElement('modal-product-desc', product.description || product.name || '');
        this.updateElement('modal-stock-qty', product.qty || 0);
        this.updateElement('modal-unit-price', product.list_price || 0);
        this.updateElement('modal-currency', product.currency_symbol || 'HKD');
        
        // 设置数量输入框
        const quantityInput = document.getElementById('buy-quantity');
        if (quantityInput) {
            quantityInput.max = product.qty || 0;
            quantityInput.value = Math.min(100, product.qty || 0);
        }
        
        // 更新总价
        this.updateTotalPrice();
    }

    /**
     * 更新DOM元素内容
     * @param {string} elementId - 元素ID
     * @param {string|number} content - 内容
     */
    updateElement(elementId, content) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = content;
        }
    }

    /**
     * 验证购买数量
     */
    validateQuantity() {
        const quantityInput = document.getElementById('buy-quantity');
        const warning = document.getElementById('quantity-warning');
        const buyBtn = document.querySelector('.hyla-buy-btn');
        
        if (!quantityInput || !warning || !buyBtn) return;
        
        const quantity = parseInt(quantityInput.value) || 0;
        const maxQty = parseInt(quantityInput.max) || 0;
        
        if (quantity > maxQty) {
            warning.style.display = 'block';
            buyBtn.disabled = true;
        } else {
            warning.style.display = 'none';
            buyBtn.disabled = false;
        }
        
        this.updateTotalPrice();
    }

    /**
     * 更新总价显示
     */
    updateTotalPrice() {
        if (!this.currentProduct) return;
        
        const quantityInput = document.getElementById('buy-quantity');
        const totalPriceElement = document.getElementById('modal-total-price');
        
        if (!quantityInput || !totalPriceElement) return;
        
        const quantity = parseInt(quantityInput.value) || 0;
        const unitPrice = parseFloat(this.currentProduct.list_price) || 0;
        const totalPrice = quantity * unitPrice;
        
        totalPriceElement.textContent = totalPrice.toLocaleString();
    }

    /**
     * 提交购买订单
     */
    submitOrder() {
        if (!this.currentProduct) return;
        
        const quantityInput = document.getElementById('buy-quantity');
        if (!quantityInput) return;
        
        const quantity = parseInt(quantityInput.value) || 0;
        
        // 验证数量
        if (quantity <= 0) {
            alert('请输入有效的购买数量');
            return;
        }
        
        if (quantity > this.currentProduct.qty) {
            alert('购买数量不能超过库存数量');
            return;
        }
        
        // 构建订单数据
        const orderData = {
            product_id: this.currentProduct.id,
            quantity: quantity,
            unit_price: this.currentProduct.list_price,
            total_price: quantity * this.currentProduct.list_price
        };
        
        // 调用提交订单的方法
        this.onSubmitOrder(orderData);
    }

    /**
     * 订单提交回调 - 可以被重写
     * @param {Object} orderData - 订单数据
     */
    onSubmitOrder(orderData) {
        console.log('提交订单:', orderData);
        
        // 这里可以添加实际的订单提交逻辑
        // 例如：发送AJAX请求到后端
        
        alert('订单提交成功！');
        this.close();
    }
}

// 全局实例
let hylaBuyModal = null;

// DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    hylaBuyModal = new HylaBuyModal();
});

// 全局函数 - 保持向后兼容
function openBuyModal(product) {
    if (hylaBuyModal) {
        hylaBuyModal.open(product);
    }
}

function closeBuyModal() {
    if (hylaBuyModal) {
        hylaBuyModal.close();
    }
}

function validateQuantity() {
    if (hylaBuyModal) {
        hylaBuyModal.validateQuantity();
    }
}

function updateTotalPrice() {
    if (hylaBuyModal) {
        hylaBuyModal.updateTotalPrice();
    }
}

function submitBuyOrder() {
    if (hylaBuyModal) {
        hylaBuyModal.submitOrder();
    }
}
