from dataclasses import dataclass


@dataclass
class SOData:
    so_list: list = None


@dataclass
class SOHeaderData:
    odoo_so_number: str = "",
    odoo_delivery_plan_number: str = "",
    odoo_separated_order_number: str = "",
    so_summary: str = "",  # 摘要
    customer_id: str = "",
    so_date: str = "",
    total_amount: str = "",
    gathering_date: str = "",
    to_date: str = "",
    log_date: str = "",
    delivery_type: str = "",
    odoo_so_lines: list = None,
    currency: str = "",
    exchange_rate: str = "",


@dataclass
class SOLineData:
    so_date: str = "",
    customer_id: str = "",
    product_id: str = "",
    product_brand: str = "",
    product_desc: str = "",
    product_id: str = "",
    qty: str = "",
    price: str = "",
    total: str = "",
    comment: str = "",
    gathering_date: str = "",
    to_date: str = "",
    line_note: str = ""
