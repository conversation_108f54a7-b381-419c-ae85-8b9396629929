# -*- coding: utf-8 -*-
# -*- coding: utf-8 -*-

import logging

from odoo import fields, models

_logger = logging.getLogger(__name__)


class galaxyInternalCustomer(models.Model):
    _name = 'galaxy.internal.customer'
    _description = 'For create trade stock automactically, need list galaxy internal customer'

    internal_customer_ids = fields.Many2many('res.partner', string="Internal Cusomers", domain="[('customer_rank','>','0')]")
    note = fields.Char('Note')
