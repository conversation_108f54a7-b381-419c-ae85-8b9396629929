# -*- coding: utf-8 -*-
import re

# 预编译正则表达式
CARTON_PATTERN = re.compile(r'^[A-Za-z]\d{5,6}$')


def check_carton_format(carton=''):
    """
    校验箱号格式
    
    验证箱号是否符合格式：以字母开头，后跟5-6位数字
    例如：A12345 或 B123456 都是有效格式
    """
    if not carton:
        return False

    return bool(CARTON_PATTERN.match(str(carton)))


def check_pallet_format(pallet=''):
    """
    校验托盘号格式

    校验给定的托盘号是否符合指定的格式要求。

    Args:
        pallet (str): 托盘号

    Returns:
        bool: True if the pallet format is valid, False otherwise
    """
    if pallet == '':
        return False
    if not str(pallet).startswith('P') or not str(pallet).rsplit('P', maxsplit=1)[-1].isdigit() or len(
            str(pallet)) != 4:
        return False
    return True
