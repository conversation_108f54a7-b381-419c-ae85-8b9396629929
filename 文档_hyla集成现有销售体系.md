# Hyla Bid 集成销售体系架构设计

## 概述

本文档详细说明Hyla Bid新流程如何集成现有的Galaxy ERP销售体系，确保从客户报价到财务流程的完整闭环。

## 目录

- [1. 架构设计概览](#1-架构设计概览)
- [2. 数据流转设计](#2-数据流转设计)
- [3. 表结构设计](#3-表结构设计)
- [4. 业务流程设计](#4-业务流程设计)
- [5. 集成接口设计](#5-集成接口设计)
- [6. 财务流程集成](#6-财务流程集成)

## 1. 架构设计概览

### 1.1 整体架构图

```mermaid
graph TB
    subgraph "Hyla前端"
        A[Hyla产品展示<br/>galaxy.hyla.product.list]
        B[客户报价请求<br/>hyla.customer.quotation]
        C[报价历史<br/>hyla.quotation.history]
    end
    
    subgraph "Hyla后台管理"
        D[报价审核<br/>hyla.quotation.review]
        E[价格管理<br/>hyla.price.management]
        F[库存同步<br/>hyla.stock.sync]
    end
    
    subgraph "现有销售体系"
        G[销售订单<br/>sale.order]
        H[销售订单行<br/>sale.order.line]
        I[产品模板<br/>product.template]
        J[产品变体<br/>product.product]
    end
    
    subgraph "现有投标体系"
        K[拍卖单<br/>bid.order]
        L[拍卖标单<br/>bid.order.lot]
        M[客户投标<br/>customer.bid.line]
    end
    
    subgraph "财务体系"
        N[会计凭证<br/>account.move]
        O[发票<br/>account.move.line]
        P[收款<br/>account.payment]
    end
    
    A --> B
    B --> D
    D --> G
    G --> H
    G --> N
    N --> O
    O --> P
    
    B -.-> M
    D -.-> L
    H --> J
    
    style A fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    style D fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    style G fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    style N fill:#fce4ec,stroke:#c2185b,stroke-width:2px
```

### 1.2 集成策略

| 集成层面 | 策略 | 实现方式 |
|---------|------|---------|
| **数据层** | 复用现有产品体系 | hyla.product.list关联product.product |
| **业务层** | 扩展现有销售流程 | 新增Hyla专用销售类型 |
| **流程层** | 借鉴投标审核机制 | 复用审核状态和工作流 |
| **财务层** | 完全集成现有体系 | 直接生成标准销售订单 |

## 2. 数据流转设计

### 2.1 数据流转架构图

```mermaid
flowchart TD
    subgraph "客户端流程"
        A[客户浏览Hyla产品] --> B[提交报价请求]
        B --> C[等待审核结果]
        C --> D[确认订单]
    end
    
    subgraph "后台管理流程"
        E[接收报价请求] --> F[价格计算]
        F --> G[管理员审核]
        G --> H{审核结果}
        H -->|通过| I[生成销售订单]
        H -->|拒绝| J[通知客户]
        I --> K[财务处理]
    end
    
    subgraph "系统集成流程"
        L[库存检查] --> M[价格策略]
        M --> N[利润计算]
        N --> O[风险评估]
    end
    
    B --> E
    F --> L
    G --> O
    I --> D
    J --> C
    
    style B fill:#e3f2fd
    style G fill:#fff3e0
    style I fill:#e8f5e8
    style K fill:#fce4ec
```

### 2.2 关键数据流转点

#### **1. 产品数据同步**
- **源头**：galaxy.hyla.product.list
- **目标**：product.product
- **同步内容**：SKU、价格、库存、规格
- **同步频率**：实时/定时

#### **2. 报价请求处理**
- **输入**：客户报价请求
- **处理**：价格计算、库存检查、风险评估
- **输出**：审核建议、报价结果

#### **3. 订单生成**
- **触发**：报价审核通过
- **数据转换**：hyla.customer.quotation → sale.order
- **关联关系**：保持完整的追溯链

## 3. 表结构设计

### 3.1 核心表结构关系图

```mermaid
erDiagram
    galaxy_hyla_product_list ||--o{ hyla_customer_quotation : "一对多"
    hyla_customer_quotation ||--o{ hyla_quotation_line : "一对多"
    hyla_customer_quotation ||--|| hyla_quotation_review : "一对一"
    hyla_quotation_review ||--o{ sale_order : "一对多"
    
    res_partner ||--o{ hyla_customer_quotation : "一对多"
    product_product ||--|| galaxy_hyla_product_list : "一对一"
    sale_order ||--o{ sale_order_line : "一对多"
    sale_order ||--o{ account_move : "一对多"
    
    galaxy_hyla_product_list {
        int id PK
        int sku_id FK "关联product.product"
        string name "产品名称"
        float list_price "标价"
        int qty "库存数量"
        string publish_state "发布状态"
        int supplier_id FK "供应商"
        string warehouse "仓库"
    }
    
    hyla_customer_quotation {
        int id PK
        string name "报价单号"
        int customer_id FK "客户"
        int product_id FK "Hyla产品"
        int quantity "数量"
        float customer_price "客户出价"
        float suggested_price "建议价格"
        string state "状态"
        datetime create_date "创建时间"
        datetime expire_date "过期时间"
        text customer_note "客户备注"
        int currency_id FK "货币"
    }
    
    hyla_quotation_line {
        int id PK
        int quotation_id FK "报价单"
        int product_id FK "产品"
        int quantity "数量"
        float unit_price "单价"
        float total_price "总价"
        text specification "规格要求"
    }
    
    hyla_quotation_review {
        int id PK
        int quotation_id FK "报价单"
        int reviewer_id FK "审核人"
        string review_state "审核状态"
        float approved_price "批准价格"
        float profit_margin "利润率"
        text review_note "审核备注"
        datetime review_date "审核时间"
        string risk_level "风险等级"
    }
    
    sale_order {
        int id PK
        string name "订单号"
        int partner_id FK "客户"
        string state "状态"
        float amount_total "总金额"
        int hyla_quotation_id FK "关联Hyla报价"
        string order_type "订单类型"
    }
```

### 3.2 新增表详细设计

#### **A. hyla.customer.quotation (客户报价单)**

```python
class HylaCustomerQuotation(models.Model):
    _name = 'hyla.customer.quotation'
    _description = 'Hyla Customer Quotation'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'create_date desc'

    # 基本信息
    name = fields.Char('报价单号', required=True, copy=False, readonly=True, index=True)
    customer_id = fields.Many2one('res.partner', '客户', required=True, index=True)
    product_id = fields.Many2one('galaxy.hyla.product.list', 'Hyla产品', required=True)
    
    # 报价信息
    quantity = fields.Integer('数量', required=True, default=1)
    customer_price = fields.Monetary('客户出价', currency_field='currency_id')
    suggested_price = fields.Monetary('建议价格', currency_field='currency_id', compute='_compute_suggested_price')
    final_price = fields.Monetary('最终价格', currency_field='currency_id')
    currency_id = fields.Many2one('res.currency', '货币', default=lambda self: self.env.company.currency_id)
    
    # 状态管理
    state = fields.Selection([
        ('draft', '草稿'),
        ('submitted', '已提交'),
        ('under_review', '审核中'),
        ('approved', '已批准'),
        ('rejected', '已拒绝'),
        ('expired', '已过期'),
        ('ordered', '已下单')
    ], '状态', default='draft', tracking=True)
    
    # 时间管理
    create_date = fields.Datetime('创建时间', default=fields.Datetime.now)
    expire_date = fields.Datetime('过期时间', compute='_compute_expire_date', store=True)
    review_deadline = fields.Datetime('审核截止时间')
    
    # 关联关系
    quotation_lines = fields.One2many('hyla.quotation.line', 'quotation_id', '报价明细')
    review_id = fields.One2one('hyla.quotation.review', 'quotation_id', '审核记录')
    sale_order_id = fields.Many2one('sale.order', '销售订单', readonly=True)
    
    # 备注信息
    customer_note = fields.Text('客户备注')
    internal_note = fields.Text('内部备注')
    
    # 计算字段
    total_amount = fields.Monetary('总金额', compute='_compute_total_amount', store=True)
    profit_margin = fields.Float('利润率%', compute='_compute_profit_margin')
    
    @api.model
    def create(self, vals):
        if vals.get('name', '/') == '/':
            vals['name'] = self.env['ir.sequence'].next_by_code('hyla.customer.quotation')
        return super().create(vals)
```

#### **B. hyla.quotation.review (报价审核)**

```python
class HylaQuotationReview(models.Model):
    _name = 'hyla.quotation.review'
    _description = 'Hyla Quotation Review'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    # 关联信息
    quotation_id = fields.Many2one('hyla.customer.quotation', '报价单', required=True, ondelete='cascade')
    reviewer_id = fields.Many2one('res.users', '审核人', default=lambda self: self.env.user)
    
    # 审核状态
    review_state = fields.Selection([
        ('pending', '待审核'),
        ('approved', '已批准'),
        ('rejected', '已拒绝'),
        ('need_info', '需要补充信息')
    ], '审核状态', default='pending', tracking=True)
    
    # 价格审核
    original_price = fields.Monetary('原始价格', related='quotation_id.customer_price')
    approved_price = fields.Monetary('批准价格', currency_field='currency_id')
    cost_price = fields.Monetary('成本价格', currency_field='currency_id')
    profit_margin = fields.Float('利润率%', compute='_compute_profit_margin')
    currency_id = fields.Many2one('res.currency', related='quotation_id.currency_id')
    
    # 风险评估
    risk_level = fields.Selection([
        ('low', '低风险'),
        ('medium', '中风险'),
        ('high', '高风险')
    ], '风险等级', compute='_compute_risk_level')
    
    # 审核信息
    review_note = fields.Text('审核备注')
    review_date = fields.Datetime('审核时间')
    auto_approve = fields.Boolean('自动审核', default=False)
    
    # 库存检查
    stock_available = fields.Integer('可用库存', compute='_compute_stock_info')
    stock_warning = fields.Text('库存警告', compute='_compute_stock_info')
    
    def action_approve(self):
        """批准报价"""
        self.write({
            'review_state': 'approved',
            'review_date': fields.Datetime.now()
        })
        self.quotation_id.write({
            'state': 'approved',
            'final_price': self.approved_price
        })
        # 发送通知给客户
        self._send_approval_notification()
    
    def action_reject(self):
        """拒绝报价"""
        self.write({
            'review_state': 'rejected',
            'review_date': fields.Datetime.now()
        })
        self.quotation_id.write({'state': 'rejected'})
        # 发送通知给客户
        self._send_rejection_notification()
```

#### **C. hyla.quotation.line (报价明细)**

```python
class HylaQuotationLine(models.Model):
    _name = 'hyla.quotation.line'
    _description = 'Hyla Quotation Line'

    # 关联信息
    quotation_id = fields.Many2one('hyla.customer.quotation', '报价单', required=True, ondelete='cascade')
    product_id = fields.Many2one('galaxy.hyla.product.list', 'Hyla产品', required=True)
    
    # 产品信息
    product_name = fields.Char('产品名称', related='product_id.name')
    specification = fields.Text('规格要求')
    
    # 数量和价格
    quantity = fields.Integer('数量', required=True, default=1)
    unit_price = fields.Monetary('单价', currency_field='currency_id')
    total_price = fields.Monetary('总价', compute='_compute_total_price', store=True)
    currency_id = fields.Many2one('res.currency', related='quotation_id.currency_id')
    
    # 交期信息
    delivery_date = fields.Date('交期要求')
    delivery_note = fields.Text('交期备注')
    
    @api.depends('quantity', 'unit_price')
    def _compute_total_price(self):
        for line in self:
            line.total_price = line.quantity * line.unit_price
```

## 4. 业务流程设计

### 4.1 完整业务流程图

```mermaid
sequenceDiagram
    participant C as 客户
    participant H as Hyla前端
    participant B as Hyla后台
    participant R as 审核系统
    participant S as 销售系统
    participant F as 财务系统

    Note over C,F: 1. 报价请求阶段
    C->>H: 浏览产品
    C->>H: 提交报价请求
    H->>B: 创建报价记录
    B->>B: 生成报价单号
    B->>R: 触发审核流程

    Note over C,F: 2. 审核阶段
    R->>R: 价格计算
    R->>R: 库存检查
    R->>R: 风险评估
    R->>R: 管理员审核

    Note over C,F: 3. 审核结果处理
    alt 审核通过
        R->>S: 生成销售订单
        S->>C: 发送确认通知
        C->>S: 确认订单
        S->>F: 创建财务凭证
    else 审核拒绝
        R->>C: 发送拒绝通知
        C->>H: 可重新报价
    end

    Note over C,F: 4. 订单执行
    S->>S: 订单确认
    S->>S: 发货处理
    F->>F: 开具发票
    F->>F: 收款处理
```

### 4.2 状态流转设计

#### A. 报价单状态流转

```mermaid
stateDiagram-v2
    [*] --> draft : 创建报价
    draft --> submitted : 客户提交
    submitted --> under_review : 进入审核
    under_review --> approved : 审核通过
    under_review --> rejected : 审核拒绝
    under_review --> need_info : 需要补充信息
    need_info --> under_review : 信息补充完成
    approved --> ordered : 客户确认下单
    approved --> expired : 超时未确认
    rejected --> draft : 重新报价
    ordered --> [*] : 流程结束
    expired --> [*] : 流程结束

    note right of under_review : 自动/手动审核
    note right of approved : 有效期内可下单
    note right of ordered : 生成销售订单
```

#### B. 审核状态流转

```mermaid
stateDiagram-v2
    [*] --> pending : 待审核
    pending --> approved : 批准
    pending --> rejected : 拒绝
    pending --> need_info : 需要更多信息
    need_info --> pending : 信息补充后重新审核
    approved --> [*] : 审核完成
    rejected --> [*] : 审核完成

    note right of pending : 可设置自动审核规则
    note right of approved : 触发订单生成
    note right of rejected : 可设置拒绝原因
```

### 4.3 关键业务规则

#### **报价规则**
1. **价格限制**：客户出价不能低于成本价的80%
2. **数量限制**：单次报价数量不能超过库存的50%
3. **时间限制**：报价有效期默认7天
4. **客户限制**：需要验证客户信用额度

#### **审核规则**
1. **自动审核条件**：
   - 利润率 > 15%
   - 客户信用等级 A级
   - 库存充足
   - 金额 < 10万

2. **必须人工审核**：
   - 利润率 < 10%
   - 新客户
   - 大额订单 > 50万
   - 特殊产品

#### **价格策略**
```python
def calculate_suggested_price(self):
    """计算建议价格"""
    base_price = self.product_id.list_price

    # 客户等级折扣
    customer_discount = self._get_customer_discount()

    # 数量折扣
    quantity_discount = self._get_quantity_discount()

    # 市场价格调整
    market_adjustment = self._get_market_adjustment()

    suggested_price = base_price * (1 - customer_discount) * (1 - quantity_discount) * market_adjustment

    return max(suggested_price, self.product_id.standard_price * 1.1)  # 不低于成本价110%
```

## 5. 集成接口设计

### 5.1 与现有销售系统集成

#### A. 销售订单生成

```python
class HylaQuotationReview(models.Model):
    _inherit = 'hyla.quotation.review'

    def create_sale_order(self):
        """生成销售订单"""
        quotation = self.quotation_id

        # 创建销售订单
        sale_order = self.env['sale.order'].create({
            'partner_id': quotation.customer_id.id,
            'date_order': fields.Datetime.now(),
            'hyla_quotation_id': quotation.id,
            'order_type': 'hyla_bid',
            'origin': quotation.name,
            'note': f'来源：Hyla报价单 {quotation.name}'
        })

        # 创建订单行
        for line in quotation.quotation_lines:
            self.env['sale.order.line'].create({
                'order_id': sale_order.id,
                'product_id': line.product_id.sku_id.id,  # 关联到标准产品
                'product_uom_qty': line.quantity,
                'price_unit': line.unit_price,
                'name': f'{line.product_name} - {line.specification or ""}'
            })

        # 更新报价单状态
        quotation.write({
            'sale_order_id': sale_order.id,
            'state': 'ordered'
        })

        return sale_order
```

#### B. 产品数据同步

```python
class GalaxyHylaProductList(models.Model):
    _inherit = 'galaxy.hyla.product.list'

    def sync_to_standard_product(self):
        """同步到标准产品体系"""
        if not self.sku_id:
            # 创建新的标准产品
            product = self.env['product.product'].create({
                'name': self.name,
                'list_price': self.list_price,
                'standard_price': self.list_price * 0.8,  # 假设成本价为售价的80%
                'type': 'product',
                'categ_id': self.env.ref('product.product_category_all').id,
                'default_code': self.external_product_number,
                'description': f'Hyla产品：{self.name}'
            })
            self.sku_id = product.id
        else:
            # 更新现有产品
            self.sku_id.write({
                'list_price': self.list_price,
                'qty_available': self.qty
            })
```

### 5.2 与投标系统的关系

#### A. 数据共享

```python
class HylaCustomerQuotation(models.Model):
    _inherit = 'hyla.customer.quotation'

    def convert_to_bid_line(self):
        """转换为投标系统的客户投标"""
        # 查找对应的拍卖标单
        bid_lot = self.env['bid.order.lot'].search([
            ('bid_order_lot_line_ids.product_id', '=', self.product_id.sku_id.id)
        ], limit=1)

        if bid_lot:
            # 创建客户投标记录
            bid_line = self.env['customer.bid.line'].create({
                'name': self.customer_id.id,
                'line_id': bid_lot.id,
                'bid_price': self.final_price,
                'customer_currency_id': self.currency_id.id,
                'ubid_time': fields.Datetime.now(),
                'from_hyla': True,  # 标记来源
                'hyla_quotation_id': self.id
            })
            return bid_line
        return False
```

#### B. 流程互补

| 场景 | Hyla Bid | 传统投标 | 集成方案 |
|------|----------|----------|----------|
| **标准产品** | 直接报价 | 拍卖竞价 | Hyla优先，可转投标 |
| **大宗采购** | 初步询价 | 正式投标 | Hyla作为预选，投标确认 |
| **紧急订单** | 快速报价 | 不适用 | 仅使用Hyla |
| **新客户** | 试单报价 | 正式投标 | 两套流程并行 |

## 6. 财务流程集成

### 6.1 财务数据流转

```mermaid
flowchart TD
    A[Hyla报价审核通过] --> B[生成销售订单]
    B --> C[订单确认]
    C --> D[创建发货单]
    D --> E[发货确认]
    E --> F[自动开票]
    F --> G[应收账款]
    G --> H[收款核销]

    subgraph "财务凭证"
        I[销售收入]
        J[成本结转]
        K[库存减少]
        L[应收账款]
    end

    F --> I
    E --> J
    E --> K
    F --> L

    style A fill:#e3f2fd
    style F fill:#fff3e0
    style H fill:#e8f5e8
```

### 6.2 会计科目设置

#### A. 收入确认

```python
class SaleOrder(models.Model):
    _inherit = 'sale.order'

    def _prepare_invoice(self):
        """准备发票数据"""
        invoice_vals = super()._prepare_invoice()

        if self.order_type == 'hyla_bid':
            # Hyla订单特殊处理
            invoice_vals.update({
                'invoice_origin': f'Hyla-{self.hyla_quotation_id.name}',
                'ref': self.hyla_quotation_id.name,
                'narration': f'Hyla Bid订单：{self.name}'
            })

        return invoice_vals
```

#### B. 成本核算

```python
class StockMove(models.Model):
    _inherit = 'stock.move'

    def _get_accounting_data_for_valuation(self):
        """获取库存计价的会计数据"""
        journal_id, acc_src, acc_dest, acc_valuation = super()._get_accounting_data_for_valuation()

        # 检查是否为Hyla订单
        if self.sale_line_id and self.sale_line_id.order_id.order_type == 'hyla_bid':
            # 使用Hyla专用的会计科目
            acc_src = self.env.ref('galaxy_hyla.account_hyla_cogs').id

        return journal_id, acc_src, acc_dest, acc_valuation
```

### 6.3 报表集成

#### A. 销售报表扩展

```python
class SaleReport(models.Model):
    _inherit = 'sale.report'

    order_type = fields.Char('订单类型')
    hyla_quotation_id = fields.Many2one('hyla.customer.quotation', 'Hyla报价单')
    profit_margin = fields.Float('利润率%')

    def _select(self):
        select_str = super()._select()
        select_str += """
            , s.order_type as order_type
            , s.hyla_quotation_id as hyla_quotation_id
            , CASE WHEN l.price_unit > 0
                THEN ((l.price_unit - pt.standard_price) / l.price_unit * 100)
                ELSE 0
              END as profit_margin
        """
        return select_str

    def _group_by(self):
        group_by_str = super()._group_by()
        group_by_str += """
            , s.order_type
            , s.hyla_quotation_id
        """
        return group_by_str
```

#### B. Hyla专用报表

```python
class HylaQuotationReport(models.Model):
    _name = 'hyla.quotation.report'
    _description = 'Hyla Quotation Analysis'
    _auto = False

    quotation_id = fields.Many2one('hyla.customer.quotation', 'Quotation')
    customer_id = fields.Many2one('res.partner', 'Customer')
    product_id = fields.Many2one('galaxy.hyla.product.list', 'Product')
    state = fields.Selection([
        ('draft', 'Draft'),
        ('submitted', 'Submitted'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('ordered', 'Ordered')
    ], 'Status')

    customer_price = fields.Monetary('Customer Price')
    final_price = fields.Monetary('Final Price')
    profit_margin = fields.Float('Profit Margin %')
    conversion_rate = fields.Float('Conversion Rate %')

    def init(self):
        tools.drop_view_if_exists(self.env.cr, self._table)
        self.env.cr.execute("""
            CREATE OR REPLACE VIEW %s AS (
                SELECT
                    hcq.id as id,
                    hcq.id as quotation_id,
                    hcq.customer_id as customer_id,
                    hcq.product_id as product_id,
                    hcq.state as state,
                    hcq.customer_price as customer_price,
                    hcq.final_price as final_price,
                    CASE WHEN hcq.final_price > 0
                        THEN ((hcq.final_price - ghpl.list_price * 0.8) / hcq.final_price * 100)
                        ELSE 0
                    END as profit_margin,
                    CASE WHEN hcq.state = 'ordered' THEN 100.0 ELSE 0.0 END as conversion_rate
                FROM hyla_customer_quotation hcq
                LEFT JOIN galaxy_hyla_product_list ghpl ON hcq.product_id = ghpl.id
            )
        """ % self._table)
```

## 7. 实施计划

### 7.1 开发阶段

| 阶段 | 内容 | 工期 | 依赖 |
|------|------|------|------|
| **阶段1** | 基础表结构创建 | 3天 | 无 |
| **阶段2** | 报价流程开发 | 5天 | 阶段1 |
| **阶段3** | 审核系统开发 | 4天 | 阶段2 |
| **阶段4** | 销售系统集成 | 3天 | 阶段3 |
| **阶段5** | 财务系统集成 | 2天 | 阶段4 |
| **阶段6** | 前端界面开发 | 4天 | 阶段2 |
| **阶段7** | 测试和优化 | 3天 | 全部 |

### 7.2 数据迁移

1. **现有Hyla产品数据**：保持不变，添加关联字段
2. **客户数据**：复用现有res.partner
3. **产品数据**：建立与product.product的关联
4. **历史数据**：可选择性迁移重要的报价记录

### 7.3 风险控制

| 风险 | 影响 | 应对措施 |
|------|------|----------|
| **数据一致性** | 高 | 严格的数据验证和事务控制 |
| **性能问题** | 中 | 数据库索引优化，缓存策略 |
| **用户接受度** | 中 | 充分的用户培训和文档 |
| **系统兼容性** | 低 | 完整的测试覆盖 |

## 8. 总结

### 8.1 架构优势

1. **无缝集成**：完全融入现有销售和财务体系
2. **流程标准化**：借鉴成熟的投标审核机制
3. **数据一致性**：统一的产品和客户数据管理
4. **财务合规**：标准的会计处理流程

### 8.2 业务价值

1. **提高效率**：快速响应客户报价需求
2. **风险控制**：完善的审核和风险评估机制
3. **数据洞察**：完整的报价和转化分析
4. **客户体验**：简化的报价和下单流程

### 8.3 技术特色

1. **模块化设计**：可独立部署和维护
2. **扩展性强**：支持未来功能扩展
3. **性能优化**：合理的数据结构和索引设计
4. **易于维护**：清晰的代码结构和文档

---

**文档版本**：v1.0
**创建日期**：2025-01-30
**维护者**：Galaxy Hyla开发团队
