services:
  # PostgreSQL 数据库服务
  # postgres:
  #   build: 
  #     context: .
  #     dockerfile: Dockerfile.postgres
  #   container_name: odoo-postgres
  #   environment:
  #     POSTGRES_DB: odoo14
  #     POSTGRES_USER: odoo
  #     POSTGRES_PASSWORD: odoo
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #   ports:
  #     - "5432:5432"
  #   networks:
  #     - odoo-network
  #   restart: unless-stopped

  # Odoo 应用服务
  odoo:
    build: .
    container_name: odoo-app
    # depends_on:
    #   - postgres
    environment:
      - TZ=UTC
    volumes:
      - type: bind
        source: D:/zxf/projects/python_projects/data/data
        target: /app/data
      - type: bind
        source: D:/zxf/projects/python_projects/data/logs
        target: /app/logs
      - type: bind
        source: D:/zxf/projects/python_projects/galaxy_erp
        target: /app/workspace
    ports:
      - "8069:8069"
      - "8072:8072"  # longpolling port
      - "2222:22"    # SSH port
    networks:
      - odoo-network
    restart: unless-stopped
    command: ["tail", "-f", "/dev/null"]

networks:
  odoo-network:
    driver: bridge

#volumes:
  # postgres_data:
  #   driver: local
  # odoo_data:
  #   driver: local
  # odoo_logs:
  #   driver: local