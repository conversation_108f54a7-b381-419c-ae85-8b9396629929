<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_packing_list_purchase_analysis_form" model="ir.ui.view">
        <field name="name">packing.list.purchase.analysis.form</field>
        <field name="model">packing.list.purchase.analysis</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <group>
                            <field name="packing_list_id"/>
                            <field name="vpi_line_id"/>
                            <field name="invoice_id" readonly="1"/>
                            <field name="glot_number"/>
                        </group>
                        <group>
                            <field name="packing_qty"/>
                            <field name="purchase_qty"/>
                            <field name="difference_qty"/>
                        </group>
                    </group>

                    <notebook>
                        <page string="Item">
                            <field name="item_ids">
                                <tree editable="bottom">
                                    <field name="sku_id"/>
                                    <field name="glot_number"/>
                                    <field name="purchase_qty"/>
                                    <field name="packing_qty"/>
                                    <field name="difference_qty"/>
                                    <field name="won_order_lot_line_id"/>
                                    <field name="purchase_order_line_id"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <record id="view_packing_list_purchase_analysis_item_tree" model="ir.ui.view">
        <field name="name">packing.list.purchase.analysis.item.tree</field>
        <field name="model">packing.list.purchase.analysis.item</field>
        <field name="arch" type="xml">
            <tree create="0" decoration-danger="won_order_lot_line_id == False">
                <field name="glot_number"/>
                <field name="sku_id"/>
                <field name="purchase_qty"/>
                <field name="packing_qty"/>
                <field name="difference_qty"/>
                <field name="is_has_alloc_line" optional="hide"/>
                <field name="won_order_lot_line_id"/>
            </tree>
        </field>
    </record>

    <record id="action_packing_list_purchase_analysis_item_view_tree" model="ir.actions.act_window">
        <field name="name">Analysis Item</field>
        <field name="res_model">packing.list.purchase.analysis.item</field>
        <field name="view_mode">tree</field>
    </record>

    <record id="view_packing_list_purchase_analysis_item_search" model="ir.ui.view">
        <field name="name">packing.list.purchase.analysis.item.search</field>
        <field name="model">packing.list.purchase.analysis.item</field>
        <field name="arch" type="xml">
            <search>
                <field name="glot_number"/>
                <field name="sku_id"/>
                <separator/>
                <filter string="Has Allocation" name="has_alloc" domain="[('is_has_alloc_line', '=', True)]"/>
                <filter string="Allocation Finished" name="alloc_finished" domain="[('difference_qty', '=', 0)]"/>
                <filter string="Need Allocate" name="need_alloc" domain="[('difference_qty', '>', 0)]"/>
                <group expand="0" string="Group By">
                    <filter string="SKU" name="group_by_sku" context="{'group_by': 'sku_id'}"/>
                    <filter string="GLOT Number" name="group_by_glot" context="{'group_by': 'glot_number'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="view_packing_list_purchase_analysis_item_form" model="ir.ui.view">
        <field name="name">packing.list.purchase.analysis.item.form</field>
        <field name="model">packing.list.purchase.analysis.item</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <group>
                            <field name="analysis_id"/>
                            <field name="packing_list_id"/>
                            <field name="sku_id"/>
                            <field name="glot_number"/>
                        </group>
                        <group>
                            <field name="packing_qty"/>
                            <field name="purchase_qty"/>
                            <field name="difference_qty"/>
                            <field name="is_has_alloc_line"/>
                        </group>
                        <group>
                            <field name="won_order_lot_line_id"/>
                            <field name="purchase_order_line_id"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Packing List">
                            <field name="packing_list_line_ids" readonly="1">
                                <tree>
                                    <field name="vendor_lot_number"/>
                                    <field name="sku_id"/>
                                    <field name="imei"/>
                                    <field name="equipment_code"/>
                                    <field name="match_type" widget="badge"
                                           decoration-success="match_type == 'auto'"
                                           decoration-info="match_type == 'manual'"/>
                                    <field name="won_order_lot_line_id" optional="hide"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <record id="view_packing_list_purchase_analysis_item_show_alloc_form" model="ir.ui.view">
        <field name="name">packing.list.purchase.analysis.item.form</field>
        <field name="model">packing.list.purchase.analysis.item</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <group>
                            <field name="analysis_id"/>
                            <field name="packing_list_id"/>
                            <field name="sku_id"/>
                            <field name="glot_number"/>
                        </group>
                        <group>
                            <field name="packing_qty"/>
                            <field name="purchase_qty"/>
                            <field name="difference_qty"/>
                            <field name="is_has_alloc_line"/>
                        </group>
                        <group>
                            <field name="won_order_lot_line_id"/>
                            <field name="purchase_order_line_id"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Packing List">
                            <field name="packing_list_line_ids" readonly="1">
                                <tree>
                                    <field name="vendor_lot_number"/>
                                    <field name="sku_id"/>
                                    <field name="imei"/>
                                    <field name="equipment_code"/>
                                    <field name="match_type" widget="badge"
                                           decoration-success="match_type == 'auto'"
                                           decoration-info="match_type == 'manual'"/>
                                    <field name="won_order_lot_line_id" optional="hide"/>
                                    <button name="action_dealloc_match" string="Dealloc" type="object" icon="fa-unlink" class="btn-link" 
                                    confirm="Are you sure to deallocate this record?"
                                    attrs="{'invisible': ['|', ('match_type', '=', False), ('won_order_lot_line_id', '=', False)]}"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

</odoo> 
