<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record
            id="sr_view_account_payment_register_invoice_form_extends_add_manual_currency_exchange_rate"
            model="ir.ui.view">
            <field name="name">sr.view.account.payment.register.invoice.form.extends.add.manual.currency.exchange.rate</field>
            <field name="model">account.payment.register</field>
            <field name="inherit_id" ref="account.view_account_payment_register_form" />
            <field name="arch" type="xml">
                <field name="journal_id" position="after">
                    <field name="active_manual_currency_rate" ></field>
                    <field name="apply_manual_currency_exchange" attrs="{'invisible': [('active_manual_currency_rate', '=', False)]}" />
                    <field name="manual_currency_exchange_rate"
                        attrs="{'invisible': ['|',('apply_manual_currency_exchange', '=', False),('active_manual_currency_rate', '=', False)],'required':[('apply_manual_currency_exchange', '=', True)]}" />
                </field>
            </field>
        </record>
    </data>
</odoo>