
<section class="oe_container">
	<div class="oe_row oe_spaced" style="width: 100%; margin-top: 10px;">
		<div class="oe_span12" style="width: 100%;">
			<center>
				<h1 style="font-size: 2rem;">Manual Currency Exchange Rate On
					Sales Order, Invoices, Purchase Orders and Payments.</h1>
			</center>
			<center>
				<p style="font-size: 20px; margin-top: 20px; margin-bottom: 20px; text-align: justify;">Currency
					rate fluctuates every day. you can configure currency rate under
					accounting in odoo but its very difficult to change exchange rate
					on daily basis. Odoo does not provide you to change currency rate
					on sales order, Invoices, purchase orders and payments level. This
					module provide the facility where you can enter manual currency
					rate on sales order, invoices, purchase order and payment level.
					Based on that rate it will generate journal entry and calculate
					foreign exchange loss or gain.</p>
			</center>
		</div>
		<div class="oe_slogan oe_spaced text-center">
			<a class="btn mt8 ml8"
				style="background-color: #2875aa; color: #FFFFFF !important; font-size: 20px; font-weight: bold; border-radius: 7px;"
				href="https://youtu.be/sDmW8wEQm4g" target="_blank"> Video
				Tutorial</a> <a class="btn mt8 ml8"
				style="background-color: #2875aa; color: #FFFFFF !important; font-size: 20px; font-weight: bold; border-radius: 7px;"
				href="skype:live:contact.hiren1188?chat" target="_blank">
				Contact Us</a>
		</div>
	</div>
</section>

<section class="oe_container"
	style="box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19); ">
	<div class="panel">
		<div class="panel-heading"
			style="padding: 8px; border-top-left-radius: 15px; border-top-right-radius: 15px;">
			<div class="alert alert-info"
				style="padding: 8px; font-weight: 300; font-size: 20px;">
				<i class="fa fa-arrow-circle-o-right"></i><b> Module Features </b>
			</div>
			<ul class="list-unstyled ">
				<li style="font-size: initial"><i
					class="fa fa-check text-primary"></i> You can set currency rate on sales order/quotations level.</li>
				<li style="font-size: initial"><i
					class="fa fa-check text-primary"></i> You can set currency rate on purchase order/request for quotations level.</li>
				<li style="font-size: initial"><i
					class="fa fa-check text-primary"></i>You can set currency rate on customer invoice and vendor bills level.</li>
				<li style="font-size: initial"><i
					class="fa fa-check text-primary"></i> You can set currency rate on payment level.</li>
				<li style="font-size: initial"><i
					class="fa fa-check text-primary"></i> Based on that rate journal entry will generate.</li>
				<li style="font-size: initial"><i
					class="fa fa-check text-primary"></i> Based on that rate foreign exchange gain and loss will be calculated.</li>
				<li style="font-size: initial"><i
					class="fa fa-check text-primary"></i> 50 days free support.</li>
			</ul>
		</div>
	</div>
</section>

	<section class="oe_container" style="margin-top: 20px; box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19); ">
		<div style="background-color: lightblue; padding:8px; border-top-left-radius: 15px; border-top-right-radius: 15px;">
			<p>
			<center>
				<h1 style="font-size:2rem;">Sales quotation To Customer Invoice (Sales Flow)</h1>
			</center>
			</p>
		</div>
		<div class="oe_row oe_spaced" style="width:100%; margin-top: 10px;  ">
			<div class="oe_span12" style="width:100%;">
				<p>
				<center>
					<h1 style="font-size:2rem;">Create sales quotation. If quotations currency and company currency is different then you will see the option where you can enter exchange rate on quotations.</h1>
				</center>
				</p>
				<div class="oe_demo oe_screenshot" style="margin-bottom:30px;">
					<img src="sales_order.png">
				</div>
			</div>
		</div>
	</section>

	<section class="oe_container oe_dark" style="margin-top: 20px; box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19); ">
		<div class="oe_row oe_spaced" style="width:100%; margin-top: 10px;  ">
			<div class="oe_span12" style="width:100%;">
				<p>
				<center>
					<h1 style="font-size:2rem;">Currency rate will pass on customer invoice when you make customer invoice from that same sales order. </h1>
				</center>
				</p>
				<div class="oe_demo oe_screenshot" style="margin-bottom:30px;">
					<img src="customer_invoice.png">
				</div>
			</div>
		</div>
	</section>
	
	<section class="oe_container" style="margin-top: 20px; box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19); ">
		<div class="oe_row oe_spaced" style="width:100%; margin-top: 10px;  ">
			<div class="oe_span12" style="width:100%;">
				<p>
				<center>
					<h1 style="font-size:2rem;">When you validate the customer invoice at that time it will create 1 journal entry based on manual currency exchange rate rather than odoo's default currency rate.</h1>
				</center>
				</p>
				<div class="oe_demo oe_screenshot" style="margin-bottom:30px;">
					<img src="customer_invoice_journal_entry_1.png">
				</div>
			</div>
		</div>
	</section>

	<section class="oe_container oe_dark" style="margin-top: 20px; box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19); ">
		<div class="oe_row oe_spaced" style="width:100%; margin-top: 10px;  ">
			<div class="oe_span12" style="width:100%;">
				<p>
				<center>
					<h1 style="font-size:2rem;">At the time of payment, you can also set currency rate.</h1>
					<h1 style="font-size:2rem;">Let's take an example</h1>
					<h1 style="font-size:2rem;">First partial payment: at the time of sales order and invoice order rate was 3. now, at the time of payment, if you enter rate 5 then payment journal entry will calculate based on 5.</h1>
				</center>
				</p>
				<div class="oe_demo oe_screenshot" style="margin-bottom:30px;">
					<img src="customer_invoice1.png">
				</div>
			</div>
		</div>
	</section>
		<section class="oe_container" style="margin-top: 20px; box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19); ">
		<div class="oe_row oe_spaced" style="width:100%; margin-top: 10px;  ">
			<div class="oe_span12" style="width:100%;">
				<p>
				<center>
					<h1 style="font-size:2rem;">This is a journal entry for first partial payment, you can see the amount currrency and calculated credit and debit amount based on new rate which we have entered at the time of payment.</h1>
				</center>
				</p>
				<div class="oe_demo oe_screenshot" style="margin-bottom:30px;">
					<img src="customer_invoice_journal_entry_2.png">
				</div>
			</div>
		</div>
	</section>
	
		<section class="oe_container oe_dark" style="margin-top: 20px; box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19); ">
		<div class="oe_row oe_spaced" style="width:100%; margin-top: 10px;  ">
			<div class="oe_span12" style="width:100%;">
				<p>
				<center>
					<h1 style="font-size:2rem;">second partial payment: at the time of second payment, if we enter currency rate 2 then payment journal will calculate based on 2.</h1>
				</center>
				</p>
				<div class="oe_demo oe_screenshot" style="margin-bottom:30px;">
					<img src="customer_invoice2.png">
				</div>
			</div>
		</div>
	</section>
	
			<section class="oe_container" style="margin-top: 20px; box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19); ">
		<div class="oe_row oe_spaced" style="width:100%; margin-top: 10px;">
			<div class="oe_span12" style="width:100%;">
				<p>
				<center>
					<h1 style="font-size:2rem;">This is a journal entry for second partial payment, you can see the amount currrency and calculated credit and debit amount based on new rate which we have entered at the time of payment.</h1>
				</center>
				</p>
				<div class="oe_demo oe_screenshot" style="margin-bottom:30px;">
					<img src="customer_invoice_journal_entry_3.png">
				</div>
			</div>
		</div>
	</section>

			<section class="oe_container oe_dark" style="margin-top: 20px; box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19); ">
		<div class="oe_row oe_spaced" style="width:100%; margin-top: 10px;">
			<div class="oe_span12" style="width:100%;">
				<p>
				<center>
					<h1 style="font-size:2rem;">Here, you can see the foreign exchange loss and gain journal</h1>
				</center>
				</p>
				<div class="oe_demo oe_screenshot" style="margin-bottom:30px;">
					<img src="exchange_rate_gain_loss_for_customer_invoice.png">
				</div>
			</div>
		</div>
	</section>

	<section class="oe_container" style="margin-top: 20px; box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19); ">
		<div style="background-color: lightblue; padding:8px; border-top-left-radius: 15px; border-top-right-radius: 15px;">
			<p>
			<center>
				<h1 style="font-size:2rem;">Request for quotation To Customer Invoice (Purchase Flow)</h1>
			</center>
			</p>
		</div>
		<div class="oe_row oe_spaced" style="width:100%; margin-top: 10px;  ">
			<div class="oe_span12" style="width:100%;">
				<p>
				<center>
					<h1 style="font-size:2rem;">Create RFQ. If RFQ currency and company currency is different then you will see the option where you can enter exchange rate on quotations.</h1>
				</center>
				</p>
				<div class="oe_demo oe_screenshot" style="margin-bottom:30px;">
					<img src="purchase_order.png">
				</div>
			</div>
		</div>
	</section>

	<section class="oe_container oe_dark" style="margin-top: 20px; box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19); ">
		<div class="oe_row oe_spaced" style="width:100%; margin-top: 10px;  ">
			<div class="oe_span12" style="width:100%;">
				<p>
				<center>
					<h1 style="font-size:2rem;">Currency rate will pass on Vendor bills when you make Vendor bills from that same purchase order. </h1>
				</center>
				</p>
				<div class="oe_demo oe_screenshot" style="margin-bottom:30px;">
					<img src="vendor_bill.png">
				</div>
			</div>
		</div>
	</section>
	
	<section class="oe_container" style="margin-top: 20px; box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19); ">
		<div class="oe_row oe_spaced" style="width:100%; margin-top: 10px;  ">
			<div class="oe_span12" style="width:100%;">
				<p>
				<center>
					<h1 style="font-size:2rem;">When you validate the vendor bills at that time it will create 1 journal entry based on manual currency exchange rate rather than odoo's default currency rate.</h1>
				</center>
				</p>
				<div class="oe_demo oe_screenshot" style="margin-bottom:30px;">
					<img src="vendor_invoice_journal_entry_1.png">
				</div>
			</div>
		</div>
	</section>

	<section class="oe_container oe_dark" style="margin-top: 20px; box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19); ">
		<div class="oe_row oe_spaced" style="width:100%; margin-top: 10px;  ">
			<div class="oe_span12" style="width:100%;">
				<p>
				<center>
					<h1 style="font-size:2rem;">At the time of payment, you can also set currency rate.</h1>
					<h1 style="font-size:2rem;">Let's take an example</h1>
					<h1 style="font-size:2rem;">First partial payment: at the time of RFQ and vendor bills rate was 5. now, at the time of payment, if you enter rate 8 then payment journal entry will calculate based on 8.</h1>
				</center>
				</p>
				<div class="oe_demo oe_screenshot" style="margin-bottom:30px;">
					<img src="vendor_bill1.png">
				</div>
			</div>
		</div>
	</section>
		<section class="oe_container" style="margin-top: 20px; box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19); ">
		<div class="oe_row oe_spaced" style="width:100%; margin-top: 10px;  ">
			<div class="oe_span12" style="width:100%;">
				<p>
				<center>
					<h1 style="font-size:2rem;">This is a journal entry for first partial payment, you can see the amount currrency and calculated credit and debit amount based on new rate which we have entered at the time of payment.</h1>
				</center>
				</p>
				<div class="oe_demo oe_screenshot" style="margin-bottom:30px;">
					<img src="vendor_invoice_journal_entry_2.png">
				</div>
			</div>
		</div>
	</section>
	
		<section class="oe_container oe_dark" style="margin-top: 20px; box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19); ">
		<div class="oe_row oe_spaced" style="width:100%; margin-top: 10px;  ">
			<div class="oe_span12" style="width:100%;">
				<p>
				<center>
					<h1 style="font-size:2rem;">second partial payment: at the time of second payment, if we enter currency rate 3 then payment journal will calculate based on 3.</h1>
				</center>
				</p>
				<div class="oe_demo oe_screenshot" style="margin-bottom:30px;">
					<img src="vendor_bill2.png">
				</div>
			</div>
		</div>
	</section>
	
			<section class="oe_container" style="margin-top: 20px; box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19); ">
		<div class="oe_row oe_spaced" style="width:100%; margin-top: 10px;">
			<div class="oe_span12" style="width:100%;">
				<p>
				<center>
					<h1 style="font-size:2rem;">This is a journal entry for second partial payment, you can see the amount currrency and calculated credit and debit amount based on new rate which we have entered at the time of payment.</h1>
				</center>
				</p>
				<div class="oe_demo oe_screenshot" style="margin-bottom:30px;">
					<img src="vendor_invoice_journal_entry_3.png">
				</div>
			</div>
		</div>
	</section>

			<section class="oe_container oe_dark" style="margin-top: 20px; box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19); ">
		<div class="oe_row oe_spaced" style="width:100%; margin-top: 10px;">
			<div class="oe_span12" style="width:100%;">
				<p>
				<center>
					<h1 style="font-size:2rem;">Here, you can see the foreign exchange loss and gain journal</h1>
				</center>
				</p>
				<div class="oe_demo oe_screenshot" style="margin-bottom:30px;">
					<img src="exchange_rate_gain_loss_for_vendor_invoice.png">
				</div>
			</div>
		</div>
	</section>
	
	<section class="oe_container"
		style="margin-top: 20px; box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19); text-align: center; ">
		<div style="text-align: center;">
			<h1 style="padding-top: 20px; padding-bottom: 20px; color: #000; font-size: 33px;">
				Need Any Help ?</h1>
		</div>
		<div style="display: block; width: 100%; margin: 0 auto; height: auto; padding-bottom: 20px; margin-bottom: 20px;">
			<div style="display: inline-block; text-align: center; color: #000; width: 33.33%;">
				<a  href="mailto:<EMAIL>"><img
					src="contact/gmail.png"
					style="display: block; text-align: center; width: 80px; height: 80px; margin: 0 auto; padding-bottom: 10px;"></a>
				<a  href="mailto:<EMAIL>"><label
					 style="font-size: 18px; color: #000;"><EMAIL></label></a>
			</div>
			<div style="display: inline-block; text-align: center; color: #000; width: 33.33%;">
				<a  href="skype:live:contact.hiren1188?chat"><img
					src="contact/skype.png"
					style="display: block; text-align: center; width: 80px; height: 80px; margin: 0 auto; padding-bottom: 10px;"></a>
				<a  href="skype:live:contact.hiren1188?chat"><label style="font-size: 20px; color: #000;">contact.hiren1188</label></a>
			</div>
			<div style="display: inline-block; text-align: center; color: #000; width: 32.33%;">
				<a  target="_blank"
					href="http://www.sitaramsolutions.in"><img
					src="contact/domain.png"
					style="display: block; text-align: center; width: 80px; height: 80px; margin: 0 auto; padding-bottom: 10px;"></a>
					<a  target="_blank" href="http://www.sitaramsolutions.in"><label  style="font-size: 20px; color: #000;">www.sitaramsolutions.in</label></a>
			</div>
		</div>
		<div style="display: block; width: 100%; margin: 0 auto; height: auto; padding-bottom: 20px; margin-bottom: 20px;">
			<div style="text-align: center; color: #000; width: 33.33%;">
				<img src="contact/customer-service.png"
					style="display: block; text-align: center; width: 80px; height: 80px; margin: 0 auto; padding-bottom: 10px;">
				<a  href="#"><label 
					style="font-size: 20px; color: #000;">50 Days Free Support</label></a><br />
				<a  href="#"><label 
					style="font-size: 20px; color: #000;">(Except data
						recovery)</label></a>
			</div>
		</div>

	</section>
	<section class="our_service" style="box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19); text-align: center; ">
		<div  style="text-align: center;">
			<h1
				style="padding-top: 20px; padding-bottom: 20px; color: #000; font-size: 33px;">
				Our Service</h1>
		</div>
		<div 
			style="display: block; width: 100%; height: auto; padding-bottom: 20px; margin-bottom: 20px;">
			<div 
				style="display: inline-block; text-align: center; color: #000; width: 33.33%;">
				<img src="services/development.png"
					style="display: block; text-align: center; width: 80px; height: 80px; margin: 0 auto; padding-bottom: 10px;">
				<label 
					style="text-transform: uppercase; font-size: 20px;">Customization</label>
			</div>
			<div 
				style="display: inline-block; text-align: center; color: #000; width: 33.33%;">
				<img src="services/implementation.png"
					style="display: block; text-align: center; width: 80px; height: 80px; margin: 0 auto; padding-bottom: 10px;">
				<label 
					style="text-transform: uppercase; font-size: 20px;">implementation</label>
			</div>
			<div 
				style="display: inline-block; text-align: center; color: #000; width: 32.33%;">
				<img src="services/solution.png"
					style="display: block; text-align: center; width: 80px; height: 80px; margin: 0 auto; padding-bottom: 10px;">
				<label 
					style="text-transform: uppercase; font-size: 20px;">integration</label>
			</div>
		</div>
		<div 
			style="display: block; width: 100%; height: auto; padding-bottom: 20px; margin-bottom: 10px;">
			<div 
				style="display: inline-block; text-align: center; color: #000; width: 33.33%;">
				<img src="services/code.png"
					style="display: block; text-align: center; width: 80px; height: 80px; margin: 0 auto; padding-bottom: 10px;">
				<label 
					style="text-transform: uppercase; font-size: 20px;">Development</label>
			</div>
			<div 
				style="display: inline-block; text-align: center; color: #000; width: 33.33%;">
				<img src="services/human-resources.png"
					style="display: block; text-align: center; width: 80px; height: 80px; margin: 0 auto; padding-bottom: 10px;">
				<label 
					style="text-transform: uppercase; font-size: 20px;">Hire
					Developer</label>
			</div>
			<div 
				style="display: inline-block; text-align: center; color: #000; width: 32.33%;">
				<img src="services/migrating.png"
					style="display: block; text-align: center; width: 80px; height: 80px; margin: 0 auto; padding-bottom: 10px;">
				<label 
					style="text-transform: uppercase; font-size: 20px;">Migrations</label>
			</div>
		</div>
	</section>
