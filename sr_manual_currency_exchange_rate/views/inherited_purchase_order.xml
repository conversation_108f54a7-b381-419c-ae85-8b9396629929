<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
         <record id="purchase_order_form_extends_add_manual_currency_exchange_rate" model="ir.ui.view">
            <field name="name">purchase.order.form.extends.add.manual.currency.exchange.rate</field>
            <field name="model">purchase.order</field>
            <field name="inherit_id" ref="purchase.purchase_order_form"/>
            <field name="arch" type="xml">
            	<field name="date_order" position="after">
            		<field name="active_manual_currency_rate"/>
            		<field name="apply_manual_currency_exchange" attrs="{'invisible': [('active_manual_currency_rate', '=', False)]}"/>
            		<field name="manual_currency_exchange_rate" attrs="{'invisible': ['|',('apply_manual_currency_exchange', '=', False),('active_manual_currency_rate', '=', False)],'required':[('apply_manual_currency_exchange', '=', True)]}"/>
            	</field>
            </field>
        </record>
	</data>
</odoo>
