<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
         <record id="view_order_form_extends_add_manual_currency_exchange_rate" model="ir.ui.view">
            <field name="name">view.order.form.extends.add.manual.currency.exchange.rate</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale.view_order_form"/>
            <field name="arch" type="xml">
            <field name="currency_id" position="after">
            	<field name="company_currency_id" string="Company Currency"/>
            	<field name="active_manual_currency_rate"/>
            </field>
            	<field name="payment_term_id" position="after">
            		<field name="apply_manual_currency_exchange" attrs="{'invisible': [('active_manual_currency_rate', '=', False)]}"/>
            		<field name="manual_currency_exchange_rate" attrs="{'invisible': ['|',('apply_manual_currency_exchange', '=', False),('active_manual_currency_rate', '=', False)],'required':[('apply_manual_currency_exchange', '=', True)]}"/>
            	</field>
            	<field name="currency_id" position="attributes">
    <attribute name="invisible">False</attribute>
    <attribute name="string">Sale Order Currency</attribute>
</field>
            </field>
        </record>
	</data>
</odoo>
