<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="view_move_form_extends_add_manual_currency_exchange_rate" model="ir.ui.view">
            <field name="name">view.move.form.extends.add.manual.currency.exchange.rate</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_move_form"/>
            <field name="arch" type="xml">
                <field name="invoice_date" position="after">
                    <field name="active_manual_currency_rate"/>
                    <field name="apply_manual_currency_exchange" attrs="{'invisible': [('active_manual_currency_rate', '=', False)]}"/>
                    <field name="manual_currency_exchange_rate" attrs="{'invisible': ['|',('apply_manual_currency_exchange', '=', False),('active_manual_currency_rate', '=', False)],'required':[('apply_manual_currency_exchange', '=', True)]}"/>
                </field>
            </field>
        </record>
    </data>
</odoo>