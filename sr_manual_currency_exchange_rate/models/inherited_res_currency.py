# -*- coding: utf-8 -*-
##############################################################################
#
#    OpenERP, Open Source Management Solution
#    Copyright (C) Sitaram Solutions (<https://sitaramsolutions.in/>).
#
#    For Module Support : <EMAIL>  or Skype : contact.hiren1188
#
##############################################################################

from odoo import models, fields, api


class ResCurrency(models.Model):
    _inherit = 'res.currency'

    @api.model
    def _get_conversion_rate(self, from_currency, to_currency, company, date):
        currency_rates = (from_currency + to_currency)._get_rates(company, date)
        if self._context.get('active_manutal_currency'):
            res = self._context.get('manual_rate')
        else:
            res = currency_rates.get(to_currency.id) / currency_rates.get(from_currency.id)
        return res
