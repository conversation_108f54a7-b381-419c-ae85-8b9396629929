# Galaxy Hyla - 配置管理和Redis缓存使用指南

## 1. 配置数据读取

### 1.1 系统参数 (推荐方式)

#### 获取系统参数
```python
# 在模型中
config_value = self.env['ir.config_parameter'].sudo().get_param('your.config.key', default='default_value')

# 在控制器中
config_value = request.env['ir.config_parameter'].sudo().get_param('your.config.key')

# 示例：获取token过期时间
token_expire = request.env['ir.config_parameter'].sudo().get_param('hyla.token.expire', '3600')
```

#### 设置系统参数
```python
# 设置参数
self.env['ir.config_parameter'].sudo().set_param('hyla.token.expire', '7200')

# 批量设置
params = {
    'hyla.token.expire': '3600',
    'hyla.redis.enabled': 'true',
    'hyla.debug.mode': 'false'
}
for key, value in params.items():
    self.env['ir.config_parameter'].sudo().set_param(key, value)
```

### 1.2 从odoo.conf配置文件读取

#### 读取配置文件
```python
from odoo.tools import config

# 读取配置文件中的值
redis_host = config.get('galaxy_redis_host', 'localhost')
redis_port = config.get('galaxy_redis_port', 6379)
debug_mode = config.get('hyla_debug_mode', False)
```

#### odoo.conf配置示例
```ini
[options]
# Redis配置
galaxy_redis_host = localhost
galaxy_redis_port = 6379
galaxy_redis_password = your_password

# Hyla配置
hyla_debug_mode = True
hyla_token_secret = your_secret_key
```

### 1.3 公司级别配置

```python
# 获取当前公司配置
company = self.env.company
company_name = company.name
currency = company.currency_id
timezone = company.partner_id.tz

# 获取指定公司配置
company = self.env['res.company'].browse(company_id)
```

### 1.4 配置优先级建议

| 配置类型 | 推荐存储方式 | 原因 |
|---------|-------------|------|
| 敏感配置 (密码、密钥) | `odoo.conf` | 安全性高，不会被意外修改 |
| 业务配置 (超时时间、开关) | 系统参数 | 可通过界面修改，灵活性好 |
| 公司相关 (时区、货币) | 公司设置 | 多公司环境下各自独立 |
| 用户相关 (语言、偏好) | 用户设置 | 个性化配置 |

## 2. Redis缓存使用

### 2.1 Redis配置类

项目中已有的Redis配置类：
```python
# base_galaxy/models/galaxy_redis.py
class redis_galaxy():
    def __init__(self, env=None):
        # 从系统参数获取Redis配置
        self.redis_server = env["ir.config_parameter"].sudo().get_param("redis.server.name")
        self.redis_port = env["ir.config_parameter"].sudo().get_param("redis.server.port")
        self.redis_password = env["ir.config_parameter"].sudo().get_param("redis.server.password")
        # 创建Redis连接
        self.o_redis = redis.StrictRedis(
            host=self.redis_server, 
            port=self.redis_port, 
            db=0, 
            password=self.redis_password, 
            decode_responses=True
        )
    
    def set_redis_key(self, key, value, ex=None):
        """设置缓存，ex为过期时间(秒)"""
        return self.o_redis.set(key, value, ex)
    
    def get_redis_key(self, key):
        """获取缓存"""
        return self.o_redis.get(key)
```

### 2.2 Redis系统参数配置

需要在Odoo系统参数中配置以下参数：

| 参数名 | 示例值 | 说明 |
|-------|--------|------|
| `redis.server.name` | `localhost` | Redis服务器地址 |
| `redis.server.port` | `6379` | Redis端口 |
| `redis.server.password` | `your_password` | Redis密码 |

### 2.3 Token缓存实现

#### 在galaxy_hyla/services/auth.py中实现token缓存
```python
import functools
import time
from odoo.http import request, Response
from odoo.addons.base_galaxy.models import galaxy_redis as gr

def token_auth(func):
    """
    自定义 token 鉴权装饰器，使用Redis缓存
    """
    @functools.wraps(func)
    def _wrapper(*args, **kw):
        # 1. 取cookie里的token
        token = request.httprequest.cookies.get('hyla_token')
        if not token:
            return Response('Missing token', status=401)
        
        # 2. 从Redis验证token
        gr1 = gr.redis_galaxy(request.env)
        cached_data = gr1.get_redis_key(token)
        if not cached_data:
            return Response('Invalid or expired token', status=401)
        
        # 3. 解析缓存数据 (格式: "user_id-secret_key")
        try:
            user_id, secret_key = cached_data.split('-')
            # 4. 设置用户上下文
            request.env.user = request.env['res.users'].browse(int(user_id))
        except (ValueError, IndexError):
            return Response('Invalid token format', status=401)
        
        return func(*args, **kw)
    return _wrapper
```

#### 登录服务中生成和缓存token
```python
# galaxy_hyla/services/login.py
from odoo.addons.base_galaxy.models import galaxy_redis as gr

class LoginService:
    def generate_and_cache_token(self, user_id, secret_key):
        """生成token并缓存到Redis"""
        # 获取token过期时间配置
        expire_time = int(request.env['ir.config_parameter'].sudo().get_param('hyla.token.expire', '3600'))
        
        # 生成token
        token = self.env['token.control'].generate_token(
            key=secret_key, 
            start_time=time.time(), 
            expire=expire_time
        )
        
        # 缓存到Redis
        gr1 = gr.redis_galaxy(request.env)
        cache_value = f'{user_id}-{secret_key}'
        gr1.set_redis_key(token, cache_value, ex=expire_time)
        
        return token
    
    def invalidate_token(self, token):
        """使token失效"""
        gr1 = gr.redis_galaxy(request.env)
        gr1.o_redis.delete(token)
```

### 2.4 其他缓存使用示例

#### 缓存用户会话信息
```python
def cache_user_session(self, user_id, session_data):
    """缓存用户会话信息"""
    gr1 = gr.redis_galaxy(self.env)
    session_key = f'user_session_{user_id}'
    # 缓存1小时
    gr1.set_redis_key(session_key, json.dumps(session_data), ex=3600)

def get_user_session(self, user_id):
    """获取用户会话信息"""
    gr1 = gr.redis_galaxy(self.env)
    session_key = f'user_session_{user_id}'
    cached_data = gr1.get_redis_key(session_key)
    return json.loads(cached_data) if cached_data else None
```

#### 缓存API调用结果
```python
def cache_api_result(self, api_key, result_data, expire=1800):
    """缓存API调用结果，默认30分钟过期"""
    gr1 = gr.redis_galaxy(self.env)
    cache_key = f'api_result_{api_key}'
    gr1.set_redis_key(cache_key, json.dumps(result_data), ex=expire)

def get_cached_api_result(self, api_key):
    """获取缓存的API结果"""
    gr1 = gr.redis_galaxy(self.env)
    cache_key = f'api_result_{api_key}'
    cached_data = gr1.get_redis_key(cache_key)
    return json.loads(cached_data) if cached_data else None
```

## 3. 最佳实践

### 3.1 缓存键命名规范
- 使用有意义的前缀：`hyla_token_`, `user_session_`, `api_result_`
- 包含必要的标识符：用户ID、时间戳等
- 避免键名冲突

### 3.2 过期时间设置
- Token缓存：1-24小时
- 会话缓存：30分钟-2小时  
- API结果缓存：5-30分钟
- 临时数据：5-15分钟

### 3.3 错误处理
```python
def safe_redis_operation(self, operation_func):
    """安全的Redis操作，带错误处理"""
    try:
        return operation_func()
    except redis.ConnectionError:
        _logger.error("Redis连接失败")
        return None
    except Exception as e:
        _logger.error(f"Redis操作失败: {e}")
        return None
```

### 3.4 配置管理建议
1. **开发环境**：使用系统参数，便于调试
2. **生产环境**：敏感配置放在odoo.conf
3. **多环境**：使用环境变量或配置文件模板
4. **监控**：定期检查Redis连接状态和缓存命中率

## 4. 故障排查

### 4.1 常见问题
- Redis连接失败：检查服务器地址、端口、密码
- Token验证失败：检查token格式、过期时间
- 缓存未命中：检查键名、过期时间设置

### 4.2 调试方法
```python
# 检查Redis连接
gr1 = gr.redis_galaxy(self.env)
try:
    gr1.o_redis.ping()
    print("Redis连接正常")
except:
    print("Redis连接失败")

# 查看所有缓存键
keys = gr1.o_redis.keys('hyla_*')
print(f"缓存键列表: {keys}")
```
