<odoo>
    <data>
<!--
          <record id="object0" model="galaxy_packing_list_purchase_order.galaxy_packing_list_purchase_order">
            <field name="name">Object 0</field>
            <field name="value">0</field>
          </record>

          <record id="object1" model="galaxy_packing_list_purchase_order.galaxy_packing_list_purchase_order">
            <field name="name">Object 1</field>
            <field name="value">10</field>
          </record>

          <record id="object2" model="galaxy_packing_list_purchase_order.galaxy_packing_list_purchase_order">
            <field name="name">Object 2</field>
            <field name="value">20</field>
          </record>

          <record id="object3" model="galaxy_packing_list_purchase_order.galaxy_packing_list_purchase_order">
            <field name="name">Object 3</field>
            <field name="value">30</field>
          </record>

          <record id="object4" model="galaxy_packing_list_purchase_order.galaxy_packing_list_purchase_order">
            <field name="name">Object 4</field>
            <field name="value">40</field>
          </record>
-->
    </data>
</odoo>