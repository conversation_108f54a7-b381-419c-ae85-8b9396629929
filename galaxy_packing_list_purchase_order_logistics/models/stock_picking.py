from odoo import models, fields, api


class StockPicking(models.Model):
    _inherit = 'stock.picking'

    logistics_order_id = fields.Many2one('galaxy.logistics.order', string='Logistics Order', index=True)
    packing_list_ids = fields.One2many('packing.list', 'picking_id', string='Packing List')
    packing_list_carton_count = fields.Integer(compute='_compute_packing_list_count', store=True)
    packing_list_quantity = fields.Integer(compute='_compute_packing_list_count', store=True)

    @api.depends('packing_list_ids')
    def _compute_packing_list_count(self):
        for picking in self:
            picking.packing_list_carton_count = len(
                picking.packing_list_ids.packing_list_line_ids.mapped('carton_number'))
            picking.packing_list_quantity = len(picking.packing_list_ids.packing_list_line_ids)

    def action_open_packing_list(self):
        action = self.env.ref('galaxy_packing_list.packing_list_action').read()[0]
        if len(self.packing_list_ids) == 1:
            action['view_mode'] = 'form'
            action['views'] = [(False, 'form')]
            action['res_id'] = self.packing_list_ids[0].id
        else:
            action['domain'] = [('id', 'in', self.packing_list_ids.ids)]
        return action

    def action_open_purchase_order(self):
        action = self.env.ref('purchase.purchase_form_action').read()[0]
        action['view_mode'] = 'form'
        action['views'] = [(False, 'form')]
        action['res_id'] = self.purchase_id.id
        return action
