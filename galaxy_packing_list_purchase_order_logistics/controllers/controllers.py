# -*- coding: utf-8 -*-
# from odoo import http


# class GalaxyPackingListPurchaseOrder(http.Controller):
#     @http.route('/galaxy_packing_list_purchase_order/galaxy_packing_list_purchase_order/', auth='public')
#     def index(self, **kw):
#         return "Hello, world"

#     @http.route('/galaxy_packing_list_purchase_order/galaxy_packing_list_purchase_order/objects/', auth='public')
#     def list(self, **kw):
#         return http.request.render('galaxy_packing_list_purchase_order.listing', {
#             'root': '/galaxy_packing_list_purchase_order/galaxy_packing_list_purchase_order',
#             'objects': http.request.env['galaxy_packing_list_purchase_order.galaxy_packing_list_purchase_order'].search([]),
#         })

#     @http.route('/galaxy_packing_list_purchase_order/galaxy_packing_list_purchase_order/objects/<model("galaxy_packing_list_purchase_order.galaxy_packing_list_purchase_order"):obj>/', auth='public')
#     def object(self, obj, **kw):
#         return http.request.render('galaxy_packing_list_purchase_order.object', {
#             'object': obj
#         })
