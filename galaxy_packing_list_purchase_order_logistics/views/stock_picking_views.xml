<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="stock_picking_for_packing_list" model="ir.ui.view">
        <field name="name">stock_picking_for_packing_list</field>
        <field name="model">stock.picking</field>
        <field name="inherit_id" ref="stock.view_picking_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='partner_id']" position="after">
                <field name='packing_list_carton_count' invisible="1"/>
                <field name='packing_list_quantity' invisible="1"/>
            </xpath>

            <xpath expr="//div[hasclass('oe_button_box')]" position="inside">
                <button name="action_open_packing_list" type="object"
                        attrs="{'invisible':[('packing_list_quantity', '=', 0)]}"
                        class="oe_stat_button" icon="fa-th">
                    Packing List
                </button>

<!--                <button name="action_open_purchase_order" type="object"-->
<!--                        class="oe_stat_button" icon="fa-bookmark-o"-->
<!--                        attrs="{'invisible':[('picking_type_sequence_code', 'in', ('PICK', 'OUT'))]}">-->
<!--                    Purchase Order-->
<!--                </button>-->
            </xpath>
        </field>
    </record>


</odoo>
