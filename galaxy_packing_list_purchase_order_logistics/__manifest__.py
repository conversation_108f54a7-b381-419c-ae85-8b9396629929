# -*- coding: utf-8 -*-
{
    'name': "galaxy_packing_list_purchase_order",

    'summary': """
        Short (1 phrase/line) summary of the module's purpose, used as
        subtitle on modules listing or apps.openerp.com""",

    'description': """
        Long description of module's purpose
    """,

    'author': "<PERSON>o",
    'website': "http://www.yourcompany.com",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/14.0/odoo/addons/base/data/ir_module_category_data.xml
    # for the full list
    'category': 'Galaxy_ERP/Galaxy_ERP',
    'version': '0.1',


    # any module necessary for this one to work correctly
    'depends': ['base', 'galaxy_packing_list', 'galaxy_purchase_order', 'galaxy_logistics', 'stock', 'galaxy_goods_receipt', 'base_bid_management'],

    # always loaded
    'data': [
        # 'security/ir.model.access.csv',
        'views/stock_picking_views.xml',
    ],
    # only loaded in demonstration mode
    'demo': [
        'demo/demo.xml',
    ],
}
