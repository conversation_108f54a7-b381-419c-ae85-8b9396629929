# -*- coding: utf-8 -*-

import logging
import time
import pytz
from functools import reduce

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
from odoo.tools.profiler import profile

_logger = logging.getLogger(__name__)


class GalaxyDelivery(models.Model):
    _inherit = 'galaxy.delivery'

    odoo_delivery_order_id = fields.Many2one('stock.picking', string='Odoo Delivery Order', index=True)
    odoo_delivery_count = fields.Integer(compute='_compute_odoo_delivery_count', string='Odoo Delivery Count')

    @api.depends('odoo_delivery_order_id')
    def _compute_odoo_delivery_count(self):
        """
        计算odoo发货单数量
        """
        for item in self:
            item.odoo_delivery_count = len(item.odoo_delivery_order_id)

    def action_view_odoo_delivery_order(self):
        """
        查看odoo发货单
        """
        self.ensure_one()
        if not self.odoo_delivery_order_id:
            raise UserError(_('Odoo delivery order not exists'))
        action = self.env.ref('stock.stock_picking_action_picking_type').read()[0]
        action['view_mode'] = 'tree, form'
        action['res_id'] = self.odoo_delivery_order_id.id
        action['domain'] = [('id', '=', self.odoo_delivery_order_id.id)]
        action['context'] = {'contact_display': 'partner_address'}
        return action

    def update_pick_delivered_carton_ids(self):
        """
        更新拣货单的已出货箱，仅仅为了继承
        """
        customer_location_id = self.sudo().env.ref('stock.stock_location_customers').id
        ch_location = self.env['stock.location'].search([('name', '=', 'CH')], limit=1)
        now = fields.Datetime.now()
        for item in self.glot_ids:
            delivered_cartons = self.glot_line_ids.filtered(lambda m: m.is_delivered and m.carton_id in
                                                            (item.carton_ids)).carton_id
            # 更新点货单的箱子状态--已出货
            carton_name_list = [carton.name for carton in delivered_cartons]
            self.env['galaxy.box'].search([('name', 'in', carton_name_list)]).write({'status': 'delivered',
                                                                                     'location_id': customer_location_id,
                                                                                     'delivery_date': now})
            # 更新点货单的箱子状态--未出货
            not_delivered_cartons = self.glot_line_ids.filtered(lambda m: not m.is_delivered and m.carton_id in
                                                                (item.carton_ids)).carton_id
            carton_name_list = [carton.name for carton in not_delivered_cartons]
            self.env['galaxy.box'].search([('name', 'in', carton_name_list)]).write({'status': 'store',
                                                                                     'location_id': ch_location.id,
                                                                                     'delivery_date': None})

    def crate_odoo_delivery(self):
        self._crate_odoo_delivery()

    def _crate_odoo_delivery(self):
        """
        基于r2的已审核订单，创建odoo的原生发货单
        """
        time.sleep(5)
        self.ensure_one()
        # if self.state != 'audited':
        #     raise UserError(_('Only audited delivery can be created'))
        if self.odoo_delivery_order_id:
            raise UserError(_('Odoo delivery order already exists'))
        for box in self.glot_line_ids:
            if box.carton_status not in ('delivery', 'audited'):
                raise UserError(_('Only delivery or audited status can create odoo delivery order'))
        # 查找package
        galaxy_box = self.env['galaxy.box']
        for line in self.glot_line_ids:
            box_name = line.carton_id.name
            galaxy_box += self.env['galaxy.box'].search([('name', '=', box_name)])
        # 此处如果是按lot出货的---package会整个一起出货
        packages = galaxy_box.mapped('package_id')
        # 创建发货单
        picking = self.env['stock.picking'].create({
            'partner_id': self.customer_id.id,
            'picking_type_id': self.env.ref('stock.picking_type_out').id,
            'location_id': packages[0].location_id.id,
            'location_dest_id': self.env.ref('stock.stock_location_customers').id,
            'scheduled_date': fields.Datetime.now(),
            'origin': self.name,
            'state': 'draft'
        })
        self.odoo_delivery_order_id = picking.id
        # 根据package创建发货单行
        picking.write({'package_level_ids_details': [(0, 0, {
            'package_id': package.id,
            'company_id': self.env.company.id,
            'location_id': packages[0].location_id.id,
            'location_dest_id': self.env.ref('stock.stock_location_customers').id,
        }) for package in packages]})
        # sale_order_name = self.env['galaxy.gjp.json'].search([('gjp_number', '=', self.sale_order)], limit=1).name
        # sale_order_id = self.env['sale.order'].search([('name', '=', sale_order_name)]).id
        # lot_ids = packages.quant_ids.lot_id
        # order_line_dict = self.get_sale_order_line_by_product_id(lot_ids, sale_order_id)
        # move_vals_list = []
        # for sale_line_id, value in order_line_dict.items():
        #     temp_lot_ids, qty = value['lot_ids'], value['qty']
        #     product = temp_lot_ids[0].quant_ids.filtered(lambda m: m.quantity > 0).product_id
        #     move_vals = {
        #         'name': product.name,
        #         'product_id': product.id,
        #         'product_uom': product.uom_id.id,
        #         'product_uom_qty': qty,  # 设置需求数量
        #         'quantity_done': qty,
        #         'location_id': 93,
        #         'location_dest_id': picking.location_dest_id.id,
        #         'picking_id': picking.id,
        #         'sale_line_id': sale_line_id,
        #         'lot_ids': [(6, 0, temp_lot_ids.ids)],
        #         'state': 'draft',
        #     }
        #     move_vals_list.append(move_vals)
        # self.env['stock.move'].create(move_vals_list)
        picking.action_confirm()
        picking.action_assign()
        picking.move_lines.write({
            'picking_type_id': picking.picking_type_id.id,
        })
        # move_line_ids = picking.move_line_ids_without_package
        # move_line_ids.write({'qty_done': 1})
        # 写入销售单的发货数量
        # for sale_line_id, value in order_line_dict.items():
        #     temp_lot_ids, qty = value['lot_ids'], value['qty']
        #     sale_order_line = self.env['sale.order.line'].search([('id', '=', sale_line_id)])
        #     sale_order_line.qty_delivered += qty
        picking.package_level_ids_details.move_line_ids.write({'qty_done': 1})
        picking.button_validate()

    def get_sale_order_line_by_product_id(self, lot_ids, sale_order_id):
        """
        根据产品id，获取销售订单行
        :param product_id:
        :return:
        """
        order_line_dict = {}
        for lot in lot_ids:
            won_detail_id = lot.won_order_lot_line_id.id
            glot_number = lot.glot_number
            if sale_order_id:
                sale_order_line = self.env['sale.order.line'].search([('order_id', '=', sale_order_id), ('won_order_lot_line_id','=', won_detail_id)])
            else:
                sale_order_line = self.env['sale.order.line'].search([('won_order_lot_line_id', '=', won_detail_id)])
            if not sale_order_line:
                # 如果没有找到对应的销售订单行，则根据Glot查找
                if sale_order_id:
                    sale_order_line = self.env['sale.order.line'].search([('order_id', '=', sale_order_id), ('glot_number', '=', glot_number)])
                else:
                    sale_order_line = self.env['sale.order.line'].search([('glot_number', '=', glot_number)])
                if len(sale_order_line) > 1:
                    # glot一样，需要安装产品id来区分
                    product_id = lot.quant_ids.filtered(lambda m: m.quantity > 0).product_id.id
                    sale_order_line = sale_order_line.filtered(lambda m: m.product_id.id == product_id and m.product_uom_qty > m.qty_delivered)
                    if len(sale_order_line) > 1:
                        # 如果还是多个，则抛出异常
                        for line in sale_order_line:
                            if not order_line_dict.get(line.id):
                                sale_order_line = line
                                break
                            else:
                                if order_line_dict.get(line.id)['qty'] >= line.product_uom_qty:
                                    continue
                                else:
                                    sale_order_line = line
                                    break
                    else:
                        if not sale_order_line:
                            continue
                        sale_order_line = sale_order_line[0]
                        sale_order_line.won_order_lot_line_id = lot.won_order_lot_line_id.id
                else:
                    if not sale_order_line:
                        continue
                    sale_order_line = sale_order_line[0]
                    sale_order_line.won_order_lot_line_id = lot.won_order_lot_line_id.id
            if sale_order_line:
                if not order_line_dict.get(sale_order_line.id):
                    order_line_dict[sale_order_line.id] = {'lot_ids': lot, 'qty': 1}
                else:
                    order_line_dict[sale_order_line.id]['qty'] += 1
                    order_line_dict[sale_order_line.id]['lot_ids'] += lot
        return order_line_dict

    def develop_fix_odoo_delivery(self):
        """
        开发用，修复odoo发货单
        """
        dos = self.env['galaxy.delivery'].search([('id', '>=', 48876)])
        # dos = self.env['galaxy.delivery'].search([('id', '=', 48880)])
        for do in dos:
            packages = do.odoo_delivery_order_id.package_level_ids_details.package_id
            sale_order_name = self.env['galaxy.gjp.json'].search([('gjp_number', '=', do.sale_order)], limit=1).name
            sale_order_id = self.env['sale.order'].search([('name', '=', sale_order_name)]).id
            lot_ids = packages.quant_ids.lot_id
            order_line_dict = self.get_sale_order_line_by_product_id(lot_ids, sale_order_id)
            # 写入销售单的发货数量
            for sale_line_id, value in order_line_dict.items():
                temp_lot_ids, qty = value['lot_ids'], value['qty']
                sale_order_line = self.env['sale.order.line'].search([('id', '=', sale_line_id)])
                sale_order_line.qty_delivered += qty

    def clear_odoo_sale_order_delivery_qty(self):
        """
        清除odoo发货单的发货数量
        """
        # 48876 从4月1号开始的发货单
        for do in self.env['galaxy.delivery'].search([('id', '>=', 48876)]):
            sale_order_name = self.env['galaxy.gjp.json'].search([('gjp_number', '=', do.sale_order)], limit=1).name
            sale_order = self.env['sale.order'].search([('name', '=', sale_order_name)])
            sale_order.order_line.write({'qty_delivered': 0})

    def recreate_odoo_sale_order_delivery(self):
        """
        创建失败的odoo发货单重新创建
        """
        # 48876 从4月1号开始的发货单
        for do in self.env['galaxy.delivery'].search([('id', '>=', 48876)]):
            if not do.odoo_delivery_order_id:
                print(do.name)
                do._crate_odoo_delivery()
            else:
                picking = do.odoo_delivery_order_id
                if picking.state == 'assigned':
                    print(do.name)
                    picking.package_level_ids_details.move_line_ids.write({'qty_done': 1})
                    picking.button_validate()

    def develop_fix_error_do_move_line(self):
        """
        开发用，修复odoo发货单的move_line
        """
        # 48876 从4月1号开始的发货单
        for do in self.env['galaxy.delivery'].search([('id', '>=', 48876)]):
            if do.odoo_delivery_order_id:
                rec = do.odoo_delivery_order_id
                rec.move_lines.write({
                    'picking_type_id': rec.picking_type_id.id,
                    })
