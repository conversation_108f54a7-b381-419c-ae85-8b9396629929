# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* galaxy_goods_receipt_r2
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0+e-20230306\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-21 03:15+0000\n"
"PO-Revision-Date: 2025-03-21 03:15+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: galaxy_goods_receipt_r2
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_r2.view_picking_method_carton_wizard_form
msgid ""
"<strong>Total</strong>\n"
"                        <br/>"
msgstr ""
"<strong>總數量</strong>\n"
"                        <br/>"

#. module: galaxy_goods_receipt_r2
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_galaxy_receipt_r2_stock_transfer_out_line_wizard__box_id
msgid "Box"
msgstr "箱"

#. module: galaxy_goods_receipt_r2
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_galaxy_receipt_r2_stock_transfer_out_line_wizard__carton
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_galaxy_receipt_r2_stock_transfer_out_wizard__carton
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_galaxy_receipt_r2_stock_transfer_out_wizard__carton_input
msgid "Box Number"
msgstr "箱號"

#. module: galaxy_goods_receipt_r2
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_r2.view_picking_method_carton_wizard_form
msgid "Cancel"
msgstr "取消"

#. module: galaxy_goods_receipt_r2
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_r2.view_picking_method_carton_wizard_form
msgid "Cartons"
msgstr "箱號"

#. module: galaxy_goods_receipt_r2
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_r2.view_stock_count_confirm_r2_carton_wizard_form
msgid "Close"
msgstr "關閉"

#. module: galaxy_goods_receipt_r2
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_r2.view_stock_count_confirm_r2_carton_wizard_form
msgid "Confirm"
msgstr "確認"

#. module: galaxy_goods_receipt_r2
#: model:ir.actions.act_window,name:galaxy_goods_receipt_r2.action_stock_count_confirm_r2_carton_wizard
msgid "Confirm Create R2 Carton"
msgstr "確認創建R2箱子"

#. module: galaxy_goods_receipt_r2
#: model:ir.model,name:galaxy_goods_receipt_r2.model_res_partner
msgid "Contact"
msgstr "聯繫人"

#. module: galaxy_goods_receipt_r2
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_galaxy_receipt_r2_stock_transfer_out_line_wizard__create_uid
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_galaxy_receipt_r2_stock_transfer_out_wizard__create_uid
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_stock_count_confirm_r2_carton_wizard__create_uid
msgid "Created by"
msgstr "創建人"

#. module: galaxy_goods_receipt_r2
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_galaxy_receipt_r2_stock_transfer_out_line_wizard__create_date
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_galaxy_receipt_r2_stock_transfer_out_wizard__create_date
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_stock_count_confirm_r2_carton_wizard__create_date
msgid "Created on"
msgstr "創建時間"

#. module: galaxy_goods_receipt_r2
#: code:addons/galaxy_goods_receipt_r2/models/stock_count_order.py:0
#, python-format
msgid "DD Pallet not found"
msgstr "未找到DD托盤"

#. module: galaxy_goods_receipt_r2
#: code:addons/galaxy_goods_receipt_r2/models/stock_count_order.py:0
#, python-format
msgid "Default DD Customer not found"
msgstr "未找到預設DD客戶"

#. module: galaxy_goods_receipt_r2
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_galaxy_receipt_r2_stock_transfer_out_wizard__location_dest_input
msgid "Destination Location"
msgstr "目標貨位"

#. module: galaxy_goods_receipt_r2
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_galaxy_delivery__display_name
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_galaxy_receipt_r2_stock_transfer_out_line_wizard__display_name
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_galaxy_receipt_r2_stock_transfer_out_wizard__display_name
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_galaxy_receipt_stock_transfer__display_name
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_galaxy_stock_count_order__display_name
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_purchase_order__display_name
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_res_partner__display_name
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_stock_count_confirm_r2_carton_wizard__display_name
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_stock_picking__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: galaxy_goods_receipt_r2
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_r2.view_picking_method_carton_wizard_form
msgid "Finish"
msgstr "完成"

#. module: galaxy_goods_receipt_r2
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_r2.view_picking_method_carton_wizard_form
msgid "Finish And Next"
msgstr "完成並繼續"

#. module: galaxy_goods_receipt_r2
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_r2.view_stock_count_confirm_r2_carton_wizard_form
msgid ""
"Following boxes has been created in R2. Existing boxes will not be created "
"again."
msgstr "以下箱子已經在R2中創建。現有的箱子不會再次創建。"


#. module: galaxy_goods_receipt_r2
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_galaxy_receipt_r2_stock_transfer_out_line_wizard__glot_number
msgid "GLot"
msgstr "GLot"

#. module: galaxy_goods_receipt_r2
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_stock_count_confirm_r2_carton_wizard__galaxy_box_ids
msgid "Galaxy Box"
msgstr "Galaxy箱子"

#. module: galaxy_goods_receipt_r2
#: model:ir.model,name:galaxy_goods_receipt_r2.model_galaxy_delivery
msgid "Galaxy Delivery"
msgstr "出庫單"

#. module: galaxy_goods_receipt_r2
#: model:ir.model,name:galaxy_goods_receipt_r2.model_galaxy_receipt_stock_transfer
msgid "Galaxy Receipt Stock Transfer"
msgstr "Galaxy收貨庫存轉移"

#. module: galaxy_goods_receipt_r2
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_galaxy_delivery__id
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_galaxy_receipt_r2_stock_transfer_out_line_wizard__id
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_galaxy_receipt_r2_stock_transfer_out_wizard__id
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_galaxy_receipt_stock_transfer__id
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_galaxy_stock_count_order__id
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_purchase_order__id
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_res_partner__id
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_stock_count_confirm_r2_carton_wizard__id
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_stock_picking__id
msgid "ID"
msgstr "編號"

#. module: galaxy_goods_receipt_r2
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_galaxy_delivery____last_update
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_galaxy_receipt_r2_stock_transfer_out_line_wizard____last_update
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_galaxy_receipt_r2_stock_transfer_out_wizard____last_update
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_galaxy_receipt_stock_transfer____last_update
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_galaxy_stock_count_order____last_update
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_purchase_order____last_update
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_res_partner____last_update
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_stock_count_confirm_r2_carton_wizard____last_update
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_stock_picking____last_update
msgid "Last Modified on"
msgstr "修改時間"

#. module: galaxy_goods_receipt_r2
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_galaxy_receipt_r2_stock_transfer_out_line_wizard__write_uid
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_galaxy_receipt_r2_stock_transfer_out_wizard__write_uid
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_stock_count_confirm_r2_carton_wizard__write_uid
msgid "Last Updated by"
msgstr "最後更新人"

#. module: galaxy_goods_receipt_r2
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_galaxy_receipt_r2_stock_transfer_out_line_wizard__write_date
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_galaxy_receipt_r2_stock_transfer_out_wizard__write_date
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_stock_count_confirm_r2_carton_wizard__write_date
msgid "Last Updated on"
msgstr "最後更新時間"

#. module: galaxy_goods_receipt_r2
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_galaxy_receipt_r2_stock_transfer_out_line_wizard__location_dest_id
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_galaxy_receipt_r2_stock_transfer_out_wizard__location_dest
msgid "Location Dest"
msgstr "目標貨位"

#. module: galaxy_goods_receipt_r2
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_galaxy_receipt_r2_stock_transfer_out_line_wizard__location_src_id
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_galaxy_receipt_r2_stock_transfer_out_wizard__location_src
msgid "Location Src"
msgstr "源貨位"

#. module: galaxy_goods_receipt_r2
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_galaxy_receipt_r2_stock_transfer_out_line_wizard__lot_number
msgid "Lot No"
msgstr "Lot No"

#. module: galaxy_goods_receipt_r2
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_galaxy_receipt_r2_stock_transfer_out_wizard__error_message
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_galaxy_receipt_r2_stock_transfer_out_wizard__message
msgid "Message"
msgstr "信息"

#. module: galaxy_goods_receipt_r2
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_galaxy_receipt_r2_stock_transfer_out_line_wizard__is_over_delivery
msgid "Over Delivery"
msgstr "多到"

#. module: galaxy_goods_receipt_r2
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_r2.view_picking_method_carton_wizard_form
msgid "Picking Lines"
msgstr "點貨行"

#. module: galaxy_goods_receipt_r2
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_r2.view_stock_count_confirm_r2_carton_wizard_form
msgid "Please confirm if you want to continue processing."
msgstr "請確認是否繼續處理。"

#. module: galaxy_goods_receipt_r2
#: code:addons/galaxy_goods_receipt_r2/models/stock_picking.py:0
#, python-format
msgid "Please putaway all boxes"
msgstr "請上架所有箱子"

#. module: galaxy_goods_receipt_r2
#: model:ir.model,name:galaxy_goods_receipt_r2.model_purchase_order
msgid "Purchase Order"
msgstr "採購訂單"

#. module: galaxy_goods_receipt_r2
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_galaxy_receipt_r2_stock_transfer_out_line_wizard__qty
msgid "Quantity"
msgstr "數量"

#. module: galaxy_goods_receipt_r2
#: model:ir.model,name:galaxy_goods_receipt_r2.model_galaxy_receipt_r2_stock_transfer_out_wizard
msgid "Quick Stock Transfer Out Wizard"
msgstr "快速調撥"

#. module: galaxy_goods_receipt_r2
#. openerp-web
#: code:addons/galaxy_goods_receipt_r2/static/src/xml/picking_tree.xml:0
#, python-format
msgid "Quick Transfer"
msgstr "快速調撥"

#. module: galaxy_goods_receipt_r2
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_purchase_order__is_inventory_in_r2
msgid "R2收货"
msgstr "R2收貨"

#. module: galaxy_goods_receipt_r2
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_galaxy_receipt_r2_stock_transfer_out_wizard__location_src_input
msgid "Source Location"
msgstr "源貨位"

#. module: galaxy_goods_receipt_r2
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_stock_count_confirm_r2_carton_wizard__count_id
msgid "Stock Count Order"
msgstr "點貨單"

#. module: galaxy_goods_receipt_r2
#: model:ir.model,name:galaxy_goods_receipt_r2.model_stock_count_confirm_r2_carton_wizard
msgid "Stock Count R2 Carton Confirmation Wizard"
msgstr "確認完成"

#. module: galaxy_goods_receipt_r2
#: model:ir.model,name:galaxy_goods_receipt_r2.model_galaxy_receipt_r2_stock_transfer_out_line_wizard
msgid "Stock Transfer Out Wizard Lines"
msgstr "快速調撥行"

#. module: galaxy_goods_receipt_r2
#: code:addons/galaxy_goods_receipt_r2/models/stock_picking.py:0
#, python-format
msgid ""
"The number of R2 cartons does not match the number of boxes. Please check if"
" all boxes are synced to R2 system."
msgstr "R2箱號數量與箱子數量不匹配。請檢查所有箱子是否已同步到R2系統。"

#. module: galaxy_goods_receipt_r2
#: code:addons/galaxy_goods_receipt_r2/models/stock_picking.py:0
#, python-format
msgid ""
"The pallet location %s of carton %s is not found. Please check if all boxes "
"are synced to R2 system."
msgstr "托盤貨位 %s 的箱子 %s 未找到。請檢查所有箱子是否已同步到R2系統。"

#. module: galaxy_goods_receipt_r2
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_galaxy_receipt_r2_stock_transfer_out_wizard__total_count
msgid "Total Count"
msgstr "總數"

#. module: galaxy_goods_receipt_r2
#: model:ir.model,name:galaxy_goods_receipt_r2.model_stock_picking
msgid "Transfer"
msgstr "調撥"

#. module: galaxy_goods_receipt_r2
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_galaxy_receipt_r2_stock_transfer_out_wizard__line_ids
msgid "Transfer Lines"
msgstr "調撥行"

#. module: galaxy_goods_receipt_r2
#: model:ir.actions.act_window,name:galaxy_goods_receipt_r2.action_picking_method_carton_wizard
msgid "Transfer Out R2"
msgstr "出庫R2"

#. module: galaxy_goods_receipt_r2
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_galaxy_receipt_r2_stock_transfer_out_line_wizard__wizard_id
msgid "Wizard Reference"
msgstr "調撥單"

#. module: galaxy_goods_receipt_r2
#: model:ir.model,name:galaxy_goods_receipt_r2.model_galaxy_stock_count_order
msgid "galaxy.stock.count.order"
msgstr "點貨單"

#. module: galaxy_goods_receipt_r2
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_res_partner__is_inventory_in_r2
#: model:ir.model.fields,field_description:galaxy_goods_receipt_r2.field_res_users__is_inventory_in_r2
msgid "是否使用R2收货"
msgstr "是否使用R2收貨"

#. module: galaxy_goods_receipt_r2
#: model:ir.model.fields,help:galaxy_goods_receipt_r2.field_purchase_order__is_inventory_in_r2
msgid "是否使用R2收货流程"
msgstr "是否使用R2收貨流程"

#. module: galaxy_goods_receipt_r2
#: model:ir.model.fields,help:galaxy_goods_receipt_r2.field_res_partner__is_inventory_in_r2
#: model:ir.model.fields,help:galaxy_goods_receipt_r2.field_res_users__is_inventory_in_r2
msgid "标记该供应商是否使用R2收货流程"
msgstr "標記該供應商是否使用R2收貨流程"

#. module: galaxy_goods_receipt_r2
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_r2.view_picking_method_carton_wizard_form
msgid "目標貨位"
msgstr "目標貨位"

#. module: galaxy_goods_receipt_r2
#: code:addons/galaxy_goods_receipt_r2/wizards/stock_transfer_out_wizard.py:0
#, python-format
msgid "目的貨位不能與源貨位相同"
msgstr "目的貨位不能與源貨位相同"

#. module: galaxy_goods_receipt_r2
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_r2.view_picking_method_carton_wizard_form
msgid "確認轉移箱子嗎？"
msgstr "確認轉移箱子嗎？"

#. module: galaxy_goods_receipt_r2
#: model_terms:ir.ui.view,arch_db:galaxy_goods_receipt_r2.view_picking_method_carton_wizard_form
msgid "箱號"
msgstr "箱號"

#. module: galaxy_goods_receipt_r2
#: code:addons/galaxy_goods_receipt_r2/wizards/stock_transfer_out_wizard.py:0
#, python-format
msgid "箱號 %s 不存在"
msgstr "箱號 %s 不存在"

#. module: galaxy_goods_receipt_r2
#: code:addons/galaxy_goods_receipt_r2/wizards/stock_transfer_out_wizard.py:0
#, python-format
msgid "箱號 %s 已存在"
msgstr "箱號 %s 已存在"

#. module: galaxy_goods_receipt_r2
#: code:addons/galaxy_goods_receipt_r2/wizards/stock_transfer_out_wizard.py:0
#, python-format
msgid "箱號 %s 狀態必須為庫存才能進行轉移"
msgstr "箱號 %s 狀態必須為庫存才能進行轉移"

#. module: galaxy_goods_receipt_r2
#: code:addons/galaxy_goods_receipt_r2/wizards/stock_transfer_out_wizard.py:0
#, python-format
msgid "箱號 %s 的超收屬性與目的貨位不一致，不能進行轉移"
msgstr "箱號 %s 的超收屬性與目的貨位不一致，不能進行轉移"

#. module: galaxy_goods_receipt_r2
#: code:addons/galaxy_goods_receipt_r2/wizards/stock_transfer_out_wizard.py:0
#, python-format
msgid "貨位 %s 不存在"
msgstr "貨位 %s 不存在"
