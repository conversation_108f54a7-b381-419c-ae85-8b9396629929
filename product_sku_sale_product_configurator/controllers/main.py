# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo import http
from odoo.http import request
from odoo.exceptions import UserError, ValidationError
from odoo.addons.base_galaxy.models.public_func import parse_int


class GalaxyProductConfiguratorController(http.Controller):

    @http.route(['/galaxy_product_configurator/configure'], type='json', auth="user", methods=['POST'])
    def configure(self, product_template_id, pricelist_id, **kw):
        '''
            返回普通产品的sku配置视图
        '''
        add_qty = int(kw.get('add_qty', 1))
        product_template = request.env['product.template'].browse(int(product_template_id))
        pricelist = self._get_pricelist(pricelist_id)
        product_combination = False
        attribute_value_ids = set(kw.get('product_template_attribute_value_ids', []))
        attribute_value_ids |= set(kw.get('product_no_variant_attribute_value_ids', []))

        if attribute_value_ids:
            product_combination = request.env['product.template.attribute.value'].browse(attribute_value_ids)
        else:
            # 如果是新创建sku，赋值unknown
            attribute_value_ids = set(product_template.attribute_line_ids.mapped('product_template_value_ids').with_context({
                'lang': 'en_US'
            }).filtered(lambda s: s.name.lower() == 'unknown').ids)
            product_combination = request.env['product.template.attribute.value'].browse(attribute_value_ids)

        if pricelist:
            product_template = product_template.with_context(pricelist=pricelist.id, partner=request.env.user.partner_id)

        return request.env['ir.ui.view']._render_template("product_sku_sale_product_configurator.configure", {
            'product': product_template,
            'pricelist': pricelist,
            'add_qty': add_qty,
            'product_combination': product_combination
        })

    @http.route(['/galaxy_product_configurator/vendor_sku_configure'], type='json', auth="user", methods=['POST'])
    def vendor_sku_configure(self, product_template_id, pricelist_id, galaxy_vendor_sku_id, **kw):
        '''
            返回供应商sku对比页面的sku配置视图
            选择产品以后这个函数才会执行
        '''
        add_qty = int(kw.get('add_qty', 1))
        product_template = request.env['product.template'].browse(int(product_template_id))
        pricelist = self._get_pricelist(pricelist_id)
        product_combination = False
        attribute_value_ids = set(kw.get('product_template_attribute_value_ids', []))
        attribute_value_ids |= set(kw.get('product_no_variant_attribute_value_ids', []))
        #指定变体ID
        if attribute_value_ids:
            product_combination = request.env['product.template.attribute.value'].browse(attribute_value_ids)
        '''
        #这边提取模板信息,做sku属性信息智能推荐
        check_attr_value = {'颜色':'','内存':''}
        galaxy_vendor_sku=request.env['galaxy.vendor.sku'].browse(int(galaxy_vendor_sku_id))
        
        if galaxy_vendor_sku.vendor_sku:
            vendor_sku = galaxy_vendor_sku.vendor_sku.split(' ')
        else:
            vendor_sku=[]
        
        for item in vendor_sku:
            if item.find('GB') != -1:
                check_attr_value['内存'] = str(parse_int(item))
            if item in ['GLD']:
                check_attr_value['颜色'] = item
        if not galaxy_vendor_sku.internal_sku:
            attr_value = check_attr_value
        else:
            attr_value = {}
        
        product_template = product_template.with_context(
            pricelist=pricelist.id, 
            partner=request.env.user.partner_id,
            prefer_attr_value = attr_value,
        )
        
       '''
        galaxy_vendor_sku = None
        return request.env['ir.ui.view']._render_template(
            "product_sku_sale_product_configurator.vendor_sku_configure", {
                'galaxy_vendor_sku': galaxy_vendor_sku,
                'product': product_template,
                'pricelist': pricelist,
                'add_qty': add_qty,
                'product_combination': product_combination
            })

    def _get_pricelist(self, pricelist_id, pricelist_fallback=False):
        return request.env['product.pricelist'].browse(int(pricelist_id or 0))

    @http.route(['/galaxy_product_configurator/save_configure'], type='json', auth="user", methods=['POST'])
    def _save_configure(self, product_template_id, attribute_value_ids, **kw):
        item = request.env['product.template'].browse(int(product_template_id))
        if item:
            #返回的是创建或者是已有的变体的记录 ID
            rec_id = item.create_product_variant('[' + str(attribute_value_ids) + ']')
            request.env.cr.commit
            return rec_id
        else:
            return 0
