<odoo>
    <data>
        <!-- for sku config-->
        <template id="configure" name="Configure">
            <div class="js_product main_product">

                <t t-set="combination" t-value="product_combination if product_combination else product._get_first_possible_combination()"/>
                <t t-set="combination_info" t-value="product._get_combination_info(combination, add_qty=add_qty or 1, pricelist=pricelist)"/>
                <t t-set="product_variant" t-value="product.env['product.product'].browse(combination_info['product_id'])"/>

                <input type="hidden" class="product_template_id" t-att-value="product.id"/>
                <input type="hidden" class="product_id" t-attf-name="product_id" t-att-value="product_variant.id"/>
                <div class="col-lg-12 text-center mt-5">
                    <t t-if="product._is_add_to_cart_possible()">
                        <div class="col-lg-5 d-inline-block text-left">
                            <t t-if="combination" t-call="product_sku.variants">
                                <t t-set="parent_combination" t-value="None"/>
                            </t>
                            
                            <p class="css_not_available_msg alert alert-warning">This combination does not exist</p>
                        </div>
                        <div class="col-lg-1 d-inline-block"></div>
                        <div class="col-lg-5 d-inline-block align-top text-left">
                            <img t-if="product_variant" t-att-src="'/web/image/product.product/%s/image_1024' % product_variant.id" class="d-block product_detail_img" alt="Product Image"/>
                            <img t-else="" t-att-src="'/web/image/product.template/%s/image_1024' % product.id" class="d-block product_detail_img" alt="Product Image"/>
                        </div>
                    </t>
                    <t t-else="">
                        <div class="col-lg-5 d-inline-block text-left">
                            <p class="alert alert-warning">There is no suitable combination of this product</p>
                        </div>
                    </t>
                </div>
            </div>
        </template>
        
        <!-- for vedor sku config-->
        <template id="vendor_sku_configure" name="vendor_sku_configure">
            <div class="js_product main_product">
                <t t-set="combination" t-value="product_combination if product_combination else product._get_first_possible_combination()"/>
                <t t-set="combination_info" t-value="product._get_combination_info(combination, add_qty=add_qty or 1, pricelist=pricelist)"/>
                <t t-set="product_variant" t-value="product.env['product.product'].browse(combination_info['product_id'])"/>
               
                <input type="hidden" class="product_template_id" t-att-value="product.id"/>
                <input type="hidden" class="product_id" t-attf-name="product_id" t-att-value="product_variant.id"/>
                
                <div class="col-lg-12 text-center mt-5">
                    <t t-if="product._is_add_to_cart_possible()">
                        <div class="col-lg-5 d-inline-block text-left">
                            <t t-if="combination" t-call="product_sku.variants">
                                <t t-set="parent_combination" t-value="None"/>
                            </t>
                            
                            <p class="css_not_available_msg alert alert-warning">This combination does not exist</p>
                        </div>
                        <div class="col-lg-1 d-inline-block"></div>
                        <div class="col-lg-5 d-inline-block align-top text-left">
                            <img t-if="product_variant" t-att-src="'/web/image/product.product/%s/image_1024' % product_variant.id" class="d-block product_detail_img" alt="Product Image"/>
                            <img t-else="" t-att-src="'/web/image/product.template/%s/image_1024' % product.id" class="d-block product_detail_img" alt="Product Image"/>
                        </div>
                    </t>
                    <t t-else="">
                        <div class="col-lg-5 d-inline-block text-left">
                            <p class="alert alert-warning">There is no suitable combination of this product</p>
                        </div>
                    </t>
                </div>
            </div>
        </template>
        
    </data>
</odoo>