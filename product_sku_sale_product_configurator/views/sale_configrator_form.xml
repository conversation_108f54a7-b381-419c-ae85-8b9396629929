<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        <!--需要关闭这个视图，避免隐藏product product字段-->
        <record id="sale_product_configurator.sale_order_view_form" model="ir.ui.view">
            <field name="active">False</field>
        </record>


        <!--普通产品SKU Form-->
    
        <record id="galaxy_product_configurator_view_form" model="ir.ui.view">
            <field name="name">galaxy_product_configurator.product.configurator.view.form</field>
            <field name="model">sale.product.configurator</field>
            <field name="arch" type="xml">
                <form js_class="galaxy_product_configurator_form">
                    <group>
                        
                        <field name="product_template_id" options="{'no_create': True,'no_edit': True,}" class="oe_product_configurator_product_template_id" />
                        <field name="product_template_attribute_value_ids" invisible="1">
                            <tree limit="10000"/>
                        </field>
                        <field name="product_custom_attribute_value_ids" invisible="1" widget="one2many" >
                            <tree limit="10000">
                                <field name="custom_product_template_attribute_value_id"/>
                                <field name="custom_value"/>
                            </tree>
                        </field>
                        <field name="product_no_variant_attribute_value_ids" invisible="1">
                            <tree limit="10000"/>
                        </field>
                    </group>
                    <footer>
                        <button string="Save" class="btn-primary o_sale_product_configurator_add" special="add"/>
                        <button string="Cancel" class="btn-secondary" special="cancel"/>
                    </footer>
                </form>
            </field>
        </record>
        <!--普通产品sku创建 下面的动作是在JS里面调用-->
        
        <record id="galaxy_product_configurator_action" model="ir.actions.act_window">
            <field name="name">Product configration</field>
            <field name="res_model">sale.product.configurator</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
            <field name="view_id" ref="product_sku_sale_product_configurator.galaxy_product_configurator_view_form"/>
        </record>
        
        <record id="product_variant_action" model="ir.actions.act_window">
                <field name="name">Product Variants</field>
                <field name="res_model">product.product</field>
                <field name="view_mode">form</field>
                <field name="view_id" ref="product.product_normal_form_view"/>
                <field name="target">new</field>
        </record>
    </data>
</odoo>