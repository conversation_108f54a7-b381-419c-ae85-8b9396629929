<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--普通产品的sku list视图-->
    <record id="galaxy_view_create_sku_tree" model="ir.ui.view">
        <field name="name">galaxy.view.create.sku.tree</field>
        <field name="model">product.product</field>
        <field eval="0" name="priority"/>
        <field name="arch" type="xml">
            <!--decoration-danger="is_complete == 'N'"-->
            <!--js_class="galaxy_button_tree" 设置打开Sku配置视图-->
            <tree  sample="1" js_class="galaxy_button_tree" default_order="id desc" string="SKU List">
                <field name="name" context="{'disable_open':True}" optional="hide"/>
                <field name="sku_full_name"/>
                <field name="spu_model_name"/>
                <field name="product_tmpl_id" invisible="1"/>
                <field name="product_template_attribute_value_ids1" widget="many2many_tags" groups="product.group_product_variant" readonly="1" />
                <field name="product_template_attribute_value_ids" invisible="1" groups="product.group_product_variant" readonly="1" />
                <field name="is_complete" invisible="1"/>
                <field name="create_date"/>
                <field name="default_code" optional="hide"/>
                <!--<button name="open_detail" type="object" class="oe_highlight" string="详情"/>-->
            </tree>
        </field>
    </record>
</odoo>