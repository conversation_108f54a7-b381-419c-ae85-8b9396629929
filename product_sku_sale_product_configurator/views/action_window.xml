<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record model="ir.actions.act_window" id="action_window_setup_SKU">
      <field name="name">SKU</field>
      <!--注意，domain注释并不会在odoo系统删除，需要手动去action里面移除domain-->
      <!--<field name="domain">[('attribute_line_ids','!=',False)]</field>-->
      <field name="res_model">product.product</field>
      <field name="view_mode">tree,form,kanban</field>
      <field name="search_view_id" ref="product_sku.galaxy_product_product_search_view"/>
      <field name="view_ids" 
                   eval="[(5, 0, 0),
                          (0, 0, {'view_mode': 'tree', 'view_id': ref('galaxy_view_create_sku_tree')}),
                          (0, 0, {'view_mode': 'form', 'view_id': ref('product.product_normal_form_view')}), 
                          (0, 0, {'view_mode': 'kanban'})]"/>
    </record>
    
    <record model="ir.actions.act_window" id="action_window_setup_SKU_form">
      <field name="name">SKU</field>
      <!--注意，domain注释并不会在odoo系统删除，需要手动去action里面移除domain-->
      <!--<field name="domain">[('attribute_line_ids','!=',False)]</field>-->
      <field name="res_model">product.product</field>
      <field name="target">main</field>
      <field name="view_mode">form,tree,kanban</field>
      <field name="search_view_id" ref="product_sku.galaxy_product_product_search_view"/>
      <field name="view_ids" 
                   eval="[(5, 0, 0),
                          
                          (0, 0, {'view_mode': 'form', 'view_id': ref('product.product_normal_form_view')}), 
                          (0, 0, {'view_mode': 'kanban'})]"/>
    </record>


</odoo>