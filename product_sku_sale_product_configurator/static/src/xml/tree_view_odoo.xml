<?xml version="1.0" encoding="UTF-8"?>

<templates>
    <t t-extend="ListView.buttons" t-name="galaxy.tree.buttons">
        <t t-jquery="button.o_list_button_add" t-operation="replace">
            <button type="button" class="btn btn-primary o_button_galaxy_tree">
                Create
            </button>
            <button type="button" class="btn btn-secondary o_button_galaxy_tree_spu">
                Create From SPU
            </button>
        </t>
    </t>
   
    <t t-extend="ListView.buttons" t-name="galaxy.product_tree.sku.buttons">
        <t t-jquery="button.o_list_button_add" t-operation="replace">
            <button type="button" class="btn btn-primary o_button_galaxy_create_sku_tree">
                Create
            </button>
        </t>
    </t>
</templates>
