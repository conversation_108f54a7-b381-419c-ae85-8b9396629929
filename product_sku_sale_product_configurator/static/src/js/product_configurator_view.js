odoo.define('galaxy_product_configurator.ProductConfiguratorFormView', function (require) {
"use strict";

var ProductConfiguratorFormController = require('sale_product_configurator.ProductConfiguratorFormController');
var ProductConfiguratorFormRenderer = require('sale_product_configurator.ProductConfiguratorFormRenderer');
var FormView = require('web.FormView');
var viewRegistry = require('web.view_registry');
var OptionalProductsModal = require('sale_product_configurator.OptionalProductsModal');
var core = require('web.core');
var _t = core._t;
/*
  因为要用自己的模板，需要覆盖这个系统方法
*/
var GalaxyProductConfiguratorFormController=ProductConfiguratorFormController.extend({
    
    //从后台指定的template模板，提取配置产品的模板
   _configureProduct: function (productTemplateId) {
        var self = this;
        var initialProduct = this.initialState.data.product_template_id;
        var changed = initialProduct && initialProduct.data.id !== productTemplateId;
        var data = this.renderer.state.data;
        return this._rpc({
            route: '/galaxy_product_configurator/configure',
            params: {
                product_template_id: productTemplateId,
                pricelist_id: this.renderer.pricelistId,
                add_qty: data.quantity,
                product_template_attribute_value_ids: changed ? [] : this._getAttributeValueIds(
                    data.product_template_attribute_value_ids
                ),
                product_no_variant_attribute_value_ids: changed ? [] : this._getAttributeValueIds(
                    data.product_no_variant_attribute_value_ids
                )
            }
        }).then(function (configurator) {
            self.renderer.configuratorHtml = configurator;
        });
    },
    /*
      just for overwrite so that the window will not close if user didn't create new sku product 
    
    */
    _addProducts: function (products) {
        this.do_action({type: 'ir.actions.act_window_close', infos: {
            mainProduct: products[0],
            options: products.slice(1)
        }});
        
    },
    
     /*
      overwrite for some additional action need to be added
    */
    
    _handleAdd: function () {
        var self = this;
        var $modal = this.$el;
        var productSelector = [
            'input[type="hidden"][name="product_id"]',
            'input[type="radio"][name="product_id"]:checked'
        ];
        return new Promise(function(resolve, reject) {
            var productId = parseInt($modal.find(productSelector.join(', ')).first().val(), 10);
            var productTemplateId = $modal.find('.product_template_id').val();
            var galaxy_vendor_sku_id = self.initialState.context.default_galaxy_vendor_sku_id;
            self.renderer.selectOrCreateProduct(
                $modal,
                productId,
                productTemplateId,
                false
            ).then(function (productId) {
                $modal.find(productSelector.join(', ')).val(productId);
                //console.log(galaxy_vendor_sku_id);
                //console.log(productId);
                self.reload();
                var variantValues = self
                    .renderer
                    .getSelectedVariantValues($modal.find('.js_product'));

                var productCustomVariantValues = self
                    .renderer
                    .getCustomVariantValues($modal.find('.js_product'));

                var noVariantAttributeValues = self
                    .renderer
                    .getNoVariantAttributeValues($modal.find('.js_product'));

                self.rootProduct = {
                    product_id: productId,
                    product_template_id: parseInt(productTemplateId),
                    quantity: parseFloat($modal.find('input[name="add_qty"]').val() || 1),
                    variant_values: variantValues,
                    product_custom_attribute_values: productCustomVariantValues,
                    no_variant_attribute_values: noVariantAttributeValues
                };
                
                if (self.renderer.state.context.configuratorMode === 'edit') {
                    // edit mode only takes care of main product
                    self._onAddRootProductOnly();
                    return;
                }
                self.optionalProductsModal = new OptionalProductsModal($('body'), {
                    rootProduct: self.rootProduct,
                    pricelistId: self.renderer.pricelistId,
                    okButtonText: _t('Confirm'),
                    cancelButtonText: _t('Back'),
                    title: _t('Configure'),
                    context: self.initialState.context,
                    previousModalHeight: self.$el.closest('.modal-content').height()
                }).open();

                self.optionalProductsModal.on('options_empty', null,
                    // no optional products found for this product, only add the root product
                    self._onAddRootProductOnly.bind(self));

                self.optionalProductsModal.on('update_quantity', null,
                    self._onOptionsUpdateQuantity.bind(self));

                self.optionalProductsModal.on('confirm', null,
                    self._onModalConfirm.bind(self));

                self.optionalProductsModal.on('closed', null,
                    self._onModalClose.bind(self));
                resolve(self.rootProduct);
            });
        });
        
    },
    
    
    _onCreate(){
            var self=this;
            console.log('sku')
            //rootProduct
            this.do_action('product_sku_sale_product_configurator.galaxy_product_configurator_action', {
                additional_context: {
                    //下面一句代码应该是可以移除的，还未找到方法，应该是耦合了vendor sku的一些属性
                    default_galaxy_vendor_sku_id:0,
                    //default_product_template_id:rootProduct.product_template_id,
                },
                on_close: function (result){
                    console.log('reload');
                    //self.$el.trigger('change');
                    self.reload();
                }
            });
    },
    //按钮功能，这边只是拦截系统指定功能的按钮，比如cancel save add等。
    _onButtonClicked: async function (event) {
        if (event.stopPropagation) {
            event.stopPropagation();
        }
        var $modal = this.$el;
        var self=this;
        var attrs = event.data.attrs;
        if (attrs.special === 'cancel') {
            this._super.apply(this, arguments);
        } else {
            var productTemplateId = $modal.find('.product_template_id').val();
            if(productTemplateId == undefined){
                alert('Plase select SPU firstly')
                return
            }
            
            if (attrs.class =='btn-primary o_sale_product_configurator_add') {        
                const a= await this._handleAdd();
                self.do_notify("保存SKU成功","");
                this.do_action({type: 'ir.actions.act_window_close', infos: {
                    mainProduct: self.rootProduct
                }});
            }
            if (attrs.class =='btn-primary o_sale_product_configurator_add_new') {        
                const a= await this._handleAdd();
                self.do_notify("增加SKU成功","");
                //console.log(self);
                //this.do_action({type: 'ir.actions.act_window_close'});
                //self.rootProduct
                this.do_action('product_sku_sale_product_configurator.galaxy_product_configurator_action', {
                    additional_context: {
                        //下面一句代码应该是可以移除的，还未找到方法，应该是耦合了vendor sku的一些属性
                        default_galaxy_vendor_sku_id:0,
                        //default_product_template_id:rootProduct.product_template_id,
                    },
                    on_close: function (result){
                        console.log('reload');
                        //self.$el.trigger('change');
                        self.reload();
                    }
                });
            }
        }
    },
})

var GalaxyProductConfiguratorFormView = FormView.extend({
    config: _.extend({}, FormView.prototype.config, {
        Controller: GalaxyProductConfiguratorFormController,
        Renderer: ProductConfiguratorFormRenderer,
    }),
});

viewRegistry.add('galaxy_product_configurator_form', GalaxyProductConfiguratorFormView);

    return GalaxyProductConfiguratorFormView;

});
