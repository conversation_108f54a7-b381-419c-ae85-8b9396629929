
//创建SKU界面，以当前SKU产品为模板
odoo.define('galaxy.product.sku.form', function (require) {
"use strict";
    var core = require('web.core');
    var FormController = require('web.FormController');
    var FormView = require('web.FormView');
    var viewRegistry = require('web.view_registry');

    var Create_sku_FormController = FormController.extend({
        
        /**
     * Called when the user wants to create a new record -> @see createRecord
     *
     * @private
     */
        _onCreate: function (ev) {
            //var rowIndex=ev.currentTarget.rowIndex;
            var product_id='';
            var product_template_attribute_value_ids='';
            var product_template_id='';
            var self = this;
            var record = self.model.localData[self.handle].data
            product_id = record.id;
            product_template_id=self.model.localData[record.product_tmpl_id].res_id;
            product_template_attribute_value_ids=self._convertFromMany2Many(
                self.model.localData[record.product_template_attribute_value_ids]);
            
            this.do_action('product_sku_sale_product_configurator.galaxy_product_configurator_action', {
                additional_context: {
                    //configuratorMode: 'edit',
                    default_galaxy_vendor_sku_id: product_id,
                    default_product_template_id:product_template_id,
                    default_product_template_attribute_value_ids:product_template_attribute_value_ids,
                },
                on_close: function (result){
                    if(result != undefined){
                        console.log(result.mainProduct.product_id)
                        //self.do_action('product_sku.action_window_setup_SKU_form',{additional_context:{id:result.mainProduct.product_id}})
                        self.do_action({
                            name: 'SKU',
                            res_model: 'product.product',
                            res_id: result.mainProduct.product_id,
                            views: [[false, 'form']],
                            type: 'ir.actions.act_window',
                            view_mode: 'form',
                            context:self.context,
                            'target': 'current',
                        });
                    }else{
                        self.reload();
                    }
                    
                }
            });
        },
        _convertFromMany2Many: function (recordData) {
            if (recordData) {
                var convertedValues = [];
                _.each(recordData.res_ids, function (resId) {
                    convertedValues.push([4, parseInt(resId)]);
                });

                return convertedValues;
            }

            return null;
        },
    });

    var sku_FormView = FormView.extend({
        config: _.extend({}, FormView.prototype.config, {
            Controller: Create_sku_FormController,
        }),
    });
    console.log('galaxy_sku_form')
    viewRegistry.add('galaxy_sku_form', sku_FormView);
});