odoo.define('galaxy.button.tree', function (require) {
"use strict";
    var core = require('web.core');
    var ListController = require('web.ListController');
    var ListView = require('web.ListView');
    var viewRegistry = require('web.view_registry');
    var _t = core._t;
    var FormController = require('web.FormController');
    var GalaxyListController = ListController.extend({
        buttons_template: 'galaxy.tree.buttons',
        events: _.extend({}, ListController.prototype.events, {
            'click .o_button_galaxy_tree': '_onCreate',
            'click .o_button_galaxy_tree_spu': '_onCreate_from_spu',
           
        }),
        _onCreate(){
            var self=this;
            this.do_action('product_sku_sale_product_configurator.product_variant_action', {
                on_close: function (result){
                    self.reload();
                }
            });
        },
        _onCreate_from_spu(){
            var self=this;
            this.do_action('product_sku.create_sku_manually_wizard_action', {
                on_close: function (result){
                    self.reload();
                }
            });
        },
    });

    
    
    //tree视图列表，需要使用ListController
    //开始 下面脚本是为了鼠标点击listview的时候，显示完全自定义的的form视图
    var ListRenderer = require('web.ListRenderer');
    var GalaxyListRender = ListRenderer.extend({
        _onRowClicked: function (ev) {
            var self=this;
            if (!ev.target.closest('.o_list_record_selector') && !$(ev.target).prop('special_click')){
                var id = $(ev.currentTarget).data('id');
                if(!id){
                    return;
                }
                var data_rec;
                /*
                if(this.$el.find("th").hasClass('o_group_name')){
                    var data_rec1=this.state.data[0].data;
                }else{
                    var data_rec1=this.state.data;
                }*/
                
                var data_rec1=this.state.data;
                var rec_id=0;
                var rowIndex=1;
                //console.log(data_rec1);
                //根据row id寻找resid
                try{
                    data_rec1.forEach(function(item) {
                        if(item.data!=undefined){
                            if(Array.isArray(item.data)){
                                item.data.forEach(function(element) {
                                    if(element.id==id){
                                        rec_id=element.res_id;
                                        data_rec=item.data;
                                        rowIndex=data_rec.indexOf(element)+1;
                                        throw new Error("LoopTerminates");
                                    }
                                })
                            }else{
                                if(item.id!=undefined && item.id==id){
                                    rec_id=item.res_id;
                                    data_rec=data_rec1;
                                    rowIndex=data_rec.indexOf(item)+1;
                                    throw new Error("LoopTerminates");
                                }
                            }
                        }
                    });
                }catch (e){
                    if (e.message !== "LoopTerminates") throw e;
                };
                //var rowIndex=ev.currentTarget.rowIndex;
                var product_id='';
                var product_template_attribute_value_ids='';
                var product_template_id='';
                product_id=rec_id;
                console.log(data_rec[rowIndex-1].data);
                product_template_id=data_rec[rowIndex-1].data.product_tmpl_id.data.id;
                product_template_attribute_value_ids=self._convertFromMany2Many(
                    data_rec[rowIndex-1].data.product_template_attribute_value_ids);
                this.do_action('product_sku_sale_product_configurator.galaxy_product_configurator_action', {
                    additional_context: {
                        //configuratorMode: 'edit',
                        default_galaxy_vendor_sku_id:0,
                        default_product_template_id:product_template_id,
                        default_product_template_attribute_value_ids:product_template_attribute_value_ids,
                    },
                    on_close: function (result){
                        console.log('reload');
                        //self.$el.trigger('change');
                        self.trigger_up('reload', { id:0, target: ev.target });
                    }
                });
            }
            
         },
         _convertFromMany2Many: function (recordData) {
            if (recordData) {
                var convertedValues = [];
                _.each(recordData.res_ids, function (resId) {
                    convertedValues.push([4, parseInt(resId)]);
                });

                return convertedValues;
            }

            return null;
        },
    })
    
    var GalaxyListView = ListView.extend({
        config: _.extend({}, ListView.prototype.config, {
            Controller: GalaxyListController,
            //Renderer: GalaxyListRender,
        }),
    });
    
    viewRegistry.add('galaxy_button_tree',GalaxyListView);
});
