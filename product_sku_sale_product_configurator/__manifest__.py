# -*- coding: utf-8 -*-
{
    'name':
    "product_sku_sale_product_configurator",
    'summary':
    """
        product_sku_sale_product_configurator""",
    'description':
    """
        Long description of module's purpose
    """,
    'author':
    "<PERSON><PERSON>",
    'website':
    "",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/14.0/odoo/addons/base/data/ir_module_category_data.xml
    # for the full list
    'category':
    'Galaxy_ERP/Galaxy_ERP',
    'version':
    '0.1',

    # any module necessary for this one to work correctly
    'depends': ['product_sku', 'sale_management', 'sale', 'sale_product_configurator'],

    # always loaded
    'data': [
        # 'security/ir.model.access.csv',
        'views/sale_configrator_form.xml',
        'views/assets.xml',
        'views/product_list_view.xml',
        'views/sku_configuration_templates.xml',
        'views/action_window.xml',
        'views/menu.xml',
    ],
    # only loaded in demonstration mode
    'demo': [
        'demo/demo.xml',
    ],
    'qweb': [
        'static/src/xml/tree_view_odoo.xml',
    ],
}
