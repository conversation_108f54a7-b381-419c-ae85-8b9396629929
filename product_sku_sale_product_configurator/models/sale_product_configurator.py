# -*- coding: utf-8 -*-
from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError
from odoo.addons.base_galaxy.models.public_func import display_notification,display_sticky_notification

class extent_sale_product_configrator(models.TransientModel):
    _inherit = 'sale.product.configurator'
    #galaxy_vendor_sku_id = fields.Many2one('galaxy.vendor.sku','供应商SKU')


'''
class extent_sale_product_configrator(models.TransientModel):
    _inherit = 'sale.product.configurator'
    galaxy_vendor_sku_id = fields.Many2one('galaxy.vendor.sku','供应商SKU')
    vendor_name = fields.Char(string='供应商名称', related='galaxy_vendor_sku_id.vendor_id.name',default=None, readonly=True, required=False)
    galaxy_model = fields.Char(string='型号', related='galaxy_vendor_sku_id.model',default=None, readonly=True, required=False)
    galaxy_brand = fields.Char(string='品牌', related='galaxy_vendor_sku_id.brand',default=None, readonly=True, required=False)
    galaxy_carrier = fields.Char(string='运营商', related='galaxy_vendor_sku_id.carrier',default=None, readonly=True, required=False)
    galaxy_item_description = fields.Char(string='描述', related='galaxy_vendor_sku_id.item_description',default=None, readonly=True, required=False)
    vendor_sku_= fields.Char(string='供应商SKU', related='galaxy_vendor_sku_id.vendor_sku',default=None, readonly=True, required=False)    
    vendor_sku_item_number= fields.Char(string='供应商SKU编码', related='galaxy_vendor_sku_id.item_number',default=None, readonly=True, required=False)
    
    
    @api.model
    def default_get(self, fields_list):
        res = super(extent_sale_product_configrator, self).default_get(fields_list = fields_list)
        galaxy_vendor_sku_id = self._context.get('default_galaxy_vendor_sku_id',0)
        rec = self.env['galaxy.vendor.sku'].browse(int(galaxy_vendor_sku_id))
        default_spu = ''
        product_template_id = False
        
        if rec and not rec.internal_sku:
            if rec.brand and rec.model:
                default_spu = rec.brand.upper()+' '+ rec.model.upper()
                #这边提取模板信息,做智能推荐
                #获取SPU信息
                self.env.cr.execute(f"select id from product_template where UPPER(name) = '{default_spu}'")
                temp_rec = self.env.cr.fetchall()
            else:
                default_spu=''
                temp_rec=None
                
            if temp_rec:
                product_template_id = temp_rec[0][0]
        else:
            product_template_id = rec.internal_sku.product_tmpl_id.id
            
        if product_template_id:
            self = self.with_context(default_product_template_id = product_template_id)
            res['product_template_id'] = product_template_id
        return res
'''