# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_sign
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 11:33+0000\n"
"PO-Revision-Date: 2017-09-20 11:33+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Spanish (Paraguay) (https://www.transifex.com/odoo/teams/41243/es_PY/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_PY\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_sign
#: model:mail.template,body_html:website_sign.website_sign_mail_template
msgid ""
"\n"
"            <link href='http://fonts.googleapis.com/css?family=Lato' rel='stylesheet' type='text/css'>\n"
"\n"
"            <div style=\"width:90%; max-width:600px; margin:auto; font-size:10pt; padding:20px 0; font-family:'Lato', serif;\">\n"
"                <div style=\"background-color:#EFEDED; padding: 20px;\">\n"
"                    % if ctx['template_type'] == \"request\":\n"
"                        ${ctx['email_from_usr']} (<a href=\"mailto:${ctx['email_from']}?subject=Re: ${ctx['subject']|safe}\" style=\"color:#428BCA; text-decoration:none;\" target=\"_blank\">${ctx['email_from_mail']}</a>) has requested your signature on this document :\n"
"                    % endif\n"
"                    % if ctx['template_type'] == \"follower\":\n"
"                        ${ctx['email_from_usr']} (<a href=\"mailto:${ctx['email_from']}?subject=Re: ${ctx['subject']|safe}\" style=\"color:#428BCA; text-decoration:none;\" target=\"_blank\">${ctx['email_from_mail']}</a>) has added you as a Follower of this Signature Request :\n"
"                    % endif\n"
"                    % if ctx['template_type'] == \"completed\":\n"
"                        This document has been completed and signed by everyone :\n"
"                    % endif\n"
"\n"
"                    <h1 style=\"font-size:1.25em; margin:15px 0 25px 0;\">${object['reference']}</h1>\n"
"                    \n"
"                    <div style=\"margin:0 -15px\">\n"
"                        <a style=\"float:left; display:inline-block; margin:0 15px 10px 15px; padding:5px 10px; border-radius: 3px; background-color:#5C6ED0; text-align:center; text-decoration:none; color:#F7FBFD;\" href=${ctx['link']}>\n"
"                            % if ctx['template_type'] == \"request\":\n"
"                                Sign Document Now\n"
"                            % endif\n"
"                            % if ctx['template_type'] == \"follower\":\n"
"                                View Signature Request\n"
"                            % endif\n"
"                            % if ctx['template_type'] == \"completed\":\n"
"                                View Signed Document\n"
"                            % endif\n"
"                        </a>\n"
"                        \n"
"                        <div style=\"font-size:0.8em; color:#999999; margin:0 15px 10px 15px\">\n"
"                            % if ctx['template_type'] == \"request\":\n"
"                                <strong style=\"font-weight:bold; color:black;\">Warning: do not forward this email to other people!</strong><br/>\n"
"                                They will be able to access this document and sign it as yourself.\n"
"                            % endif\n"
"                            % if ctx['template_type'] == \"follower\":\n"
"                                You will be notified once the document has been signed by everyone involved.\n"
"                            % endif\n"
"                        </div>\n"
"                        <div style=\"clear:both;\"/>\n"
"                    </div>\n"
"                    \n"
"                    % if ctx['msgbody']:\n"
"                    <div style=\"background-color:white; padding:10px;\">\n"
"                        ${ctx['msgbody']|safe}\n"
"                    </div>\n"
"                    % endif\n"
"                </div>\n"
"                \n"
"                <div style=\"font-size:0.8em; margin-top:10px;\">\n"
"                    <span style=\"color:#428BCA;\">Sent with <strong style=\"font-weight:bold;\">Odoo Sign</strong></span>\n"
"                </div>\n"
"            </div>\n"
"        \n"
"        "
msgstr ""

#. module: website_sign
#: model:mail.template,subject:website_sign.website_sign_mail_template
msgid "${ctx['subject']|safe}"
msgstr ""

#. module: website_sign
#: model:ir.ui.view,arch_db:website_sign._doc_sign
msgid "<br/>(the email access has not been sent)"
msgstr ""

#. module: website_sign
#: model:ir.ui.view,arch_db:website_sign.signature_item_type_view_form
msgid "<span>(1.0 = full page size)</span>"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_backend.js:1109
#, python-format
msgid "A copy has been sent to the new followers."
msgstr ""

#. module: website_sign
#: model:ir.ui.view,arch_db:website_sign.signature_request_template_view_search
#: model:ir.ui.view,arch_db:website_sign.signature_request_view_search
msgid "Active"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_backend.js:200
#, python-format
msgid "Add Initials"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_backend.js:209
#, python-format
msgid "Add on all pages"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_backend.js:205
#, python-format
msgid "Add once"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_common.js:662
#, python-format
msgid "Adopt Your Signature"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_common.js:667
#, python-format
msgid "Adopt and Sign"
msgstr ""

#. module: website_sign
#: model:ir.ui.view,arch_db:website_sign.signature_request_template_view_kanban
#: model:ir.ui.view,arch_db:website_sign.signature_request_view_kanban
msgid "Archive"
msgstr ""

#. module: website_sign
#: model:ir.model.fields,field_description:website_sign.field_signature_request_archived
#: model:ir.model.fields,field_description:website_sign.field_signature_request_template_archived
#: model:ir.ui.view,arch_db:website_sign.signature_request_template_view_search
#: model:ir.ui.view,arch_db:website_sign.signature_request_view_search
msgid "Archived"
msgstr ""

#. module: website_sign
#: model:ir.model.fields,field_description:website_sign.field_signature_request_template_attachment_id
msgid "Attachment"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/xml/website_sign_common.xml:55
#, python-format
msgid "Auto"
msgstr ""

#. module: website_sign
#: model:ir.model.fields,field_description:website_sign.field_signature_item_type_auto_field
msgid "Automatic Partner Field"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/xml/website_sign_common.xml:61
#, python-format
msgid ""
"By clicking Adopt and Sign, I agree that the chosen signature/initials will "
"be a valid electronic representation of my hand-written signature/initials "
"for all purposes when it is used on documents, including legally binding "
"contracts."
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_backend.js:255
#: code:addons/website_sign/static/src/js/website_sign_common.js:283
#: code:addons/website_sign/static/src/js/website_sign_common.js:670
#: code:addons/website_sign/static/src/js/website_sign_common.js:925
#: model:ir.ui.view,arch_db:website_sign.signature_request_view_form
#, python-format
msgid "Cancel"
msgstr "Cancelar"

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_backend.js:1178
#, python-format
msgid "Cancel Request"
msgstr ""

#. module: website_sign
#: selection:signature.request,state:0
msgid "Canceled"
msgstr ""

#. module: website_sign
#: code:addons/website_sign/models/signature_request.py:378
#, python-format
msgid "Canceled."
msgstr ""

#. module: website_sign
#: model:ir.actions.act_window,name:website_sign.signature_request_template_action
#: model:ir.actions.act_window,name:website_sign.signature_request_template_with_archived_action
msgid "Choose a Template"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/xml/website_sign_common.xml:53
#, python-format
msgid "Clear Signature"
msgstr ""

#. module: website_sign
#: model:ir.actions.act_window,help:website_sign.signature_request_action
msgid ""
"Click on <em>Request a Signature</em> to upload a template (or use an existing one) to automate\n"
"                your signature process for employment contracts, trade sale, NDA to sign, ..."
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_common.js:810
#, python-format
msgid "Click to start"
msgstr ""

#. module: website_sign
#: model:ir.model.fields,field_description:website_sign.field_signature_request_color
#: model:ir.model.fields,field_description:website_sign.field_signature_request_template_color
msgid "Color"
msgstr ""

#. module: website_sign
#: model:signature.item.party,name:website_sign.signature_item_party_company
#: model:signature.item.type,name:website_sign.signature_item_type_company
msgid "Company"
msgstr "Compañía"

#. module: website_sign
#: selection:signature.request.item,state:0
msgid "Completed"
msgstr ""

#. module: website_sign
#: model:ir.model.fields,field_description:website_sign.field_signature_request_completed_document
msgid "Completed Document"
msgstr ""

#. module: website_sign
#: model:ir.model.fields,field_description:website_sign.field_signature_request_nb_closed
msgid "Completed Signatures"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_common.js:363
#: code:addons/website_sign/static/src/js/website_sign_common.js:449
#: code:addons/website_sign/static/src/js/website_sign_common.js:461
#, python-format
msgid "Create: \""
msgstr ""

#. module: website_sign
#: model:ir.model.fields,field_description:website_sign.field_signature_item_create_uid
#: model:ir.model.fields,field_description:website_sign.field_signature_item_party_create_uid
#: model:ir.model.fields,field_description:website_sign.field_signature_item_type_create_uid
#: model:ir.model.fields,field_description:website_sign.field_signature_item_value_create_uid
#: model:ir.model.fields,field_description:website_sign.field_signature_request_create_uid
#: model:ir.model.fields,field_description:website_sign.field_signature_request_item_create_uid
#: model:ir.model.fields,field_description:website_sign.field_signature_request_template_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: website_sign
#: model:ir.model.fields,field_description:website_sign.field_signature_item_create_date
#: model:ir.model.fields,field_description:website_sign.field_signature_item_party_create_date
#: model:ir.model.fields,field_description:website_sign.field_signature_item_type_create_date
#: model:ir.model.fields,field_description:website_sign.field_signature_item_value_create_date
#: model:ir.model.fields,field_description:website_sign.field_signature_request_create_date
#: model:ir.model.fields,field_description:website_sign.field_signature_request_item_create_date
#: model:ir.model.fields,field_description:website_sign.field_signature_request_last_action_date
#: model:ir.model.fields,field_description:website_sign.field_signature_request_template_create_date
msgid "Created on"
msgstr "Creado en"

#. module: website_sign
#: model:signature.item.party,name:website_sign.signature_item_party_customer
msgid "Customer"
msgstr "Cliente"

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_backend.js:151
#, python-format
msgid "Customize Field"
msgstr ""

#. module: website_sign
#: model:signature.item.type,name:website_sign.signature_item_type_date
msgid "Date"
msgstr ""

#. module: website_sign
#: model:ir.model.fields,field_description:website_sign.field_signature_item_type_default_height
msgid "Default Height"
msgstr ""

#. module: website_sign
#: model:ir.model.fields,field_description:website_sign.field_signature_item_type_default_width
msgid "Default Width"
msgstr ""

#. module: website_sign
#: model:ir.ui.view,arch_db:website_sign.signature_request_template_view_kanban
#: model:ir.ui.view,arch_db:website_sign.signature_request_view_kanban
msgid "Delete"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_backend.js:1117
#, python-format
msgid "Discard"
msgstr ""

#. module: website_sign
#: model:ir.ui.view,arch_db:website_sign.signature_item_view_form
msgid "Display"
msgstr ""

#. module: website_sign
#: model:ir.model.fields,field_description:website_sign.field_signature_item_display_name
#: model:ir.model.fields,field_description:website_sign.field_signature_item_party_display_name
#: model:ir.model.fields,field_description:website_sign.field_signature_item_type_display_name
#: model:ir.model.fields,field_description:website_sign.field_signature_item_value_display_name
#: model:ir.model.fields,field_description:website_sign.field_signature_request_display_name
#: model:ir.model.fields,field_description:website_sign.field_signature_request_item_display_name
#: model:ir.model.fields,field_description:website_sign.field_signature_request_template_display_name
msgid "Display Name"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/xml/website_sign_common.xml:109
#, python-format
msgid "Do you also send documents to sign?"
msgstr ""

#. module: website_sign
#: model:ir.model.fields,field_description:website_sign.field_signature_request_follower_ids
msgid "Document Followers"
msgstr ""

#. module: website_sign
#: model:ir.model.fields,field_description:website_sign.field_signature_item_page
msgid "Document Page"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/xml/website_sign_common.xml:106
#, python-format
msgid "Document Sign"
msgstr ""

#. module: website_sign
#: model:ir.model.fields,field_description:website_sign.field_signature_item_template_id
msgid "Document Template"
msgstr ""

#. module: website_sign
#: model:ir.ui.menu,name:website_sign.signature_request_template_menu
msgid "Document Templates"
msgstr ""

#. module: website_sign
#: model:ir.model,name:website_sign.model_signature_request
msgid "Document To Sign"
msgstr ""

#. module: website_sign
#: model:ir.actions.act_window,name:website_sign.signature_request_action
#: model:ir.ui.menu,name:website_sign.digital_signatures_menu
msgid "Documents"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_backend.js:1014
#: model:ir.ui.view,arch_db:website_sign._doc_sign
#, python-format
msgid "Download Document"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_common.js:77
#, python-format
msgid "Download original document"
msgstr ""

#. module: website_sign
#: model:ir.ui.view,arch_db:website_sign.signature_request_view_form
#: selection:signature.request,state:0
#: selection:signature.request.item,state:0
msgid "Draft"
msgstr ""

#. module: website_sign
#: model:ir.model.fields,field_description:website_sign.field_signature_request_nb_draft
msgid "Draft Requests"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/xml/website_sign_backend.xml:124
#, python-format
msgid "Drag & Drop a field in the PDF"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/xml/website_sign_common.xml:56
#, python-format
msgid "Draw"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_backend.js:964
#, python-format
msgid "Duplicated Template"
msgstr ""

#. module: website_sign
#: model:ir.ui.view,arch_db:website_sign.signature_request_template_view_form
msgid "Edit fields"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/xml/website_sign_backend.xml:104
#, python-format
msgid "Edit template name"
msgstr ""

#. module: website_sign
#: model:ir.model.fields,field_description:website_sign.field_signature_request_item_signer_email
#: model:signature.item.type,name:website_sign.signature_item_type_email
msgid "Email"
msgstr ""

#. module: website_sign
#: model:signature.item.party,name:website_sign.signature_item_party_employee
msgid "Employee"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_common.js:452
#, python-format
msgid "Enter email (and name if you want)"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_common.js:1170
#: code:addons/website_sign/static/src/js/website_sign_common.js:1218
#, python-format
msgid "Error"
msgstr ""

#. module: website_sign
#: code:addons/website_sign/controllers/main.py:170
#, python-format
msgid "Everybody Signed."
msgstr ""

#. module: website_sign
#: model:ir.model.fields,field_description:website_sign.field_signature_request_favorited_ids
#: model:ir.model.fields,field_description:website_sign.field_signature_request_template_favorited_ids
msgid "Favorite of"
msgstr ""

#. module: website_sign
#: model:ir.ui.view,arch_db:website_sign.signature_request_template_view_search
#: model:ir.ui.view,arch_db:website_sign.signature_request_view_search
msgid "Favorites"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/xml/website_sign_backend.xml:6
#: model:ir.model.fields,field_description:website_sign.field_signature_item_name
#: model:ir.model.fields,field_description:website_sign.field_signature_item_type_name
#, python-format
msgid "Field Name"
msgstr ""

#. module: website_sign
#: model:ir.ui.menu,name:website_sign.signature_item_type_menu
msgid "Field Types"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/xml/website_sign_backend.xml:68
#: model:ir.model.fields,field_description:website_sign.field_signature_request_reference
#, python-format
msgid "Filename"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_common.js:903
#, python-format
msgid "Final Validation"
msgstr ""

#. module: website_sign
#: model:ir.ui.view,arch_db:website_sign._doc_sign
msgid "Followers ("
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/xml/website_sign_common.xml:45
#, python-format
msgid "Full Name"
msgstr ""

#. module: website_sign
#: selection:signature.request,state:0
msgid "Fully Signed"
msgstr ""

#. module: website_sign
#: model:ir.ui.view,arch_db:website_sign.signature_request_view_form
msgid "Get Completed Document"
msgstr ""

#. module: website_sign
#: model:ir.model.fields,field_description:website_sign.field_signature_item_height
msgid "Height"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_common.js:263
#, python-format
msgid "History"
msgstr ""

#. module: website_sign
#: model:ir.model.fields,field_description:website_sign.field_signature_item_id
#: model:ir.model.fields,field_description:website_sign.field_signature_item_party_id
#: model:ir.model.fields,field_description:website_sign.field_signature_item_type_id_7338
#: model:ir.model.fields,field_description:website_sign.field_signature_item_value_id
#: model:ir.model.fields,field_description:website_sign.field_signature_request_id
#: model:ir.model.fields,field_description:website_sign.field_signature_request_item_id
#: model:ir.model.fields,field_description:website_sign.field_signature_request_template_id_7307
msgid "ID"
msgstr "ID"

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/xml/website_sign_backend.xml:41
#, python-format
msgid ""
"If you leave your document without any fields, the signers will be requested"
" for an unique signature alongside the document"
msgstr ""

#. module: website_sign
#: model:ir.ui.view,arch_db:website_sign.signature_item_view_form
msgid "Information"
msgstr ""

#. module: website_sign
#: selection:signature.item.type,type:0
msgid "Initial"
msgstr ""

#. module: website_sign
#: model:signature.item.type,name:website_sign.signature_item_type_initial
msgid "Initials"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/xml/website_sign_common.xml:109
#, python-format
msgid ""
"It's easy, incredibly efficient and totally free: you just have to create an"
" Odoo account."
msgstr ""

#. module: website_sign
#: model:ir.model.fields,field_description:website_sign.field_signature_item___last_update
#: model:ir.model.fields,field_description:website_sign.field_signature_item_party___last_update
#: model:ir.model.fields,field_description:website_sign.field_signature_item_type___last_update
#: model:ir.model.fields,field_description:website_sign.field_signature_item_value___last_update
#: model:ir.model.fields,field_description:website_sign.field_signature_request___last_update
#: model:ir.model.fields,field_description:website_sign.field_signature_request_item___last_update
#: model:ir.model.fields,field_description:website_sign.field_signature_request_template___last_update
msgid "Last Modified on"
msgstr ""

#. module: website_sign
#: model:ir.model.fields,field_description:website_sign.field_signature_item_party_write_uid
#: model:ir.model.fields,field_description:website_sign.field_signature_item_type_write_uid
#: model:ir.model.fields,field_description:website_sign.field_signature_item_value_write_uid
#: model:ir.model.fields,field_description:website_sign.field_signature_item_write_uid
#: model:ir.model.fields,field_description:website_sign.field_signature_request_item_write_uid
#: model:ir.model.fields,field_description:website_sign.field_signature_request_template_write_uid
#: model:ir.model.fields,field_description:website_sign.field_signature_request_write_uid
msgid "Last Updated by"
msgstr "Ultima actualización por"

#. module: website_sign
#: model:ir.model.fields,field_description:website_sign.field_signature_item_party_write_date
#: model:ir.model.fields,field_description:website_sign.field_signature_item_type_write_date
#: model:ir.model.fields,field_description:website_sign.field_signature_item_value_write_date
#: model:ir.model.fields,field_description:website_sign.field_signature_item_write_date
#: model:ir.model.fields,field_description:website_sign.field_signature_request_item_write_date
#: model:ir.model.fields,field_description:website_sign.field_signature_request_template_write_date
#: model:ir.model.fields,field_description:website_sign.field_signature_request_write_date
msgid "Last Updated on"
msgstr "Ultima actualización en"

#. module: website_sign
#: model:ir.model.fields,field_description:website_sign.field_signature_request_item_latitude
msgid "Latitude"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/xml/website_sign_backend.xml:83
#, python-format
msgid "Link to Share"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/xml/website_sign_common.xml:57
#, python-format
msgid "Load"
msgstr ""

#. module: website_sign
#: model:ir.model.fields,field_description:website_sign.field_signature_request_item_longitude
msgid "Longitude"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/xml/website_sign_backend.xml:21
#, python-format
msgid "Mandatory field"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/xml/website_sign_backend.xml:62
#, python-format
msgid "Message"
msgstr ""

#. module: website_sign
#: model:ir.ui.view,arch_db:website_sign.deleted_sign_request
msgid "Missing signature request"
msgstr ""

#. module: website_sign
#: selection:signature.item.type,type:0
msgid "Multiline Text"
msgstr ""

#. module: website_sign
#: model:signature.item.type,name:website_sign.signature_item_type_multiline_text
msgid "Multiline text"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_backend.js:426
#, python-format
msgid "Multiple Signature Requests"
msgstr ""

#. module: website_sign
#: model:ir.ui.view,arch_db:website_sign.signature_request_view_search
msgid "My documents"
msgstr ""

#. module: website_sign
#: model:ir.model.fields,field_description:website_sign.field_signature_item_party_name
#: model:signature.item.type,name:website_sign.signature_item_type_name
msgid "Name"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_common.js:57
#, python-format
msgid "Need a valid PDF to add signature fields !"
msgstr ""

#. module: website_sign
#: model:ir.ui.view,arch_db:website_sign.doc_sign
msgid "Need to sign? Check your inbox to get your secure access"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_backend.js:399
#, python-format
msgid "New Document"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_backend.js:105
#, python-format
msgid "New Template"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_common.js:467
#: code:addons/website_sign/static/src/js/website_sign_common.js:471
#, python-format
msgid "New: "
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_backend.js:1228
#, python-format
msgid "Ok"
msgstr ""

#. module: website_sign
#: model:ir.ui.view,arch_db:website_sign.signature_request_template_view_kanban
#: model:ir.ui.view,arch_db:website_sign.signature_request_view_kanban
msgid "Open"
msgstr ""

#. module: website_sign
#: model:ir.actions.act_window,help:website_sign.signature_request_template_action
msgid "Or, you can"
msgstr ""

#. module: website_sign
#: model:ir.ui.menu,name:website_sign.signature_item_party_menu
msgid "Parties"
msgstr ""

#. module: website_sign
#: model:ir.model.fields,field_description:website_sign.field_signature_request_item_partner_id
msgid "Partner"
msgstr "Empresa"

#. module: website_sign
#: model:ir.model.fields,help:website_sign.field_signature_item_type_auto_field
msgid "Partner field to use to auto-complete the fields of this type"
msgstr ""

#. module: website_sign
#: model:signature.item.type,name:website_sign.signature_item_type_phone
msgid "Phone"
msgstr ""

#. module: website_sign
#: model:ir.model.fields,field_description:website_sign.field_signature_item_type_placeholder
msgid "Placeholder"
msgstr ""

#. module: website_sign
#: model:ir.ui.view,arch_db:website_sign.doc_sign
msgid "Please Review And Act On This Document"
msgstr ""

#. module: website_sign
#: model:ir.model.fields,field_description:website_sign.field_signature_item_posX
msgid "Position X"
msgstr ""

#. module: website_sign
#: model:ir.model.fields,field_description:website_sign.field_signature_item_posY
msgid "Position Y"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_common.js:76
#, python-format
msgid "Print original document"
msgstr ""

#. module: website_sign
#: model:ir.model.fields,field_description:website_sign.field_signature_request_progress
msgid "Progress"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_backend.js:164
#, python-format
msgid "Remove"
msgstr ""

#. module: website_sign
#: model:ir.model.fields,field_description:website_sign.field_signature_request_request_item_infos
msgid "Request Item Infos"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_backend.js:79
#, python-format
msgid "Request a Signature"
msgstr ""

#. module: website_sign
#: model:ir.model.fields,field_description:website_sign.field_signature_item_required
msgid "Required"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_backend.js:1202
#, python-format
msgid "Resend the invitation"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_backend.js:1142
#, python-format
msgid "Resent !"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/xml/website_sign_backend.xml:12
#: code:addons/website_sign/static/src/xml/website_sign_backend.xml:31
#: model:ir.model.fields,field_description:website_sign.field_signature_item_responsible_id
#, python-format
msgid "Responsible"
msgstr ""

#. module: website_sign
#: model:ir.ui.view,arch_db:website_sign.signature_request_template_view_kanban
#: model:ir.ui.view,arch_db:website_sign.signature_request_view_kanban
msgid "Restore"
msgstr ""

#. module: website_sign
#: model:ir.model.fields,field_description:website_sign.field_signature_request_item_role_id
msgid "Role"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/xml/website_sign_backend.xml:103
#, python-format
msgid "Saved"
msgstr ""

#. module: website_sign
#: model:ir.model.fields,field_description:website_sign.field_signature_request_access_token
#: model:ir.model.fields,field_description:website_sign.field_signature_request_item_access_token
msgid "Security Token"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/xml/website_sign_common.xml:52
#, python-format
msgid "Select Signature Style"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_common.js:356
#, python-format
msgid "Select the responsible"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_backend.js:252
#: code:addons/website_sign/static/src/js/website_sign_backend.js:775
#: code:addons/website_sign/static/src/js/website_sign_backend.js:1098
#: model:ir.ui.view,arch_db:website_sign.signature_request_view_form
#, python-format
msgid "Send"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_backend.js:248
#, python-format
msgid "Send Signature Request"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_backend.js:1171
#: code:addons/website_sign/static/src/xml/website_sign_backend.xml:94
#, python-format
msgid "Send a copy"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/xml/website_sign_backend.xml:46
#, python-format
msgid "Send a copy to"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_backend.js:1092
#, python-format
msgid "Send a copy to third parties"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/xml/website_sign_common.xml:24
#, python-format
msgid "Send a note..."
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_common.js:271
#, python-format
msgid "Send note"
msgstr ""

#. module: website_sign
#: model:ir.ui.view,arch_db:website_sign._doc_sign
msgid "Sender"
msgstr ""

#. module: website_sign
#: model:ir.model.fields,field_description:website_sign.field_signature_request_nb_wait
msgid "Sent Requests"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_backend.js:781
#, python-format
msgid "Share"
msgstr ""

#. module: website_sign
#: model:ir.model.fields,field_description:website_sign.field_signature_request_template_share_link
msgid "Share Link"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/xml/website_sign_backend.xml:81
#, python-format
msgid ""
"Share this link and Odoo will create a new document per person who clicks on"
" the link. The link is private, only those that receive the link will be "
"able to sign it."
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_backend.js:1160
#: model:ir.ui.menu,name:website_sign.menu_document
#, python-format
msgid "Sign"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_backend.js:1155
#: model:ir.ui.view,arch_db:website_sign._doc_sign
#, python-format
msgid "Sign Document"
msgstr ""

#. module: website_sign
#: model:ir.model.fields,field_description:website_sign.field_signature_request_item_signature
#: model:signature.item.type,name:website_sign.signature_item_type_signature
#: selection:signature.item.type,type:0
msgid "Signature"
msgstr ""

#. module: website_sign
#: model:ir.model,name:website_sign.model_signature_item
msgid "Signature Field For Document To Sign"
msgstr ""

#. module: website_sign
#: model:ir.model,name:website_sign.model_signature_item_value
msgid "Signature Field Value For Document To Sign"
msgstr ""

#. module: website_sign
#: model:ir.model.fields,field_description:website_sign.field_signature_item_value_signature_item_id
msgid "Signature Item"
msgstr ""

#. module: website_sign
#: model:ir.actions.act_window,name:website_sign.signature_item_party_action
msgid "Signature Item Party"
msgstr ""

#. module: website_sign
#: model:ir.actions.act_window,name:website_sign.signature_item_type_action
msgid "Signature Item Type"
msgstr ""

#. module: website_sign
#: model:ir.model.fields,field_description:website_sign.field_signature_request_template_signature_item_ids
msgid "Signature Items"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/xml/website_sign_backend.xml:58
#: model:ir.model,name:website_sign.model_signature_request_item
#: model:ir.model.fields,field_description:website_sign.field_signature_item_value_signature_request_id
#: model:ir.model.fields,field_description:website_sign.field_signature_request_item_signature_request_id
#, python-format
msgid "Signature Request"
msgstr ""

#. module: website_sign
#: model:ir.model,name:website_sign.model_signature_request_template
msgid "Signature Request Template"
msgstr ""

#. module: website_sign
#: model:ir.model.fields,field_description:website_sign.field_signature_request_template_signature_request_ids
#: model:ir.ui.menu,name:website_sign.signature_request_menu
msgid "Signature Requests"
msgstr ""

#. module: website_sign
#: selection:signature.request,state:0
msgid "Signatures in Progress"
msgstr ""

#. module: website_sign
#: model:ir.ui.view,arch_db:website_sign.signature_request_view_search
msgid "Signed"
msgstr ""

#. module: website_sign
#: model:ir.ui.view,arch_db:website_sign._doc_sign
msgid "Signed ("
msgstr ""

#. module: website_sign
#: model:ir.model.fields,field_description:website_sign.field_signature_request_item_signing_date
#: model:ir.ui.view,arch_db:website_sign._doc_sign
msgid "Signed on"
msgstr ""

#. module: website_sign
#: code:addons/website_sign/controllers/main.py:168
#, python-format
msgid "Signed."
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_backend.js:307
#: model:ir.model.fields,field_description:website_sign.field_signature_request_request_item_ids
#, python-format
msgid "Signers"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/xml/website_sign_common.xml:101
#, python-format
msgid "So you can close the window and come back later."
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_common.js:1153
#, python-format
msgid "Some fields have still to be completed !"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_backend.js:953
#, python-format
msgid "Somebody is already filling a document which uses this template"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_common.js:1169
#: code:addons/website_sign/static/src/js/website_sign_common.js:1217
#, python-format
msgid "Sorry, an error occured, please try to fill the document again."
msgstr ""

#. module: website_sign
#: model:ir.model,name:website_sign.model_signature_item_type
msgid "Specialized type for signature fields"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_common.js:958
#, python-format
msgid "Start Using Odoo Sign Now"
msgstr ""

#. module: website_sign
#: model:ir.model.fields,field_description:website_sign.field_signature_request_item_state
#: model:ir.model.fields,field_description:website_sign.field_signature_request_state
msgid "State"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/xml/website_sign_common.xml:64
#, python-format
msgid "Styles"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/xml/website_sign_backend.xml:56
#, python-format
msgid "Subject"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_backend.js:395
#: code:addons/website_sign/static/src/js/website_sign_backend.js:1109
#: code:addons/website_sign/static/src/js/website_sign_common.js:277
#, python-format
msgid "Success"
msgstr ""

#. module: website_sign
#: model:ir.model.fields,field_description:website_sign.field_signature_request_template_id
msgid "Template"
msgstr ""

#. module: website_sign
#: model:signature.item.type,name:website_sign.signature_item_type_text
#: selection:signature.item.type,type:0
msgid "Text"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_common.js:952
#, python-format
msgid "Thank You !"
msgstr ""

#. module: website_sign
#: model:ir.ui.view,arch_db:website_sign.deleted_sign_request
msgid ""
"The signature access you are trying to reach does not exist. Maybe the signature request has been deleted or modified. <br/>\n"
"                    If there still exists a signature request for this document, check your inbox to get your access!"
msgstr ""

#. module: website_sign
#: model:ir.ui.view,arch_db:website_sign.doc_sign
msgid "This document cannot be filled right now"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/xml/website_sign_backend.xml:108
#, python-format
msgid ""
"This template is used by some signature requests.\n"
"            If you want to edit it,"
msgstr ""

#. module: website_sign
#: model:ir.ui.view,arch_db:website_sign.signature_request_view_form
msgid ""
"This will delete all the already completed documents of this request and "
"disable every sent accesses, are you sure?"
msgstr ""

#. module: website_sign
#: model:ir.model.fields,field_description:website_sign.field_signature_item_type_tip
msgid "Tip"
msgstr ""

#. module: website_sign
#: model:ir.ui.view,arch_db:website_sign.signature_request_view_search
msgid "To Sign"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/xml/website_sign_backend.xml:88
#, python-format
msgid ""
"To be able to share, there must be only one responsible for all fields !"
msgstr ""

#. module: website_sign
#: model:ir.model.fields,field_description:website_sign.field_signature_item_type_id
#: model:ir.model.fields,field_description:website_sign.field_signature_item_type_type
msgid "Type"
msgstr ""

#. module: website_sign
#: model:ir.model,name:website_sign.model_signature_item_party
msgid "Type of partner which can access a particular signature field"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_backend.js:70
#, python-format
msgid "Upload a PDF Template"
msgstr ""

#. module: website_sign
#: model:ir.actions.act_window,help:website_sign.signature_request_template_action
#: model:ir.actions.act_window,help:website_sign.signature_request_template_with_archived_action
msgid "Upload a PDF of contract to sign."
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_common.js:908
#, python-format
msgid "Validate & Send"
msgstr ""

#. module: website_sign
#: model:ir.ui.view,arch_db:website_sign._doc_sign
msgid "Validate &amp; Send Completed Document"
msgstr ""

#. module: website_sign
#: model:ir.model.fields,field_description:website_sign.field_signature_item_value_value
msgid "Value"
msgstr ""

#. module: website_sign
#: model:ir.ui.view,arch_db:website_sign.signature_request_view_form
msgid "View"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_backend.js:1015
#: model:ir.ui.view,arch_db:website_sign._doc_sign
#, python-format
msgid "View History"
msgstr ""

#. module: website_sign
#: model:ir.ui.view,arch_db:website_sign._doc_sign
msgid "Waiting Signatures ("
msgstr ""

#. module: website_sign
#: selection:signature.request.item,state:0
msgid "Waiting for completion"
msgstr ""

#. module: website_sign
#: code:addons/website_sign/models/signature_request.py:368
#, python-format
msgid "Waiting for signatures."
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_common.js:1153
#, python-format
msgid "Warning"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/xml/website_sign_common.xml:101
#, python-format
msgid "We will let you know by email once everyone has signed."
msgstr ""

#. module: website_sign
#: model:ir.model.fields,field_description:website_sign.field_signature_item_width
msgid "Width"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_backend.js:325
#: code:addons/website_sign/static/src/xml/website_sign_backend.xml:48
#: code:addons/website_sign/static/src/xml/website_sign_backend.xml:96
#, python-format
msgid "Write email or search contact..."
msgstr ""

#. module: website_sign
#: model:ir.ui.view,arch_db:website_sign.doc_sign
msgid "You have completed the document"
msgstr ""

#. module: website_sign
#: model:ir.actions.act_window,help:website_sign.signature_request_action
msgid "You have no document to sign."
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/xml/website_sign_common.xml:93
#: code:addons/website_sign/static/src/xml/website_sign_common.xml:95
#, python-format
msgid "Your email"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_common.js:277
#, python-format
msgid "Your message has been sent."
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/xml/website_sign_common.xml:87
#: code:addons/website_sign/static/src/xml/website_sign_common.xml:89
#, python-format
msgid "Your name"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/js/website_sign_common.js:953
#, python-format
msgid "Your signature has been saved."
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/xml/website_sign_backend.xml:110
#, python-format
msgid "click here"
msgstr ""

#. module: website_sign
#: model:ir.ui.view,arch_db:website_sign._doc_sign
msgid "comment"
msgstr ""

#. module: website_sign
#: model:ir.actions.act_window,help:website_sign.signature_request_template_action
msgid "load our templates of contract"
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/xml/website_sign_common.xml:33
#, python-format
msgid "on"
msgstr ""

#. module: website_sign
#: model:ir.actions.act_window,help:website_sign.signature_request_template_action
msgid "to quickly evaluate this application."
msgstr ""

#. module: website_sign
#. openerp-web
#: code:addons/website_sign/static/src/xml/website_sign_backend.xml:110
#, python-format
msgid "to start a new template from this one !"
msgstr ""
