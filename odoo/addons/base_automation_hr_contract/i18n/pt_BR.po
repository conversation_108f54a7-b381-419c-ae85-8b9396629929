# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_automation_hr_contract
# 
# Translators:
# <PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# graz<PERSON>no <<EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2020
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~13.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-09-01 07:39+0000\n"
"PO-Revision-Date: 2020-09-07 08:18+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2020\n"
"Language-Team: Portuguese (Brazil) (https://www.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: base_automation_hr_contract
#: model:ir.model,name:base_automation_hr_contract.model_base_automation
msgid "Automated Action"
msgstr "Ação automatizada"

#. module: base_automation_hr_contract
#: model:ir.model.fields,field_description:base_automation_hr_contract.field_base_automation__display_name
msgid "Display Name"
msgstr "Nome exibido"

#. module: base_automation_hr_contract
#: model:ir.model.fields,field_description:base_automation_hr_contract.field_base_automation__id
msgid "ID"
msgstr "ID"

#. module: base_automation_hr_contract
#: model:ir.model.fields,field_description:base_automation_hr_contract.field_base_automation____last_update
msgid "Last Modified on"
msgstr "Última modificação em"

#. module: base_automation_hr_contract
#: model:ir.model.fields,field_description:base_automation_hr_contract.field_base_automation__trg_date_resource_field_id
msgid "Use employee work schedule"
msgstr "Use horário de trabalho do funcionário"

#. module: base_automation_hr_contract
#: model:ir.model.fields,help:base_automation_hr_contract.field_base_automation__trg_date_resource_field_id
msgid "Use the user's working schedule."
msgstr "Use cronograma de trabalho do usuário."
