<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.crm</field>
        <field name="model">res.config.settings</field>
        <field name="priority" eval="5"/>
        <field name="inherit_id" ref="base.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[hasclass('settings')]" position="inside">
                <div class="app_settings_block" data-string="CRM" string="CRM" data-key="crm" groups="sales_team.group_sale_manager">
                    <h2>CRM</h2>
                    <div class="row mt16 o_settings_container" name="qualification_setting_container">
                        <div class="col-12 col-lg-6 o_setting_box" id="crm_lead"
                            title="Use leads if you need a qualification step before creating an opportunity or a customer. It can be a business card you received, a contact form filled in your website, or a file of unqualified prospects you import, etc. Once qualified, the lead can be converted into a business opportunity and/or a new customer in your address book.">
                            <div class="o_setting_left_pane">
                                <field name="group_use_lead"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="group_use_lead"/>
                                <div class="text-muted">
                                    Add a qualification step before the creation of an opportunity
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-lg-6 o_setting_box" id="crm_lead"
                                attrs="{'invisible': [('group_use_lead','=',False)]}"
                                title="Emails received to that address generate new leads not assigned to any Sales Team yet. This can be made when converting them into opportunities. Incoming emails can be automatically assigned to specific Sales Teams. To do so, set an email alias on the Sales Team.">
                            <div class="o_setting_left_pane">
                                <field name="generate_lead_from_alias"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="generate_lead_from_alias" string="Incoming Emails"/>
                                <div class="text-muted">
                                    Create leads from incoming emails
                                </div>
                                <div class="content-group" attrs="{'invisible': [('generate_lead_from_alias','=',False)]}">
                                    <div class="mt16">
                                        <field name="crm_alias_prefix" class="oe_inline"
                                            attrs="{'required': [('generate_lead_from_alias', '=', True)]}"/>
                                        <label class="mr-0" for="alias_domain" string="@"/>
                                        <field name="alias_domain" readonly="1" force_save="1" class="oe_inline"/>
                                    </div>
                                    <div attrs="{'invisible': [('alias_domain', 'not in', ['localhost', '', False])]}">
                                        <button type="action"
                                            name="base_setup.action_general_configuration"
                                            string="Use an External Email Server" icon="fa-arrow-right" class="oe_link"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt16 o_settings_container">
                        <div class="col-12 col-lg-6 o_setting_box">
                            <div class="o_setting_left_pane">
                                <field name="group_use_recurring_revenues"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="group_use_recurring_revenues"/>
                                <div class="text-muted">
                                    Define recurring plans and revenues on Opportunities
                                </div>
                                <div attrs="{'invisible': [('group_use_recurring_revenues', '=', False)]}">
                                    <button type="action" name="crm.crm_recurring_plan_action"
                                            string="Manage Recurring Plans" icon="fa-arrow-right" class="oe_link"/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt16 o_settings_container">
                        <div class="col-12 col-lg-6 o_setting_box"
                            title="This can be used to compute statistical probability to close a lead"
                            name="predictive_lead_setting_container">
                            <div class="o_setting_left_pane"></div>
                            <div class="o_setting_right_pane">
                                <b>Predictive Lead Scoring</b>
                                <div class="text-muted">
                                    The success rate is computed based on the stage, but you can add more fields in the statistical analysis.
                                </div>
                                <div class="mt16">
                                    <field name="predictive_lead_scoring_fields" class="oe_inline" widget="many2many_tags" placeholder="Extra fields..."/>
                                    <field name="predictive_lead_scoring_fields_str" invisible="1"/>
                                </div>
                                <div class="mt16">
                                    Consider leads created as of the
                                    <field name="predictive_lead_scoring_start_date_str" invisible="1"/>
                                    <field name="predictive_lead_scoring_start_date" class="oe_inline" required="1"/>
                                </div>
                                <div class="mt16" groups="base.group_erp_manager">
                                    <div class="text-muted">
                                        Use this button to update the probabilities of all leads. This can take up to several minutes depending on how many there are.
                                    </div>
                                    <button name="action_reset_lead_probabilities" type="object" string="Update Probabilities" class="btn-primary"/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <h2>Lead Generation</h2>
                    <div class="row mt16 o_settings_container" name="convert_visitor_setting_container">
                        <div class="col-lg-6 o_setting_box">
                            <div class="o_setting_left_pane">
                                <field name="module_crm_iap_lead_website"/>
                            </div>

                            <div class="o_setting_right_pane" id="crm_iap_lead_website_settings">
                                <label string="Visits to Leads" for="module_crm_iap_lead_website"/>
                                <div class="text-muted">
                                    Convert visitors of your website into leads and perform data enrichment based on their IP address
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 o_setting_box">
                            <div class="o_setting_left_pane">
                                <field name="module_crm_iap_lead_enrich"/>
                            </div>

                            <div class="o_setting_right_pane" id="crm_iap_lead_enrich">
                                <label string="Lead Enrichment" for="module_crm_iap_lead_enrich"/>
                                <div class="text-muted">
                                    Enrich your leads automatically with company data based on their email address
                                </div>
                                <div id="crm_iap_lead_enrich">
                                    <div class="mt8" attrs="{'invisible': [('module_crm_iap_lead_enrich','=',False)]}">
                                        <field name="lead_enrich_auto" class="o_light_label" widget="radio" required="True"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt16 o_settings_container" name="generate_lead_setting_container">
                        <div class="col-lg-6 o_setting_box">
                            <div class="o_setting_left_pane">
                                <field name="module_crm_iap_lead"/>
                            </div>

                            <div class="o_setting_right_pane" id="crm_iap_lead_settings">
                                <label string="Lead Mining" for="module_crm_iap_lead"/>
                                <a href="https://www.odoo.com/documentation/14.0/applications/sales/crm/acquire_leads/lead_mining.html" title="Documentation" class="o_doc_link" target="_blank"></a>
                                <div class="text-muted">
                                    Generate new leads based on their country, industry, size, etc.
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 o_setting_box">
                            <div class="o_setting_left_pane">
                                <field name="module_mail_client_extension"/>
                            </div>

                            <div class="o_setting_right_pane" id="mail_client_extension">
                                <label string="Outlook CRM Extension" for="module_mail_client_extension"/>
                                <a href="https://www.odoo.com/documentation/14.0/applications/sales/crm/optimize/outlook_extension.html" title="Documentation" class="o_doc_link" target="_blank"></a>
                                <div class="text-muted">
                                    Turn emails received in your Outlook mailbox into leads and log their content as internal notes.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </xpath>
        </field>
    </record>

    <record id="crm_config_settings_action" model="ir.actions.act_window">
        <field name="name">Settings</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">res.config.settings</field>
        <field name="view_id" ref="res_config_settings_view_form"/>
        <field name="view_mode">form</field>
        <field name="target">inline</field>
        <field name="context">{'module' : 'crm', 'bin_size': False}</field>
    </record>

    <record id="crm_config_settings_menu" model="ir.ui.menu">
        <field name="action" ref="crm.crm_config_settings_action"/>
    </record>

</odoo>
