# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* voip
# 
# Translators:
# <PERSON> <<EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2020
# <PERSON>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2020
# <PERSON> <<EMAIL>>, 2020
# <AUTHOR> <EMAIL>, 2020
# <PERSON> <luciano<PERSON><EMAIL>>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-11-27 14:15+0000\n"
"PO-Revision-Date: 2020-09-07 08:24+0000\n"
"Last-Translator: Éder Brito <<EMAIL>>, 2021\n"
"Language-Team: Portuguese (Brazil) (https://www.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report__nbr
msgid "# of Cases"
msgstr "# de Casos"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Accept"
msgstr "Aceitar"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_mail_activity_type__category
msgid "Action to Perform"
msgstr "Ação a Ser Executada"

#. module: voip
#: model:ir.model.fields,help:voip.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr ""
"As ações podem acionar comportamentos específicos, como abrir a visualização"
" do calendário ou marcar automaticamente como concluído quando um documento "
"é carregado"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: model:ir.model,name:voip.model_mail_activity
#, python-format
msgid "Activity"
msgstr "Atividade"

#. module: voip
#: model:ir.model,name:voip.model_mail_activity_type
msgid "Activity Type"
msgstr "Tipo de Atividade"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/call_center_field.js:0
#, python-format
msgid "Add to Call Queue"
msgstr "Adicionar à Lista de Chamadas"

#. module: voip
#: model:ir.actions.server,name:voip.action_add_to_call_queue
msgid "Add to call queue"
msgstr "Adicionar à fila de chamada"

#. module: voip
#: model:ir.model,name:voip.model_voip_queue_mixin
msgid "Add voip queue support to a model"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__sip_always_transfer
msgid "Always Redirect to Handset"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"Are you sure that you want to close this website? There's a call ongoing."
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__res_users__mobile_call_method__ask
msgid "Ask"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call.xml:0
#: code:addons/voip/static/src/xml/phone_call.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Avatar"
msgstr "Avatar"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Backspace"
msgstr "Retrocesso"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "CONTACTS"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/dialing_panel.js:0
#: code:addons/voip/static/src/js/dialing_panel.js:0
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Call"
msgstr "Ligação"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__call_date
msgid "Call Date"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__name
msgid "Call Name"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "Call rejected (reason: \"%s\")"
msgstr ""

#. module: voip
#: code:addons/voip/models/voip_phonecall.py:0
#: code:addons/voip/models/voip_phonecall.py:0
#: code:addons/voip/models/voip_phonecall.py:0
#, python-format
msgid "Call to %s"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/phone_field.js:0
#, python-format
msgid "Calling %s"
msgstr ""

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Calls Date"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Cancel"
msgstr "Cancelar"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "Cancel failed: %s"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call.xml:0
#: code:addons/voip/static/src/xml/phone_call.xml:0
#, python-format
msgid "Cancel the activity"
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall__state__cancel
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_report__state__cancel
msgid "Cancelled"
msgstr "Cancelado"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "Click to unblock"
msgstr "Clique para desbloquear"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Close"
msgstr "Fechar"

#. module: voip
#: model:ir.model,name:voip.model_res_config_settings
msgid "Config Settings"
msgstr "Definições de Configuração"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "Connecting..."
msgstr ""

#. module: voip
#: model:ir.model,name:voip.model_res_partner
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__partner_id
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report__partner_id
msgid "Contact"
msgstr "Contato"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_configurator__create_uid
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__create_uid
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_configurator__create_date
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__create_date
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__create_date
msgid "Created on"
msgstr "Criado em"

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Creation Date"
msgstr "Data de Criação"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Customer"
msgstr "Cliente"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "Customer unavailable. Please try later."
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report__call_date
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Date"
msgstr "Data"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__res_config_settings__mode__demo
msgid "Demo"
msgstr "Demonstração"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Display Dialing Panel"
msgstr "Exibir Painel de Discagem"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_mail_activity__display_name
#: model:ir.model.fields,field_description:voip.field_mail_activity_type__display_name
#: model:ir.model.fields,field_description:voip.field_res_config_settings__display_name
#: model:ir.model.fields,field_description:voip.field_res_partner__display_name
#: model:ir.model.fields,field_description:voip.field_res_users__display_name
#: model:ir.model.fields,field_description:voip.field_voip_configurator__display_name
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__display_name
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__display_name
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report__display_name
#: model:ir.model.fields,field_description:voip.field_voip_queue_mixin__display_name
msgid "Display Name"
msgstr "Nome exibido"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Document"
msgstr "Documento"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_log_wizard__reschedule_option__no_reschedule
msgid "Don't Reschedule"
msgstr "Não Reprogramar"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__date_deadline
msgid "Due Date"
msgstr "Data de Vencimento"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__duration
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report__duration
msgid "Duration"
msgstr "Duração"

#. module: voip
#: model:ir.model.fields,help:voip.field_voip_phonecall__duration
msgid "Duration in minutes."
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Edit"
msgstr "Editar"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/dialing_panel.js:0
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "End Call"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Enter number or name"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Enter the number..."
msgstr "Entre o número..."

#. module: voip
#: model:ir.model.fields,help:voip.field_voip_phonecall__sequence
msgid "Gives the sequence order when displaying a list of Phonecalls."
msgstr "Fornece a ordem sequencial ao exibir uma lista de telefonemas."

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Group By"
msgstr "Agrupar Por"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__sip_external_phone
msgid "Handset Extension"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Hang up but keep call in queue"
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall__state__done
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_report__state__done
msgid "Held"
msgstr "Realizada"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_mail_activity__id
#: model:ir.model.fields,field_description:voip.field_mail_activity_type__id
#: model:ir.model.fields,field_description:voip.field_res_config_settings__id
#: model:ir.model.fields,field_description:voip.field_res_partner__id
#: model:ir.model.fields,field_description:voip.field_res_users__id
#: model:ir.model.fields,field_description:voip.field_voip_configurator__id
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__id
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__id
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report__id
#: model:ir.model.fields,field_description:voip.field_voip_queue_mixin__id
msgid "ID"
msgstr "ID"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_log_wizard__reschedule_option__7d
msgid "In 1 Week"
msgstr "Em 1 semana"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_log_wizard__reschedule_option__15d
msgid "In 15 Day"
msgstr "Em 15 Dias"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_log_wizard__reschedule_option__2m
msgid "In 2 Months"
msgstr "Em 2 Meses"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__in_queue
msgid "In Call Queue"
msgstr "Lista de Em Ligação"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "In call for:"
msgstr ""

#. module: voip
#: model_terms:ir.actions.act_window,help:voip.voip_phonecall_view
msgid ""
"In order to follow up on the call, you can trigger a request for\n"
"        another call, a meeting."
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall__phonecall_type__incoming
msgid "Incoming"
msgstr "Entrada"

#. module: voip
#. openerp-web
#: code:addons/voip/models/voip_phonecall.py:0
#: code:addons/voip/models/voip_phonecall.py:0
#: code:addons/voip/models/voip_phonecall.py:0
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "Incoming call from %s"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "Incoming call from %s (%s)"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Incoming call from..."
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_partner__has_call_in_queue
#: model:ir.model.fields,field_description:voip.field_res_users__has_call_in_queue
#: model:ir.model.fields,field_description:voip.field_voip_queue_mixin__has_call_in_queue
msgid "Is in the Call Queue"
msgstr "Está na Lista de Ligações"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Keypad"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_mail_activity____last_update
#: model:ir.model.fields,field_description:voip.field_mail_activity_type____last_update
#: model:ir.model.fields,field_description:voip.field_res_config_settings____last_update
#: model:ir.model.fields,field_description:voip.field_res_partner____last_update
#: model:ir.model.fields,field_description:voip.field_res_users____last_update
#: model:ir.model.fields,field_description:voip.field_voip_configurator____last_update
#: model:ir.model.fields,field_description:voip.field_voip_phonecall____last_update
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard____last_update
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report____last_update
#: model:ir.model.fields,field_description:voip.field_voip_queue_mixin____last_update
msgid "Last Modified on"
msgstr "Última modificação em"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__last_seen_phone_call
msgid "Last Seen Phone Call"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_configurator__write_uid
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__write_uid
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__write_uid
msgid "Last Updated by"
msgstr "Última atualização por"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_configurator__write_date
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__write_date
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__write_date
msgid "Last Updated on"
msgstr "Última atualização em"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__activity_id
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__activity_id
msgid "Linked Activity"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__mail_message_id
msgid "Linked Chatter Message"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_mail_activity__voip_phonecall_id
msgid "Linked Voip Phonecall"
msgstr ""

#. module: voip
#: model_terms:ir.actions.act_window,help:voip.voip_phonecall_view
msgid "Log the summary of a phonecall"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__phonecall_id
msgid "Logged Phonecall"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "Make a call using:"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Mark as done"
msgstr "Marcar como Completo"

#. module: voip
#: model:ir.model.fields,help:voip.field_res_users__mobile_call_method
msgid ""
"Method to use to made a call on mobile:\n"
"        * VoIP: Always used as a softphone\n"
"        * Phone: Always use the device's phone\n"
"        * Ask: Always prompt"
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall__state__missed
msgid "Missed"
msgstr ""

#. module: voip
#: code:addons/voip/models/voip_phonecall.py:0
#, python-format
msgid "Missed Call from %s"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_mail_activity__mobile
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__mobile
msgid "Mobile"
msgstr "Celular"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__mobile_call_method
msgid "Mobile call"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_partner__sanitized_mobile
#: model:ir.model.fields,field_description:voip.field_res_users__sanitized_mobile
msgid "Mobile number sanitized"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Mute"
msgstr ""

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "My Phonecalls"
msgstr "Minhas Ligações"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "NEXT ACTIVITIES"
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall__state__pending
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_report__state__pending
msgid "Not Held"
msgstr "Pendente"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__note
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__note
msgid "Note"
msgstr "Nota"

#. module: voip
#: model_terms:ir.actions.act_window,help:voip.voip_phonecall_view
msgid ""
"Odoo allows you to log inbound calls on the fly to track the\n"
"        history of the communication with a customer or to inform another\n"
"        team member."
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall__phonecall_type__outgoing
msgid "Outgoing"
msgstr "Enviando"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_config_settings__pbx_ip
msgid "PBX Server IP"
msgstr "IP do Servidor PBX"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "PBX or Websocket address is missing. Please check your settings."
msgstr ""
"Endereço de PBX ou Websocket não encontrado. Verifique suas definições."

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call.xml:0
#: code:addons/voip/static/src/xml/phone_call.xml:0
#, python-format
msgid "Pending"
msgstr "Pendente"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/dialing_panel.js:0
#: model:ir.model.fields,field_description:voip.field_mail_activity__phone
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__phone
#: model:ir.model.fields.selection,name:voip.selection__res_users__mobile_call_method__phone
#, python-format
msgid "Phone"
msgstr "Telefone"

#. module: voip
#: code:addons/voip/models/voip_queue_mixin.py:0
#, python-format
msgid ""
"Phone call cannot be created. Is it any phone number linked to record %s?"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_partner__sanitized_phone
#: model:ir.model.fields,field_description:voip.field_res_users__sanitized_phone
msgid "Phone number sanitized"
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__mail_activity_type__category__phonecall
msgid "Phonecall"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Phonecall details"
msgstr ""

#. module: voip
#: model:ir.actions.act_window,name:voip.voip_phonecall_view
#: model:ir.ui.menu,name:voip.menu_voip_phonecall_view
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
#: model_terms:ir.ui.view,arch_db:voip.voip_phonecall_tree_view
msgid "Phonecalls"
msgstr "Ligações Telefônicas"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "Please Allow the use of the microphone"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:0
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "Please accept the use of the microphone."
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__res_config_settings__mode__prod
msgid "Production"
msgstr "Produção"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "RECENT"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Refresh the Panel"
msgstr "Atualizar Painel"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Reject"
msgstr "Rejeitar"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__sip_ignore_incoming
msgid "Reject All Incoming Calls"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Reject call"
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall__state__rejected
msgid "Rejected"
msgstr "Rejeitado"

#. module: voip
#: code:addons/voip/models/voip_phonecall.py:0
#, python-format
msgid "Rejected Incoming Call from %s"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "Remember ?"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/call_center_field.js:0
#, python-format
msgid "Remove from Call Queue"
msgstr "Remover da Lista Chamadas"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call.xml:0
#: code:addons/voip/static/src/xml/phone_call.xml:0
#, python-format
msgid "Remove from the queue"
msgstr "Remover da fila"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__user_id
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report__user_id
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Responsible"
msgstr "Responsável"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Ringing..."
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__sip_login
msgid "SIP Login / Browser's Extension"
msgstr "Login SIP/Extensão do Navegador"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__sip_password
msgid "SIP Password"
msgstr "Senha SIP"

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.res_config_settings_view_form
msgid "Schedule &amp; make calls from your database"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__reschedule_option
msgid "Schedule A New Activity"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Schedule Activity"
msgstr "Agendar Atividade"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Schedule Next"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Search"
msgstr "Pesquisar"

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Search Phonecalls"
msgstr "Pesquisar Ligações Telefônicas"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Send mail"
msgstr "Enviar E-mail"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__sequence
msgid "Sequence"
msgstr "Sequência"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__reschedule_date
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_log_wizard__reschedule_option__custom
msgid "Specific Date"
msgstr "Especificar Data"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__start_time
msgid "Start time"
msgstr "Iniciar tempo"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__state
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report__state
msgid "Status"
msgstr "Situação"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__summary
msgid "Summary"
msgstr "Resumo"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Take call"
msgstr ""

#. module: voip
#: model:ir.model.fields,help:voip.field_res_config_settings__pbx_ip
msgid "The IP adress of your PBX Server"
msgstr "O endereço IP de seu Servidor PBX"

#. module: voip
#: model:ir.model.fields,help:voip.field_res_config_settings__wsServer
msgid "The URL of your WebSocket"
msgstr "A URL de seu WebSocket"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"The call was rejected as access rights to the microphone were not given"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"The connection cannot be made.</br> Please check your configuration.</br> "
"(Reason received: %s)"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "The person you try to contact is currently unavailable."
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "The phonecall has no number"
msgstr "A chamada não tem número"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"The server configuration could be wrong. Please check your configuration."
msgstr ""
"A configuração do servidor pode estar errada. Por favor, verifique a sua "
"configuração."

#. module: voip
#: model:ir.model.fields,help:voip.field_voip_phonecall__state
msgid ""
"The status is set to To Do, when a call is created.\n"
"When the call is over, the status is set to Held.\n"
"If the call is not applicable anymore, the status can be set to Cancelled."
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "The websocket uri could be wrong. Please check your configuration."
msgstr ""
"A uri do websocket pode estar errada. Por favor, verifique a sua "
"configuração."

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"There was an error with your registration: Please check your configuration."
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall__state__open
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_report__state__open
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "To Do"
msgstr "A Fazer"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_log_wizard__reschedule_option__1d
msgid "Tomorrow"
msgstr "Amanhã"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Transfer"
msgstr "Transferir"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__phonecall_type
msgid "Type"
msgstr "Tipo"

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Unassigned"
msgstr "Não atribuído"

#. module: voip
#: model:ir.model,name:voip.model_res_users
msgid "Users"
msgstr "Usuários"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/dialing_panel.js:0
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "VOIP"
msgstr "VOIP"

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.res_user_form
msgid "VOIP Configuration"
msgstr "Configuração VOIP"

#. module: voip
#: model:ir.model,name:voip.model_voip_configurator
msgid "VOIP Configurator"
msgstr "Configurador VOIP"

#. module: voip
#: model:ir.model,name:voip.model_voip_phonecall
msgid "VOIP Phonecall"
msgstr ""

#. module: voip
#: model:ir.model,name:voip.model_voip_phonecall_log_wizard
msgid "VOIP Phonecall log Wizard"
msgstr ""

#. module: voip
#: model:ir.model,name:voip.model_voip_phonecall_report
msgid "VOIP Phonecalls by user report"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_config_settings__mode
msgid "VoIP Environment"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/dialing_panel.js:0
#: model:ir.model.fields.selection,name:voip.selection__res_users__mobile_call_method__voip
#, python-format
msgid "Voip"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_config_settings__wsServer
msgid "WebSocket"
msgstr "WebSocket"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "You are already in a call"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid ""
"You must allow the access to the microphone on your device. Otherwise, the "
"VoIP call receiver will not hear you."
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/dialing_panel.js:0
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"Your browser could not support WebRTC. Please check your configuration."
msgstr "Seu navegador não suporta WebRTC. Por favor cheque sua configuração."

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"Your credentials are not correctly set. Please contact your administrator."
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "min"
msgstr "min"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "missed call(s)"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "sec"
msgstr ""
