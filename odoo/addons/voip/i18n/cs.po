# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* voip
# 
# Translators:
# <PERSON>, 2020
# <PERSON> <jan.ho<PERSON><PERSON>@centrum.cz>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# ka<PERSON><PERSON><PERSON> s<PERSON> <karolina.schus<PERSON><PERSON>@vdp.sk>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <rast<PERSON>.<EMAIL>>, 2021
# trendspotter, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-11-27 14:15+0000\n"
"PO-Revision-Date: 2020-09-07 08:24+0000\n"
"Last-Translator: trendspotter, 2021\n"
"Language-Team: Czech (https://www.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report__nbr
msgid "# of Cases"
msgstr "# případů"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Accept"
msgstr "Přijmout"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_mail_activity_type__category
msgid "Action to Perform"
msgstr "Akce k provedení"

#. module: voip
#: model:ir.model.fields,help:voip.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr ""
"Akce mohou vyvolat určité chování, jako je otevření zobrazení kalendáře, "
"nebo se automaticky označí jako provedené při nahrávání dokumentu"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: model:ir.model,name:voip.model_mail_activity
#, python-format
msgid "Activity"
msgstr "Činnost"

#. module: voip
#: model:ir.model,name:voip.model_mail_activity_type
msgid "Activity Type"
msgstr "Typ činnosti"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/call_center_field.js:0
#, python-format
msgid "Add to Call Queue"
msgstr "Přidat do fronty hovorů"

#. module: voip
#: model:ir.actions.server,name:voip.action_add_to_call_queue
msgid "Add to call queue"
msgstr "Přidat do fronty hovorů"

#. module: voip
#: model:ir.model,name:voip.model_voip_queue_mixin
msgid "Add voip queue support to a model"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__sip_always_transfer
msgid "Always Redirect to Handset"
msgstr "Vždy přesměrovat do sluchátka"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"Are you sure that you want to close this website? There's a call ongoing."
msgstr ""
"Jste si jisti, že chcete zavřít tuto stránku? Je zde probíhající odchozí "
"hovor."

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__res_users__mobile_call_method__ask
msgid "Ask"
msgstr "Dotaz:"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call.xml:0
#: code:addons/voip/static/src/xml/phone_call.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Avatar"
msgstr "Avatar"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Backspace"
msgstr "Backspace"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "CONTACTS"
msgstr "KONTAKTY"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/dialing_panel.js:0
#: code:addons/voip/static/src/js/dialing_panel.js:0
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Call"
msgstr "Hovor"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__call_date
msgid "Call Date"
msgstr "Datum volání"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__name
msgid "Call Name"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "Call rejected (reason: \"%s\")"
msgstr "Hovor zamítnut (důvod: \"%s\")"

#. module: voip
#: code:addons/voip/models/voip_phonecall.py:0
#: code:addons/voip/models/voip_phonecall.py:0
#: code:addons/voip/models/voip_phonecall.py:0
#, python-format
msgid "Call to %s"
msgstr "Volat %s"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/phone_field.js:0
#, python-format
msgid "Calling %s"
msgstr "Volání %s"

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Calls Date"
msgstr "Datum volání"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Cancel"
msgstr "Zrušit"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "Cancel failed: %s"
msgstr "Přerušení se nezdařilo: %s"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call.xml:0
#: code:addons/voip/static/src/xml/phone_call.xml:0
#, python-format
msgid "Cancel the activity"
msgstr "Zrušit aktivitu"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall__state__cancel
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_report__state__cancel
msgid "Cancelled"
msgstr "Zrušeno"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "Click to unblock"
msgstr "Kliknout na odblokování"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Close"
msgstr "Zavřít"

#. module: voip
#: model:ir.model,name:voip.model_res_config_settings
msgid "Config Settings"
msgstr "Nastavení konfigurace"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "Connecting..."
msgstr "Připojuji..."

#. module: voip
#: model:ir.model,name:voip.model_res_partner
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__partner_id
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report__partner_id
msgid "Contact"
msgstr "Kontakt"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_configurator__create_uid
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__create_uid
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__create_uid
msgid "Created by"
msgstr "Vytvořeno od"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_configurator__create_date
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__create_date
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__create_date
msgid "Created on"
msgstr "Vytvořeno"

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Creation Date"
msgstr "Datum vytvoření"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Customer"
msgstr "Zákazník"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "Customer unavailable. Please try later."
msgstr "Zákazník nedostupný. Prosím zkuste to později."

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report__call_date
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Date"
msgstr "Datum"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__res_config_settings__mode__demo
msgid "Demo"
msgstr "Demo"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Display Dialing Panel"
msgstr "Zobrazit vytáčecí panel"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_mail_activity__display_name
#: model:ir.model.fields,field_description:voip.field_mail_activity_type__display_name
#: model:ir.model.fields,field_description:voip.field_res_config_settings__display_name
#: model:ir.model.fields,field_description:voip.field_res_partner__display_name
#: model:ir.model.fields,field_description:voip.field_res_users__display_name
#: model:ir.model.fields,field_description:voip.field_voip_configurator__display_name
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__display_name
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__display_name
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report__display_name
#: model:ir.model.fields,field_description:voip.field_voip_queue_mixin__display_name
msgid "Display Name"
msgstr "Zobrazované jméno"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Document"
msgstr "Dokument"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_log_wizard__reschedule_option__no_reschedule
msgid "Don't Reschedule"
msgstr "Znovu neplánovat"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__date_deadline
msgid "Due Date"
msgstr "Termín splnění"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__duration
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report__duration
msgid "Duration"
msgstr "Trvání"

#. module: voip
#: model:ir.model.fields,help:voip.field_voip_phonecall__duration
msgid "Duration in minutes."
msgstr "Trvání v minutách"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Edit"
msgstr "Upravit"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/dialing_panel.js:0
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "End Call"
msgstr "Konec hovoru"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Enter number or name"
msgstr "Vložte číslo nebo jméno"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Enter the number..."
msgstr "Zadejte číslo ..."

#. module: voip
#: model:ir.model.fields,help:voip.field_voip_phonecall__sequence
msgid "Gives the sequence order when displaying a list of Phonecalls."
msgstr ""

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Group By"
msgstr "Seskupit podle"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__sip_external_phone
msgid "Handset Extension"
msgstr "Sluchátko telefonu"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Hang up but keep call in queue"
msgstr "Zavěsit, ale podržet hovor ve frontě"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall__state__done
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_report__state__done
msgid "Held"
msgstr "Voláno"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_mail_activity__id
#: model:ir.model.fields,field_description:voip.field_mail_activity_type__id
#: model:ir.model.fields,field_description:voip.field_res_config_settings__id
#: model:ir.model.fields,field_description:voip.field_res_partner__id
#: model:ir.model.fields,field_description:voip.field_res_users__id
#: model:ir.model.fields,field_description:voip.field_voip_configurator__id
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__id
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__id
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report__id
#: model:ir.model.fields,field_description:voip.field_voip_queue_mixin__id
msgid "ID"
msgstr "ID"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_log_wizard__reschedule_option__7d
msgid "In 1 Week"
msgstr "Za 1 týden"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_log_wizard__reschedule_option__15d
msgid "In 15 Day"
msgstr "Posledních 15 dní"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_log_wizard__reschedule_option__2m
msgid "In 2 Months"
msgstr "Poslední 2 měsíce"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__in_queue
msgid "In Call Queue"
msgstr "Ve frontě hovorů"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "In call for:"
msgstr ""

#. module: voip
#: model_terms:ir.actions.act_window,help:voip.voip_phonecall_view
msgid ""
"In order to follow up on the call, you can trigger a request for\n"
"        another call, a meeting."
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall__phonecall_type__incoming
msgid "Incoming"
msgstr "Příchozí"

#. module: voip
#. openerp-web
#: code:addons/voip/models/voip_phonecall.py:0
#: code:addons/voip/models/voip_phonecall.py:0
#: code:addons/voip/models/voip_phonecall.py:0
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "Incoming call from %s"
msgstr "Příchozí volání od %s"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "Incoming call from %s (%s)"
msgstr "Příchozí volání od %s (%s)"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Incoming call from..."
msgstr "Příchozí volání od..."

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_partner__has_call_in_queue
#: model:ir.model.fields,field_description:voip.field_res_users__has_call_in_queue
#: model:ir.model.fields,field_description:voip.field_voip_queue_mixin__has_call_in_queue
msgid "Is in the Call Queue"
msgstr "Ve volací frontě"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Keypad"
msgstr "Klávesnice"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_mail_activity____last_update
#: model:ir.model.fields,field_description:voip.field_mail_activity_type____last_update
#: model:ir.model.fields,field_description:voip.field_res_config_settings____last_update
#: model:ir.model.fields,field_description:voip.field_res_partner____last_update
#: model:ir.model.fields,field_description:voip.field_res_users____last_update
#: model:ir.model.fields,field_description:voip.field_voip_configurator____last_update
#: model:ir.model.fields,field_description:voip.field_voip_phonecall____last_update
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard____last_update
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report____last_update
#: model:ir.model.fields,field_description:voip.field_voip_queue_mixin____last_update
msgid "Last Modified on"
msgstr "Naposled změněno"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__last_seen_phone_call
msgid "Last Seen Phone Call"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_configurator__write_uid
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__write_uid
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__write_uid
msgid "Last Updated by"
msgstr "Naposledy upraveno od"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_configurator__write_date
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__write_date
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__write_date
msgid "Last Updated on"
msgstr "Naposled upraveno"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__activity_id
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__activity_id
msgid "Linked Activity"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__mail_message_id
msgid "Linked Chatter Message"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_mail_activity__voip_phonecall_id
msgid "Linked Voip Phonecall"
msgstr ""

#. module: voip
#: model_terms:ir.actions.act_window,help:voip.voip_phonecall_view
msgid "Log the summary of a phonecall"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__phonecall_id
msgid "Logged Phonecall"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "Make a call using:"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Mark as done"
msgstr "označit jako hotové"

#. module: voip
#: model:ir.model.fields,help:voip.field_res_users__mobile_call_method
msgid ""
"Method to use to made a call on mobile:\n"
"        * VoIP: Always used as a softphone\n"
"        * Phone: Always use the device's phone\n"
"        * Ask: Always prompt"
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall__state__missed
msgid "Missed"
msgstr "Zmeškané"

#. module: voip
#: code:addons/voip/models/voip_phonecall.py:0
#, python-format
msgid "Missed Call from %s"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_mail_activity__mobile
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__mobile
msgid "Mobile"
msgstr "Mobilní"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__mobile_call_method
msgid "Mobile call"
msgstr "Mobilní volání"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_partner__sanitized_mobile
#: model:ir.model.fields,field_description:voip.field_res_users__sanitized_mobile
msgid "Mobile number sanitized"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Mute"
msgstr "Ztišit"

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "My Phonecalls"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "NEXT ACTIVITIES"
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall__state__pending
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_report__state__pending
msgid "Not Held"
msgstr "Nevoláno"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__note
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__note
msgid "Note"
msgstr "Poznámka"

#. module: voip
#: model_terms:ir.actions.act_window,help:voip.voip_phonecall_view
msgid ""
"Odoo allows you to log inbound calls on the fly to track the\n"
"        history of the communication with a customer or to inform another\n"
"        team member."
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall__phonecall_type__outgoing
msgid "Outgoing"
msgstr "Odchozí"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_config_settings__pbx_ip
msgid "PBX Server IP"
msgstr "PBX Server IP"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "PBX or Websocket address is missing. Please check your settings."
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call.xml:0
#: code:addons/voip/static/src/xml/phone_call.xml:0
#, python-format
msgid "Pending"
msgstr "Čekající"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/dialing_panel.js:0
#: model:ir.model.fields,field_description:voip.field_mail_activity__phone
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__phone
#: model:ir.model.fields.selection,name:voip.selection__res_users__mobile_call_method__phone
#, python-format
msgid "Phone"
msgstr "Telefon"

#. module: voip
#: code:addons/voip/models/voip_queue_mixin.py:0
#, python-format
msgid ""
"Phone call cannot be created. Is it any phone number linked to record %s?"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_partner__sanitized_phone
#: model:ir.model.fields,field_description:voip.field_res_users__sanitized_phone
msgid "Phone number sanitized"
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__mail_activity_type__category__phonecall
msgid "Phonecall"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Phonecall details"
msgstr ""

#. module: voip
#: model:ir.actions.act_window,name:voip.voip_phonecall_view
#: model:ir.ui.menu,name:voip.menu_voip_phonecall_view
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
#: model_terms:ir.ui.view,arch_db:voip.voip_phonecall_tree_view
msgid "Phonecalls"
msgstr "Telefonní hovory"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "Please Allow the use of the microphone"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:0
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "Please accept the use of the microphone."
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__res_config_settings__mode__prod
msgid "Production"
msgstr "Výroba"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "RECENT"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Refresh the Panel"
msgstr "Obnovit panel"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Reject"
msgstr "Odmítnout"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__sip_ignore_incoming
msgid "Reject All Incoming Calls"
msgstr "Zamítnout všechny příchozí hovory"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Reject call"
msgstr "Zamítnout hovor"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall__state__rejected
msgid "Rejected"
msgstr "Odmítnuto"

#. module: voip
#: code:addons/voip/models/voip_phonecall.py:0
#, python-format
msgid "Rejected Incoming Call from %s"
msgstr "Zamítnout příchozí hovor od %s"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "Remember ?"
msgstr "Zapamatovat ?"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/call_center_field.js:0
#, python-format
msgid "Remove from Call Queue"
msgstr "Odstranit z volací fronty"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call.xml:0
#: code:addons/voip/static/src/xml/phone_call.xml:0
#, python-format
msgid "Remove from the queue"
msgstr "Odstranit z fronty"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__user_id
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report__user_id
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Responsible"
msgstr "Odpovědný"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Ringing..."
msgstr "Vyzvání..."

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__sip_login
msgid "SIP Login / Browser's Extension"
msgstr "SIP Login / rozšíření prohlížeče"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_users__sip_password
msgid "SIP Password"
msgstr "SIP Heslo"

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.res_config_settings_view_form
msgid "Schedule &amp; make calls from your database"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__reschedule_option
msgid "Schedule A New Activity"
msgstr "Naplánovat novou aktivitu"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Schedule Activity"
msgstr "Naplánovat aktivitu"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Schedule Next"
msgstr "Naplánovat další"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/dialing_panel.xml:0
#, python-format
msgid "Search"
msgstr "Hledání"

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Search Phonecalls"
msgstr "Hledat telefonáty"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Send mail"
msgstr "Poslat mail"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__sequence
msgid "Sequence"
msgstr "Číselná řada"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__reschedule_date
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_log_wizard__reschedule_option__custom
msgid "Specific Date"
msgstr "Konkrétní datum"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__start_time
msgid "Start time"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__state
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_report__state
msgid "Status"
msgstr "Stav"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall_log_wizard__summary
msgid "Summary"
msgstr "Shrnutí"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Take call"
msgstr "Vzít hovor"

#. module: voip
#: model:ir.model.fields,help:voip.field_res_config_settings__pbx_ip
msgid "The IP adress of your PBX Server"
msgstr "IP adresa vašeho PBX serveru"

#. module: voip
#: model:ir.model.fields,help:voip.field_res_config_settings__wsServer
msgid "The URL of your WebSocket"
msgstr "Adresa URL vašeho webu WebSocket"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"The call was rejected as access rights to the microphone were not given"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"The connection cannot be made.</br> Please check your configuration.</br> "
"(Reason received: %s)"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "The person you try to contact is currently unavailable."
msgstr "Osoba, kterou se snažíte kontaktovat, je momentálně nedostupná."

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "The phonecall has no number"
msgstr "Telefonní číslo nemá žádné číslo"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"The server configuration could be wrong. Please check your configuration."
msgstr ""
"Konfigurace serveru může být nesprávná. Zkontrolujte prosím konfiguraci."

#. module: voip
#: model:ir.model.fields,help:voip.field_voip_phonecall__state
msgid ""
"The status is set to To Do, when a call is created.\n"
"When the call is over, the status is set to Held.\n"
"If the call is not applicable anymore, the status can be set to Cancelled."
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid "The websocket uri could be wrong. Please check your configuration."
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"There was an error with your registration: Please check your configuration."
msgstr ""

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall__state__open
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_report__state__open
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "To Do"
msgstr "K udělání"

#. module: voip
#: model:ir.model.fields.selection,name:voip.selection__voip_phonecall_log_wizard__reschedule_option__1d
msgid "Tomorrow"
msgstr "Zítra"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "Transfer"
msgstr "Převod"

#. module: voip
#: model:ir.model.fields,field_description:voip.field_voip_phonecall__phonecall_type
msgid "Type"
msgstr "Typ"

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.view_voip_case_phonecalls_filter
msgid "Unassigned"
msgstr "Nepřiřazeno"

#. module: voip
#: model:ir.model,name:voip.model_res_users
msgid "Users"
msgstr "Uživatelé"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/dialing_panel.js:0
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "VOIP"
msgstr "VOIP"

#. module: voip
#: model_terms:ir.ui.view,arch_db:voip.res_user_form
msgid "VOIP Configuration"
msgstr "VOIP konfigurace"

#. module: voip
#: model:ir.model,name:voip.model_voip_configurator
msgid "VOIP Configurator"
msgstr "Konfigurátor VOIP"

#. module: voip
#: model:ir.model,name:voip.model_voip_phonecall
msgid "VOIP Phonecall"
msgstr ""

#. module: voip
#: model:ir.model,name:voip.model_voip_phonecall_log_wizard
msgid "VOIP Phonecall log Wizard"
msgstr ""

#. module: voip
#: model:ir.model,name:voip.model_voip_phonecall_report
msgid "VOIP Phonecalls by user report"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_config_settings__mode
msgid "VoIP Environment"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/dialing_panel.js:0
#: model:ir.model.fields.selection,name:voip.selection__res_users__mobile_call_method__voip
#, python-format
msgid "Voip"
msgstr ""

#. module: voip
#: model:ir.model.fields,field_description:voip.field_res_config_settings__wsServer
msgid "WebSocket"
msgstr "WebSocket"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "You are already in a call"
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid ""
"You must allow the access to the microphone on your device. Otherwise, the "
"VoIP call receiver will not hear you."
msgstr ""
"V zařízení musíte povolit přístup k mikrofonu. V opačném případě vás "
"přijímač hovoru VoIP neuslyší."

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/dialing_panel.js:0
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"Your browser could not support WebRTC. Please check your configuration."
msgstr ""

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/user_agent.js:0
#, python-format
msgid ""
"Your credentials are not correctly set. Please contact your administrator."
msgstr ""
"Vaše přihlašovací údaje nejsou správně nastaveny. Obraťte se na svého "
"správce."

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "min"
msgstr "min"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/js/dialing_panel.js:0
#, python-format
msgid "missed call(s)"
msgstr "zmeškaný hovor"

#. module: voip
#. openerp-web
#: code:addons/voip/static/src/xml/phone_call.xml:0
#: code:addons/voip/static/src/xml/phone_call_details.xml:0
#, python-format
msgid "sec"
msgstr ""
