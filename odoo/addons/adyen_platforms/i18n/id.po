# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* adyen_platforms
# 
# Translators:
# <PERSON>, 2020
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <AUTHOR> <EMAIL>, 2020
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON> Useful <<EMAIL>>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2020
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2020
# Ikh<PERSON><PERSON>irsa <<EMAIL>>, 2020
# whenwesober, 2020
# Abe Manyo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-11-27 15:42+0000\n"
"PO-Revision-Date: 2020-11-16 13:49+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: Indonesian (https://www.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: adyen_platforms
#. openerp-web
#: code:addons/adyen_platforms/static/src/xml/adyen_account_templates.xml:0
#, python-format
msgid ""
". If we or our\n"
"            Processor at any time discover that the information you provided about your\n"
"            business is incorrect or has changed without informing us or if you violate any of\n"
"            these conditions, the services may be suspended and/or terminated with\n"
"            immediate effect and fines may be applied by the Credit Card Schemes and/or the\n"
"            authorities for unregistered or inappropriate use of payment services which will in\n"
"            such case be payable by you."
msgstr ""

#. module: adyen_platforms
#: model_terms:ir.ui.view,arch_db:adyen_platforms.adyen_account_view_form
msgid "<span class=\"o_stat_text\"> Transactions</span>"
msgstr ""

#. module: adyen_platforms
#: code:addons/adyen_platforms/models/adyen_account.py:0
#, python-format
msgid "A timeout occured while trying to reach the Adyen proxy."
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_payout__code
msgid "Account Code"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__account_holder_code
msgid "Account Holder Code"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_bank_account__account_number
msgid "Account Number"
msgstr "Nomor Rekening"

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_bank_account__account_type
msgid "Account Type"
msgstr "Tipe Akun"

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__message_needaction
msgid "Action Needed"
msgstr "Perlu Tindakan"

#. module: adyen_platforms
#. openerp-web
#: code:addons/adyen_platforms/static/src/xml/adyen_account_templates.xml:0
#: model_terms:ir.ui.view,arch_db:adyen_platforms.adyen_account_view_form
#: model_terms:ir.ui.view,arch_db:adyen_platforms.adyen_shareholder_view_form
#, python-format
msgid "Address"
msgstr "Alamat"

#. module: adyen_platforms
#: code:addons/adyen_platforms/models/adyen_account.py:0
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_bank_account__adyen_account_id
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_payout__adyen_account_id
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_shareholder__adyen_account_id
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_transaction__adyen_account_id
#: model:ir.model.fields,field_description:adyen_platforms.field_res_company__adyen_account_id
#: model_terms:ir.ui.view,arch_db:adyen_platforms.adyen_account_view_form
#, python-format
msgid "Adyen Account"
msgstr ""

#. module: adyen_platforms
#: model_terms:ir.ui.view,arch_db:adyen_platforms.adyen_bank_account_view_form
msgid "Adyen Bank Account"
msgstr ""

#. module: adyen_platforms
#. openerp-web
#: code:addons/adyen_platforms/static/src/xml/adyen_account_templates.xml:0
#, python-format
msgid "Adyen MarketPay Terms and Conditions (click"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_transaction__adyen_payout_id
#: model_terms:ir.ui.view,arch_db:adyen_platforms.adyen_payout_view_form
msgid "Adyen Payout"
msgstr ""

#. module: adyen_platforms
#. openerp-web
#: code:addons/adyen_platforms/static/src/xml/adyen_account_templates.xml:0
#, python-format
msgid "Adyen Restricted and Prohibited Products and Services list (click"
msgstr ""

#. module: adyen_platforms
#: model_terms:ir.ui.view,arch_db:adyen_platforms.adyen_shareholder_view_form
msgid "Adyen Shareholder"
msgstr ""

#. module: adyen_platforms
#: model:ir.actions.server,name:adyen_platforms.adyen_sync_cron_ir_actions_server
#: model:ir.cron,cron_name:adyen_platforms.adyen_sync_cron
#: model:ir.cron,name:adyen_platforms.adyen_sync_cron
msgid "Adyen Sync"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__adyen_uuid
msgid "Adyen UUID"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.constraint,message:adyen_platforms.constraint_adyen_account_adyen_uuid_uniq
msgid "Adyen UUID should be unique"
msgstr ""

#. module: adyen_platforms
#: model:ir.model,name:adyen_platforms.model_adyen_account
msgid "Adyen for Platforms Account"
msgstr ""

#. module: adyen_platforms
#: model:ir.model,name:adyen_platforms.model_adyen_address_mixin
msgid "Adyen for Platforms Address Mixin"
msgstr ""

#. module: adyen_platforms
#: model:ir.model,name:adyen_platforms.model_adyen_bank_account
msgid "Adyen for Platforms Bank Account"
msgstr ""

#. module: adyen_platforms
#: model:ir.model,name:adyen_platforms.model_adyen_id_mixin
msgid "Adyen for Platforms ID Mixin"
msgstr ""

#. module: adyen_platforms
#: model:ir.model,name:adyen_platforms.model_adyen_payout
msgid "Adyen for Platforms Payout"
msgstr ""

#. module: adyen_platforms
#: model:ir.model,name:adyen_platforms.model_adyen_shareholder
msgid "Adyen for Platforms Shareholder"
msgstr ""

#. module: adyen_platforms
#: model:ir.model,name:adyen_platforms.model_adyen_transaction
msgid "Adyen for Platforms Transaction"
msgstr ""

#. module: adyen_platforms
#: code:addons/adyen_platforms/models/adyen_account.py:0
#, python-format
msgid "Allowed file formats for bank statements are jpeg, jpg, pdf or png"
msgstr ""

#. module: adyen_platforms
#: code:addons/adyen_platforms/models/adyen_account.py:0
#, python-format
msgid "Allowed file formats for photo IDs are jpeg, jpg, pdf or png"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,help:adyen_platforms.field_adyen_account__id_back
#: model:ir.model.fields,help:adyen_platforms.field_adyen_account__id_front
#: model:ir.model.fields,help:adyen_platforms.field_adyen_id_mixin__id_back
#: model:ir.model.fields,help:adyen_platforms.field_adyen_id_mixin__id_front
#: model:ir.model.fields,help:adyen_platforms.field_adyen_shareholder__id_back
#: model:ir.model.fields,help:adyen_platforms.field_adyen_shareholder__id_front
msgid "Allowed formats: jpg, pdf, png. Maximum allowed size: 4MB."
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_transaction__amount
msgid "Amount"
msgstr "Jumlah"

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__message_attachment_count
msgid "Attachment Count"
msgstr "Hitungan Lampiran"

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_payout__adyen_bank_account_id
msgid "Bank Account"
msgstr "Akun Bank"

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__bank_account_ids
#: model_terms:ir.ui.view,arch_db:adyen_platforms.adyen_account_view_form
msgid "Bank Accounts"
msgstr "Rekening Bank"

#. module: adyen_platforms
#: model_terms:ir.ui.view,arch_db:adyen_platforms.kyc_status_message
msgid "Bank Accounts:"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_bank_account__bank_code
msgid "Bank Code"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_bank_account__bank_statement
msgid "Bank Statement"
msgstr "Rekening Koran"

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_bank_account__bank_statement_filename
msgid "Bank Statement Filename"
msgstr ""

#. module: adyen_platforms
#: code:addons/adyen_platforms/models/adyen_account.py:0
#, python-format
msgid ""
"Bank statements must be greater than 10kB (except for PDFs) and smaller than"
" 10MB"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_bank_account__branch_code
msgid "Branch Code"
msgstr ""

#. module: adyen_platforms
#. openerp-web
#: code:addons/adyen_platforms/static/src/xml/adyen_account_templates.xml:0
#: model_terms:ir.ui.view,arch_db:adyen_platforms.adyen_account_view_form
#, python-format
msgid "Business"
msgstr ""

#. module: adyen_platforms
#. openerp-web
#: code:addons/adyen_platforms/static/src/js/adyen_account_views.js:0
#, python-format
msgid "Cancel"
msgstr "Batal"

#. module: adyen_platforms
#: model:ir.model.fields.selection,name:adyen_platforms.selection__adyen_transaction__status__chargeback
msgid "Chargeback"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields.selection,name:adyen_platforms.selection__adyen_transaction__status__chargebackreceived
msgid "Chargeback Received"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields.selection,name:adyen_platforms.selection__adyen_transaction__status__chargebackreversed
msgid "Chargeback Reversed"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields.selection,name:adyen_platforms.selection__adyen_transaction__status__chargebackreversedreceived
msgid "Chargeback Reversed Received"
msgstr ""

#. module: adyen_platforms
#. openerp-web
#: code:addons/adyen_platforms/static/src/xml/adyen_account_templates.xml:0
#, python-format
msgid "Chargebacks cost 7.5€ each."
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields.selection,name:adyen_platforms.selection__adyen_bank_account__account_type__checking
msgid "Checking"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__city
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_address_mixin__city
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_shareholder__city
#: model_terms:ir.ui.view,arch_db:adyen_platforms.adyen_account_view_form
#: model_terms:ir.ui.view,arch_db:adyen_platforms.adyen_bank_account_view_form
#: model_terms:ir.ui.view,arch_db:adyen_platforms.adyen_shareholder_view_form
msgid "City"
msgstr "Kota"

#. module: adyen_platforms
#: model:ir.model,name:adyen_platforms.model_res_company
msgid "Companies"
msgstr "Perusahaan"

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__company_id
msgid "Company"
msgstr "Perusahaan"

#. module: adyen_platforms
#. openerp-web
#: code:addons/adyen_platforms/static/src/js/adyen_account_views.js:0
#, python-format
msgid "Confirm your Adyen Account Creation"
msgstr ""

#. module: adyen_platforms
#. openerp-web
#: code:addons/adyen_platforms/static/src/xml/adyen_account_templates.xml:0
#: model:ir.model.fields.selection,name:adyen_platforms.selection__adyen_account__kyc_status__passed
#: model:ir.model.fields.selection,name:adyen_platforms.selection__adyen_bank_account__kyc_status__passed
#: model:ir.model.fields.selection,name:adyen_platforms.selection__adyen_shareholder__kyc_status__passed
#, python-format
msgid "Confirmed"
msgstr "Dikonfirmasi"

#. module: adyen_platforms
#: model_terms:ir.ui.view,arch_db:adyen_platforms.adyen_account_view_form
msgid "Contact"
msgstr "Kontak"

#. module: adyen_platforms
#. openerp-web
#: code:addons/adyen_platforms/static/src/xml/adyen_account_templates.xml:0
#, python-format
msgid "Contractual Relationship"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields.selection,name:adyen_platforms.selection__adyen_transaction__status__converted
msgid "Converted"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__country_id
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_address_mixin__country_id
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_bank_account__country_id
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_shareholder__country_id
#: model_terms:ir.ui.view,arch_db:adyen_platforms.adyen_account_view_form
#: model_terms:ir.ui.view,arch_db:adyen_platforms.adyen_bank_account_view_form
#: model_terms:ir.ui.view,arch_db:adyen_platforms.adyen_shareholder_view_form
msgid "Country"
msgstr "Negara"

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__country_code
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_address_mixin__country_code
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_bank_account__country_code
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_shareholder__country_code
msgid "Country Code"
msgstr "Kode Negara"

#. module: adyen_platforms
#. openerp-web
#: code:addons/adyen_platforms/static/src/js/adyen_account_views.js:0
#, python-format
msgid "Create"
msgstr "Buat"

#. module: adyen_platforms
#: model:ir.actions.act_window,name:adyen_platforms.adyen_account_action_create
msgid "Create an Adyen Account"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__create_uid
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_bank_account__create_uid
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_payout__create_uid
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_shareholder__create_uid
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_transaction__create_uid
msgid "Created by"
msgstr "Dibuat oleh"

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__create_date
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_bank_account__create_date
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_payout__create_date
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_shareholder__create_date
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_transaction__create_date
msgid "Created on"
msgstr "Dibuat pada"

#. module: adyen_platforms
#: model:ir.model.fields.selection,name:adyen_platforms.selection__adyen_transaction__status__creditfailed
msgid "Credit Failed"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields.selection,name:adyen_platforms.selection__adyen_transaction__status__credited
msgid "Credited"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_bank_account__currency_id
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_transaction__currency_id
msgid "Currency"
msgstr "Mata Uang"

#. module: adyen_platforms
#: model:ir.model.fields.selection,name:adyen_platforms.selection__adyen_payout__payout_schedule__day
msgid "Daily"
msgstr "Harian"

#. module: adyen_platforms
#. openerp-web
#: code:addons/adyen_platforms/static/src/xml/adyen_account_templates.xml:0
#: model:ir.model.fields.selection,name:adyen_platforms.selection__adyen_account__kyc_status__awaiting_data
#: model:ir.model.fields.selection,name:adyen_platforms.selection__adyen_bank_account__kyc_status__awaiting_data
#: model:ir.model.fields.selection,name:adyen_platforms.selection__adyen_shareholder__kyc_status__awaiting_data
#, python-format
msgid "Data to provide"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_transaction__date
msgid "Date"
msgstr "Tanggal"

#. module: adyen_platforms
#. openerp-web
#: code:addons/adyen_platforms/static/src/xml/adyen_account_templates.xml:0
#, python-format
msgid "Date of Birth"
msgstr "Tanggal Lahir"

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__date_of_birth
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_shareholder__date_of_birth
msgid "Date of birth"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields.selection,name:adyen_platforms.selection__adyen_transaction__status__debitfailed
msgid "Debit Failed"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields.selection,name:adyen_platforms.selection__adyen_transaction__status__debitedreversed
msgid "Debit Reversed"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields.selection,name:adyen_platforms.selection__adyen_transaction__status__debitreversedreceived
msgid "Debit Reversed Received"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields.selection,name:adyen_platforms.selection__adyen_transaction__status__debited
msgid "Debited"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_transaction__description
msgid "Description"
msgstr "Deskripsi"

#. module: adyen_platforms
#. openerp-web
#: code:addons/adyen_platforms/static/src/xml/adyen_account_templates.xml:0
#, python-format
msgid "Disclaimers"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__display_name
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_address_mixin__display_name
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_bank_account__display_name
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_id_mixin__display_name
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_payout__display_name
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_shareholder__display_name
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_transaction__display_name
#: model:ir.model.fields,field_description:adyen_platforms.field_res_company__display_name
msgid "Display Name"
msgstr "Nama Tampilan"

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__document_type
msgid "Document Type"
msgstr ""

#. module: adyen_platforms
#. openerp-web
#: code:addons/adyen_platforms/static/src/xml/adyen_account_templates.xml:0
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__doing_business_as
#, python-format
msgid "Doing Business As"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields.selection,name:adyen_platforms.selection__adyen_account__id_type__driving_license
#: model:ir.model.fields.selection,name:adyen_platforms.selection__adyen_id_mixin__id_type__driving_license
#: model:ir.model.fields.selection,name:adyen_platforms.selection__adyen_shareholder__id_type__driving_license
msgid "Driving License"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields.selection,name:adyen_platforms.selection__adyen_account__document_type__drivinglicense
msgid "Driving license"
msgstr ""

#. module: adyen_platforms
#. openerp-web
#: code:addons/adyen_platforms/static/src/xml/adyen_account_templates.xml:0
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__email
#, python-format
msgid "Email"
msgstr "Email"

#. module: adyen_platforms
#. openerp-web
#: code:addons/adyen_platforms/static/src/xml/adyen_account_templates.xml:0
#: model:ir.model.fields.selection,name:adyen_platforms.selection__adyen_account__kyc_status__failed
#: model:ir.model.fields.selection,name:adyen_platforms.selection__adyen_bank_account__kyc_status__failed
#: model:ir.model.fields.selection,name:adyen_platforms.selection__adyen_shareholder__kyc_status__failed
#, python-format
msgid "Failed"
msgstr "Gagal"

#. module: adyen_platforms
#. openerp-web
#: code:addons/adyen_platforms/static/src/xml/adyen_account_templates.xml:0
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__first_name
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_shareholder__first_name
#, python-format
msgid "First Name"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__message_follower_ids
msgid "Followers"
msgstr "Pengikut"

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__message_channel_ids
msgid "Followers (Channels)"
msgstr "Pengikut (Saluran)"

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__message_partner_ids
msgid "Followers (Partners)"
msgstr "Pengikut (Rekanan)"

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__full_name
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_shareholder__full_name
msgid "Full Name"
msgstr "Nama lengkap"

#. module: adyen_platforms
#: model:ir.model.fields.selection,name:adyen_platforms.selection__adyen_transaction__status__fundtransfer
msgid "Fund Transfer"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__house_number_or_name
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_address_mixin__house_number_or_name
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_shareholder__house_number_or_name
msgid "House Number Or Name"
msgstr ""

#. module: adyen_platforms
#: model_terms:ir.ui.view,arch_db:adyen_platforms.adyen_account_view_form
#: model_terms:ir.ui.view,arch_db:adyen_platforms.adyen_bank_account_view_form
#: model_terms:ir.ui.view,arch_db:adyen_platforms.adyen_shareholder_view_form
msgid "House number or name"
msgstr ""

#. module: adyen_platforms
#. openerp-web
#: code:addons/adyen_platforms/static/src/xml/adyen_account_templates.xml:0
#, python-format
msgid ""
"I confirm I have taken notice of and accept the following terms and "
"restrictions:"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_bank_account__iban
msgid "IBAN"
msgstr "IBAN"

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__id
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_address_mixin__id
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_bank_account__id
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_id_mixin__id
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_payout__id
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_shareholder__id
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_transaction__id
#: model:ir.model.fields,field_description:adyen_platforms.field_res_company__id
#: model:ir.model.fields.selection,name:adyen_platforms.selection__adyen_account__document_type__id
msgid "ID"
msgstr "ID"

#. module: adyen_platforms
#: model:ir.model.fields.selection,name:adyen_platforms.selection__adyen_account__id_type__id_card
#: model:ir.model.fields.selection,name:adyen_platforms.selection__adyen_id_mixin__id_type__id_card
#: model:ir.model.fields.selection,name:adyen_platforms.selection__adyen_shareholder__id_type__id_card
msgid "ID Card"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__document_number
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_shareholder__document_number
msgid "ID Number"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__id_back_filename
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_id_mixin__id_back_filename
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_shareholder__id_back_filename
msgid "Id Back Filename"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__id_front_filename
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_id_mixin__id_front_filename
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_shareholder__id_front_filename
msgid "Id Front Filename"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,help:adyen_platforms.field_adyen_account__message_needaction
#: model:ir.model.fields,help:adyen_platforms.field_adyen_account__message_unread
msgid "If checked, new messages require your attention."
msgstr "Jika dicentang, pesan baru memerlukan penanganan dan perhatian Anda."

#. module: adyen_platforms
#: model:ir.model.fields,help:adyen_platforms.field_adyen_account__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Jika dicentang, beberapa pesan mempunyai kesalahan dalam pengiriman."

#. module: adyen_platforms
#. openerp-web
#: code:addons/adyen_platforms/static/src/xml/adyen_account_templates.xml:0
#: model_terms:ir.ui.view,arch_db:adyen_platforms.adyen_account_view_form
#, python-format
msgid "Individual"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__message_is_follower
msgid "Is Follower"
msgstr "Pengikut"

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__is_business
msgid "Is a business"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__kyc_status
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_bank_account__kyc_status
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_shareholder__kyc_status
msgid "KYC Status"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__kyc_status_message
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_bank_account__kyc_status_message
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_shareholder__kyc_status_message
msgid "KYC Status Message"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account____last_update
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_address_mixin____last_update
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_bank_account____last_update
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_id_mixin____last_update
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_payout____last_update
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_shareholder____last_update
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_transaction____last_update
#: model:ir.model.fields,field_description:adyen_platforms.field_res_company____last_update
msgid "Last Modified on"
msgstr "Terakhir diubah pada"

#. module: adyen_platforms
#. openerp-web
#: code:addons/adyen_platforms/static/src/xml/adyen_account_templates.xml:0
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__last_name
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_shareholder__last_name
#, python-format
msgid "Last Name"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__write_uid
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_bank_account__write_uid
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_payout__write_uid
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_shareholder__write_uid
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_transaction__write_uid
msgid "Last Updated by"
msgstr "Terakhir diperbarui oleh"

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__write_date
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_bank_account__write_date
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_payout__write_date
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_shareholder__write_date
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_transaction__write_date
msgid "Last Updated on"
msgstr "Terakhir diperbarui pada"

#. module: adyen_platforms
#. openerp-web
#: code:addons/adyen_platforms/static/src/xml/adyen_account_templates.xml:0
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__legal_business_name
#, python-format
msgid "Legal Business Name"
msgstr ""

#. module: adyen_platforms
#. openerp-web
#: code:addons/adyen_platforms/static/src/xml/adyen_account_templates.xml:0
#: code:addons/adyen_platforms/static/src/xml/adyen_account_templates.xml:0
#, python-format
msgid "Legal Entity"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__message_main_attachment_id
msgid "Main Attachment"
msgstr "Lampiran Utama"

#. module: adyen_platforms
#: model:ir.model.fields.selection,name:adyen_platforms.selection__adyen_transaction__status__manualcorrected
msgid "Manual Corrected"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__message_has_error
msgid "Message Delivery error"
msgstr "Kesalahan Pengiriman Pesan"

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__message_ids
msgid "Messages"
msgstr "Pesan"

#. module: adyen_platforms
#: code:addons/adyen_platforms/models/adyen_account.py:0
#, python-format
msgid "Missing required fields: "
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields.selection,name:adyen_platforms.selection__adyen_payout__payout_schedule__month
msgid "Monthly"
msgstr "Bulanan"

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_payout__name
msgid "Name"
msgstr "Nama"

#. module: adyen_platforms
#: model_terms:ir.ui.view,arch_db:adyen_platforms.kyc_status_message
msgid "New KYC Status:"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_payout__next_scheduled_payout
msgid "Next scheduled payout"
msgstr ""

#. module: adyen_platforms
#: code:addons/adyen_platforms/models/adyen_account.py:0
#, python-format
msgid "No balance is currently awaitng payout."
msgstr ""

#. module: adyen_platforms
#: code:addons/adyen_platforms/models/adyen_account.py:0
#, python-format
msgid "No pending balance"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__message_needaction_counter
msgid "Number of Actions"
msgstr "Jumlah Tindakan"

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__message_has_error_counter
msgid "Number of errors"
msgstr "Jumlah kesalahan"

#. module: adyen_platforms
#: model:ir.model.fields,help:adyen_platforms.field_adyen_account__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Jumlah pesan yang butuh tindakan"

#. module: adyen_platforms
#: model:ir.model.fields,help:adyen_platforms.field_adyen_account__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Jumlah dari pesan dengan kesalahan pengiriman"

#. module: adyen_platforms
#: model:ir.model.fields,help:adyen_platforms.field_adyen_account__message_unread_counter
msgid "Number of unread messages"
msgstr "Jumlah pesan yang belum dibaca"

#. module: adyen_platforms
#. openerp-web
#: code:addons/adyen_platforms/static/src/xml/adyen_account_templates.xml:0
#, python-format
msgid "Onboarding and KYC cost 5€."
msgstr ""

#. module: adyen_platforms
#: model_terms:ir.ui.view,arch_db:adyen_platforms.adyen_bank_account_view_form
msgid "Owner Address"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_bank_account__owner_city
msgid "Owner City"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_bank_account__owner_country_id
msgid "Owner Country"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_bank_account__owner_house_number_or_name
msgid "Owner House Number or Name"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_bank_account__owner_name
msgid "Owner Name"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_bank_account__owner_state_id
msgid "Owner State"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_bank_account__owner_street
msgid "Owner Street"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_bank_account__owner_zip
msgid "Owner ZIP"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields.selection,name:adyen_platforms.selection__adyen_account__document_type__passport
#: model:ir.model.fields.selection,name:adyen_platforms.selection__adyen_account__id_type__passport
#: model:ir.model.fields.selection,name:adyen_platforms.selection__adyen_id_mixin__id_type__passport
#: model:ir.model.fields.selection,name:adyen_platforms.selection__adyen_shareholder__id_type__passport
msgid "Passport"
msgstr ""

#. module: adyen_platforms
#. openerp-web
#: code:addons/adyen_platforms/static/src/xml/adyen_account_templates.xml:0
#, python-format
msgid "Payment and processing fees are listed on"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields.selection,name:adyen_platforms.selection__adyen_transaction__status__payout
msgid "Payout"
msgstr ""

#. module: adyen_platforms
#: code:addons/adyen_platforms/models/adyen_account.py:0
#, python-format
msgid "Payout Request sent"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields.selection,name:adyen_platforms.selection__adyen_transaction__status__payoutreversed
msgid "Payout Reversed"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__payout_ids
#: model_terms:ir.ui.view,arch_db:adyen_platforms.adyen_account_view_form
msgid "Payouts"
msgstr ""

#. module: adyen_platforms
#. openerp-web
#: code:addons/adyen_platforms/static/src/xml/adyen_account_templates.xml:0
#, python-format
msgid "Payouts cost 0.20€ (EU) or 0.22$ (US) each."
msgstr ""

#. module: adyen_platforms
#. openerp-web
#: code:addons/adyen_platforms/static/src/xml/adyen_account_templates.xml:0
#, python-format
msgid "Payouts will be blocked until your application has been accepted."
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields.selection,name:adyen_platforms.selection__adyen_transaction__status__pendingcredit
msgid "Pending Credit"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields.selection,name:adyen_platforms.selection__adyen_transaction__status__pendingdebit
msgid "Pending Debit"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields.selection,name:adyen_platforms.selection__adyen_transaction__status__pendingfundtransfer
msgid "Pending Fund Transfer"
msgstr ""

#. module: adyen_platforms
#. openerp-web
#: code:addons/adyen_platforms/static/src/xml/adyen_account_templates.xml:0
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__phone_number
#, python-format
msgid "Phone Number"
msgstr "Nomor Telepon"

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__id_back
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_id_mixin__id_back
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_shareholder__id_back
msgid "Photo ID Back"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__id_front
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_id_mixin__id_front
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_shareholder__id_front
msgid "Photo ID Front"
msgstr ""

#. module: adyen_platforms
#: code:addons/adyen_platforms/models/adyen_account.py:0
#, python-format
msgid "Photo ID file size must be between 100kB (1kB for PDFs) and 4MB"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__id_type
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_id_mixin__id_type
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_shareholder__id_type
msgid "Photo ID type"
msgstr ""

#. module: adyen_platforms
#. openerp-web
#: code:addons/adyen_platforms/static/src/xml/adyen_account_templates.xml:0
#, python-format
msgid "Pricing"
msgstr "Harga"

#. module: adyen_platforms
#. openerp-web
#: code:addons/adyen_platforms/static/src/xml/adyen_account_templates.xml:0
#, python-format
msgid "Prohibited Products and Services List of our Processor"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__proxy_token
msgid "Proxy Token"
msgstr ""

#. module: adyen_platforms
#: model_terms:ir.ui.view,arch_db:adyen_platforms.kyc_status_message
msgid "Reason(s):"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_bank_account__bank_account_reference
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_shareholder__shareholder_reference
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_transaction__reference
msgid "Reference"
msgstr "Referensi"

#. module: adyen_platforms
#. openerp-web
#: code:addons/adyen_platforms/static/src/xml/adyen_account_templates.xml:0
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__registration_number
#, python-format
msgid "Registration Number"
msgstr ""

#. module: adyen_platforms
#: model_terms:ir.ui.view,arch_db:adyen_platforms.adyen_payout_view_form
msgid "Request a payout now"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields.selection,name:adyen_platforms.selection__adyen_bank_account__account_type__savings
msgid "Savings"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_payout__payout_schedule
msgid "Schedule"
msgstr "Jadwal"

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__shareholder_ids
#: model_terms:ir.ui.view,arch_db:adyen_platforms.adyen_account_view_form
msgid "Shareholders"
msgstr ""

#. module: adyen_platforms
#: model_terms:ir.ui.view,arch_db:adyen_platforms.kyc_status_message
msgid "Shareholders:"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__state_id
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_address_mixin__state_id
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_shareholder__state_id
#: model_terms:ir.ui.view,arch_db:adyen_platforms.adyen_account_view_form
#: model_terms:ir.ui.view,arch_db:adyen_platforms.adyen_bank_account_view_form
#: model_terms:ir.ui.view,arch_db:adyen_platforms.adyen_shareholder_view_form
msgid "State"
msgstr "Status"

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__state_code
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_address_mixin__state_code
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_shareholder__state_code
msgid "State Code"
msgstr "Kode Propinsi"

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__street
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_address_mixin__street
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_shareholder__street
#: model_terms:ir.ui.view,arch_db:adyen_platforms.adyen_account_view_form
#: model_terms:ir.ui.view,arch_db:adyen_platforms.adyen_bank_account_view_form
#: model_terms:ir.ui.view,arch_db:adyen_platforms.adyen_shareholder_view_form
msgid "Street"
msgstr "Jalan"

#. module: adyen_platforms
#. openerp-web
#: code:addons/adyen_platforms/static/src/xml/adyen_account_templates.xml:0
#, python-format
msgid "Submitted data"
msgstr ""

#. module: adyen_platforms
#: code:addons/adyen_platforms/models/adyen_account.py:0
#, python-format
msgid "Successfully sent payout request for %s"
msgstr ""

#. module: adyen_platforms
#. openerp-web
#: code:addons/adyen_platforms/static/src/xml/adyen_transactions_templates.xml:0
#, python-format
msgid "Sync Transactions"
msgstr ""

#. module: adyen_platforms
#: code:addons/adyen_platforms/models/adyen_account.py:0
#, python-format
msgid "The Adyen proxy is not reachable, please try again later."
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,help:adyen_platforms.field_adyen_account__country_code
#: model:ir.model.fields,help:adyen_platforms.field_adyen_address_mixin__country_code
#: model:ir.model.fields,help:adyen_platforms.field_adyen_bank_account__country_code
#: model:ir.model.fields,help:adyen_platforms.field_adyen_shareholder__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"Kode ISO negara dalam dua karakter.\n"
"Anda dapat menggunakan kolom ini untuk pencarian cepat."

#. module: adyen_platforms
#: model:ir.model.fields,help:adyen_platforms.field_adyen_payout__adyen_bank_account_id
msgid ""
"The bank account to which the payout is to be made. If left blank, a bank "
"account is automatically selected"
msgstr ""

#. module: adyen_platforms
#. openerp-web
#: code:addons/adyen_platforms/static/src/xml/adyen_account_templates.xml:0
#, python-format
msgid ""
"The payment processing services ordered by you by placing this order will be\n"
"            provided to you by Adyen N.V. (hereafter “Processor”), with which you are\n"
"            entering into a direct agreement by confirming this order. Odoo S.A.\n"
"            (hereafter “We /Us”) will assist and support you in your use of the services to be\n"
"            provided by the Processor and we will provide you first line assistance with and\n"
"            enable you to connect to the systems of Processor to be able to use its services.\n"
"            For this purpose, you hereby instruct Processor to provide us access to your data\n"
"            and setting in Processor’s systems which are used by Processor to provide the\n"
"            services and authorise us to manage these on your behalf."
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,help:adyen_platforms.field_adyen_account__state_code
#: model:ir.model.fields,help:adyen_platforms.field_adyen_address_mixin__state_code
#: model:ir.model.fields,help:adyen_platforms.field_adyen_shareholder__state_code
msgid "The state code."
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,help:adyen_platforms.field_adyen_account__document_number
#: model:ir.model.fields,help:adyen_platforms.field_adyen_shareholder__document_number
msgid ""
"The type of ID Number required depends on the country:\n"
"US: Social Security Number (9 digits or last 4 digits)\n"
"Canada: Social Insurance Number\n"
"Italy: Codice fiscale\n"
"Australia: Document Number"
msgstr ""

#. module: adyen_platforms
#: code:addons/adyen_platforms/models/adyen_account.py:0
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__transaction_ids
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_payout__transaction_ids
#, python-format
msgid "Transactions"
msgstr "Transaksi"

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__transactions_count
msgid "Transactions Count"
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_transaction__status
msgid "Type"
msgstr "Tipe"

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_bank_account__bank_account_uuid
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_shareholder__shareholder_uuid
msgid "UUID"
msgstr "UUID"

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__message_unread
msgid "Unread Messages"
msgstr "Pesan Belum Dibaca"

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Penghitung Pesan yang Belum Dibaca"

#. module: adyen_platforms
#: model:ir.model.fields.selection,name:adyen_platforms.selection__adyen_account__document_type__visa
msgid "Visa"
msgstr ""

#. module: adyen_platforms
#. openerp-web
#: code:addons/adyen_platforms/static/src/xml/adyen_account_templates.xml:0
#, python-format
msgid ""
"WARNING: Please note that the right to use the payment services is only for\n"
"            sales in your own name. You may not resell, hire or on any other basis allow third\n"
"            parties to use the payment services to enable such third parties to be paid for their\n"
"            services. You may not use the payment services for different types of product and\n"
"            services than as registered with your application. In particular you confirm that you\n"
"            will not use the payment services for any type of product or service appearing in the"
msgstr ""

#. module: adyen_platforms
#. openerp-web
#: code:addons/adyen_platforms/static/src/xml/adyen_account_templates.xml:0
#: model:ir.model.fields.selection,name:adyen_platforms.selection__adyen_account__kyc_status__pending
#: model:ir.model.fields.selection,name:adyen_platforms.selection__adyen_bank_account__kyc_status__pending
#: model:ir.model.fields.selection,name:adyen_platforms.selection__adyen_shareholder__kyc_status__pending
#, python-format
msgid "Waiting for validation"
msgstr "Menunggu untuk divalidasi"

#. module: adyen_platforms
#: code:addons/adyen_platforms/models/adyen_account.py:0
#, python-format
msgid ""
"We had troubles reaching Adyen, please retry later or contact the support if"
" the problem persists"
msgstr ""

#. module: adyen_platforms
#. openerp-web
#: code:addons/adyen_platforms/static/src/xml/adyen_account_templates.xml:0
#, python-format
msgid ""
"We will notify you when the status of the review changes, or if additional "
"data is required."
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields.selection,name:adyen_platforms.selection__adyen_payout__payout_schedule__week
msgid "Weekly"
msgstr "Mingguan"

#. module: adyen_platforms
#. openerp-web
#: code:addons/adyen_platforms/static/src/xml/adyen_account_templates.xml:0
#, python-format
msgid "You can start processing payments as soon as your application is sent."
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,help:adyen_platforms.field_adyen_bank_account__bank_statement
msgid ""
"You need to provide a bank statement to allow payouts.         The file must"
" be a bank statement, a screenshot of your online banking environment, a "
"letter from the bank or a cheque and must contain         the logo of the "
"bank or it's name in a unique font, the bank account details, the name of "
"the account holder.        Allowed formats: jpg, pdf, png. Maximum allowed "
"size: 10MB."
msgstr ""

#. module: adyen_platforms
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_account__zip
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_address_mixin__zip
#: model:ir.model.fields,field_description:adyen_platforms.field_adyen_shareholder__zip
#: model_terms:ir.ui.view,arch_db:adyen_platforms.adyen_account_view_form
#: model_terms:ir.ui.view,arch_db:adyen_platforms.adyen_bank_account_view_form
#: model_terms:ir.ui.view,arch_db:adyen_platforms.adyen_shareholder_view_form
msgid "ZIP"
msgstr "Kode Pos"

#. module: adyen_platforms
#. openerp-web
#: code:addons/adyen_platforms/static/src/xml/adyen_account_templates.xml:0
#, python-format
msgid "adyen.com/pricing"
msgstr ""

#. module: adyen_platforms
#. openerp-web
#: code:addons/adyen_platforms/static/src/xml/adyen_account_templates.xml:0
#: code:addons/adyen_platforms/static/src/xml/adyen_account_templates.xml:0
#, python-format
msgid "here"
msgstr "here"

#. module: adyen_platforms
#. openerp-web
#: code:addons/adyen_platforms/static/src/xml/adyen_account_templates.xml:0
#: code:addons/adyen_platforms/static/src/xml/adyen_account_templates.xml:0
#, python-format
msgid "to download and review)"
msgstr ""
