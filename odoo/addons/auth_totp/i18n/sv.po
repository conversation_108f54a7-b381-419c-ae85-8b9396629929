# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_totp
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON> <and<PERSON>.<EMAIL>>, 2021
# <PERSON>, 2022
# <PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-08-19 09:43+0000\n"
"PO-Revision-Date: 2020-09-07 08:10+0000\n"
"Last-Translator: <PERSON>, 2022\n"
"Language-Team: Swedish (https://www.transifex.com/odoo/teams/41243/sv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sv\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: auth_totp
#: code:addons/auth_totp/controllers/home.py:0
#, python-format
msgid "%(browser)s on %(platform)s"
msgstr "%(browser)s på %(platform)s"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
msgid "(Disable two-factor authentication)"
msgstr "(Inaktivera tvåfaktorsautentisering)"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
msgid ""
"<i class=\"fa fa-2x fa-mobile pull-left\"/>\n"
"                        Open the two-factor authentication app on your\n"
"                        device to obtain a code and verify your identity"
msgstr ""
"<i class=\"fa fa-2x fa-mobile pull-left\"/>\n"
"                        Öppna tvåfaktorsautentiseringsappen på din\n"
"                        enhet för att få en kod och verifiera din identitet"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
msgid ""
"<i class=\"fa fa-check-circle\"/>\n"
"                                Two-factor authentication enabled"
msgstr ""
"<i class=\"fa fa-check-circle\"/>\n"
"                                Tvåfaktorsautentisering aktiverad"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
msgid ""
"<i class=\"fa fa-question-circle text-primary\" title=\"If checked, you "
"won't be asked for two-factor authentication codes with this device.\"/>"
msgstr ""
"<i class=\"fa fa-question-circle text-primary\" title=\"If checked, you "
"won't be asked for two-factor authentication codes with this device.\"/>"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
msgid ""
"<i class=\"fa fa-warning\"/>\n"
"                                Two-factor authentication not enabled"
msgstr ""
"<i class=\"fa fa-warning\"/>\n"
"                                Tvåfaktorsautentisering ej aktiverad"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid ""
"<span class=\"alert alert-info\" role=\"status\">\n"
"                            <i class=\"fa fa-warning\"/>\n"
"                            Two-factor authentication not enabled\n"
"                        </span>"
msgstr ""
"<span class=\"alert alert-info\" role=\"status\">\n"
"                            <i class=\"fa fa-warning\"/>\n"
"                            Tvåfaktorsautentisering ej aktiverad\n"
"                        </span>"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid ""
"<span class=\"text-success\">\n"
"                            <i class=\"fa fa-check-circle\"/>\n"
"                            Two-factor authentication enabled\n"
"                        </span>"
msgstr ""
"<span class=\"text-success\">\n"
"                            <i class=\"fa fa-check-circle\"/>\n"
"                            Tvåfaktorsautentisering aktiverad\n"
"                        </span>"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
msgid "Added On"
msgstr ""

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid ""
"After scanning the barcode, the app will display a 6-digit code that you\n"
"                                should enter below. Don't worry if the code changes in the app,\n"
"                                it stays valid a bit longer."
msgstr ""
"Efter att streckkoden scannats så kommer appen att visa en 6-siffrig kod som du\n"
"ska skriva av nedan. Var inte orolig om koden ändras i appen, den kommer att\n"
"vara giltig en stund till."

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
msgid ""
"Are you sure? Two-factor authentication will be required again on all your "
"devices"
msgstr ""
"Är du säker? Tvåfaktorsautentisering kommer fortsättningsvis att\n"
"krävas på alla dina enheter"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
msgid "Authentication Code (6 digits)"
msgstr "Autentiseringskod (6-siffrig)"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Cancel"
msgstr "Avbryt"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__create_uid
msgid "Created by"
msgstr "Skapad av"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__create_date
msgid "Created on"
msgstr "Skapad den"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
msgid "Device Name"
msgstr "Enhetsnamn"

#. module: auth_totp
#: model:ir.actions.server,name:auth_totp.action_disable_totp
msgid "Disable TOTP on users"
msgstr ""

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__display_name
#: model:ir.model.fields,field_description:auth_totp.field_ir_http__display_name
#: model:ir.model.fields,field_description:auth_totp.field_res_users__display_name
msgid "Display Name"
msgstr "Visningsnamn"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
msgid "Don't ask again for this device"
msgstr "Fråga inte igen för den här enheten"

#. module: auth_totp
#: code:addons/auth_totp/models/res_users.py:0
#, python-format
msgid "Enable Two-Factor Authentication"
msgstr "Aktivera tvåfaktorsautentisering"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Enable two-factor authentication"
msgstr "Aktivera tvåfaktorsautentisering"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Enter the 6-digit code from your app"
msgstr "Skriv in den 6-siffriga koden från din app"

#. module: auth_totp
#: model:ir.model,name:auth_totp.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP-rutt"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__id
#: model:ir.model.fields,field_description:auth_totp.field_ir_http__id
#: model:ir.model.fields,field_description:auth_totp.field_res_users__id
msgid "ID"
msgstr "ID"

#. module: auth_totp
#: code:addons/auth_totp/controllers/home.py:0
#, python-format
msgid "Invalid authentication code format."
msgstr "Felaktigt autentiseringsformat på koden."

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard____last_update
#: model:ir.model.fields,field_description:auth_totp.field_ir_http____last_update
#: model:ir.model.fields,field_description:auth_totp.field_res_users____last_update
msgid "Last Modified on"
msgstr "Senast redigerad"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__write_uid
msgid "Last Updated by"
msgstr "Senast uppdaterad av"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__write_date
msgid "Last Updated on"
msgstr "Senast uppdaterad"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Or enter the secret code manually:"
msgstr ""

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__qrcode
msgid "Qrcode"
msgstr ""

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
msgid "Revoke"
msgstr ""

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
msgid "Revoke All"
msgstr "Återkalla alla"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid ""
"Scan the image below with the authenticator app on your phone.<br/>\n"
"                                If you cannot scan the barcode, here are some alternative options:"
msgstr ""

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Scan this barcode with your app"
msgstr ""

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__secret
msgid "Secret"
msgstr "Hemlighet"

#. module: auth_totp
#: code:addons/auth_totp/models/res_users.py:0
#, python-format
msgid "The verification code should only contain numbers"
msgstr "Verifieringskoden bör endast innehålla siffror"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_res_users__totp_secret
msgid "Totp Secret"
msgstr ""

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
msgid "Trusted Device"
msgstr ""

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_res_users__totp_trusted_device_ids
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
msgid "Trusted Devices"
msgstr ""

#. module: auth_totp
#: model:ir.model,name:auth_totp.model_auth_totp_wizard
msgid "Two-Factor Setup Wizard"
msgstr ""

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
msgid "Two-factor Authentication"
msgstr "Tvåfaktorsautentisering"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_res_users__totp_enabled
msgid "Two-factor authentication"
msgstr "Tvåfaktorsautentisering"

#. module: auth_totp
#: code:addons/auth_totp/models/res_users.py:0
#, python-format
msgid "Two-factor authentication already enabled"
msgstr "Tvåfaktorsautentisering redan aktiverad"

#. module: auth_totp
#: code:addons/auth_totp/models/res_users.py:0
#, python-format
msgid "Two-factor authentication can only be enabled for yourself"
msgstr "Tvåfaktorsautentisering kan endast aktiveras för dig själv"

#. module: auth_totp
#: code:addons/auth_totp/models/res_users.py:0
#, python-format
msgid "Two-factor authentication disabled for user(s) %s"
msgstr "Tvåfaktorsautentisering inaktiverad för användare %s"

#. module: auth_totp
#: code:addons/auth_totp/models/res_users.py:0
#, python-format
msgid "Two-factor authentication is now enabled."
msgstr ""

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__url
msgid "Url"
msgstr "Url"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__user_id
msgid "User"
msgstr "Användare"

#. module: auth_totp
#: model:ir.model,name:auth_totp.model_res_users
msgid "Users"
msgstr "Användare"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__code
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Verification Code"
msgstr "Verifieringskod"

#. module: auth_totp
#: code:addons/auth_totp/controllers/home.py:0
#: code:addons/auth_totp/models/res_users.py:0
#, python-format
msgid "Verification failed, please double-check the 6-digit code"
msgstr ""
"Verifieringen misslyckades, var vänlig dubbelkolla den 6-siffriga koden"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
msgid "Verify"
msgstr "Verifiera"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
msgid "What is this?"
msgstr ""

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Your two-factor secret:"
msgstr "Din två-faktor hemlighet:"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "show the code"
msgstr "visa koden"
