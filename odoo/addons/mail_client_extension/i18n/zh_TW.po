# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mail_client_extension
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-09-29 13:45+0000\n"
"PO-Revision-Date: 2020-09-07 08:14+0000\n"
"Last-Translator: 敬雲 林 <<EMAIL>>, 2021\n"
"Language-Team: Chinese (Taiwan) (https://www.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: mail_client_extension
#: model_terms:ir.ui.view,arch_db:mail_client_extension.app_auth
msgid "Allow"
msgstr "允許"

#. module: mail_client_extension
#: model:ir.model,name:mail_client_extension.model_res_partner
msgid "Contact"
msgstr "聯繫人"

#. module: mail_client_extension
#: model_terms:ir.ui.view,arch_db:mail_client_extension.app_auth
msgid "Deny"
msgstr ""

#. module: mail_client_extension
#: model:ir.model.fields,field_description:mail_client_extension.field_crm_lead__display_name
#: model:ir.model.fields,field_description:mail_client_extension.field_ir_http__display_name
#: model:ir.model.fields,field_description:mail_client_extension.field_res_partner__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: mail_client_extension
#: model:ir.model,name:mail_client_extension.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP 路由"

#. module: mail_client_extension
#: model:ir.model.fields,field_description:mail_client_extension.field_res_partner__iap_enrich_info
#: model:ir.model.fields,field_description:mail_client_extension.field_res_users__iap_enrich_info
msgid "IAP Enrich Info"
msgstr ""

#. module: mail_client_extension
#: model:ir.model.fields,field_description:mail_client_extension.field_crm_lead__id
#: model:ir.model.fields,field_description:mail_client_extension.field_ir_http__id
#: model:ir.model.fields,field_description:mail_client_extension.field_res_partner__id
msgid "ID"
msgstr "ID"

#. module: mail_client_extension
#: model:ir.model.fields,field_description:mail_client_extension.field_crm_lead____last_update
#: model:ir.model.fields,field_description:mail_client_extension.field_ir_http____last_update
#: model:ir.model.fields,field_description:mail_client_extension.field_res_partner____last_update
msgid "Last Modified on"
msgstr "最後修改於"

#. module: mail_client_extension
#: model:ir.model,name:mail_client_extension.model_crm_lead
msgid "Lead/Opportunity"
msgstr "線索/商機"

#. module: mail_client_extension
#: model_terms:ir.ui.view,arch_db:mail_client_extension.app_auth
msgid "Let"
msgstr ""

#. module: mail_client_extension
#: model:ir.actions.server,name:mail_client_extension.lead_creation_prefilled_action
msgid "Redirection to the lead creation form with prefilled info"
msgstr "返回線索，使用預設資訊創建表單"

#. module: mail_client_extension
#: model:ir.model.fields,help:mail_client_extension.field_res_partner__iap_enrich_info
#: model:ir.model.fields,help:mail_client_extension.field_res_users__iap_enrich_info
msgid "Stores additional info retrieved from IAP in JSON"
msgstr ""

#. module: mail_client_extension
#: model_terms:ir.ui.view,arch_db:mail_client_extension.app_auth
msgid "access your Odoo database?"
msgstr ""
