# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_appraisal
# 
# Translators:
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2020
# <PERSON>, 2020
# <AUTHOR> <EMAIL>, 2020
# <PERSON> <<EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2020
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2020
# <PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-11-27 15:44+0000\n"
"PO-Revision-Date: 2020-09-07 08:20+0000\n"
"Last-Translator: 苏州远鼎 <<EMAIL>>, 2021\n"
"Language-Team: Chinese (China) (https://www.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_appraisal
#: model:mail.template,report_name:hr_appraisal.mail_template_appraisal_confirm_employee
#: model:mail.template,report_name:hr_appraisal.mail_template_appraisal_confirm_manager
#: model:mail.template,report_name:hr_appraisal.mail_template_appraisal_request
#: model:mail.template,report_name:hr_appraisal.mail_template_appraisal_request_from_employee
msgid "${(object.name or '').replace('/','_')}"
msgstr "${(object.name or '').replace('/','_')}"

#. module: hr_appraisal
#: model:mail.template,subject:hr_appraisal.mail_template_appraisal_confirm_employee
#: model:mail.template,subject:hr_appraisal.mail_template_appraisal_confirm_manager
msgid "${object.employee_id.name}: Appraisal Confirmed"
msgstr "${object.employee_id.name}: 评估已确认"

#. module: hr_appraisal
#: model:mail.template,subject:hr_appraisal.mail_template_appraisal_request_from_employee
msgid "${object.name} requests an Appraisal"
msgstr "${object.name} 要求评估"

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
#, python-format
msgid "%s Goals"
msgstr "%s 目标"

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal_plan.py:0
#, python-format
msgid "%s months %s"
msgstr "%s 月份 %s"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_goal__progression__100
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_kanban
msgid "100 %"
msgstr "100 %"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_goal__progression__25
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_kanban
msgid "25 %"
msgstr "25 %"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__module_hr_appraisal_survey
msgid "360 Feedback"
msgstr "360 反馈"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_goal__progression__50
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_kanban
msgid "50 %"
msgstr "50 %"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_goal__progression__75
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_kanban
msgid "75 %"
msgstr "75 %"

#. module: hr_appraisal
#: model:mail.template,body_html:hr_appraisal.mail_template_appraisal_confirm_employee
#: model:mail.template,body_html:hr_appraisal.mail_template_appraisal_confirm_manager
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Dear ${ctx.get('employee_to_name', 'employee')},\n"
"                        <br/><br/>\n"
"                        An appraisal was requested.\n"
"                        <br/>\n"
"                        Please schedule an appraisal date together.\n"
"                        <br/><br/>\n"
"                        Thank you!\n"
"                        The HR department\n"
"                        <br/><br/>\n"
"                        % if ctx.get('recipient_users'):\n"
"                        <p style=\"margin: 16px 0px 16px 0px;\">\n"
"                            <a href=\"${ctx['url']}\" style=\"background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                View Appraisal\n"
"                            </a>\n"
"                        </p>\n"
"                        % endif\n"
"                        <br/><br/>\n"
"                        <tr><td style=\"padding:15px 20px 10px 20px;\">${(object.signature or '')| safe}</td></tr>\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        亲爱的 ${ctx.get('employee_to_name', 'employee')},\n"
"                        <br/><br/>\n"
"                        要求进行评估。\n"
"                        <br/>\n"
"                        请一起安排一个评估日期。\n"
"                        <br/><br/>\n"
"                        谢谢！\n"
"                        人力资源部\n"
"                        <br/><br/>\n"
"                        % if ctx.get('recipient_users'):\n"
"                        <p style=\"margin: 16px 0px 16px 0px;\">\n"
"                            <a href=\"${ctx['url']}\" style=\"background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                查看评估\n"
"                            </a>\n"
"                        </p>\n"
"                        % endif\n"
"                        <br/><br/>\n"
"                        <tr><td style=\"padding:15px 20px 10px 20px;\">${(object.signature or '')| safe}</td></tr>\n"
"                    </p>\n"
"                </div>\n"
"            "

#. module: hr_appraisal
#: model:mail.template,body_html:hr_appraisal.mail_template_appraisal_request_from_employee
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Dear ${ctx['partner_to_name']},\n"
"                        <br/><br/>\n"
"                        I wish to request an appraisal.<br/>\n"
"                        % if ctx.get('recipient_users'):\n"
"                        Here is the link of my appraisal:\n"
"                        <p style=\"margin: 16px 0px 16px 0px;\">\n"
"                            <a href=\"${ctx['url']}\" style=\"background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                View Appraisal\n"
"                            </a>\n"
"                        </p>\n"
"                        % endif\n"
"                        <br/><br/>\n"
"                        <tr><td style=\"padding:15px 20px 10px 20px;\">${(object.employee_id.user_id.signature or '')| safe}</td></tr>\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        亲爱的 ${ctx['partner_to_name']},\n"
"                        <br/><br/>\n"
"                        我想要求一个评估。<br/>\n"
"                        % if ctx.get('recipient_users'):\n"
"                        以下是我评估的链接：\n"
"                        <p style=\"margin: 16px 0px 16px 0px;\">\n"
"                            <a href=\"${ctx['url']}\" style=\"background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                查看评估\n"
"                            </a>\n"
"                        </p>\n"
"                        % endif\n"
"                        <br/><br/>\n"
"                        <tr><td style=\"padding:15px 20px 10px 20px;\">${(object.employee_id.user_id.signature or '')| safe}</td></tr>\n"
"                    </p>\n"
"                </div>"

#. module: hr_appraisal
#: model:mail.template,body_html:hr_appraisal.mail_template_appraisal_request
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Dear ${ctx['partner_to_name']},\n"
"                        <br/><br/>\n"
"                        I would like to start an Appraisal for you.\n"
"\n"
"                        % if ctx.get('recipient_users'):\n"
"                        <p style=\"margin: 16px 0px 16px 0px;\">\n"
"                            <a href=\"${ctx['url']}\" style=\"background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                View Appraisal\n"
"                            </a>\n"
"                        </p>\n"
"                        % endif\n"
"                        <br/><br/>\n"
"                        <tr><td style=\"padding:15px 20px 10px 20px;\">${(object.signature or '')| safe}</td></tr>\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        亲爱的 ${ctx['partner_to_name']},\n"
"                        <br/><br/>\n"
"                        我想为你开始一个评估。\n"
"\n"
"                        % if ctx.get('recipient_users'):\n"
"                        <p style=\"margin: 16px 0px 16px 0px;\">\n"
"                            <a href=\"${ctx['url']}\" style=\"background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                查看评估\n"
"                            </a>\n"
"                        </p>\n"
"                        % endif\n"
"                        <br/><br/>\n"
"                        <tr><td style=\"padding:15px 20px 10px 20px;\">${(object.signature or '')| safe}</td></tr>\n"
"                    </p>\n"
"                </div>\n"
"            "

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_kanban
msgid "<span class=\"bg-info\">Ready</span>"
msgstr "<span class=\"bg-info\">准备</span>"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_kanban
msgid "<span class=\"bg-secondary\">Canceled</span>"
msgstr "<span class=\"bg-secondary\">取消</span>"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_kanban
msgid "<span class=\"bg-success\">Done</span>"
msgstr "<span class=\"bg-success\">完成 </span>"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid ""
"<span class=\"col-3 text-right\" attrs=\"{'invisible': ['|', ('employee_feedback_published', '=', True), ('state', '=', 'new')]}\">Unpublished</span>\n"
"                                <span class=\"col-3 text-right\" attrs=\"{'invisible': ['|', ('employee_feedback_published', '=', False), ('state', '=', 'new')]}\">Published</span>"
msgstr ""
"<span class=\"col-3 text-right\" attrs=\"{'invisible': ['|', ('employee_feedback_published', '=', True), ('state', '=', 'new')]}\">发表</span>\n"
"                                <span class=\"col-3 text-right\" attrs=\"{'invisible': ['|', ('employee_feedback_published', '=', False), ('state', '=', 'new')]}\">发表</span>"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid ""
"<span class=\"col-3 text-right\" attrs=\"{'invisible': ['|', ('manager_feedback_published', '=', True), ('state', '=', 'new')]}\">Unpublished</span>\n"
"                                <span class=\"col-3 text-right\" attrs=\"{'invisible': ['|', ('manager_feedback_published', '=', False), ('state', '=', 'new')]}\">Published</span>"
msgstr ""
"<span class=\"col-3 text-right\" attrs=\"{'invisible': ['|', ('manager_feedback_published', '=', True), ('state', '=', 'new')]}\">发表</span>\n"
"                                <span class=\"col-3 text-right\" attrs=\"{'invisible': ['|', ('manager_feedback_published', '=', False), ('state', '=', 'new')]}\">发表</span>"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid ""
"<span class=\"o_form_label\">360 Feedback</span>\n"
"                                <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" role=\"img\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"o_form_label\">360 反馈</span>\n"
"                                <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" role=\"img\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\"/>"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid ""
"<span class=\"o_form_label\">Appraisal Plans</span>\n"
"                                <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" role=\"img\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"o_form_label\">评估计划</span>\n"
"                                <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" role=\"img\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\"/>"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "<span class=\"o_form_label\">Confirmation Email Template</span>"
msgstr "<span class=\"o_form_label\">确认电子邮件模板</span>"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid ""
"<span class=\"o_form_label\">Feedback Templates</span>\n"
"                                <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" role=\"img\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"o_form_label\">反馈模板</span>\n"
"                                <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" role=\"img\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\"/>"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_employee_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_users_view_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                            Last Appraisal\n"
"                        </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                            上次评估\n"
"                        </span>"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid ""
"<span class=\"o_stat_text\">Employee's</span>\n"
"                            <span class=\"o_stat_text\">Goals</span>"
msgstr ""
"<span class=\"o_stat_text\">员工</span>\n"
"                            <span class=\"o_stat_text\">目标</span>"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid ""
"<span class=\"o_stat_value\" attrs=\"{'invisible': [('meeting_id', '=', False)]}\">1 Meeting</span>\n"
"                            <span class=\"o_stat_value\" attrs=\"{'invisible': [('meeting_id', '!=', False)]}\">No Meeting</span>"
msgstr ""
"<span class=\"o_stat_value\" attrs=\"{'invisible': [('meeting_id', '=', False)]}\">1 会议</span>\n"
"                            <span class=\"o_stat_value\" attrs=\"{'invisible': [('meeting_id', '!=', False)]}\">没有会议</span>"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "<span class=\"o_stat_value\">Last Appraisal</span>"
msgstr "<span class=\"o_stat_value\">上次评估</span>"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_kanban
msgid "<strong><span>Final Interview: </span></strong>"
msgstr "<strong><span>最后一次面试: </span></strong>"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_needaction
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_needaction
msgid "Action Needed"
msgstr "需要行动"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__active
msgid "Active"
msgstr "启用"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_ids
msgid "Activities"
msgstr "活动"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_exception_decoration
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "活动异常勋章"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_state
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_state
msgid "Activity State"
msgstr "活动状态"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_type_icon
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_type_icon
msgid "Activity Type Icon"
msgstr "活动类型图表"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.request_appraisal_view_form
msgid "Add existing contacts..."
msgstr "添加现有联系人..."

#. module: hr_appraisal
#: model:res.groups,name:hr_appraisal.group_hr_appraisal_manager
msgid "Administrator"
msgstr "管理员"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_plan__event
msgid "After"
msgstr "之后"

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.hr_appraisal_action
#: model:ir.actions.act_window,name:hr_appraisal.open_view_hr_appraisal_tree
#: model:ir.actions.act_window,name:hr_appraisal.open_view_hr_appraisal_tree2
#: model:ir.ui.menu,name:hr_appraisal.menu_hr_appraisal_root
#: model:ir.ui.menu,name:hr_appraisal.menu_open_view_hr_appraisal_tree
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_view_activity
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_tree
msgid "Appraisal"
msgstr "评价"

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.action_appraisal_report_all
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_graph
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_pivot
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Appraisal Analysis"
msgstr "评价分析"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_appraisal_note
msgid "Appraisal Assessment Note"
msgstr "评估评估说明"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company__appraisal_confirm_employee_mail_template
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__appraisal_confirm_employee_mail_template
msgid "Appraisal Confirm Employee Mail Template"
msgstr "评估确认员工邮件模板"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company__appraisal_confirm_manager_mail_template
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__appraisal_confirm_manager_mail_template
msgid "Appraisal Confirm Manager Mail Template"
msgstr "评估确认经理邮件模板"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__date_close
msgid "Appraisal Deadline"
msgstr "评价截止时间"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__employee_id
msgid "Appraisal Employee"
msgstr "评估的员工"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company__appraisal_employee_feedback_template
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__appraisal_employee_feedback_template
msgid "Appraisal Employee Feedback Template"
msgstr "考核员工反馈模板"

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
#, python-format
msgid "Appraisal Form to Fill"
msgstr "待填写评价表单"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_appraisal_goal
msgid "Appraisal Goal"
msgstr "评估目标"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company__appraisal_manager_feedback_template
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__appraisal_manager_feedback_template
msgid "Appraisal Manager Feedback Template"
msgstr "评估经理反馈模板"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company__appraisal_plan_ids
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_plan_tree
msgid "Appraisal Plan"
msgstr "评估计划"

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.action_hr_appraisal_plan
#: model:ir.ui.menu,name:hr_appraisal.menu_appraisal_plan
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "Appraisal Plans"
msgstr "评估计划"

#. module: hr_appraisal
#: model:mail.template,subject:hr_appraisal.mail_template_appraisal_request
msgid "Appraisal Requested"
msgstr "评估已请求"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_report__state__pending
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Appraisal Sent"
msgstr "评价送出"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_appraisal_report
msgid "Appraisal Statistics"
msgstr "评价统计"

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal_plan.py:0
#: code:addons/hr_appraisal/models/hr_appraisal_plan.py:0
#: code:addons/hr_appraisal/wizard/request_appraisal.py:0
#, python-format
msgid "Appraisal to Confirm and Send"
msgstr "确认和发送评估"

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.hr_appraisal_action_from_department
msgid "Appraisal to start"
msgstr "待开始的评价"

#. module: hr_appraisal
#: model:ir.actions.server,name:hr_appraisal.ir_cron_scheduler_appraisal_ir_actions_server
#: model:ir.cron,cron_name:hr_appraisal.ir_cron_scheduler_appraisal
#: model:ir.cron,name:hr_appraisal.ir_cron_scheduler_appraisal
msgid "Appraisal: Run employee appraisal"
msgstr "评估：运行员工评估"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_job_view_form
msgid "Appraisals"
msgstr "评价"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_department__appraisals_to_process_count
msgid "Appraisals to Process"
msgstr "待处理评价"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Archived"
msgstr "已归档"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "Ask to fill a survey to other employees"
msgstr "要求向其他员工填写调查"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__assessment_note
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company__assessment_note_ids
msgid "Assessment Note"
msgstr "评估说明"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_attachment_count
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_attachment_count
msgid "Attachment Count"
msgstr "附件数量"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__attachment_ids
msgid "Attachments"
msgstr "附件"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__author_id
msgid "Author"
msgstr "作者"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_request_appraisal__author_id
msgid "Author of the message."
msgstr "信息的作者。"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "Automatic email sent after confirm the appraisal"
msgstr "确认评估后发送的自动电子邮件"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company__appraisal_plan
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__appraisal_plan
msgid "Automatically Generate Appraisals"
msgstr "自动生成评估"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "Automatically generate appraisals"
msgstr "自动生成评估"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_employee_base
msgid "Basic Employee"
msgstr "基本员工"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_calendar_event
msgid "Calendar Event"
msgstr "日历事件"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__can_see_employee_publish
msgid "Can See Employee Publish"
msgstr "可以查看员工发布"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__can_see_manager_publish
msgid "Can See Manager Publish"
msgstr "可以看到管理器发布"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.request_appraisal_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Cancel"
msgstr "取消"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_departure_wizard__cancel_appraisal
msgid "Cancel Future Appraisals"
msgstr "取消未来评估"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_departure_wizard_view_form
msgid "Cancel all appraisals after this date"
msgstr "在此日期之后取消所有评估"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal__state__cancel
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_report__state__cancel
msgid "Cancelled"
msgstr "已取消"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__manager_id
msgid "Challenged By"
msgstr "挑战者"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__color
msgid "Color"
msgstr "颜色"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_res_company
msgid "Companies"
msgstr "公司"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__company_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__company_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_plan__company_id
msgid "Company"
msgstr "公司"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.request_appraisal_view_form
msgid "Compose Email"
msgstr "撰写邮件"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_res_config_settings
msgid "Config Settings"
msgstr "配置设置"

#. module: hr_appraisal
#: model:ir.ui.menu,name:hr_appraisal.menu_hr_appraisal_configuration
msgid "Configuration"
msgstr "基础配置"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "Configure Feedback Templates by Job"
msgstr "按作业配置反馈模板"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Confirm"
msgstr "确认"

#. module: hr_appraisal
#: code:addons/hr_appraisal/wizard/request_appraisal.py:0
#, python-format
msgid "Confirm and send appraisal of %s"
msgstr "确认并发送 %s 的评估"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal__state__pending
msgid "Confirmed"
msgstr "已确认"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__body
msgid "Contents"
msgstr "内容"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__create_date
msgid "Create Date"
msgstr "创建日期"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_plan__duration
msgid "Create a New Appraisal"
msgstr "创建新评估"

#. module: hr_appraisal
#: model_terms:ir.actions.act_window,help:hr_appraisal.open_view_hr_appraisal_tree
msgid "Create a new appraisal"
msgstr "创建一个新的评价"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__create_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__create_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__create_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_plan__create_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__create_uid
msgid "Created by"
msgstr "创建人"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__create_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__create_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__create_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_plan__create_date
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__create_date
msgid "Created on"
msgstr "创建时间"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Creation Date"
msgstr "创建日期"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Date"
msgstr "日期"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__deadline
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__deadline
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_search
msgid "Deadline"
msgstr "截止日期"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_view_activity
msgid "Deadline:"
msgstr "截止日期"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_kanban
msgid "Delete"
msgstr "删除"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_department
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__department_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__department_id
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Department"
msgstr "部门"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_departure_wizard
msgid "Departure Wizard"
msgstr "离职向导"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__description
msgid "Description"
msgstr "说明"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__deadline
msgid "Desired Deadline"
msgstr "所需的最后期限"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_calendar_event__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_plan__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_department__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_departure_wizard__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_base__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_public__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_job__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_res_users__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal__state__done
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_report__state__done
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Done"
msgstr "完成"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_kanban
msgid "Dropdown menu"
msgstr "下拉菜单"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_request_appraisal__email_from
msgid "Email address of the sender"
msgstr "发件人的电子邮件地址"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_employee
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__employee_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__employee_id
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Employee"
msgstr "员工"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_appraisal
msgid "Employee Appraisal"
msgstr "员工评价"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_appraisal_plan
msgid "Employee Appraisal Plan"
msgstr "员工考核计划"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__employee_feedback
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Employee Feedback"
msgstr "员工反馈"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__employee_feedback_published
msgid "Employee Feedback Published"
msgstr "已发布员工反馈"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__employee_feedback_template
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_job__employee_feedback_template
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_job_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "Employee Feedback Template"
msgstr "员工反馈模板"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__name
msgid "Employee Name"
msgstr "员工姓名"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Employee's Name"
msgstr "员工姓名"

#. module: hr_appraisal
#: model:ir.ui.menu,name:hr_appraisal.hr_appraisal_menu_employee
msgid "Employees"
msgstr "员工"

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.action_hr_appraisal_note
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__assessment_note_ids
#: model:ir.ui.menu,name:hr_appraisal.menu_hr_appraisal_note
msgid "Evaluation Scale"
msgstr "评估规模"

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/res_company.py:0
#, python-format
msgid "Exceeds expectations"
msgstr "超出预期"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Extended Filters..."
msgstr "扩展筛选..."

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "Feedback Templates"
msgstr "反馈模板"

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
#, python-format
msgid "Fill appraisal for <a href=\"#\" data-oe-model=\"%s\" data-oe-id=\"%s\">%s</a>"
msgstr "为填写评估<a href=\"#\" data-oe-model=\"%s\" data-oe-id=\"%s\">%s</a>"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__date_final_interview
msgid "Final Interview"
msgstr "最终面试"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Final Interview Date"
msgstr "最终面谈日期"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_follower_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_follower_ids
msgid "Followers"
msgstr "关注者"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_channel_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_channel_ids
msgid "Followers (Channels)"
msgstr "关注者(频道)"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_partner_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_partner_ids
msgid "Followers (Partners)"
msgstr "关注者(业务伙伴)"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__activity_type_icon
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "完美的图标，例如FA任务"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__email_from
msgid "From"
msgstr "从"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Future Activities"
msgstr "未来活动"

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.action_hr_appraisal_goal
#: model:ir.ui.menu,name:hr_appraisal.menu_hr_appraisal_goal
msgid "Goals"
msgstr "目标"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Group By"
msgstr "分组"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Group by..."
msgstr "分组"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_calendar_event__id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_plan__id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_department__id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_departure_wizard__id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_base__id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_public__id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_job__id
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__id
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company__id
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__id
#: model:ir.model.fields,field_description:hr_appraisal.field_res_users__id
msgid "ID"
msgstr "ID"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_exception_icon
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_exception_icon
msgid "Icon"
msgstr "图标"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__activity_exception_icon
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "表示异常活动的图标。"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__message_needaction
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__message_unread
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__message_needaction
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__message_unread
msgid "If checked, new messages require your attention."
msgstr "确认后, 出现提示消息."

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__message_has_error
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__message_has_sms_error
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__message_has_error
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "如果勾选此项， 某些消息将会产生传递错误。"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__image_1920
msgid "Image"
msgstr "图像"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__image_128
msgid "Image 128"
msgstr "图像128"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "In progress Evaluations"
msgstr "正在进行的评估"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__final_interview
msgid "Interview"
msgstr "面试"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_is_follower
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_is_follower
msgid "Is Follower"
msgstr "关注者"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__is_manager
msgid "Is Manager"
msgstr "是经理"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_job
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__job_id
msgid "Job Position"
msgstr "工作岗位"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "Jobs Configuration"
msgstr "作业配置"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__last_appraisal_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__last_appraisal_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_base__last_appraisal_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_public__last_appraisal_id
#: model:ir.model.fields,field_description:hr_appraisal.field_res_users__last_appraisal_id
msgid "Last Appraisal"
msgstr "上次评估"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__last_appraisal_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__last_appraisal_date
#: model:ir.model.fields,field_description:hr_appraisal.field_res_users__last_appraisal_date
msgid "Last Appraisal Date"
msgstr "上次评估日期"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_calendar_event____last_update
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal____last_update
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal____last_update
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note____last_update
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_plan____last_update
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report____last_update
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_department____last_update
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_departure_wizard____last_update
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee____last_update
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_base____last_update
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_public____last_update
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_job____last_update
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal____last_update
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company____last_update
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings____last_update
#: model:ir.model.fields,field_description:hr_appraisal.field_res_users____last_update
msgid "Last Modified on"
msgstr "最后修改日"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__write_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__write_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__write_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_plan__write_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__write_uid
msgid "Last Updated by"
msgstr "最后更新人"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__write_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__write_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__write_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_plan__write_date
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__write_date
msgid "Last Updated on"
msgstr "最后更新时间"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_search
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Late"
msgstr "迟到"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Late Activities"
msgstr "最近的活动"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_main_attachment_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_main_attachment_id
msgid "Main Attachment"
msgstr "主要附件"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__manager_ids
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "Manager"
msgstr "经理"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__manager_feedback
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Manager Feedback"
msgstr "经理反馈"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__manager_feedback_published
msgid "Manager Feedback Published"
msgstr "经理反馈发布"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__manager_feedback_template
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_job__manager_feedback_template
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_job_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "Manager Feedback Template"
msgstr "经理反馈模板"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_tree
msgid "Mark as Done"
msgstr "标记为完成"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__meeting_id
msgid "Meeting"
msgstr "会议"

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/res_company.py:0
#, python-format
msgid "Meets expectations"
msgstr "满足期望"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_has_error
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_has_error
msgid "Message Delivery error"
msgstr "消息传递错误"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_ids
msgid "Messages"
msgstr "消息"

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.hr_appraisal_action_my
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "My Appraisals"
msgstr "我的评估"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_search
msgid "My Goals"
msgstr "我的目标"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__name
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_tree
msgid "Name"
msgstr "名称"

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/res_company.py:0
#, python-format
msgid "Needs improvement"
msgstr "需要改进"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_date_deadline
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "下一活动截止日期"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_summary
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_summary
msgid "Next Activity Summary"
msgstr "下一活动摘要"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_type_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_type_id
msgid "Next Activity Type"
msgstr "下一活动类型"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__next_appraisal_date
#: model:ir.model.fields,field_description:hr_appraisal.field_res_users__next_appraisal_date
msgid "Next Appraisal Date"
msgstr "下一个评价日期"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_needaction_counter
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_needaction_counter
msgid "Number of Actions"
msgstr "动作个数"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_has_error_counter
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_has_error_counter
msgid "Number of errors"
msgstr "错误数"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__message_needaction_counter
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "需要作业消息数量"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__message_has_error_counter
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "发送错误的消息数量"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__message_unread_counter
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__message_unread_counter
msgid "Number of unread messages"
msgstr "未读消息数量"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__employee_id
msgid "Owner"
msgstr "所有者"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__parent_user_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_base__parent_user_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_public__parent_user_id
msgid "Parent User"
msgstr "上一级用户"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_search
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "People I Manage"
msgstr "人们 I 管理"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__progression
msgid "Progression"
msgstr "进展"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_employee_public
msgid "Public Employee"
msgstr "公共员工"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__recipient_ids
msgid "Recipients"
msgstr "收件人"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__related_partner_id
msgid "Related Partner"
msgstr "相关的业务伙伴"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__employee_user_id
#: model:ir.model.fields,help:hr_appraisal.field_hr_employee__parent_user_id
#: model:ir.model.fields,help:hr_appraisal.field_hr_employee_base__parent_user_id
msgid "Related user name for the resource to manage its access."
msgstr "管理资源访问权限的相关用户名"

#. module: hr_appraisal
#: model:ir.ui.menu,name:hr_appraisal.menu_hr_appraisal_report
msgid "Reporting"
msgstr "报告"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_employee_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_users_view_form
msgid "Request Appraisal"
msgstr "要求评估"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_request_appraisal
msgid "Request an Appraisal"
msgstr "要求评估"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_user_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_user_id
msgid "Responsible User"
msgstr "负责用户"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_has_sms_error
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_has_sms_error
msgid "SMS Delivery error"
msgstr "短信发送错误"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_kanban
msgid "Schedule The Final Interview"
msgstr "安排最终面谈"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Search Appraisal"
msgstr "搜索评价"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.request_appraisal_view_form
msgid "Send Request"
msgstr "发送请求"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__sequence
msgid "Sequence"
msgstr "单号规则"

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.hr_appraisal_config_settings_action
#: model:ir.ui.menu,name:hr_appraisal.hr_appraisal_menu_configuration
msgid "Settings"
msgstr "设置"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Show all records which has next action date is before today"
msgstr "显示所有的在今天之前的下一个行动日期的记录"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__state
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__state
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Status"
msgstr "状态"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__activity_state
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"基于活动的状态 \n"
" 逾期：已经超过截止日期 \n"
" 现今：活动日期是当天 \n"
" 计划：未来活动。"

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/res_company.py:0
#, python-format
msgid "Strongly Exceed Expectations"
msgstr "大大超出预期"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__subject
msgid "Subject"
msgstr "主题"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.request_appraisal_view_form
msgid "Subject..."
msgstr "主题..."

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__last_appraisal_date
#: model:ir.model.fields,help:hr_appraisal.field_hr_employee__last_appraisal_date
#: model:ir.model.fields,help:hr_appraisal.field_res_users__last_appraisal_date
msgid "The date of the last appraisal"
msgstr "上次评估的日期"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_employee__next_appraisal_date
#: model:ir.model.fields,help:hr_appraisal.field_res_users__next_appraisal_date
msgid ""
"The date of the next appraisal is computed by the appraisal plan's dates "
"(first appraisal + periodicity)."
msgstr "下次评价的日期是按评价计划的日期计算出来的 （首次评价+ 周期）"

#. module: hr_appraisal
#: model:ir.model.constraint,message:hr_appraisal.constraint_hr_appraisal_plan_positif_number_months
msgid "The duration time must be bigger or equal to 1 month."
msgstr "持续时间必须大于或等于 1 个月。"

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal_plan.py:0
#, python-format
msgid ""
"The employee %s arrived %s months ago. An appraisal for %s is created. You "
"can assess %s & determinate the date for '1to1' meeting before %s"
msgstr "员工%s几个月前到达%s。 创建一个评估给%s。 您可以访问%s并在%s之前评估“ 1对1”会议的日期"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal__state__new
msgid "To Confirm"
msgstr "待确认"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "To Do"
msgstr "待办"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_report__state__new
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "To Start"
msgstr "开始"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Today Activities"
msgstr "今天的活动"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__activity_exception_decoration
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "记录的异常活动类型。"

#. module: hr_appraisal
#: code:addons/hr_appraisal/wizard/request_appraisal.py:0
#, python-format
msgid "Unable to post message, please configure the sender's email address."
msgstr "无法发布邮件，请配置发件人的EMail地址。"

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
#, python-format
msgid "Unpublished"
msgstr "未发布"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_unread
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_unread
msgid "Unread Messages"
msgstr "未读消息"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_unread_counter
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_unread_counter
msgid "Unread Messages Counter"
msgstr "未读消息数"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__template_id
msgid "Use template"
msgstr "使用模版"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__employee_user_id
#: model:res.groups,name:hr_appraisal.group_hr_appraisal_user
msgid "User"
msgstr "用户"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_res_users
msgid "Users"
msgstr "用户"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__waiting_feedback
msgid "Waiting Feedback from Employee/Managers"
msgstr " 员工/经理的等待反馈"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__website_message_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__website_message_ids
msgid "Website Messages"
msgstr "网站消息"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__website_message_ids
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__website_message_ids
msgid "Website communication history"
msgstr "网上沟通记录"

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal_plan.py:0
#, python-format
msgid ""
"You arrived %s months ago. Your appraisal is created you can assess yourself"
" here. Your manager will determinate the date for your '1to1' meeting."
msgstr "你几个月前到达的%s  。 你的评价是创造出来的，你可以在这里评估自己。 你的经理将决定你的 '1to1' 会议的日期。"

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
#, python-format
msgid "You cannot delete appraisal which is not in draft or canceled state"
msgstr " 您不能删除未处于草稿或取消状态的评估"

#. module: hr_appraisal
#: model_terms:ir.actions.act_window,help:hr_appraisal.open_view_hr_appraisal_tree
msgid ""
"You will be able to plan an appraisal with your employees, to ask your appraisal with your\n"
"            manager, to realize 360° Feedback with the Survey app, to make custom forms and to see the results."
msgstr ""
"‎您将能够计划与您的员工进行评估，并询问您的评估‎\n"
"           ‎经理，通过调查应用程序实现 360° 反馈，制作自定义表单并查看结果。‎"

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal_plan.py:0
#, python-format
msgid ""
"Your employee's last appraisal was %s months ago. An appraisal for %s is "
"created. You can assess %s & determinate the date for '1to1' meeting before "
"%s"
msgstr "您的员工的最后一次评估是%s个月前。 创建一个评估给%s。 您可以访问%s并在%s之前确定“一对一”会议的日期"

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal_plan.py:0
#, python-format
msgid ""
"Your last appraisal was %s months ago. Your appraisal is created you can "
"assess yourself here. Your manager will determinate the date for your '1to1'"
" meeting."
msgstr " 你上次的评价是几个月前 %s 。 你的评价是创造出来的，你可以在这里评估自己。 你的经理将决定你的'1to1'会议的日期。"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_form
msgid "e.g. Present yourself to your new team"
msgstr "e.g.  向你的新团队展示自己"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_plan__event__arrival
msgid "month after the arrival date"
msgstr "到达日期后一个月"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_plan__event__last_appraisal
msgid "month after the last appraisal"
msgstr " 上一次评估后的一个月"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_kanban
msgid "oe_kanban_text_red"
msgstr "oe_kanban_text_red"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_view_activity
msgid "text-danger"
msgstr "text-danger"
