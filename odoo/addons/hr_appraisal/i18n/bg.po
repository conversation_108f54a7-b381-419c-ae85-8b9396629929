# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_appraisal
# 
# Translators:
# <PERSON>, 2020
# <PERSON> <igor.shelud<PERSON>@gmail.com>, 2020
# <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>, 2020
# <PERSON><PERSON><PERSON> <alben<PERSON><EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2020
# Ивайло М<PERSON>линов <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-11-27 15:44+0000\n"
"PO-Revision-Date: 2020-09-07 08:20+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2021\n"
"Language-Team: Bulgarian (https://www.transifex.com/odoo/teams/41243/bg/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bg\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_appraisal
#: model:mail.template,report_name:hr_appraisal.mail_template_appraisal_confirm_employee
#: model:mail.template,report_name:hr_appraisal.mail_template_appraisal_confirm_manager
#: model:mail.template,report_name:hr_appraisal.mail_template_appraisal_request
#: model:mail.template,report_name:hr_appraisal.mail_template_appraisal_request_from_employee
msgid "${(object.name or '').replace('/','_')}"
msgstr "${(object.name or '').replace('/','_')}"

#. module: hr_appraisal
#: model:mail.template,subject:hr_appraisal.mail_template_appraisal_confirm_employee
#: model:mail.template,subject:hr_appraisal.mail_template_appraisal_confirm_manager
msgid "${object.employee_id.name}: Appraisal Confirmed"
msgstr "${object.employee_id.name}: Потвърдена оценка"

#. module: hr_appraisal
#: model:mail.template,subject:hr_appraisal.mail_template_appraisal_request_from_employee
msgid "${object.name} requests an Appraisal"
msgstr "${object.name} иска оценка"

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
#, python-format
msgid "%s Goals"
msgstr "%s Цели"

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal_plan.py:0
#, python-format
msgid "%s months %s"
msgstr "%s месеци %s"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_goal__progression__100
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_kanban
msgid "100 %"
msgstr "100 %"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_goal__progression__25
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_kanban
msgid "25 %"
msgstr "25 %"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__module_hr_appraisal_survey
msgid "360 Feedback"
msgstr "360° Обратна връзка"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_goal__progression__50
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_kanban
msgid "50 %"
msgstr "50 %"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_goal__progression__75
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_kanban
msgid "75 %"
msgstr "75 %"

#. module: hr_appraisal
#: model:mail.template,body_html:hr_appraisal.mail_template_appraisal_confirm_employee
#: model:mail.template,body_html:hr_appraisal.mail_template_appraisal_confirm_manager
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Dear ${ctx.get('employee_to_name', 'employee')},\n"
"                        <br/><br/>\n"
"                        An appraisal was requested.\n"
"                        <br/>\n"
"                        Please schedule an appraisal date together.\n"
"                        <br/><br/>\n"
"                        Thank you!\n"
"                        The HR department\n"
"                        <br/><br/>\n"
"                        % if ctx.get('recipient_users'):\n"
"                        <p style=\"margin: 16px 0px 16px 0px;\">\n"
"                            <a href=\"${ctx['url']}\" style=\"background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                View Appraisal\n"
"                            </a>\n"
"                        </p>\n"
"                        % endif\n"
"                        <br/><br/>\n"
"                        <tr><td style=\"padding:15px 20px 10px 20px;\">${(object.signature or '')| safe}</td></tr>\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Уважаеми/а ${ctx.get('employee_to_name', 'employee')},\n"
"                        <br/><br/>\n"
"                        Заявено е атестиране.\n"
"                        <br/>\n"
"                        Моля, планирайте заедно дата за атестация.\n"
"                        <br/><br/>\n"
"                        Благодаря!\n"
"                        Отдел Човешки ресурси\n"
"                        <br/><br/>\n"
"                        % if ctx.get('recipient_users'):\n"
"                        <p style=\"margin: 16px 0px 16px 0px;\">\n"
"                            <a href=\"${ctx['url']}\" style=\"background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                Преглед на оценка\n"
"                            </a>\n"
"                        </p>\n"
"                        % endif\n"
"                        <br/><br/>\n"
"                        <tr><td style=\"padding:15px 20px 10px 20px;\">${(object.signature or '')| safe}</td></tr>\n"
"                    </p>\n"
"                </div>\n"
"            "

#. module: hr_appraisal
#: model:mail.template,body_html:hr_appraisal.mail_template_appraisal_request_from_employee
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Dear ${ctx['partner_to_name']},\n"
"                        <br/><br/>\n"
"                        I wish to request an appraisal.<br/>\n"
"                        % if ctx.get('recipient_users'):\n"
"                        Here is the link of my appraisal:\n"
"                        <p style=\"margin: 16px 0px 16px 0px;\">\n"
"                            <a href=\"${ctx['url']}\" style=\"background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                View Appraisal\n"
"                            </a>\n"
"                        </p>\n"
"                        % endif\n"
"                        <br/><br/>\n"
"                        <tr><td style=\"padding:15px 20px 10px 20px;\">${(object.employee_id.user_id.signature or '')| safe}</td></tr>\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Уважаеми/а ${ctx['partner_to_name']},\n"
"                        <br/><br/>\n"
"                        Искам да заявя атестиране.<br/>\n"
"                        % if ctx.get('recipient_users'):\n"
"                        Ето връзката към моята атестация:\n"
"                        <p style=\"margin: 16px 0px 16px 0px;\">\n"
"                            <a href=\"${ctx['url']}\" style=\"background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                Преглед на оценка\n"
"                            </a>\n"
"                        </p>\n"
"                        % endif\n"
"                        <br/><br/>\n"
"                        <tr><td style=\"padding:15px 20px 10px 20px;\">${(object.employee_id.user_id.signature or '')| safe}</td></tr>\n"
"                    </p>\n"
"                </div>\n"
"            "

#. module: hr_appraisal
#: model:mail.template,body_html:hr_appraisal.mail_template_appraisal_request
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Dear ${ctx['partner_to_name']},\n"
"                        <br/><br/>\n"
"                        I would like to start an Appraisal for you.\n"
"\n"
"                        % if ctx.get('recipient_users'):\n"
"                        <p style=\"margin: 16px 0px 16px 0px;\">\n"
"                            <a href=\"${ctx['url']}\" style=\"background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                View Appraisal\n"
"                            </a>\n"
"                        </p>\n"
"                        % endif\n"
"                        <br/><br/>\n"
"                        <tr><td style=\"padding:15px 20px 10px 20px;\">${(object.signature or '')| safe}</td></tr>\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Уважаеми/а ${ctx['partner_to_name']},\n"
"                        <br/><br/>\n"
"                        Бих искал да започна оценяването Ви.\n"
"\n"
"                        % if ctx.get('recipient_users'):\n"
"                        <p style=\"margin: 16px 0px 16px 0px;\">\n"
"                            <a href=\"${ctx['url']}\" style=\"background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                Преглед на оценка\n"
"                            </a>\n"
"                        </p>\n"
"                        % endif\n"
"                        <br/><br/>\n"
"                        <tr><td style=\"padding:15px 20px 10px 20px;\">${(object.signature or '')| safe}</td></tr>\n"
"                    </p>\n"
"                </div>\n"
"            "

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_kanban
msgid "<span class=\"bg-info\">Ready</span>"
msgstr "<span class=\"bg-info\">Готово</span>"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_kanban
msgid "<span class=\"bg-secondary\">Canceled</span>"
msgstr "<span class=\"bg-secondary\">Отменена</span>"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_kanban
msgid "<span class=\"bg-success\">Done</span>"
msgstr "<span class=\"bg-success\">Извършена</span>"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid ""
"<span class=\"col-3 text-right\" attrs=\"{'invisible': ['|', ('employee_feedback_published', '=', True), ('state', '=', 'new')]}\">Unpublished</span>\n"
"                                <span class=\"col-3 text-right\" attrs=\"{'invisible': ['|', ('employee_feedback_published', '=', False), ('state', '=', 'new')]}\">Published</span>"
msgstr ""
"<span class=\"col-3 text-right\" attrs=\"{'invisible': ['|', ('employee_feedback_published', '=', True), ('state', '=', 'new')]}\">Непубликувана</span>\n"
"                                <span class=\"col-3 text-right\" attrs=\"{'invisible': ['|', ('employee_feedback_published', '=', False), ('state', '=', 'new')]}\">Публикувана</span>"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid ""
"<span class=\"col-3 text-right\" attrs=\"{'invisible': ['|', ('manager_feedback_published', '=', True), ('state', '=', 'new')]}\">Unpublished</span>\n"
"                                <span class=\"col-3 text-right\" attrs=\"{'invisible': ['|', ('manager_feedback_published', '=', False), ('state', '=', 'new')]}\">Published</span>"
msgstr ""
"<span class=\"col-3 text-right\" attrs=\"{'invisible': ['|', ('manager_feedback_published', '=', True), ('state', '=', 'new')]}\">Непубликувана</span>\n"
"                                <span class=\"col-3 text-right\" attrs=\"{'invisible': ['|', ('manager_feedback_published', '=', False), ('state', '=', 'new')]}\">Публикувана</span>"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid ""
"<span class=\"o_form_label\">360 Feedback</span>\n"
"                                <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" role=\"img\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"o_form_label\">360° Обратна връзка</span>\n"
"                                <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" role=\"img\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\"/>"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid ""
"<span class=\"o_form_label\">Appraisal Plans</span>\n"
"                                <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" role=\"img\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"o_form_label\">Планове за атестиране</span>\n"
"                                <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" role=\"img\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\"/>"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "<span class=\"o_form_label\">Confirmation Email Template</span>"
msgstr "<span class=\"o_form_label\">Имейл шаблон за потвърждение</span>"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid ""
"<span class=\"o_form_label\">Feedback Templates</span>\n"
"                                <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" role=\"img\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"o_form_label\">Шаблони за обратна връзка</span>\n"
"                                <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" role=\"img\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\"/>"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_employee_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_users_view_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                            Last Appraisal\n"
"                        </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                            Последна атестация\n"
"                        </span>"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid ""
"<span class=\"o_stat_text\">Employee's</span>\n"
"                            <span class=\"o_stat_text\">Goals</span>"
msgstr ""
"<span class=\"o_stat_text\">Цели на</span>\n"
"                            <span class=\"o_stat_text\">на служители</span>"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid ""
"<span class=\"o_stat_value\" attrs=\"{'invisible': [('meeting_id', '=', False)]}\">1 Meeting</span>\n"
"                            <span class=\"o_stat_value\" attrs=\"{'invisible': [('meeting_id', '!=', False)]}\">No Meeting</span>"
msgstr ""
"<span class=\"o_stat_value\" attrs=\"{'invisible': [('meeting_id', '=', False)]}\">1 Среща</span>\n"
"                            <span class=\"o_stat_value\" attrs=\"{'invisible': [('meeting_id', '!=', False)]}\">Няма среща</span>"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "<span class=\"o_stat_value\">Last Appraisal</span>"
msgstr "<span class=\"o_stat_value\">Последна атестация</span>"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_kanban
msgid "<strong><span>Final Interview: </span></strong>"
msgstr "<strong><span>Окончателно интервю: </span></strong>"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_needaction
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_needaction
msgid "Action Needed"
msgstr "Нужно е действие"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__active
msgid "Active"
msgstr "Активен"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_ids
msgid "Activities"
msgstr "Дейности"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_exception_decoration
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Декорация за изключение на дейност"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_state
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_state
msgid "Activity State"
msgstr "Състояние на дейност"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_type_icon
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_type_icon
msgid "Activity Type Icon"
msgstr "Икона за вид дейност"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.request_appraisal_view_form
msgid "Add existing contacts..."
msgstr "Добавяне на съществуващи контакти..."

#. module: hr_appraisal
#: model:res.groups,name:hr_appraisal.group_hr_appraisal_manager
msgid "Administrator"
msgstr "Администратор"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_plan__event
msgid "After"
msgstr "След"

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.hr_appraisal_action
#: model:ir.actions.act_window,name:hr_appraisal.open_view_hr_appraisal_tree
#: model:ir.actions.act_window,name:hr_appraisal.open_view_hr_appraisal_tree2
#: model:ir.ui.menu,name:hr_appraisal.menu_hr_appraisal_root
#: model:ir.ui.menu,name:hr_appraisal.menu_open_view_hr_appraisal_tree
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_view_activity
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_tree
msgid "Appraisal"
msgstr "Оценяване"

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.action_appraisal_report_all
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_graph
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_pivot
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Appraisal Analysis"
msgstr "Анализ на атестиране"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_appraisal_note
msgid "Appraisal Assessment Note"
msgstr "Бележка за преценка на атестация"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company__appraisal_confirm_employee_mail_template
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__appraisal_confirm_employee_mail_template
msgid "Appraisal Confirm Employee Mail Template"
msgstr "Имейл шаблон на служител за потвърждение на атестация"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company__appraisal_confirm_manager_mail_template
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__appraisal_confirm_manager_mail_template
msgid "Appraisal Confirm Manager Mail Template"
msgstr "Имейл шаблон на ръководител за потвърждение на атестация"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__date_close
msgid "Appraisal Deadline"
msgstr "Краен срок за оценка"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__employee_id
msgid "Appraisal Employee"
msgstr "Служител за оценка"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company__appraisal_employee_feedback_template
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__appraisal_employee_feedback_template
msgid "Appraisal Employee Feedback Template"
msgstr "Шаблон на служител за обратна връзка на атестация"

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
#, python-format
msgid "Appraisal Form to Fill"
msgstr "Формуляр за попълване на атестация"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_appraisal_goal
msgid "Appraisal Goal"
msgstr "Цел за атестация"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company__appraisal_manager_feedback_template
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__appraisal_manager_feedback_template
msgid "Appraisal Manager Feedback Template"
msgstr "Шаблон на ръководител за обратна връзка на атестация"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company__appraisal_plan_ids
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_plan_tree
msgid "Appraisal Plan"
msgstr "План за атестация"

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.action_hr_appraisal_plan
#: model:ir.ui.menu,name:hr_appraisal.menu_appraisal_plan
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "Appraisal Plans"
msgstr "Планове за атестация"

#. module: hr_appraisal
#: model:mail.template,subject:hr_appraisal.mail_template_appraisal_request
msgid "Appraisal Requested"
msgstr "Заявена атестация"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_report__state__pending
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Appraisal Sent"
msgstr "Изпратена атестация"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_appraisal_report
msgid "Appraisal Statistics"
msgstr "Статистика на атестация"

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal_plan.py:0
#: code:addons/hr_appraisal/models/hr_appraisal_plan.py:0
#: code:addons/hr_appraisal/wizard/request_appraisal.py:0
#, python-format
msgid "Appraisal to Confirm and Send"
msgstr "Потвърждаване на атестация и изпращане"

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.hr_appraisal_action_from_department
msgid "Appraisal to start"
msgstr "Начало на атестиране"

#. module: hr_appraisal
#: model:ir.actions.server,name:hr_appraisal.ir_cron_scheduler_appraisal_ir_actions_server
#: model:ir.cron,cron_name:hr_appraisal.ir_cron_scheduler_appraisal
#: model:ir.cron,name:hr_appraisal.ir_cron_scheduler_appraisal
msgid "Appraisal: Run employee appraisal"
msgstr "Атестация: Начало на оценяване на служител"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_job_view_form
msgid "Appraisals"
msgstr "Атестации"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_department__appraisals_to_process_count
msgid "Appraisals to Process"
msgstr "Атестации за обработка"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Archived"
msgstr "Архивиран"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "Ask to fill a survey to other employees"
msgstr "Искане за попълване на анкета за други служители"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__assessment_note
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company__assessment_note_ids
msgid "Assessment Note"
msgstr "Бележка атестиране"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_attachment_count
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_attachment_count
msgid "Attachment Count"
msgstr "Брой прикачени файлове"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__attachment_ids
msgid "Attachments"
msgstr "Прикачени файлове"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__author_id
msgid "Author"
msgstr "Автор"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_request_appraisal__author_id
msgid "Author of the message."
msgstr "Автор на съобщението."

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "Automatic email sent after confirm the appraisal"
msgstr "Автоматичен имейл се изпраща след потвърждаване на атестацията"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company__appraisal_plan
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__appraisal_plan
msgid "Automatically Generate Appraisals"
msgstr "Автоматично генериране на атестации"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "Automatically generate appraisals"
msgstr "Автоматично генериране на атестации"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_employee_base
msgid "Basic Employee"
msgstr "Основен служител"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_calendar_event
msgid "Calendar Event"
msgstr "Календар Събитие"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__can_see_employee_publish
msgid "Can See Employee Publish"
msgstr "Може да се виждат публикации на служител"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__can_see_manager_publish
msgid "Can See Manager Publish"
msgstr "Може да се виждат публикации на ръководител"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.request_appraisal_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Cancel"
msgstr "Отказ"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_departure_wizard__cancel_appraisal
msgid "Cancel Future Appraisals"
msgstr "Отмяна на бъдещи атестации"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_departure_wizard_view_form
msgid "Cancel all appraisals after this date"
msgstr "Отмяна всички атестации след тази дата"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal__state__cancel
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_report__state__cancel
msgid "Cancelled"
msgstr "Отменен"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__manager_id
msgid "Challenged By"
msgstr "Предизвикано от"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__color
msgid "Color"
msgstr "Цвят"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_res_company
msgid "Companies"
msgstr "Фирми"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__company_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__company_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_plan__company_id
msgid "Company"
msgstr "Фирма"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.request_appraisal_view_form
msgid "Compose Email"
msgstr "Съставяне на имейл"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_res_config_settings
msgid "Config Settings"
msgstr "Настройки"

#. module: hr_appraisal
#: model:ir.ui.menu,name:hr_appraisal.menu_hr_appraisal_configuration
msgid "Configuration"
msgstr "Конфигурация"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "Configure Feedback Templates by Job"
msgstr "Конфигуриране шаблони за обратна връзка по работна позиция"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Confirm"
msgstr "Потвърждение"

#. module: hr_appraisal
#: code:addons/hr_appraisal/wizard/request_appraisal.py:0
#, python-format
msgid "Confirm and send appraisal of %s"
msgstr "Потвърждение и изпращане на атестация на %s"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal__state__pending
msgid "Confirmed"
msgstr "Потвърдена"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__body
msgid "Contents"
msgstr "Съдържание"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__create_date
msgid "Create Date"
msgstr "Задайте дата"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_plan__duration
msgid "Create a New Appraisal"
msgstr "Създаване на нова атестация"

#. module: hr_appraisal
#: model_terms:ir.actions.act_window,help:hr_appraisal.open_view_hr_appraisal_tree
msgid "Create a new appraisal"
msgstr "Създаване на нова атестация"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__create_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__create_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__create_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_plan__create_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__create_uid
msgid "Created by"
msgstr "Създадено от"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__create_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__create_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__create_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_plan__create_date
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__create_date
msgid "Created on"
msgstr "Създадено на"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Creation Date"
msgstr "Дата на създаване"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Date"
msgstr "Дата"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__deadline
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__deadline
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_search
msgid "Deadline"
msgstr "Краен срок"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_view_activity
msgid "Deadline:"
msgstr "Краен срок:"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_kanban
msgid "Delete"
msgstr "Изтриване"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_department
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__department_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__department_id
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Department"
msgstr "Отдел"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_departure_wizard
msgid "Departure Wizard"
msgstr "Съветник напускане"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__description
msgid "Description"
msgstr "Описание"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__deadline
msgid "Desired Deadline"
msgstr "Краен срок"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_calendar_event__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_plan__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_department__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_departure_wizard__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_base__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_public__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_job__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_res_users__display_name
msgid "Display Name"
msgstr "Име за показване"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal__state__done
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_report__state__done
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Done"
msgstr "Извършен"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_kanban
msgid "Dropdown menu"
msgstr "Падащо меню"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_request_appraisal__email_from
msgid "Email address of the sender"
msgstr "Имейл адрес на подателя"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_employee
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__employee_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__employee_id
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Employee"
msgstr "Служител"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_appraisal
msgid "Employee Appraisal"
msgstr "Атестиране на служител"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_appraisal_plan
msgid "Employee Appraisal Plan"
msgstr "План за атестиране на служител"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__employee_feedback
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Employee Feedback"
msgstr "Обратна връзка на служител"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__employee_feedback_published
msgid "Employee Feedback Published"
msgstr "Публикувана е обратна връзка на служител"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__employee_feedback_template
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_job__employee_feedback_template
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_job_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "Employee Feedback Template"
msgstr "Шаблон на служител за обратна връзка"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__name
msgid "Employee Name"
msgstr "Име на служител"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Employee's Name"
msgstr "Име на служителя"

#. module: hr_appraisal
#: model:ir.ui.menu,name:hr_appraisal.hr_appraisal_menu_employee
msgid "Employees"
msgstr "Служители"

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.action_hr_appraisal_note
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__assessment_note_ids
#: model:ir.ui.menu,name:hr_appraisal.menu_hr_appraisal_note
msgid "Evaluation Scale"
msgstr "Скала за оценка"

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/res_company.py:0
#, python-format
msgid "Exceeds expectations"
msgstr "Надминава очакванията"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Extended Filters..."
msgstr "Разширени филтри..."

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "Feedback Templates"
msgstr "Шаблони за обратна връзка"

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
#, python-format
msgid "Fill appraisal for <a href=\"#\" data-oe-model=\"%s\" data-oe-id=\"%s\">%s</a>"
msgstr ""
"Попълване атестация на <a href=\"#\" data-oe-model=\"%s\" data-oe-"
"id=\"%s\">%s</a>"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__date_final_interview
msgid "Final Interview"
msgstr "Окончателно интервю"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Final Interview Date"
msgstr "Дата на окончателно интервю"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_follower_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_follower_ids
msgid "Followers"
msgstr "Последователи"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_channel_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_channel_ids
msgid "Followers (Channels)"
msgstr "Последователи (канали)"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_partner_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_partner_ids
msgid "Followers (Partners)"
msgstr "Последователи (партньори)"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__activity_type_icon
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Икона, примерно fa-tasks"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__email_from
msgid "From"
msgstr "От"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Future Activities"
msgstr "Бъдещи дейности"

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.action_hr_appraisal_goal
#: model:ir.ui.menu,name:hr_appraisal.menu_hr_appraisal_goal
msgid "Goals"
msgstr "Цели"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Group By"
msgstr "Групиране по"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Group by..."
msgstr "Групиране по ..."

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_calendar_event__id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_plan__id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_department__id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_departure_wizard__id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_base__id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_public__id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_job__id
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__id
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company__id
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__id
#: model:ir.model.fields,field_description:hr_appraisal.field_res_users__id
msgid "ID"
msgstr "№"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_exception_icon
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_exception_icon
msgid "Icon"
msgstr "Икона"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__activity_exception_icon
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Икона за обозначаване на дейност с изключение."

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__message_needaction
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__message_unread
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__message_needaction
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__message_unread
msgid "If checked, new messages require your attention."
msgstr "Ако е отметнато, новите съобщения ще изискват внимание."

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__message_has_error
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__message_has_sms_error
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__message_has_error
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Ако е отметнато, някои от съобщенията имат грешка при предаването."

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__image_1920
msgid "Image"
msgstr "Изображение"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__image_128
msgid "Image 128"
msgstr "Изображение 128"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "In progress Evaluations"
msgstr "В процес на атестиране"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__final_interview
msgid "Interview"
msgstr "Интервю"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_is_follower
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_is_follower
msgid "Is Follower"
msgstr "е последовател"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__is_manager
msgid "Is Manager"
msgstr "е Ръководител"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_job
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__job_id
msgid "Job Position"
msgstr "Работна позиция"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "Jobs Configuration"
msgstr "Конфигуриране на работна позиция"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__last_appraisal_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__last_appraisal_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_base__last_appraisal_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_public__last_appraisal_id
#: model:ir.model.fields,field_description:hr_appraisal.field_res_users__last_appraisal_id
msgid "Last Appraisal"
msgstr "Последна атестация"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__last_appraisal_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__last_appraisal_date
#: model:ir.model.fields,field_description:hr_appraisal.field_res_users__last_appraisal_date
msgid "Last Appraisal Date"
msgstr "Последна дата на атестиране"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_calendar_event____last_update
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal____last_update
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal____last_update
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note____last_update
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_plan____last_update
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report____last_update
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_department____last_update
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_departure_wizard____last_update
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee____last_update
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_base____last_update
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_public____last_update
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_job____last_update
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal____last_update
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company____last_update
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings____last_update
#: model:ir.model.fields,field_description:hr_appraisal.field_res_users____last_update
msgid "Last Modified on"
msgstr "Последно променено на"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__write_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__write_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__write_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_plan__write_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__write_uid
msgid "Last Updated by"
msgstr "Последно обновено от"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__write_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__write_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__write_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_plan__write_date
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__write_date
msgid "Last Updated on"
msgstr "Последно обновено на"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_search
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Late"
msgstr "Последен"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Late Activities"
msgstr "Последни дейности"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_main_attachment_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_main_attachment_id
msgid "Main Attachment"
msgstr "Основен Прикачен Файл"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__manager_ids
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "Manager"
msgstr "Ръководител"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__manager_feedback
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Manager Feedback"
msgstr "Обратна връзка на ръководител"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__manager_feedback_published
msgid "Manager Feedback Published"
msgstr "Публикувана обратна връзка на ръководител"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__manager_feedback_template
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_job__manager_feedback_template
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_job_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "Manager Feedback Template"
msgstr "Шаблон на ръководител за обратна връзка"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_tree
msgid "Mark as Done"
msgstr "Маркиране като извършено"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__meeting_id
msgid "Meeting"
msgstr "Среща"

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/res_company.py:0
#, python-format
msgid "Meets expectations"
msgstr "Отговаря на очакванията"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_has_error
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_has_error
msgid "Message Delivery error"
msgstr "Грешка при доставката на съобщение"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_ids
msgid "Messages"
msgstr "Съобщения"

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.hr_appraisal_action_my
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "My Appraisals"
msgstr "Моите атестации"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_search
msgid "My Goals"
msgstr "Моите цели"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__name
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_tree
msgid "Name"
msgstr "Име"

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/res_company.py:0
#, python-format
msgid "Needs improvement"
msgstr "Нуждае се от усъвършенстване"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_date_deadline
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Краен срок на следващо действие"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_summary
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_summary
msgid "Next Activity Summary"
msgstr "Обобщение на следваща дейност"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_type_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_type_id
msgid "Next Activity Type"
msgstr "Вид на следващо действие"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__next_appraisal_date
#: model:ir.model.fields,field_description:hr_appraisal.field_res_users__next_appraisal_date
msgid "Next Appraisal Date"
msgstr "Дата на следващо атестиране"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_needaction_counter
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_needaction_counter
msgid "Number of Actions"
msgstr "Брой действия"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_has_error_counter
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_has_error_counter
msgid "Number of errors"
msgstr "Брой грешки"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__message_needaction_counter
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Брой съобщения, които изискват внимание"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__message_has_error_counter
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Брой съобщения с грешка при доставка"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__message_unread_counter
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__message_unread_counter
msgid "Number of unread messages"
msgstr "Брой непрочетени съобщения"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__employee_id
msgid "Owner"
msgstr "Собственик"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__parent_user_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_base__parent_user_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_public__parent_user_id
msgid "Parent User"
msgstr "Висш потребител"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_search
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "People I Manage"
msgstr "Хора, които управлявам"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__progression
msgid "Progression"
msgstr "Напредък"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_employee_public
msgid "Public Employee"
msgstr "Публичен служител"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__recipient_ids
msgid "Recipients"
msgstr "Получатели"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__related_partner_id
msgid "Related Partner"
msgstr "Свързан партньор"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__employee_user_id
#: model:ir.model.fields,help:hr_appraisal.field_hr_employee__parent_user_id
#: model:ir.model.fields,help:hr_appraisal.field_hr_employee_base__parent_user_id
msgid "Related user name for the resource to manage its access."
msgstr "Име на свързан потребител, който управлява достъпа до този ресурс."

#. module: hr_appraisal
#: model:ir.ui.menu,name:hr_appraisal.menu_hr_appraisal_report
msgid "Reporting"
msgstr "Отчитане"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_employee_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_users_view_form
msgid "Request Appraisal"
msgstr "Заявка за атестация"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_request_appraisal
msgid "Request an Appraisal"
msgstr "Заявка за атестиране"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_user_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_user_id
msgid "Responsible User"
msgstr "Отговорник"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_has_sms_error
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS грешка при доставка"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_kanban
msgid "Schedule The Final Interview"
msgstr "График на окончателното интервю"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Search Appraisal"
msgstr "Търси атестация"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.request_appraisal_view_form
msgid "Send Request"
msgstr "Изпрати заявка"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__sequence
msgid "Sequence"
msgstr "Последователност"

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.hr_appraisal_config_settings_action
#: model:ir.ui.menu,name:hr_appraisal.hr_appraisal_menu_configuration
msgid "Settings"
msgstr "Настройки"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Show all records which has next action date is before today"
msgstr ""
"Показване на всички записи, на които следващата дата на действие е преди "
"днес"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__state
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__state
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Status"
msgstr "Състояние"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__activity_state
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Статус според дейностите\n"
"Пресрочени: Крайната дата е в миналото\n"
"Днес: Дейности с дата на изпълнение днес \n"
"Планирани: Бъдещи дейности."

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/res_company.py:0
#, python-format
msgid "Strongly Exceed Expectations"
msgstr "Силно надминаха очакванията"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__subject
msgid "Subject"
msgstr "Тема"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.request_appraisal_view_form
msgid "Subject..."
msgstr "Тема..."

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__last_appraisal_date
#: model:ir.model.fields,help:hr_appraisal.field_hr_employee__last_appraisal_date
#: model:ir.model.fields,help:hr_appraisal.field_res_users__last_appraisal_date
msgid "The date of the last appraisal"
msgstr "Датата на последната атестация"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_employee__next_appraisal_date
#: model:ir.model.fields,help:hr_appraisal.field_res_users__next_appraisal_date
msgid ""
"The date of the next appraisal is computed by the appraisal plan's dates "
"(first appraisal + periodicity)."
msgstr ""
"Датата на следващата атестация се изчислява според датите на плана за "
"атестиране (първа атестация + периодичност)."

#. module: hr_appraisal
#: model:ir.model.constraint,message:hr_appraisal.constraint_hr_appraisal_plan_positif_number_months
msgid "The duration time must be bigger or equal to 1 month."
msgstr ""
"Времето за продължителност трябва да е по-голямо или равно на 1 месец."

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal_plan.py:0
#, python-format
msgid ""
"The employee %s arrived %s months ago. An appraisal for %s is created. You "
"can assess %s & determinate the date for '1to1' meeting before %s"
msgstr ""
"Служителят %s пристигна преди %s месеца. Създава се атестация за %s. Можете "
"да атестирате %s и да определите датата за срещата '1 до 1' преди %s"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal__state__new
msgid "To Confirm"
msgstr "За потвърждение"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "To Do"
msgstr "За извършване"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_report__state__new
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "To Start"
msgstr "Да начало"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Today Activities"
msgstr "Днешни дейности"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__activity_exception_decoration
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Вид на изключение на дейност в базата."

#. module: hr_appraisal
#: code:addons/hr_appraisal/wizard/request_appraisal.py:0
#, python-format
msgid "Unable to post message, please configure the sender's email address."
msgstr ""
"Не може да се публикува съобщение. Моля, конфигурирайте имейл адреса на "
"подателя."

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
#, python-format
msgid "Unpublished"
msgstr "Непубликуван"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_unread
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_unread
msgid "Unread Messages"
msgstr "Непрочетени съобщения"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_unread_counter
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Брой непрочетени съобщения"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__template_id
msgid "Use template"
msgstr "Използване на шаблон"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__employee_user_id
#: model:res.groups,name:hr_appraisal.group_hr_appraisal_user
msgid "User"
msgstr "Потребител"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_res_users
msgid "Users"
msgstr "Потребители"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__waiting_feedback
msgid "Waiting Feedback from Employee/Managers"
msgstr "Изчакваща обратна връзка от служители / ръководители"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__website_message_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__website_message_ids
msgid "Website Messages"
msgstr "Съобщения в уебсайт"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__website_message_ids
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__website_message_ids
msgid "Website communication history"
msgstr "История на комуникацията на уебсайт"

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal_plan.py:0
#, python-format
msgid ""
"You arrived %s months ago. Your appraisal is created you can assess yourself"
" here. Your manager will determinate the date for your '1to1' meeting."
msgstr ""
"Пристигнахте преди %s месеца. Вашата атестация е създадена, можете да се "
"атестирате тук. Вашият ръководител ще определи датата за вашата среща '1 към"
" 1'."

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
#, python-format
msgid "You cannot delete appraisal which is not in draft or canceled state"
msgstr ""
"Не можете да изтриете атестация, която не е в състояние на чернови или "
"отменена"

#. module: hr_appraisal
#: model_terms:ir.actions.act_window,help:hr_appraisal.open_view_hr_appraisal_tree
msgid ""
"You will be able to plan an appraisal with your employees, to ask your appraisal with your\n"
"            manager, to realize 360° Feedback with the Survey app, to make custom forms and to see the results."
msgstr ""
"Вие ще можете да планирате атестация с вашите служители, да поискате "
"атестиране от вашия ръководител, да реализирате 360° обратна връзка с "
"приложението Survey, да направите персонализирани формуляри и да видите "
"резултатите."

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal_plan.py:0
#, python-format
msgid ""
"Your employee's last appraisal was %s months ago. An appraisal for %s is "
"created. You can assess %s & determinate the date for '1to1' meeting before "
"%s"
msgstr ""
"Последната атестация на вашия служител беше преди %s месеца. Създава се "
"атестация за %s. Можете да атестирате %s и да определите датата за срещата "
"'1 до 1' преди %s"

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal_plan.py:0
#, python-format
msgid ""
"Your last appraisal was %s months ago. Your appraisal is created you can "
"assess yourself here. Your manager will determinate the date for your '1to1'"
" meeting."
msgstr ""
"Последната ви атестация беше преди %s месеца. Вашата атестация е създадена, "
"можете да се атестирате тук. Вашият ръководител ще определи датата за вашата"
" среща '1 към 1'."

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_form
msgid "e.g. Present yourself to your new team"
msgstr "например: Представете се на новия си екип"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_plan__event__arrival
msgid "month after the arrival date"
msgstr "месец след датата на пристигане"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_plan__event__last_appraisal
msgid "month after the last appraisal"
msgstr "месец след последната атестация"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_kanban
msgid "oe_kanban_text_red"
msgstr "oe_kanban_text_red"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_view_activity
msgid "text-danger"
msgstr "text-danger"
