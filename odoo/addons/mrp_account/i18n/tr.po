# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_account
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <AUTHOR> <EMAIL>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~13.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-09-01 07:29+0000\n"
"PO-Revision-Date: 2020-09-07 08:15+0000\n"
"Last-Translator: Nadir <PERSON> <<EMAIL>>, 2021\n"
"Language-Team: Turkish (https://www.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter__costs_hour_account_id
msgid "Analytic Account"
msgstr "Analitik Hesap"

#. module: mrp_account
#: model:ir.actions.server,name:mrp_account.action_compute_price_bom_product
#: model:ir.actions.server,name:mrp_account.action_compute_price_bom_template
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_ext_form_view2
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_view_form_normal_inherit_extended
#: model_terms:ir.ui.view,arch_db:mrp_account.product_variant_easy_edit_view_bom_inherit
msgid "Compute Price from BoM"
msgstr "BoM'dan fiyat hesapla"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_ext_form_view2
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_view_form_normal_inherit_extended
#: model_terms:ir.ui.view,arch_db:mrp_account.product_variant_easy_edit_view_bom_inherit
msgid ""
"Compute the price of the product using products and operations of related "
"bill of materials, for manufactured products only."
msgstr ""
"Ürünün fiyatını yalnızca üretilmiş ürünlerin ürün ağaçlarıyla ilişkili "
"ürünleri ve işlemleri kullanarak hesapla."

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter_productivity__cost_already_recorded
msgid "Cost Recorded"
msgstr "Maliyet Kaydedildi"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_production__display_name
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter__display_name
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter_productivity__display_name
#: model:ir.model.fields,field_description:mrp_account.field_product_product__display_name
#: model:ir.model.fields,field_description:mrp_account.field_product_template__display_name
msgid "Display Name"
msgstr "Görünüm Adı"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_production__extra_cost
msgid "Extra Cost"
msgstr "Ekstra Maliyet"

#. module: mrp_account
#: model:ir.model.fields,help:mrp_account.field_mrp_production__extra_cost
msgid "Extra cost per produced unit"
msgstr "Üretilen birim başına ekstra maliyet"

#. module: mrp_account
#: model:ir.model.fields,help:mrp_account.field_mrp_workcenter__costs_hour_account_id
msgid ""
"Fill this only if you want automatic analytic accounting entries on "
"production orders."
msgstr ""
"Bunu yalnızca üretim emirlerinde otomatik analitik muhasebe girişleri "
"isterseniz doldurun."

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_production__id
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter__id
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter_productivity__id
#: model:ir.model.fields,field_description:mrp_account.field_product_product__id
#: model:ir.model.fields,field_description:mrp_account.field_product_template__id
msgid "ID"
msgstr "ID"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_production____last_update
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter____last_update
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter_productivity____last_update
#: model:ir.model.fields,field_description:mrp_account.field_product_product____last_update
#: model:ir.model.fields,field_description:mrp_account.field_product_template____last_update
msgid "Last Modified on"
msgstr "Son Düzenleme"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_product_product
msgid "Product"
msgstr "Ürün"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_product_template
msgid "Product Template"
msgstr "Ürün Şablonu"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_production
msgid "Production Order"
msgstr "Üretim Emri"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_production__show_valuation
msgid "Show Valuation"
msgstr "Show Valuation"

#. module: mrp_account
#: model:ir.model.fields,help:mrp_account.field_mrp_workcenter_productivity__cost_already_recorded
msgid ""
"Technical field automatically checked when a ongoing production posts "
"journal entries for its costs. This way, we can record one production's cost"
" multiple times and only consider new entries in the work centers time "
"lines."
msgstr ""
"Teknik alan, devam eden bir üretim günlüğüne maliyet kayıtları gönderdiğinde"
" otomatik olarak kontrol edildi. Böylelikle, bir üretim maliyetini birden "
"çok kez kaydedebilir ve yalnızca iş merkezleri zaman çizelgelerinde yeni "
"girdiler düşünebiliriz."

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.mrp_production_form_view_inherited
msgid "Valuation"
msgstr "Değerleme"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_workcenter
msgid "Work Center"
msgstr "İş Merkezi"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_workcenter_productivity
msgid "Workcenter Productivity Log"
msgstr "İş Merkezi Verimlilik Günlüğü"
