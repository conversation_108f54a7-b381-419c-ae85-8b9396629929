# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_account
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~13.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-09-01 07:29+0000\n"
"PO-Revision-Date: 2020-09-07 08:15+0000\n"
"Last-Translator: 敬雲 林 <<EMAIL>>, 2021\n"
"Language-Team: Chinese (Taiwan) (https://www.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter__costs_hour_account_id
msgid "Analytic Account"
msgstr "分析帳戶"

#. module: mrp_account
#: model:ir.actions.server,name:mrp_account.action_compute_price_bom_product
#: model:ir.actions.server,name:mrp_account.action_compute_price_bom_template
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_ext_form_view2
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_view_form_normal_inherit_extended
#: model_terms:ir.ui.view,arch_db:mrp_account.product_variant_easy_edit_view_bom_inherit
msgid "Compute Price from BoM"
msgstr "從BOM計算成本"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_ext_form_view2
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_view_form_normal_inherit_extended
#: model_terms:ir.ui.view,arch_db:mrp_account.product_variant_easy_edit_view_bom_inherit
msgid ""
"Compute the price of the product using products and operations of related "
"bill of materials, for manufactured products only."
msgstr "使用相關物料清單BOM的產品和操作計算產品的價格(僅使用於製造產品)。"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter_productivity__cost_already_recorded
msgid "Cost Recorded"
msgstr "已記錄的成本"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_production__display_name
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter__display_name
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter_productivity__display_name
#: model:ir.model.fields,field_description:mrp_account.field_product_product__display_name
#: model:ir.model.fields,field_description:mrp_account.field_product_template__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_production__extra_cost
msgid "Extra Cost"
msgstr "額外成本"

#. module: mrp_account
#: model:ir.model.fields,help:mrp_account.field_mrp_production__extra_cost
msgid "Extra cost per produced unit"
msgstr "每生產單位的額外成本"

#. module: mrp_account
#: model:ir.model.fields,help:mrp_account.field_mrp_workcenter__costs_hour_account_id
msgid ""
"Fill this only if you want automatic analytic accounting entries on "
"production orders."
msgstr "僅當需要生產訂單上的自動分析會計項目時，才填寫此項。"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_production__id
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter__id
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter_productivity__id
#: model:ir.model.fields,field_description:mrp_account.field_product_product__id
#: model:ir.model.fields,field_description:mrp_account.field_product_template__id
msgid "ID"
msgstr "ID"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_production____last_update
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter____last_update
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter_productivity____last_update
#: model:ir.model.fields,field_description:mrp_account.field_product_product____last_update
#: model:ir.model.fields,field_description:mrp_account.field_product_template____last_update
msgid "Last Modified on"
msgstr "最後修改於"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_product_product
msgid "Product"
msgstr "產品"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_product_template
msgid "Product Template"
msgstr "產品模板"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_production
msgid "Production Order"
msgstr "製造訂單"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_production__show_valuation
msgid "Show Valuation"
msgstr "顯示估值"

#. module: mrp_account
#: model:ir.model.fields,help:mrp_account.field_mrp_workcenter_productivity__cost_already_recorded
msgid ""
"Technical field automatically checked when a ongoing production posts "
"journal entries for its costs. This way, we can record one production's cost"
" multiple times and only consider new entries in the work centers time "
"lines."
msgstr "當正在進行的生產過帳日誌條目以說明其成本時，技術欄位會自動檢查。這樣，我們可以多次記錄該生產的成本，並且只考慮工作中心工行中的新項目。"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.mrp_production_form_view_inherited
msgid "Valuation"
msgstr "計價"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_workcenter
msgid "Work Center"
msgstr "工作中心"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_workcenter_productivity
msgid "Workcenter Productivity Log"
msgstr "工作中心生產力日誌"
