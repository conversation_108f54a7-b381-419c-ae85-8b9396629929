# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* planning
# 
# Translators:
# <PERSON><PERSON><PERSON>emec <<EMAIL>>, 2020
# <PERSON> <jan.ho<PERSON><PERSON>@centrum.cz>, 2020
# <PERSON>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# trendspotter, 2020
# <PERSON> <<EMAIL>>, 2020
# ka<PERSON><PERSON>a schustero<PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-11-27 13:47+0000\n"
"PO-Revision-Date: 2020-09-07 08:22+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2021\n"
"Language-Team: Czech (https://www.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: planning
#: model:mail.template,subject:planning.email_template_slot_single
msgid "${ctx.mail_subject}"
msgstr "${ctx.mail_subject}"

#. module: planning
#: code:addons/planning/models/planning_template.py:0
#, python-format
msgid "(%s days span)"
msgstr "(%s dny)"

#. module: planning
#: model_terms:digest.tip,tip_description:planning.digest_tip_planning_0
msgid "<b class=\"tip_title\">Tip: Record your planning faster</b>"
msgstr "<b class=\"tip_title\">Tip: Zaznamenejte své plánování rychleji</b>"

#. module: planning
#: model:mail.template,body_html:planning.email_template_planning_planning
msgid ""
"<div>\n"
"                    <p>\n"
"                    % if ctx.get('employee'):\n"
"                        Dear ${ctx['employee'].name},\n"
"                    % else:\n"
"                        Hello,\n"
"                    % endif\n"
"                        <br/><br/>\n"
"                    % if ctx.get('assigned_new_shift'):\n"
"                        You have been assigned new shifts:\n"
"                    % else:\n"
"                        Please, find your planning for the following period:\n"
"                    % endif\n"
"                    </p>\n"
"                    <br/>\n"
"\n"
"                    <table style=\"table-layout: fixed; width: 80%; margin: auto;\">\n"
"                        <tr>\n"
"                            <th style=\"padding: 5px;text-align: left; width: 15%;\">From</th>\n"
"                            <td style=\"padding: 5px;\">${format_date(ctx.get('start_datetime'))}</td>\n"
"                        </tr>\n"
"                        <tr>\n"
"                            <th style=\"padding: 5px;text-align: left; width: 15%;\">To</th>\n"
"                            <td style=\"padding: 5px;\">${format_date(ctx.get('end_datetime'))}</td>\n"
"                        </tr>\n"
"                    </table>\n"
"\n"
"                    % if ctx.get('planning_url'):\n"
"                        <div style=\"margin: 15px;\">\n"
"                            <a href=\"${ctx.get('planning_url')}\" target=\"_blank\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">View Your Planning</a>\n"
"                        </div>\n"
"                    % endif\n"
"\n"
"                    % if ctx.get('slot_unassigned_count'):\n"
"                    <div>\n"
"                        <p>There are new open shifts available. Please, assign yourself if you are available.</p>\n"
"                    </div>\n"
"                    % endif\n"
"\n"
"                    % if ctx.get('message'):\n"
"                        <p>${ctx['message']}</p>\n"
"                    % endif\n"
"                </div>\n"
"            "
msgstr ""

#. module: planning
#: model:mail.template,body_html:planning.email_template_slot_single
msgid ""
"<div>\n"
"                    <p>Dear ${ctx.employee_name or ''},<br/><br/></p>\n"
"                    % if ctx.get('open_shift_available')\n"
"                    <p>A new open shift is available:</p>\n"
"                    % else\n"
"                    <p>You have been assigned the following schedule:</p>\n"
"                    % endif\n"
"                    <br/>\n"
"                    <table style=\"table-layout: fixed; width: 80%; margin: auto;\">\n"
"                        <tr>\n"
"                            <th style=\"padding: 5px;text-align: left; width: 15%;\">From</th>\n"
"                            <td style=\"padding: 5px;\">${ctx.get('start_datetime')}</td>\n"
"                        </tr>\n"
"                        <tr>\n"
"                            <th style=\"padding: 5px;text-align: left; width: 15%;\">To</th>\n"
"                            <td style=\"padding: 5px;\">${ctx.get('end_datetime')}</td>\n"
"                        </tr>\n"
"                        % if object.role_id\n"
"                        <tr>\n"
"                            <th style=\"padding: 5px;text-align: left; width: 15%;\">Role</th>\n"
"                            <td style=\"padding: 5px;\">${object.role_id.name or ''}</td>\n"
"                        </tr>\n"
"                        % endif\n"
"                        % if object.project_id\n"
"                        <tr>\n"
"                            <th style=\"padding: 5px;text-align: left; width: 15%;\">Project</th>\n"
"                            <td style=\"padding: 5px;\">${object.project_id.name or ''}</td>\n"
"                        </tr>\n"
"                        % endif\n"
"                        % if object.task_id\n"
"                        <tr>\n"
"                            <th style=\"padding: 5px;text-align: left; width: 15%;\">Task</th>\n"
"                            <td style=\"padding: 5px;\">${object.task_id.name or ''}</td>\n"
"                        </tr>\n"
"                        % endif\n"
"                        % if object.name\n"
"                        <tr>\n"
"                            <th style=\"padding: 5px;text-align: left; width: 15%;\">Note</th>\n"
"                            <td style=\"padding: 5px;\">${object.name or ''}</td>\n"
"                        </tr>\n"
"                        % endif\n"
"                    </table>\n"
"                    % if ctx.get('available_link')\n"
"                    <div>\n"
"                        <br/>\n"
"                        <span>Please, assign yourself if you are available.</span>\n"
"                    </div>\n"
"                    % endif\n"
"                    <div style=\"text-align: center\">\n"
"                        % if ctx.get('unavailable_link')\n"
"                        <div style=\"display: inline-block; margin: 15px; text-align: center\">\n"
"                            <a href=\"${ctx.unavailable_link}\" target=\"_blank\" style=\"padding: 5px 10px; color: #875A7B; text-decoration: none; background-color: #FFFFFF; border: 1px solid #FFFFFF; border-radius: 3px\">I am unavailable</a>\n"
"                        </div>\n"
"                        % endif\n"
"                        % if ctx.get('available_link')\n"
"                        <div style=\"display: inline-block; margin: 15px; text-align: center\">\n"
"                            <a href=\"${ctx.available_link}\" target=\"_blank\" style=\"padding: 5px 10px; color: #875A7B; text-decoration: none; background-color: #FFFFFF; border: 1px solid #FFFFFF; border-radius: 3px\">Assign me this shift</a>\n"
"                        </div>\n"
"                        % endif\n"
"                        % if ctx.get('link')\n"
"                        <div style=\"display: inline-block; margin: 15px; text-align: center\">\n"
"                            <a href=\"${ctx.link}\" target=\"_blank\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">View Planning</a>\n"
"                        </div>\n"
"                        % endif\n"
"                    </div>\n"
"                </div>\n"
"            "
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_shift_notification
msgid ""
"<i class=\"fa fa-check-circle text-success\"/> This shift is no longer "
"assigned to you."
msgstr ""
"<i class=\"fa fa-check-circle text-success\"/> Tato směna vám již není "
"přiřazena."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_shift_notification
msgid ""
"<i class=\"fa fa-check-circle text-success\"/> You were successfully "
"assigned this open shift."
msgstr ""
"<i class=\"fa fa-check-circle text-success\"/> Úspěšně vám byla přidělena "
"tato otevřená směna."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_kanban
msgid "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"
msgstr "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Date\" title=\"Datum\"/>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_shift_notification
msgid ""
"<i class=\"fa fa-exclamation-circle text-warning\"/> This shift is already "
"assigned to another employee."
msgstr ""
"<i class=\"fa fa-exclamation-circle text-warning\"/> Tato směna je již "
"přiřazena jinému zaměstnanci."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2 oe_edit_only\" aria-label=\"Arrow "
"icon\" title=\"Arrow\"/>"
msgstr ""
"<i class=\"fa fa-long-arrow-right mx-2 oe_edit_only\" aria-label=\"Arrow "
"icon\" title=\"Arrow\"/>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_send_view_form
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\"/>"
msgstr ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\"/>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "<span aria-label=\"Close\">×</span>"
msgstr "<span aria-label=\"Close\">×</span>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
msgid ""
"<span attrs=\"{'invisible': ['|', ('employee_id', '=', False), "
"('work_email', '!=', False)]}\" class=\"fa fa-exclamation-triangle text-"
"danger\" role=\"alert\" title=\"There is no work email address configured "
"for this employee. You will not be able to send the planning to them.\"> "
"</span>"
msgstr ""
"<span attrs=\"{'invisible': ['|', ('employee_id', '=', False), "
"('work_email', '!=', False)]}\" class=\"fa fa-exclamation-triangle text-"
"danger\" role=\"alert\" title=\"There is no work email address configured "
"for this employee. You will not be able to send the planning to them.\"> "
"</span>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
msgid "<span class=\"align-middle\">for this employee at the same time.</span>"
msgstr "<span class=\"align-middle\">pro tohoto zaměstnance současně.</span>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\" name=\"project_forecast_msg\">\n"
"                                    Recurring Shifts\n"
"                                </span>"
msgstr ""
"<span class=\"o_form_label\" name=\"project_forecast_msg\">\n"
"                                  Opakující se směny\n"
"                                </span>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
msgid "<span>Weeks</span>"
msgstr "<span>týden(y)</span>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
msgid "<span>months</span>"
msgstr "<span>měsíce</span>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_gantt
msgid "<strong>Allocated Hours: </strong>"
msgstr "<strong>Apřidělené hodiny: </strong>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_gantt
msgid "<strong>Start Date: </strong>"
msgstr "<strong>Datum začátku: </strong>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_gantt
msgid "<strong>Stop Date: </strong>"
msgstr "<strong>Datum ukončení: </strong>"

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_planning_recurrency_check_until_limit
msgid ""
"A recurrence repeating itself until a certain date must have its limit set"
msgstr ""
"Opakování, které se opakuje do určitého data, musí mít nastavený limit"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "ASSIGN ME THIS SHIFT"
msgstr "PŘIŘAZTE MĚ TENTO POSUN"

#. module: planning
#. openerp-web
#: code:addons/planning/static/src/xml/planning_gantt.xml:0
#, python-format
msgid "Add"
msgstr "Přidat"

#. module: planning
#. openerp-web
#: code:addons/planning/static/src/xml/planning_gantt.xml:0
#, python-format
msgid "Add record"
msgstr "Přidat záznam"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_send_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
msgid "Additional message"
msgstr "Další zpráva"

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_send__note
msgid "Additional message displayed in the email sent to employees"
msgstr "Další zpráva zobrazená v e-mailu zaslaném zaměstnancům"

#. module: planning
#: model:res.groups,name:planning.group_planning_manager
msgid "Administrator"
msgstr "Administrátor"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
msgid ""
"All subsequent shifts will be deleted. Are you sure you want to continue?"
msgstr ""
"Všechny následující směny budou smazány. Jste si jistý, že chcete "
"pokračovat?"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__allocated_hours
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "Allocated Hours"
msgstr "Přidělené hodiny"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__allocated_percentage
msgid "Allocated Time (%)"
msgstr "Přidělený čas (%)"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__allocation_type
msgid "Allocation Type"
msgstr "Typ přidělení"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__allow_template_creation
msgid "Allow Template Creation"
msgstr "Povolit vytváření šablon"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_res_config_settings__planning_allow_self_unassign
msgid "Allow Unassignment"
msgstr "Povolit nepřiřazení"

#. module: planning
#: code:addons/planning/models/planning_recurrency.py:0
#, python-format
msgid "An shift must be in the same company as its recurrency."
msgstr "Posun musí být ve stejné společnosti jako jeho opakování."

#. module: planning
#: model:ir.ui.menu,name:planning.planning_menu_schedule_by_employee
msgid "By Employee"
msgstr "Podle zaměstnance"

#. module: planning
#: model:ir.ui.menu,name:planning.planning_menu_schedule_by_role
msgid "By Role"
msgstr "Podle role"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_res_company__planning_allow_self_unassign
msgid "Can Employee Un-Assign Themselves?"
msgstr "Může zaměstnanec sám zrušit přiřazení"

#. module: planning
#. openerp-web
#: code:addons/planning/static/src/xml/planning_gantt.xml:0
#, python-format
msgid "Collapse rows"
msgstr "Sbalit řádky"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_role__color
#: model:ir.model.fields,field_description:planning.field_planning_slot__color
msgid "Color"
msgstr "Barva"

#. module: planning
#: model:ir.model,name:planning.model_res_company
msgid "Companies"
msgstr "Společnosti"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__company_id
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__company_id
#: model:ir.model.fields,field_description:planning.field_planning_slot__company_id
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__company_id
#: model:ir.model.fields,field_description:planning.field_slot_planning_select_send__company_id
msgid "Company"
msgstr "Firma"

#. module: planning
#: model:ir.model,name:planning.model_res_config_settings
msgid "Config Settings"
msgstr "Nastavení konfigurace"

#. module: planning
#: model:ir.ui.menu,name:planning.planning_menu_settings
msgid "Configuration"
msgstr "Konfigurace"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__confirm_delete
msgid "Confirm Slots Deletion"
msgstr "Potvrďte odstranění slotů"

#. module: planning
#. openerp-web
#: code:addons/planning/static/src/xml/planning_gantt.xml:0
#: code:addons/planning/static/src/xml/planning_gantt.xml:0
#, python-format
msgid "Copy previous week"
msgstr "Kopírovat předchozí týden"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_report_action_analysis
msgid "Create shifts to get statistics."
msgstr "Vytvořte směny a získejte statistiky."

#. module: planning
#. openerp-web
#: code:addons/planning/static/src/js/tours/planning.js:0
#, python-format
msgid ""
"Create your first shift by clicking on Add. Alternatively, you can use the "
"(+) button on each cell."
msgstr ""
"Vytvořte svůj první posun kliknutím na Přidat. Případně můžete použít "
"tlačítko (+) na každé buňce."

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__create_uid
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__create_uid
#: model:ir.model.fields,field_description:planning.field_planning_role__create_uid
#: model:ir.model.fields,field_description:planning.field_planning_send__create_uid
#: model:ir.model.fields,field_description:planning.field_planning_slot__create_uid
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__create_uid
#: model:ir.model.fields,field_description:planning.field_slot_planning_select_send__create_uid
msgid "Created by"
msgstr "Vytvořeno od"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__create_date
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__create_date
#: model:ir.model.fields,field_description:planning.field_planning_role__create_date
#: model:ir.model.fields,field_description:planning.field_planning_send__create_date
#: model:ir.model.fields,field_description:planning.field_planning_slot__create_date
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__create_date
#: model:ir.model.fields,field_description:planning.field_slot_planning_select_send__create_date
msgid "Created on"
msgstr "Vytvořeno"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
msgid "Date"
msgstr "Datum"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_hr_employee__default_planning_role_id
msgid "Default Planning Role"
msgstr "Výchozí role plánování"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "Define the different roles filled by your employees."
msgstr "Definujte různé role obsažené vašimi zaměstnanci."

#. module: planning
#: model:ir.model.fields,help:planning.field_res_company__planning_generation_interval
msgid ""
"Delay for the rate at which recurring shift should be generated in month"
msgstr ""
"Zpoždění pro rychlost, s jakou by měl být v měsíci generován opakující se "
"posun"

#. module: planning
#: model:ir.model.fields,help:planning.field_res_config_settings__planning_generation_interval
msgid "Delay for the rate at which recurring shifts should be generated"
msgstr ""
"Zpoždění pro rychlost, s jakou by se měly generovat opakující se směny"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
#: model_terms:ir.ui.view,arch_db:planning.planning_view_kanban
msgid "Delete"
msgstr "Smazat"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__department_id
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search
msgid "Department"
msgstr "Oddělení"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_send_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_report_view_form_in_gantt
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
#: model_terms:ir.ui.view,arch_db:planning.slot_planning_select_send_view_form
msgid "Discard"
msgstr "Zrušit"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_hr_employee__display_name
#: model:ir.model.fields,field_description:planning.field_planning_planning__display_name
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__display_name
#: model:ir.model.fields,field_description:planning.field_planning_role__display_name
#: model:ir.model.fields,field_description:planning.field_planning_send__display_name
#: model:ir.model.fields,field_description:planning.field_planning_slot__display_name
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__display_name
#: model:ir.model.fields,field_description:planning.field_res_company__display_name
#: model:ir.model.fields,field_description:planning.field_res_config_settings__display_name
#: model:ir.model.fields,field_description:planning.field_slot_planning_select_send__display_name
msgid "Display Name"
msgstr "Zobrazované jméno"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
msgid "Documentation"
msgstr "Dokumentace"

#. module: planning
#: model_terms:digest.tip,tip_description:planning.digest_tip_planning_0
msgid ""
"Drag a shift to another day to reschedule it, or to another row to reassign "
"the shift. If the shift was published, the user will automatically be "
"notified of the change. Press CTRL (or Cmd on Mac) while dragging a shift to"
" duplicate it."
msgstr ""
"Přetažením směny do jiného dne ji můžete změnit na jiný termín, nebo do "
"jiného řádku směnu znovu přiřadíte. Pokud byla směna zveřejněna, bude "
"uživatel automaticky informován o změně. Stisknutím klávesy CTRL (nebo Cmd "
"na Macu) při tažení posunu duplikujte."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_kanban
msgid "Dropdown menu"
msgstr "Rozbalovací nabídka"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__duration
msgid "Duration (Hours)"
msgstr "Doba trvání (hodiny)"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_kanban
msgid "Edit Slot"
msgstr "Upravit slot"

#. module: planning
#: model:ir.model,name:planning.model_hr_employee
#: model:ir.model.fields,field_description:planning.field_planning_slot__employee_id
#: model:ir.model.fields,field_description:planning.field_slot_planning_select_send__employee_ids
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search
msgid "Employee"
msgstr "Zaměstnanec"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_role__employee_ids
#: model:ir.model.fields,field_description:planning.field_planning_send__employee_ids
msgid "Employees"
msgstr "Zaměstnanci"

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_send__employee_ids
msgid ""
"Employees who will receive planning by email if you click on publish & send."
msgstr ""
"Zaměstnanci, kteří obdrží plánování e-mailem, pokud kliknete na publikovat a"
" odeslat."

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_shift_template
msgid "Encode your shifts in one click by using templates."
msgstr "Zakódujte své směny jedním kliknutím pomocí šablon."

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__end_datetime
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "End Date"
msgstr "Datum ukončení"

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_hr_employee_employee_token_unique
msgid "Error: each employee token must be unique"
msgstr "Chyba: každý token zaměstnance musí být jedinečný"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
msgid "Every"
msgstr "Každý"

#. module: planning
#: code:addons/planning/models/planning_recurrency.py:0
#, python-format
msgid "Every %s week(s) until %s"
msgstr "Každý %s týden (y) do %s"

#. module: planning
#. openerp-web
#: code:addons/planning/static/src/xml/planning_gantt.xml:0
#, python-format
msgid "Expand rows"
msgstr "Rozbalte řádky"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_send__note
msgid "Extra Message"
msgstr "Zvláštní zpráva"

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__allocation_type__forecast
msgid "Forecast"
msgstr "Předpověď"

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__planning_recurrency__repeat_type__forever
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__repeat_type__forever
msgid "Forever"
msgstr "Trvale"

#. module: planning
#: code:addons/planning/models/planning_recurrency.py:0
#, python-format
msgid "Forever, every %s week(s)"
msgstr "Navždy každý %s týden (y)"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "From"
msgstr "Od"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search
msgid "Future"
msgstr "Budoucnost"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.brand_promotion
msgid "Give depth to your"
msgstr "Dejte hloubku svému"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_search
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search
msgid "Group By"
msgstr "Seskupit podle"

#. module: planning
#: model:ir.filters,name:planning.planning_filter_by_employee
msgid "Hours per Employee"
msgstr "Hodiny na zaměstnance"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
msgid "I Am Unavailable"
msgstr "Jsem nedostupný"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
msgid "I Take It"
msgstr "Beru to"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "I am unavailable"
msgstr "Jsem nedostupný"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_hr_employee__id
#: model:ir.model.fields,field_description:planning.field_planning_planning__id
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__id
#: model:ir.model.fields,field_description:planning.field_planning_role__id
#: model:ir.model.fields,field_description:planning.field_planning_send__id
#: model:ir.model.fields,field_description:planning.field_planning_slot__id
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__id
#: model:ir.model.fields,field_description:planning.field_res_company__id
#: model:ir.model.fields,field_description:planning.field_res_config_settings__id
#: model:ir.model.fields,field_description:planning.field_slot_planning_select_send__id
msgid "ID"
msgstr "ID"

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_slot__publication_warning
msgid ""
"If checked, it means that the shift contains has changed since its last "
"publish."
msgstr ""
"Je-li zaškrtnuto, znamená to, že směna obsahuje se od posledního publikování"
" změnila."

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_slot__is_published
msgid ""
"If checked, this means the planning entry has been sent to the employee. "
"Modifying the planning entry will mark it as not sent."
msgstr ""
"Pokud je zatrženo, znamená to, že záznam o plánování byl odeslán "
"zaměstnanci. Úpravou položky plánování ji označíte jako neodeslanou."

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_slot__repeat_until
msgid ""
"If set, the recurrence stop at that date. Otherwise, the recurrence is "
"applied indefinitely."
msgstr ""
"Pokud je nastaveno, opakování se zastaví k tomuto datu. Jinak se opakování "
"použije na dobu neurčitou."

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_send__include_unassigned
msgid "Include Open Shifts"
msgstr "Zahrnout otevřené směny"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__include_unassigned
msgid "Includes Open Shifts"
msgstr "Zahrnuje otevřené směny"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__is_published
msgid "Is The Shift Sent"
msgstr "Je směna odeslána"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__is_assigned_to_me
msgid "Is This Shift Assigned To The Current User"
msgstr "Je tento posun přiřazen aktuálnímu uživateli"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__is_past
msgid "Is This Shift In The Past?"
msgstr "Je to posun v minulosti?"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__last_generated_end_datetime
msgid "Last Generated End Date"
msgstr "Datum poslední generované"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_hr_employee____last_update
#: model:ir.model.fields,field_description:planning.field_planning_planning____last_update
#: model:ir.model.fields,field_description:planning.field_planning_recurrency____last_update
#: model:ir.model.fields,field_description:planning.field_planning_role____last_update
#: model:ir.model.fields,field_description:planning.field_planning_send____last_update
#: model:ir.model.fields,field_description:planning.field_planning_slot____last_update
#: model:ir.model.fields,field_description:planning.field_planning_slot_template____last_update
#: model:ir.model.fields,field_description:planning.field_res_company____last_update
#: model:ir.model.fields,field_description:planning.field_res_config_settings____last_update
#: model:ir.model.fields,field_description:planning.field_slot_planning_select_send____last_update
msgid "Last Modified on"
msgstr "Naposled změněno"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__write_uid
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__write_uid
#: model:ir.model.fields,field_description:planning.field_planning_role__write_uid
#: model:ir.model.fields,field_description:planning.field_planning_send__write_uid
#: model:ir.model.fields,field_description:planning.field_planning_slot__write_uid
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__write_uid
#: model:ir.model.fields,field_description:planning.field_slot_planning_select_send__write_uid
msgid "Last Updated by"
msgstr "Naposledy upraveno od"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__write_date
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__write_date
#: model:ir.model.fields,field_description:planning.field_planning_role__write_date
#: model:ir.model.fields,field_description:planning.field_planning_send__write_date
#: model:ir.model.fields,field_description:planning.field_planning_slot__write_date
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__write_date
#: model:ir.model.fields,field_description:planning.field_slot_planning_select_send__write_date
msgid "Last Updated on"
msgstr "Naposled upraveno"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__allow_self_unassign
msgid "Let Employee Unassign Themselves"
msgstr "Nechte zaměstnance zrušit přiřazení"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
msgid "Let employees unassign themselves from shifts"
msgstr "Nechte zaměstnance, aby se přiřadili ke směnám"

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_slot__allow_self_unassign
#: model:ir.model.fields,help:planning.field_res_company__planning_allow_self_unassign
#: model:ir.model.fields,help:planning.field_res_config_settings__planning_allow_self_unassign
msgid "Let your employees un-assign themselves from shifts when unavailable"
msgstr ""
"Nechte své zaměstnance zrušit přiřazení ze směn, pokud nejsou k dispozici"

#. module: planning
#. openerp-web
#: code:addons/planning/static/src/js/tours/planning.js:0
#, python-format
msgid "Let's start managing your employees' schedule!"
msgstr "Začněme spravovat plán vašich zaměstnanců!"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_my_calendar
#: model_terms:ir.actions.act_window,help:planning.planning_action_my_gantt
#: model_terms:ir.actions.act_window,help:planning.planning_action_schedule_by_employee
#: model_terms:ir.actions.act_window,help:planning.planning_action_schedule_by_role
msgid "Let's start your planning by adding a new shift."
msgstr "Začněme s plánováním přidáním nové směny."

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__manager_id
msgid "Manager"
msgstr "Manažer"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__publication_warning
msgid "Modified Since Last Publication"
msgstr "Upraveno od poslední publikace"

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_action_my_calendar
#: model:ir.actions.act_window,name:planning.planning_action_my_gantt
#: model:ir.actions.act_window,name:planning.planning_action_open_shift
#: model:ir.ui.menu,name:planning.planning_menu_my_planning
msgid "My Planning"
msgstr "Moje plánování"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search
msgid "My Shifts"
msgstr "Moje směny"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search
msgid "My Team"
msgstr "Můj tým"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_role__name
msgid "Name"
msgstr "Název"

#. module: planning
#. openerp-web
#: code:addons/planning/static/src/xml/planning_gantt.xml:0
#, python-format
msgid "Next"
msgstr "Další"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_report_action_analysis
msgid "No data yet!"
msgstr "Zatím žádná data!"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "No roles found"
msgstr "Nebyly nalezeny žádné role"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_shift_template
msgid "No shift templates found"
msgstr "Nebyly nalezeny žádné šablony směn"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_my_calendar
#: model_terms:ir.actions.act_window,help:planning.planning_action_my_gantt
#: model_terms:ir.actions.act_window,help:planning.planning_action_schedule_by_employee
#: model_terms:ir.actions.act_window,help:planning.planning_action_schedule_by_role
msgid "No shifts found"
msgstr "Nebyly nalezeny žádné směny"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__name
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "Note"
msgstr "Poznámka"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__working_days_count
msgid "Number of Working Days"
msgstr "Počet pracovních dnů"

#. module: planning
#. openerp-web
#: code:addons/planning/static/src/js/planning_gantt_controller.js:0
#, python-format
msgid "Open"
msgstr "Otevřít"

#. module: planning
#. openerp-web
#: code:addons/planning/static/src/js/planning_gantt_model.js:0
#: code:addons/planning/static/src/js/planning_gantt_model.js:0
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search
#, python-format
msgid "Open Shifts"
msgstr "Otevřené směny"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "Open Shifts Available"
msgstr "Otevřené směny k dispozici"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search
msgid "Open Shifts for my Roles"
msgstr "Otevřít směny pro mé role"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__overlap_slot_count
msgid "Overlapping Slots"
msgstr "Překrývající se sloty"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search
msgid "Past"
msgstr "Minulý"

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_slot__allocated_percentage
msgid "Percentage of time the employee is supposed to work during the shift."
msgstr "Procento času, kdy má zaměstnanec během směny pracovat."

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_send__start_datetime
#: model_terms:ir.ui.view,arch_db:planning.planning_send_view_form
msgid "Period"
msgstr "Období"

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__allocation_type__planning
#: model:ir.ui.menu,name:planning.planning_menu_root
#: model_terms:ir.ui.view,arch_db:planning.hr_employee_view_form_inherit
#: model_terms:ir.ui.view,arch_db:planning.planning_view_calendar
#: model_terms:ir.ui.view,arch_db:planning.planning_view_my_calendar
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
msgid "Planning"
msgstr "Plánování"

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_report_action_analysis
#: model:ir.ui.menu,name:planning.planning_menu_planning_analysis
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_report_view_graph
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_report_view_pivot
#: model_terms:ir.ui.view,arch_db:planning.planning_view_graph
#: model_terms:ir.ui.view,arch_db:planning.planning_view_pivot
msgid "Planning Analysis"
msgstr "Analýza plánování"

#. module: planning
#: model:ir.model,name:planning.model_planning_recurrency
msgid "Planning Recurrence"
msgstr "Plánování opakování"

#. module: planning
#: model:ir.model,name:planning.model_planning_role
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_form
msgid "Planning Role"
msgstr "Role plánování"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_tree
msgid "Planning Role List"
msgstr "Seznam rolí plánování"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_hr_employee__planning_role_ids
msgid "Planning Roles"
msgstr "Plánovací role"

#. module: planning
#: model:ir.model,name:planning.model_planning_slot
msgid "Planning Shift"
msgstr "Plánování směny"

#. module: planning
#: code:addons/planning/models/planning.py:0
#, python-format
msgid "Planning from %s to %s"
msgstr "Plánování od %s na %s"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "Planning:"
msgstr "Plánování:"

#. module: planning
#: model:ir.actions.server,name:planning.ir_cron_forecast_schedule_ir_actions_server
#: model:ir.cron,cron_name:planning.ir_cron_forecast_schedule
#: model:ir.cron,name:planning.ir_cron_forecast_schedule
msgid "Planning: generate next recurring shifts"
msgstr "Plánování: generování dalších opakujících se směn"

#. module: planning
#: code:addons/planning/models/planning.py:0
#, python-format
msgid "Planning: new open shift available"
msgstr "Plánování: k dispozici nová otevřená směna"

#. module: planning
#: code:addons/planning/models/planning.py:0
#, python-format
msgid "Planning: new shift"
msgstr "Plánování: nová směna"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.brand_promotion
msgid "Plans"
msgstr "Plány"

#. module: planning
#. openerp-web
#: code:addons/planning/static/src/xml/planning_gantt.xml:0
#, python-format
msgid "Previous"
msgstr "Předchozí"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__previous_template_id
msgid "Previous Template"
msgstr "Předchozí šablona"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_send_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
msgid "Publish"
msgstr "Publikovat"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_send_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
msgid "Publish & Send"
msgstr "Publikovat a odeslat"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search
msgid "Published"
msgstr "Publikováno"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_res_company__planning_generation_interval
#: model:ir.model.fields,field_description:planning.field_res_config_settings__planning_generation_interval
msgid "Rate Of Shift Generation"
msgstr "Rychlost generování směny"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
msgid "Rate of shift generation"
msgstr "Rychlost generování směny"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__recurrency_id
msgid "Recurrency"
msgstr "Opakování"

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_planning_recurrency_check_repeat_interval_positive
msgid "Recurrency repeat interval should be at least 1"
msgstr "Interval opakování opakování by měl být alespoň 1"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__slot_ids
msgid "Related Planning Entries"
msgstr "Související položky plánování"

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_slot__user_id
msgid "Related user name for the resource to manage its access."
msgstr "Vztažené uživatelské jméno pro zdroj ke spravování jeho přístupu."

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__repeat
msgid "Repeat"
msgstr "Opakovat"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__repeat_interval
msgid "Repeat Every"
msgstr "Opakovat každý"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__repeat_type
msgid "Repeat Type"
msgstr "Opakujte typ"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__repeat_until
#: model:ir.model.fields,field_description:planning.field_planning_slot__repeat_until
msgid "Repeat Until"
msgstr "Konec opakování"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__repeat_interval
msgid "Repeat every"
msgstr "Opakujte každý"

#. module: planning
#: model:ir.ui.menu,name:planning.planning_menu_reporting
msgid "Reporting"
msgstr "Přehledy"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__role_id
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__role_id
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_search
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search
msgid "Role"
msgstr "Role"

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_action_roles
#: model:ir.ui.menu,name:planning.planning_menu_settings_role
msgid "Roles"
msgstr "Role"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
msgid "Save"
msgstr "Uložit"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
msgid "Save as Template"
msgstr "Uložit jako šablonu"

#. module: planning
#. openerp-web
#: code:addons/planning/static/src/js/tours/planning.js:0
#, python-format
msgid "Save this shift once it is ready."
msgstr "Jakmile je změna připravena, uložte ji."

#. module: planning
#: model:ir.model,name:planning.model_planning_planning
#: model:ir.ui.menu,name:planning.planning_menu_schedule
#: model_terms:ir.ui.view,arch_db:planning.planning_view_gantt
msgid "Schedule"
msgstr "Naplánovat"

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_action_schedule_by_employee
msgid "Schedule by Employee"
msgstr "Rozvrh podle zaměstnance"

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_action_schedule_by_role
msgid "Schedule by Role"
msgstr "Rozvrh podle role"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_hr_employee__employee_token
#: model:ir.model.fields,field_description:planning.field_planning_planning__access_token
#: model:ir.model.fields,field_description:planning.field_planning_slot__access_token
msgid "Security Token"
msgstr "Bezpečnostní token"

#. module: planning
#: model:ir.model,name:planning.model_slot_planning_select_send
msgid "Select Employees and Send One Slot"
msgstr "Vyberte zaměstnance a odešlete jeden slot"

#. module: planning
#: code:addons/planning/wizard/planning_send.py:0
#, python-format
msgid "Select the employees you would like to send the planning to."
msgstr "Vyberte zaměstnance, kterým chcete poslat plánování."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
#: model_terms:ir.ui.view,arch_db:planning.slot_planning_select_send_view_form
msgid "Send"
msgstr "Odeslat"

#. module: planning
#: model:ir.model,name:planning.model_planning_send
msgid "Send Planning"
msgstr "Odeslat plánování"

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_send_action
msgid "Send Planning By Email"
msgstr "Zašlete plánování e-mailem"

#. module: planning
#. openerp-web
#: code:addons/planning/static/src/xml/planning_gantt.xml:0
#: code:addons/planning/static/src/xml/planning_gantt.xml:0
#, python-format
msgid "Send schedule"
msgstr "Odeslat plán"

#. module: planning
#. openerp-web
#: code:addons/planning/static/src/js/tours/planning.js:0
#, python-format
msgid "Send the schedule and mark the shifts as published. Congratulations!"
msgstr "Odešlete plán a označte směny jako zveřejněné. Gratulujeme!"

#. module: planning
#. openerp-web
#: code:addons/planning/static/src/js/tours/planning.js:0
#, python-format
msgid "Send the schedule to your employees once it is ready."
msgstr "Jakmile je plán připraven, odešlete jej svým zaměstnancům."

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_role__sequence
msgid "Sequence"
msgstr "Číselná řada"

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_action_settings
#: model:ir.ui.menu,name:planning.planning_menu_settings_config
msgid "Settings"
msgstr "Nastavení"

#. module: planning
#: code:addons/planning/controllers/main.py:0
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
#, python-format
msgid "Shift"
msgstr "Posun"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_tree
msgid "Shift List"
msgstr "Shift list"

#. module: planning
#: model:ir.model,name:planning.model_planning_slot_template
msgid "Shift Template"
msgstr "Šablona posunu"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_form
msgid "Shift Template Form"
msgstr "Formulář šablony Shift"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_tree
msgid "Shift Template List"
msgstr "Posun seznamu šablon"

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_action_shift_template
#: model:ir.model.fields,field_description:planning.field_planning_slot__template_id
#: model:ir.ui.menu,name:planning.planning_menu_settings_shift_template
msgid "Shift Templates"
msgstr "Šablony směn"

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_planning_slot_check_start_date_lower_end_date
msgid "Shift end date should be greater than its start date"
msgstr "Datum ukončení posunu by mělo být větší než datum zahájení"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_slot_planning_select_send__slot_id
msgid "Shifts"
msgstr "Směny"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "Shifts from"
msgstr "Posun od"

#. module: planning
#: code:addons/planning/models/planning.py:0
#, python-format
msgid "Shifts in conflict"
msgstr "Změny v konfliktu"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search
msgid "Shifts of Your Team Member"
msgstr "Posuny člena vašeho týmu"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__slot_ids
#: model:ir.model.fields,field_description:planning.field_planning_send__slot_ids
msgid "Slot"
msgstr "Slot"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_gantt
msgid "Some changes were made since this shift was published."
msgstr "Od zveřejnění tohoto posunu byly provedeny určité změny."

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__start_datetime
#: model:ir.model.fields,field_description:planning.field_planning_slot__start_datetime
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search
msgid "Start Date"
msgstr "Počáteční datum"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__start_time
msgid "Start Hour"
msgstr "Počáteční hodina"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_search
msgid "Start Time"
msgstr "Doba spuštění"

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_planning_slot_template_check_start_time_positive
msgid "Start hour must be a positive number"
msgstr "Počáteční hodina musí být kladné číslo"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__end_datetime
#: model:ir.model.fields,field_description:planning.field_planning_send__end_datetime
msgid "Stop Date"
msgstr "Datum zastavení"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__template_autocomplete_ids
msgid "Template Autocomplete"
msgstr "Automatické doplňování šablony"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__template_reset
msgid "Template Reset"
msgstr "Obnovení šablony"

#. module: planning
#: code:addons/planning/models/planning.py:0
#, python-format
msgid "The company does not allow you to self unassign."
msgstr "Společnost vám neumožňuje zrušit přiřazení."

#. module: planning
#: code:addons/planning/models/planning.py:0
#, python-format
msgid "The recurrence until date should be after the shift start date"
msgstr "Opakování do data by mělo být po datu zahájení směny"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "There are no planning slot to display. Please, refer to your manager."
msgstr ""
"Neexistuje žádný plánovací slot k zobrazení. Obraťte se na svého "
"nadřízeného."

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__was_copied
msgid "This Shift Was Copied From Previous Week"
msgstr "Tento posun byl zkopírován z předchozího týdne"

#. module: planning
#: code:addons/planning/wizard/planning_send.py:0
#, python-format
msgid ""
"This action is not allowed as there are no shifts planned for the selected "
"time period."
msgstr ""
"Tato akce není povolena, protože pro vybrané časové období nejsou plánovány "
"žádné směny."

#. module: planning
#: model:digest.tip,name:planning.digest_tip_planning_0
msgid "Tip: Record your planning faster"
msgstr "Tip: Zaznamenejte své plánování rychleji"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "To"
msgstr "Do"

#. module: planning
#. openerp-web
#: code:addons/planning/static/src/xml/planning_gantt.xml:0
#, python-format
msgid "Today"
msgstr "Dnes"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search
msgid "Unpublished"
msgstr "Nepublikovaný"

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__planning_recurrency__repeat_type__until
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__repeat_type__until
msgid "Until"
msgstr "Konec"

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_recurrency__repeat_until
msgid "Up to which date should the plannings be repeated"
msgstr "Do kterého data by se plánování měla opakovat"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__user_id
#: model:res.groups,name:planning.group_planning_user
msgid "User"
msgstr "Uživatel"

#. module: planning
#: code:addons/planning/models/hr.py:0
#: model:ir.actions.server,name:planning.action_hr_employee_planning_view
#, python-format
msgid "View Planning"
msgstr "Zobrazit plánování"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__repeat_type
msgid "Weeks"
msgstr "Týdny"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__work_email
msgid "Work Email"
msgstr "Pracovní email"

#. module: planning
#: code:addons/planning/models/planning.py:0
#, python-format
msgid "You can not assign yourself to an already assigned shift."
msgstr "Nemůžete se přiřadit k již přiřazené směně."

#. module: planning
#: code:addons/planning/models/planning.py:0
#, python-format
msgid "You can not unassign another employee than yourself."
msgstr "Nemůžete zrušit přiřazení jiného zaměstnance než sebe."

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_planning_slot_template_check_duration_positive
msgid "You cannot have a negative duration"
msgstr "Nemůžete mít záporné trvání"

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_planning_slot_template_check_start_time_lower_than_24
msgid "You cannot have a start hour greater than 24"
msgstr "Nemůžete mít počáteční hodinu větší než 24"

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_planning_slot_check_allocated_hours_positive
msgid "You cannot have negative shift"
msgstr "Nemůžete mít negativní posun"

#. module: planning
#: code:addons/planning/wizard/slot_planning_select_send.py:0
#, python-format
msgid "You cannot send a past unassigned slot"
msgstr "Nelze odeslat minulý nepřiřazený slot"

#. module: planning
#: code:addons/planning/models/planning.py:0
#, python-format
msgid "You don't the right to self assign."
msgstr "Nemáte právo na vlastní přiřazení."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.slot_unassign
msgid "You have been successfully unassigned from this shift"
msgstr "Z této směny jste byli úspěšně zrušeni"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.slot_unassign
msgid "Your Planning"
msgstr "Vaše plánování"

#. module: planning
#: model:mail.template,subject:planning.email_template_planning_planning
msgid ""
"Your planning from ${format_date(ctx.get('start_datetime'))} to "
"${format_date(ctx.get('end_datetime'))}"
msgstr ""
"Vaše plánování od $ {format_date (ctx.get ('start_datetime'))} do $ "
"{format_date (ctx.get ('end_datetime'))}"

#. module: planning
#: code:addons/planning/models/planning.py:0
#, python-format
msgid "Your template was successfully saved."
msgstr "Vaše šablona byla úspěšně uložena."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
msgid "other shift(s)"
msgstr "jiná směna"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_gantt
msgid "other shift(s) in conflict."
msgstr "jiné směny v konfliktu."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "to"
msgstr "k"
