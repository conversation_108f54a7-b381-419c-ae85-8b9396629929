# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* planning
# 
# Translators:
# <PERSON>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# Tuomo <PERSON>ra <<EMAIL>>, 2021
# Jarmo <PERSON>tjärvi <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-11-27 13:47+0000\n"
"PO-Revision-Date: 2020-09-07 08:22+0000\n"
"Last-Translator: Jarmo Kortetjärvi <<EMAIL>>, 2022\n"
"Language-Team: Finnish (https://www.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: planning
#: model:mail.template,subject:planning.email_template_slot_single
msgid "${ctx.mail_subject}"
msgstr ""

#. module: planning
#: code:addons/planning/models/planning_template.py:0
#, python-format
msgid "(%s days span)"
msgstr ""

#. module: planning
#: model_terms:digest.tip,tip_description:planning.digest_tip_planning_0
msgid "<b class=\"tip_title\">Tip: Record your planning faster</b>"
msgstr ""

#. module: planning
#: model:mail.template,body_html:planning.email_template_planning_planning
msgid ""
"<div>\n"
"                    <p>\n"
"                    % if ctx.get('employee'):\n"
"                        Dear ${ctx['employee'].name},\n"
"                    % else:\n"
"                        Hello,\n"
"                    % endif\n"
"                        <br/><br/>\n"
"                    % if ctx.get('assigned_new_shift'):\n"
"                        You have been assigned new shifts:\n"
"                    % else:\n"
"                        Please, find your planning for the following period:\n"
"                    % endif\n"
"                    </p>\n"
"                    <br/>\n"
"\n"
"                    <table style=\"table-layout: fixed; width: 80%; margin: auto;\">\n"
"                        <tr>\n"
"                            <th style=\"padding: 5px;text-align: left; width: 15%;\">From</th>\n"
"                            <td style=\"padding: 5px;\">${format_date(ctx.get('start_datetime'))}</td>\n"
"                        </tr>\n"
"                        <tr>\n"
"                            <th style=\"padding: 5px;text-align: left; width: 15%;\">To</th>\n"
"                            <td style=\"padding: 5px;\">${format_date(ctx.get('end_datetime'))}</td>\n"
"                        </tr>\n"
"                    </table>\n"
"\n"
"                    % if ctx.get('planning_url'):\n"
"                        <div style=\"margin: 15px;\">\n"
"                            <a href=\"${ctx.get('planning_url')}\" target=\"_blank\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">View Your Planning</a>\n"
"                        </div>\n"
"                    % endif\n"
"\n"
"                    % if ctx.get('slot_unassigned_count'):\n"
"                    <div>\n"
"                        <p>There are new open shifts available. Please, assign yourself if you are available.</p>\n"
"                    </div>\n"
"                    % endif\n"
"\n"
"                    % if ctx.get('message'):\n"
"                        <p>${ctx['message']}</p>\n"
"                    % endif\n"
"                </div>\n"
"            "
msgstr ""

#. module: planning
#: model:mail.template,body_html:planning.email_template_slot_single
msgid ""
"<div>\n"
"                    <p>Dear ${ctx.employee_name or ''},<br/><br/></p>\n"
"                    % if ctx.get('open_shift_available')\n"
"                    <p>A new open shift is available:</p>\n"
"                    % else\n"
"                    <p>You have been assigned the following schedule:</p>\n"
"                    % endif\n"
"                    <br/>\n"
"                    <table style=\"table-layout: fixed; width: 80%; margin: auto;\">\n"
"                        <tr>\n"
"                            <th style=\"padding: 5px;text-align: left; width: 15%;\">From</th>\n"
"                            <td style=\"padding: 5px;\">${ctx.get('start_datetime')}</td>\n"
"                        </tr>\n"
"                        <tr>\n"
"                            <th style=\"padding: 5px;text-align: left; width: 15%;\">To</th>\n"
"                            <td style=\"padding: 5px;\">${ctx.get('end_datetime')}</td>\n"
"                        </tr>\n"
"                        % if object.role_id\n"
"                        <tr>\n"
"                            <th style=\"padding: 5px;text-align: left; width: 15%;\">Role</th>\n"
"                            <td style=\"padding: 5px;\">${object.role_id.name or ''}</td>\n"
"                        </tr>\n"
"                        % endif\n"
"                        % if object.project_id\n"
"                        <tr>\n"
"                            <th style=\"padding: 5px;text-align: left; width: 15%;\">Project</th>\n"
"                            <td style=\"padding: 5px;\">${object.project_id.name or ''}</td>\n"
"                        </tr>\n"
"                        % endif\n"
"                        % if object.task_id\n"
"                        <tr>\n"
"                            <th style=\"padding: 5px;text-align: left; width: 15%;\">Task</th>\n"
"                            <td style=\"padding: 5px;\">${object.task_id.name or ''}</td>\n"
"                        </tr>\n"
"                        % endif\n"
"                        % if object.name\n"
"                        <tr>\n"
"                            <th style=\"padding: 5px;text-align: left; width: 15%;\">Note</th>\n"
"                            <td style=\"padding: 5px;\">${object.name or ''}</td>\n"
"                        </tr>\n"
"                        % endif\n"
"                    </table>\n"
"                    % if ctx.get('available_link')\n"
"                    <div>\n"
"                        <br/>\n"
"                        <span>Please, assign yourself if you are available.</span>\n"
"                    </div>\n"
"                    % endif\n"
"                    <div style=\"text-align: center\">\n"
"                        % if ctx.get('unavailable_link')\n"
"                        <div style=\"display: inline-block; margin: 15px; text-align: center\">\n"
"                            <a href=\"${ctx.unavailable_link}\" target=\"_blank\" style=\"padding: 5px 10px; color: #875A7B; text-decoration: none; background-color: #FFFFFF; border: 1px solid #FFFFFF; border-radius: 3px\">I am unavailable</a>\n"
"                        </div>\n"
"                        % endif\n"
"                        % if ctx.get('available_link')\n"
"                        <div style=\"display: inline-block; margin: 15px; text-align: center\">\n"
"                            <a href=\"${ctx.available_link}\" target=\"_blank\" style=\"padding: 5px 10px; color: #875A7B; text-decoration: none; background-color: #FFFFFF; border: 1px solid #FFFFFF; border-radius: 3px\">Assign me this shift</a>\n"
"                        </div>\n"
"                        % endif\n"
"                        % if ctx.get('link')\n"
"                        <div style=\"display: inline-block; margin: 15px; text-align: center\">\n"
"                            <a href=\"${ctx.link}\" target=\"_blank\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">View Planning</a>\n"
"                        </div>\n"
"                        % endif\n"
"                    </div>\n"
"                </div>\n"
"            "
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_shift_notification
msgid ""
"<i class=\"fa fa-check-circle text-success\"/> This shift is no longer "
"assigned to you."
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_shift_notification
msgid ""
"<i class=\"fa fa-check-circle text-success\"/> You were successfully "
"assigned this open shift."
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_kanban
msgid "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"
msgstr ""
"<i class = \"fa fa-clock-o\" role = \"img\" aria-label = \"Date\" title = "
"\"Päiväys\" />"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_shift_notification
msgid ""
"<i class=\"fa fa-exclamation-circle text-warning\"/> This shift is already "
"assigned to another employee."
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2 oe_edit_only\" aria-label=\"Arrow "
"icon\" title=\"Arrow\"/>"
msgstr ""
"<i class=\"fa fa-long-arrow-right mx-2 oe_edit_only\" aria-label=\"Arrow "
"icon\" title=\"Arrow\"/>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_send_view_form
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\"/>"
msgstr ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\"/>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "<span aria-label=\"Close\">×</span>"
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
msgid ""
"<span attrs=\"{'invisible': ['|', ('employee_id', '=', False), "
"('work_email', '!=', False)]}\" class=\"fa fa-exclamation-triangle text-"
"danger\" role=\"alert\" title=\"There is no work email address configured "
"for this employee. You will not be able to send the planning to them.\"> "
"</span>"
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
msgid "<span class=\"align-middle\">for this employee at the same time.</span>"
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\" name=\"project_forecast_msg\">\n"
"                                    Recurring Shifts\n"
"                                </span>"
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
msgid "<span>Weeks</span>"
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
msgid "<span>months</span>"
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_gantt
msgid "<strong>Allocated Hours: </strong>"
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_gantt
msgid "<strong>Start Date: </strong>"
msgstr "<strong>Aloituspäivä: </strong>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_gantt
msgid "<strong>Stop Date: </strong>"
msgstr "<strong>Lopetuspäivä: </strong>"

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_planning_recurrency_check_until_limit
msgid ""
"A recurrence repeating itself until a certain date must have its limit set"
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "ASSIGN ME THIS SHIFT"
msgstr ""

#. module: planning
#. openerp-web
#: code:addons/planning/static/src/xml/planning_gantt.xml:0
#, python-format
msgid "Add"
msgstr "Lisää"

#. module: planning
#. openerp-web
#: code:addons/planning/static/src/xml/planning_gantt.xml:0
#, python-format
msgid "Add record"
msgstr "Lisää tietue"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_send_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
msgid "Additional message"
msgstr ""

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_send__note
msgid "Additional message displayed in the email sent to employees"
msgstr ""

#. module: planning
#: model:res.groups,name:planning.group_planning_manager
msgid "Administrator"
msgstr "Hallinnoija"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
msgid ""
"All subsequent shifts will be deleted. Are you sure you want to continue?"
msgstr ""

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__allocated_hours
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "Allocated Hours"
msgstr ""

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__allocated_percentage
msgid "Allocated Time (%)"
msgstr ""

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__allocation_type
msgid "Allocation Type"
msgstr ""

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__allow_template_creation
msgid "Allow Template Creation"
msgstr ""

#. module: planning
#: model:ir.model.fields,field_description:planning.field_res_config_settings__planning_allow_self_unassign
msgid "Allow Unassignment"
msgstr ""

#. module: planning
#: code:addons/planning/models/planning_recurrency.py:0
#, python-format
msgid "An shift must be in the same company as its recurrency."
msgstr ""

#. module: planning
#: model:ir.ui.menu,name:planning.planning_menu_schedule_by_employee
msgid "By Employee"
msgstr "Työntekijöittäin"

#. module: planning
#: model:ir.ui.menu,name:planning.planning_menu_schedule_by_role
msgid "By Role"
msgstr ""

#. module: planning
#: model:ir.model.fields,field_description:planning.field_res_company__planning_allow_self_unassign
msgid "Can Employee Un-Assign Themselves?"
msgstr ""

#. module: planning
#. openerp-web
#: code:addons/planning/static/src/xml/planning_gantt.xml:0
#, python-format
msgid "Collapse rows"
msgstr "Supista rivit"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_role__color
#: model:ir.model.fields,field_description:planning.field_planning_slot__color
msgid "Color"
msgstr "Väri"

#. module: planning
#: model:ir.model,name:planning.model_res_company
msgid "Companies"
msgstr "Yritykset"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__company_id
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__company_id
#: model:ir.model.fields,field_description:planning.field_planning_slot__company_id
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__company_id
#: model:ir.model.fields,field_description:planning.field_slot_planning_select_send__company_id
msgid "Company"
msgstr "Yritys"

#. module: planning
#: model:ir.model,name:planning.model_res_config_settings
msgid "Config Settings"
msgstr "Konfiguraatioasetukset"

#. module: planning
#: model:ir.ui.menu,name:planning.planning_menu_settings
msgid "Configuration"
msgstr "Asetukset"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__confirm_delete
msgid "Confirm Slots Deletion"
msgstr ""

#. module: planning
#. openerp-web
#: code:addons/planning/static/src/xml/planning_gantt.xml:0
#: code:addons/planning/static/src/xml/planning_gantt.xml:0
#, python-format
msgid "Copy previous week"
msgstr ""

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_report_action_analysis
msgid "Create shifts to get statistics."
msgstr ""

#. module: planning
#. openerp-web
#: code:addons/planning/static/src/js/tours/planning.js:0
#, python-format
msgid ""
"Create your first shift by clicking on Add. Alternatively, you can use the "
"(+) button on each cell."
msgstr ""

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__create_uid
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__create_uid
#: model:ir.model.fields,field_description:planning.field_planning_role__create_uid
#: model:ir.model.fields,field_description:planning.field_planning_send__create_uid
#: model:ir.model.fields,field_description:planning.field_planning_slot__create_uid
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__create_uid
#: model:ir.model.fields,field_description:planning.field_slot_planning_select_send__create_uid
msgid "Created by"
msgstr "Luonut"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__create_date
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__create_date
#: model:ir.model.fields,field_description:planning.field_planning_role__create_date
#: model:ir.model.fields,field_description:planning.field_planning_send__create_date
#: model:ir.model.fields,field_description:planning.field_planning_slot__create_date
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__create_date
#: model:ir.model.fields,field_description:planning.field_slot_planning_select_send__create_date
msgid "Created on"
msgstr "Luotu"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
msgid "Date"
msgstr "Päivämäärä"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_hr_employee__default_planning_role_id
msgid "Default Planning Role"
msgstr ""

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "Define the different roles filled by your employees."
msgstr ""

#. module: planning
#: model:ir.model.fields,help:planning.field_res_company__planning_generation_interval
msgid ""
"Delay for the rate at which recurring shift should be generated in month"
msgstr ""

#. module: planning
#: model:ir.model.fields,help:planning.field_res_config_settings__planning_generation_interval
msgid "Delay for the rate at which recurring shifts should be generated"
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
#: model_terms:ir.ui.view,arch_db:planning.planning_view_kanban
msgid "Delete"
msgstr "Poista"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__department_id
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search
msgid "Department"
msgstr "Osasto"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_send_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_report_view_form_in_gantt
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
#: model_terms:ir.ui.view,arch_db:planning.slot_planning_select_send_view_form
msgid "Discard"
msgstr "Hylkää"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_hr_employee__display_name
#: model:ir.model.fields,field_description:planning.field_planning_planning__display_name
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__display_name
#: model:ir.model.fields,field_description:planning.field_planning_role__display_name
#: model:ir.model.fields,field_description:planning.field_planning_send__display_name
#: model:ir.model.fields,field_description:planning.field_planning_slot__display_name
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__display_name
#: model:ir.model.fields,field_description:planning.field_res_company__display_name
#: model:ir.model.fields,field_description:planning.field_res_config_settings__display_name
#: model:ir.model.fields,field_description:planning.field_slot_planning_select_send__display_name
msgid "Display Name"
msgstr "Näyttönimi"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
msgid "Documentation"
msgstr "Dokumentaatio"

#. module: planning
#: model_terms:digest.tip,tip_description:planning.digest_tip_planning_0
msgid ""
"Drag a shift to another day to reschedule it, or to another row to reassign "
"the shift. If the shift was published, the user will automatically be "
"notified of the change. Press CTRL (or Cmd on Mac) while dragging a shift to"
" duplicate it."
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_kanban
msgid "Dropdown menu"
msgstr "Alasvetovalikko"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__duration
msgid "Duration (Hours)"
msgstr "Kesto (tunteja)"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_kanban
msgid "Edit Slot"
msgstr ""

#. module: planning
#: model:ir.model,name:planning.model_hr_employee
#: model:ir.model.fields,field_description:planning.field_planning_slot__employee_id
#: model:ir.model.fields,field_description:planning.field_slot_planning_select_send__employee_ids
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search
msgid "Employee"
msgstr "Työntekijä"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_role__employee_ids
#: model:ir.model.fields,field_description:planning.field_planning_send__employee_ids
msgid "Employees"
msgstr "Työntekijät"

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_send__employee_ids
msgid ""
"Employees who will receive planning by email if you click on publish & send."
msgstr ""

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_shift_template
msgid "Encode your shifts in one click by using templates."
msgstr ""

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__end_datetime
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "End Date"
msgstr "Päättymispäivä"

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_hr_employee_employee_token_unique
msgid "Error: each employee token must be unique"
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
msgid "Every"
msgstr "Joka"

#. module: planning
#: code:addons/planning/models/planning_recurrency.py:0
#, python-format
msgid "Every %s week(s) until %s"
msgstr "Joka %s viikko kunnes %s"

#. module: planning
#. openerp-web
#: code:addons/planning/static/src/xml/planning_gantt.xml:0
#, python-format
msgid "Expand rows"
msgstr "Laajenna rivit"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_send__note
msgid "Extra Message"
msgstr ""

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__allocation_type__forecast
msgid "Forecast"
msgstr "Ennuste"

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__planning_recurrency__repeat_type__forever
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__repeat_type__forever
msgid "Forever"
msgstr "Ikuisesti"

#. module: planning
#: code:addons/planning/models/planning_recurrency.py:0
#, python-format
msgid "Forever, every %s week(s)"
msgstr "Pysyvästi, joka %s viikko"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "From"
msgstr "Lähtöpaikka"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search
msgid "Future"
msgstr "Tulevaisuudessa"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.brand_promotion
msgid "Give depth to your"
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_search
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search
msgid "Group By"
msgstr "Ryhmittely"

#. module: planning
#: model:ir.filters,name:planning.planning_filter_by_employee
msgid "Hours per Employee"
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
msgid "I Am Unavailable"
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
msgid "I Take It"
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "I am unavailable"
msgstr ""

#. module: planning
#: model:ir.model.fields,field_description:planning.field_hr_employee__id
#: model:ir.model.fields,field_description:planning.field_planning_planning__id
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__id
#: model:ir.model.fields,field_description:planning.field_planning_role__id
#: model:ir.model.fields,field_description:planning.field_planning_send__id
#: model:ir.model.fields,field_description:planning.field_planning_slot__id
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__id
#: model:ir.model.fields,field_description:planning.field_res_company__id
#: model:ir.model.fields,field_description:planning.field_res_config_settings__id
#: model:ir.model.fields,field_description:planning.field_slot_planning_select_send__id
msgid "ID"
msgstr "Tunniste (ID)"

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_slot__publication_warning
msgid ""
"If checked, it means that the shift contains has changed since its last "
"publish."
msgstr ""

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_slot__is_published
msgid ""
"If checked, this means the planning entry has been sent to the employee. "
"Modifying the planning entry will mark it as not sent."
msgstr ""

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_slot__repeat_until
msgid ""
"If set, the recurrence stop at that date. Otherwise, the recurrence is "
"applied indefinitely."
msgstr ""

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_send__include_unassigned
msgid "Include Open Shifts"
msgstr ""

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__include_unassigned
msgid "Includes Open Shifts"
msgstr ""

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__is_published
msgid "Is The Shift Sent"
msgstr ""

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__is_assigned_to_me
msgid "Is This Shift Assigned To The Current User"
msgstr ""

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__is_past
msgid "Is This Shift In The Past?"
msgstr ""

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__last_generated_end_datetime
msgid "Last Generated End Date"
msgstr ""

#. module: planning
#: model:ir.model.fields,field_description:planning.field_hr_employee____last_update
#: model:ir.model.fields,field_description:planning.field_planning_planning____last_update
#: model:ir.model.fields,field_description:planning.field_planning_recurrency____last_update
#: model:ir.model.fields,field_description:planning.field_planning_role____last_update
#: model:ir.model.fields,field_description:planning.field_planning_send____last_update
#: model:ir.model.fields,field_description:planning.field_planning_slot____last_update
#: model:ir.model.fields,field_description:planning.field_planning_slot_template____last_update
#: model:ir.model.fields,field_description:planning.field_res_company____last_update
#: model:ir.model.fields,field_description:planning.field_res_config_settings____last_update
#: model:ir.model.fields,field_description:planning.field_slot_planning_select_send____last_update
msgid "Last Modified on"
msgstr "Viimeksi muokattu"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__write_uid
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__write_uid
#: model:ir.model.fields,field_description:planning.field_planning_role__write_uid
#: model:ir.model.fields,field_description:planning.field_planning_send__write_uid
#: model:ir.model.fields,field_description:planning.field_planning_slot__write_uid
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__write_uid
#: model:ir.model.fields,field_description:planning.field_slot_planning_select_send__write_uid
msgid "Last Updated by"
msgstr "Viimeksi päivitetty"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__write_date
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__write_date
#: model:ir.model.fields,field_description:planning.field_planning_role__write_date
#: model:ir.model.fields,field_description:planning.field_planning_send__write_date
#: model:ir.model.fields,field_description:planning.field_planning_slot__write_date
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__write_date
#: model:ir.model.fields,field_description:planning.field_slot_planning_select_send__write_date
msgid "Last Updated on"
msgstr "Viimeksi päivitetty"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__allow_self_unassign
msgid "Let Employee Unassign Themselves"
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
msgid "Let employees unassign themselves from shifts"
msgstr ""

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_slot__allow_self_unassign
#: model:ir.model.fields,help:planning.field_res_company__planning_allow_self_unassign
#: model:ir.model.fields,help:planning.field_res_config_settings__planning_allow_self_unassign
msgid "Let your employees un-assign themselves from shifts when unavailable"
msgstr ""

#. module: planning
#. openerp-web
#: code:addons/planning/static/src/js/tours/planning.js:0
#, python-format
msgid "Let's start managing your employees' schedule!"
msgstr ""

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_my_calendar
#: model_terms:ir.actions.act_window,help:planning.planning_action_my_gantt
#: model_terms:ir.actions.act_window,help:planning.planning_action_schedule_by_employee
#: model_terms:ir.actions.act_window,help:planning.planning_action_schedule_by_role
msgid "Let's start your planning by adding a new shift."
msgstr ""

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__manager_id
msgid "Manager"
msgstr "Päällikkö"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__publication_warning
msgid "Modified Since Last Publication"
msgstr ""

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_action_my_calendar
#: model:ir.actions.act_window,name:planning.planning_action_my_gantt
#: model:ir.actions.act_window,name:planning.planning_action_open_shift
#: model:ir.ui.menu,name:planning.planning_menu_my_planning
msgid "My Planning"
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search
msgid "My Shifts"
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search
msgid "My Team"
msgstr "Oma tiimi"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_role__name
msgid "Name"
msgstr "Nimi"

#. module: planning
#. openerp-web
#: code:addons/planning/static/src/xml/planning_gantt.xml:0
#, python-format
msgid "Next"
msgstr "Seuraava"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_report_action_analysis
msgid "No data yet!"
msgstr "Ei vielä tietoja!"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "No roles found"
msgstr ""

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_shift_template
msgid "No shift templates found"
msgstr ""

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_my_calendar
#: model_terms:ir.actions.act_window,help:planning.planning_action_my_gantt
#: model_terms:ir.actions.act_window,help:planning.planning_action_schedule_by_employee
#: model_terms:ir.actions.act_window,help:planning.planning_action_schedule_by_role
msgid "No shifts found"
msgstr ""

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__name
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "Note"
msgstr "Muistiinpano"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__working_days_count
msgid "Number of Working Days"
msgstr ""

#. module: planning
#. openerp-web
#: code:addons/planning/static/src/js/planning_gantt_controller.js:0
#, python-format
msgid "Open"
msgstr "Avoin"

#. module: planning
#. openerp-web
#: code:addons/planning/static/src/js/planning_gantt_model.js:0
#: code:addons/planning/static/src/js/planning_gantt_model.js:0
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search
#, python-format
msgid "Open Shifts"
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "Open Shifts Available"
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search
msgid "Open Shifts for my Roles"
msgstr ""

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__overlap_slot_count
msgid "Overlapping Slots"
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search
msgid "Past"
msgstr "Mennyt"

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_slot__allocated_percentage
msgid "Percentage of time the employee is supposed to work during the shift."
msgstr ""

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_send__start_datetime
#: model_terms:ir.ui.view,arch_db:planning.planning_send_view_form
msgid "Period"
msgstr "Jakso"

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__allocation_type__planning
#: model:ir.ui.menu,name:planning.planning_menu_root
#: model_terms:ir.ui.view,arch_db:planning.hr_employee_view_form_inherit
#: model_terms:ir.ui.view,arch_db:planning.planning_view_calendar
#: model_terms:ir.ui.view,arch_db:planning.planning_view_my_calendar
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
msgid "Planning"
msgstr "Suunnittelu"

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_report_action_analysis
#: model:ir.ui.menu,name:planning.planning_menu_planning_analysis
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_report_view_graph
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_report_view_pivot
#: model_terms:ir.ui.view,arch_db:planning.planning_view_graph
#: model_terms:ir.ui.view,arch_db:planning.planning_view_pivot
msgid "Planning Analysis"
msgstr ""

#. module: planning
#: model:ir.model,name:planning.model_planning_recurrency
msgid "Planning Recurrence"
msgstr ""

#. module: planning
#: model:ir.model,name:planning.model_planning_role
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_form
msgid "Planning Role"
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_tree
msgid "Planning Role List"
msgstr ""

#. module: planning
#: model:ir.model.fields,field_description:planning.field_hr_employee__planning_role_ids
msgid "Planning Roles"
msgstr ""

#. module: planning
#: model:ir.model,name:planning.model_planning_slot
msgid "Planning Shift"
msgstr ""

#. module: planning
#: code:addons/planning/models/planning.py:0
#, python-format
msgid "Planning from %s to %s"
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "Planning:"
msgstr ""

#. module: planning
#: model:ir.actions.server,name:planning.ir_cron_forecast_schedule_ir_actions_server
#: model:ir.cron,cron_name:planning.ir_cron_forecast_schedule
#: model:ir.cron,name:planning.ir_cron_forecast_schedule
msgid "Planning: generate next recurring shifts"
msgstr ""

#. module: planning
#: code:addons/planning/models/planning.py:0
#, python-format
msgid "Planning: new open shift available"
msgstr ""

#. module: planning
#: code:addons/planning/models/planning.py:0
#, python-format
msgid "Planning: new shift"
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.brand_promotion
msgid "Plans"
msgstr "Suunnitelmat"

#. module: planning
#. openerp-web
#: code:addons/planning/static/src/xml/planning_gantt.xml:0
#, python-format
msgid "Previous"
msgstr "Edellinen"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__previous_template_id
msgid "Previous Template"
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_send_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
msgid "Publish"
msgstr "Julkaise"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_send_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
msgid "Publish & Send"
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search
msgid "Published"
msgstr "Julkaistu"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_res_company__planning_generation_interval
#: model:ir.model.fields,field_description:planning.field_res_config_settings__planning_generation_interval
msgid "Rate Of Shift Generation"
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
msgid "Rate of shift generation"
msgstr ""

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__recurrency_id
msgid "Recurrency"
msgstr "Toistuvuus"

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_planning_recurrency_check_repeat_interval_positive
msgid "Recurrency repeat interval should be at least 1"
msgstr ""

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__slot_ids
msgid "Related Planning Entries"
msgstr ""

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_slot__user_id
msgid "Related user name for the resource to manage its access."
msgstr "Liittyvä käyttäjätunnus resurssille sen oikeuksien määrittämiseksi"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__repeat
msgid "Repeat"
msgstr "Toista"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__repeat_interval
msgid "Repeat Every"
msgstr "Toista joka"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__repeat_type
msgid "Repeat Type"
msgstr ""

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__repeat_until
#: model:ir.model.fields,field_description:planning.field_planning_slot__repeat_until
msgid "Repeat Until"
msgstr "Toista kunnes"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__repeat_interval
msgid "Repeat every"
msgstr ""

#. module: planning
#: model:ir.ui.menu,name:planning.planning_menu_reporting
msgid "Reporting"
msgstr "Raportointi"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__role_id
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__role_id
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_search
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search
msgid "Role"
msgstr "Rooli"

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_action_roles
#: model:ir.ui.menu,name:planning.planning_menu_settings_role
msgid "Roles"
msgstr "Roolit"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
msgid "Save"
msgstr "Tallenna"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
msgid "Save as Template"
msgstr ""

#. module: planning
#. openerp-web
#: code:addons/planning/static/src/js/tours/planning.js:0
#, python-format
msgid "Save this shift once it is ready."
msgstr ""

#. module: planning
#: model:ir.model,name:planning.model_planning_planning
#: model:ir.ui.menu,name:planning.planning_menu_schedule
#: model_terms:ir.ui.view,arch_db:planning.planning_view_gantt
msgid "Schedule"
msgstr "Aikatauluta"

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_action_schedule_by_employee
msgid "Schedule by Employee"
msgstr ""

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_action_schedule_by_role
msgid "Schedule by Role"
msgstr ""

#. module: planning
#: model:ir.model.fields,field_description:planning.field_hr_employee__employee_token
#: model:ir.model.fields,field_description:planning.field_planning_planning__access_token
#: model:ir.model.fields,field_description:planning.field_planning_slot__access_token
msgid "Security Token"
msgstr "Turvatunnus"

#. module: planning
#: model:ir.model,name:planning.model_slot_planning_select_send
msgid "Select Employees and Send One Slot"
msgstr ""

#. module: planning
#: code:addons/planning/wizard/planning_send.py:0
#, python-format
msgid "Select the employees you would like to send the planning to."
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
#: model_terms:ir.ui.view,arch_db:planning.slot_planning_select_send_view_form
msgid "Send"
msgstr "Lähetä"

#. module: planning
#: model:ir.model,name:planning.model_planning_send
msgid "Send Planning"
msgstr ""

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_send_action
msgid "Send Planning By Email"
msgstr ""

#. module: planning
#. openerp-web
#: code:addons/planning/static/src/xml/planning_gantt.xml:0
#: code:addons/planning/static/src/xml/planning_gantt.xml:0
#, python-format
msgid "Send schedule"
msgstr ""

#. module: planning
#. openerp-web
#: code:addons/planning/static/src/js/tours/planning.js:0
#, python-format
msgid "Send the schedule and mark the shifts as published. Congratulations!"
msgstr ""

#. module: planning
#. openerp-web
#: code:addons/planning/static/src/js/tours/planning.js:0
#, python-format
msgid "Send the schedule to your employees once it is ready."
msgstr ""

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_role__sequence
msgid "Sequence"
msgstr "Järjestys"

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_action_settings
#: model:ir.ui.menu,name:planning.planning_menu_settings_config
msgid "Settings"
msgstr "Asetukset"

#. module: planning
#: code:addons/planning/controllers/main.py:0
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
#, python-format
msgid "Shift"
msgstr "Vuoro"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_tree
msgid "Shift List"
msgstr ""

#. module: planning
#: model:ir.model,name:planning.model_planning_slot_template
msgid "Shift Template"
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_form
msgid "Shift Template Form"
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_tree
msgid "Shift Template List"
msgstr ""

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_action_shift_template
#: model:ir.model.fields,field_description:planning.field_planning_slot__template_id
#: model:ir.ui.menu,name:planning.planning_menu_settings_shift_template
msgid "Shift Templates"
msgstr ""

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_planning_slot_check_start_date_lower_end_date
msgid "Shift end date should be greater than its start date"
msgstr ""

#. module: planning
#: model:ir.model.fields,field_description:planning.field_slot_planning_select_send__slot_id
msgid "Shifts"
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "Shifts from"
msgstr ""

#. module: planning
#: code:addons/planning/models/planning.py:0
#, python-format
msgid "Shifts in conflict"
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search
msgid "Shifts of Your Team Member"
msgstr ""

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__slot_ids
#: model:ir.model.fields,field_description:planning.field_planning_send__slot_ids
msgid "Slot"
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_gantt
msgid "Some changes were made since this shift was published."
msgstr ""

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__start_datetime
#: model:ir.model.fields,field_description:planning.field_planning_slot__start_datetime
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search
msgid "Start Date"
msgstr "Alkupäivä"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__start_time
msgid "Start Hour"
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_search
msgid "Start Time"
msgstr ""

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_planning_slot_template_check_start_time_positive
msgid "Start hour must be a positive number"
msgstr ""

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__end_datetime
#: model:ir.model.fields,field_description:planning.field_planning_send__end_datetime
msgid "Stop Date"
msgstr ""

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__template_autocomplete_ids
msgid "Template Autocomplete"
msgstr ""

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__template_reset
msgid "Template Reset"
msgstr ""

#. module: planning
#: code:addons/planning/models/planning.py:0
#, python-format
msgid "The company does not allow you to self unassign."
msgstr ""

#. module: planning
#: code:addons/planning/models/planning.py:0
#, python-format
msgid "The recurrence until date should be after the shift start date"
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "There are no planning slot to display. Please, refer to your manager."
msgstr ""

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__was_copied
msgid "This Shift Was Copied From Previous Week"
msgstr ""

#. module: planning
#: code:addons/planning/wizard/planning_send.py:0
#, python-format
msgid ""
"This action is not allowed as there are no shifts planned for the selected "
"time period."
msgstr ""

#. module: planning
#: model:digest.tip,name:planning.digest_tip_planning_0
msgid "Tip: Record your planning faster"
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "To"
msgstr "Kohdepaikka"

#. module: planning
#. openerp-web
#: code:addons/planning/static/src/xml/planning_gantt.xml:0
#, python-format
msgid "Today"
msgstr "Tänään"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search
msgid "Unpublished"
msgstr "Julkaisematon"

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__planning_recurrency__repeat_type__until
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__repeat_type__until
msgid "Until"
msgstr "Kunnes"

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_recurrency__repeat_until
msgid "Up to which date should the plannings be repeated"
msgstr ""

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__user_id
#: model:res.groups,name:planning.group_planning_user
msgid "User"
msgstr "Käyttäjä"

#. module: planning
#: code:addons/planning/models/hr.py:0
#: model:ir.actions.server,name:planning.action_hr_employee_planning_view
#, python-format
msgid "View Planning"
msgstr ""

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__repeat_type
msgid "Weeks"
msgstr "Viikot"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__work_email
msgid "Work Email"
msgstr "Työsähköposti"

#. module: planning
#: code:addons/planning/models/planning.py:0
#, python-format
msgid "You can not assign yourself to an already assigned shift."
msgstr ""

#. module: planning
#: code:addons/planning/models/planning.py:0
#, python-format
msgid "You can not unassign another employee than yourself."
msgstr ""

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_planning_slot_template_check_duration_positive
msgid "You cannot have a negative duration"
msgstr ""

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_planning_slot_template_check_start_time_lower_than_24
msgid "You cannot have a start hour greater than 24"
msgstr ""

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_planning_slot_check_allocated_hours_positive
msgid "You cannot have negative shift"
msgstr ""

#. module: planning
#: code:addons/planning/wizard/slot_planning_select_send.py:0
#, python-format
msgid "You cannot send a past unassigned slot"
msgstr ""

#. module: planning
#: code:addons/planning/models/planning.py:0
#, python-format
msgid "You don't the right to self assign."
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.slot_unassign
msgid "You have been successfully unassigned from this shift"
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.slot_unassign
msgid "Your Planning"
msgstr ""

#. module: planning
#: model:mail.template,subject:planning.email_template_planning_planning
msgid ""
"Your planning from ${format_date(ctx.get('start_datetime'))} to "
"${format_date(ctx.get('end_datetime'))}"
msgstr ""

#. module: planning
#: code:addons/planning/models/planning.py:0
#, python-format
msgid "Your template was successfully saved."
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
msgid "other shift(s)"
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_gantt
msgid "other shift(s) in conflict."
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "to"
msgstr " "
