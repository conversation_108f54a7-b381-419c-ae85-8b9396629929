# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* quality
# 
# Translators:
# <PERSON>, 2020
# <PERSON> <<EMAIL>>, 2020
# <PERSON>, 2020
# <PERSON>abi<PERSON> <<EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-11-27 13:47+0000\n"
"PO-Revision-Date: 2020-09-07 08:22+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Italian (https://www.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alert_count
#: model:ir.model.fields,field_description:quality.field_quality_check__alert_count
msgid "# Quality Alerts"
msgstr "N. avvisi qualità"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__check_count
msgid "# Quality Checks"
msgstr "N. controlli qualità"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"Dizionario Python che verrà esaminato per fornire valori predefiniti durante"
" la creazione di nuovi record per l'alias."

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_needaction
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_needaction
#: model:ir.model.fields,field_description:quality.field_quality_check__message_needaction
#: model:ir.model.fields,field_description:quality.field_quality_point__message_needaction
msgid "Action Needed"
msgstr "Azione richiesta"

#. module: quality
#: model:quality.alert.stage,name:quality.quality_alert_stage_2
msgid "Action Proposed"
msgstr "Proposta azione"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_point__active
msgid "Active"
msgstr "Attivo"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_ids
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_ids
msgid "Activities"
msgstr "Attività"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_exception_decoration
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decorazione eccezione attività"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_state
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_state
msgid "Activity State"
msgstr "Stato attività"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_type_icon
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icona tipo di attività"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__additional_note
msgid "Additional Note"
msgstr "Nota aggiuntiva"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_check__additional_note
msgid "Additional remarks concerning this check."
msgstr "Osservazioni aggiuntive che riguardano il controllo."

#. module: quality
#: model:res.groups,name:quality.group_quality_manager
msgid "Administrator"
msgstr "Amministratore"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__done
msgid "Alert Processed"
msgstr "Avviso elaborato"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__alert_ids
msgid "Alerts"
msgstr "Avvisi"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_id
msgid "Alias"
msgstr "Alias"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_contact
msgid "Alias Contact Security"
msgstr "Sicurezza contatto alias"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_name
msgid "Alias Name"
msgstr "Nome alias"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_domain
msgid "Alias domain"
msgstr "Dominio alias"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_model_id
msgid "Aliased Model"
msgstr "Modello con alias"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_form
msgid "Archived"
msgstr "In archivio"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_attachment_count
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_attachment_count
#: model:ir.model.fields,field_description:quality.field_quality_check__message_attachment_count
#: model:ir.model.fields,field_description:quality.field_quality_point__message_attachment_count
msgid "Attachment Count"
msgstr "Numero allegati"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_point__available_product_ids
msgid "Available Product"
msgstr "Prodotto disponibile"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_point__reason
msgid "Cause"
msgstr "Causa"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__check_id
#: model:ir.model.fields,field_description:quality.field_quality_point__check_ids
msgid "Check"
msgstr "Controllo"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_point__check_count
msgid "Check Count"
msgstr "Numero controlli"

#. module: quality
#. openerp-web
#: code:addons/quality/static/src/xml/widget_template.xml:0
#: code:addons/quality/static/src/xml/widget_template.xml:0
#, python-format
msgid "Clear"
msgstr "Cancella"

#. module: quality
#. openerp-web
#: code:addons/quality/static/src/xml/widget_template.xml:0
#: code:addons/quality/static/src/xml/widget_template.xml:0
#, python-format
msgid "Close"
msgstr "Chiudi"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__color
msgid "Color"
msgstr "Colore"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_tag__color
msgid "Color Index"
msgstr "Indice colore"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__company_id
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__company_id
#: model:ir.model.fields,field_description:quality.field_quality_check__company_id
#: model:ir.model.fields,field_description:quality.field_quality_point__company_id
msgid "Company"
msgstr "Azienda"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__lot_id
msgid "Component Lot/Serial"
msgstr "Lotto/Serie del componente"

#. module: quality
#: model:quality.alert.stage,name:quality.quality_alert_stage_1
msgid "Confirmed"
msgstr "Confermato"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__control_date
msgid "Control Date"
msgstr "Data controllo"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__point_id
msgid "Control Point"
msgstr "Punto di controllo"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__action_corrective
msgid "Corrective Action"
msgstr "Azione correttiva"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__create_uid
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__create_uid
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__create_uid
#: model:ir.model.fields,field_description:quality.field_quality_check__create_uid
#: model:ir.model.fields,field_description:quality.field_quality_point__create_uid
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type__create_uid
#: model:ir.model.fields,field_description:quality.field_quality_reason__create_uid
#: model:ir.model.fields,field_description:quality.field_quality_tag__create_uid
msgid "Created by"
msgstr "Creato da"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__create_date
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__create_date
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__create_date
#: model:ir.model.fields,field_description:quality.field_quality_check__create_date
#: model:ir.model.fields,field_description:quality.field_quality_point__create_date
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type__create_date
#: model:ir.model.fields,field_description:quality.field_quality_reason__create_date
#: model:ir.model.fields,field_description:quality.field_quality_tag__create_date
msgid "Created on"
msgstr "Data creazione"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Creation Date"
msgstr "Data creazione"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "Messaggio personalizzato di non recapito"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__date_assign
msgid "Date Assigned"
msgstr "Data di assegnazione"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__date_close
msgid "Date Closed"
msgstr "Data di chiusura"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_defaults
msgid "Default Values"
msgstr "Valori predefiniti"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_point__test_type_id
msgid "Defines the type of the quality control point."
msgstr "Definisce la tipologia del punto di controllo qualità."

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_form
msgid "Describe the quality check to do..."
msgstr "Descrivi il controllo qualità da effettuare..."

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_form
msgid "Describe why you need to perform this quality check..."
msgstr "Descrivi perché è necessario eseguire questo controllo qualità..."

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__description
msgid "Description"
msgstr "Descrizione"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__display_name
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__display_name
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__display_name
#: model:ir.model.fields,field_description:quality.field_quality_check__display_name
#: model:ir.model.fields,field_description:quality.field_quality_point__display_name
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type__display_name
#: model:ir.model.fields,field_description:quality.field_quality_reason__display_name
#: model:ir.model.fields,field_description:quality.field_quality_tag__display_name
msgid "Display Name"
msgstr "Nome visualizzato"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Done"
msgstr "Completato"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__email_cc
msgid "Email cc"
msgstr "E-mail in cc"

#. module: quality
#: model:ir.model.fields.selection,name:quality.selection__quality_check__quality_state__fail
msgid "Failed"
msgstr "Non superato"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__folded
msgid "Folded"
msgstr "Minimizzato"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_follower_ids
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_follower_ids
#: model:ir.model.fields,field_description:quality.field_quality_check__message_follower_ids
#: model:ir.model.fields,field_description:quality.field_quality_point__message_follower_ids
msgid "Followers"
msgstr "Seguito da"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_channel_ids
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_channel_ids
#: model:ir.model.fields,field_description:quality.field_quality_check__message_channel_ids
#: model:ir.model.fields,field_description:quality.field_quality_point__message_channel_ids
msgid "Followers (Channels)"
msgstr "Seguito da (canali)"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_partner_ids
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_partner_ids
#: model:ir.model.fields,field_description:quality.field_quality_check__message_partner_ids
#: model:ir.model.fields,field_description:quality.field_quality_point__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguito da (partner)"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert__activity_type_icon
#: model:ir.model.fields,help:quality.field_quality_check__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icona Font Awesome es. fa-tasks"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Future Activities"
msgstr "Attività future"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Group By"
msgstr "Raggruppa per"

#. module: quality
#: model:ir.model.fields.selection,name:quality.selection__quality_alert__priority__2
msgid "High"
msgstr "Alta"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__id
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__id
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__id
#: model:ir.model.fields,field_description:quality.field_quality_check__id
#: model:ir.model.fields,field_description:quality.field_quality_point__id
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type__id
#: model:ir.model.fields,field_description:quality.field_quality_reason__id
#: model:ir.model.fields,field_description:quality.field_quality_tag__id
msgid "ID"
msgstr "ID"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"ID del record primario contenente l’alias (esempio: progetto che contiene "
"l’alias di creazione del lavoro)"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_exception_icon
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_exception_icon
msgid "Icon"
msgstr "Icona"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert__activity_exception_icon
#: model:ir.model.fields,help:quality.field_quality_check__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icona per indicare un'attività eccezione."

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert__message_needaction
#: model:ir.model.fields,help:quality.field_quality_alert__message_unread
#: model:ir.model.fields,help:quality.field_quality_alert_team__message_needaction
#: model:ir.model.fields,help:quality.field_quality_alert_team__message_unread
#: model:ir.model.fields,help:quality.field_quality_check__message_needaction
#: model:ir.model.fields,help:quality.field_quality_check__message_unread
#: model:ir.model.fields,help:quality.field_quality_point__message_needaction
#: model:ir.model.fields,help:quality.field_quality_point__message_unread
msgid "If checked, new messages require your attention."
msgstr "Se selezionata, nuovi messaggi richiedono attenzione."

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert__message_has_error
#: model:ir.model.fields,help:quality.field_quality_alert__message_has_sms_error
#: model:ir.model.fields,help:quality.field_quality_alert_team__message_has_error
#: model:ir.model.fields,help:quality.field_quality_alert_team__message_has_sms_error
#: model:ir.model.fields,help:quality.field_quality_check__message_has_error
#: model:ir.model.fields,help:quality.field_quality_check__message_has_sms_error
#: model:ir.model.fields,help:quality.field_quality_point__message_has_error
#: model:ir.model.fields,help:quality.field_quality_point__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Se selezionata, alcuni messaggi presentano un errore di consegna."

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"Se impostato, il contenuto verrà inviato automaticamente, al posto del "
"messaggio predefinito, agli utenti non autorizzati."

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "In Progress"
msgstr "In corso"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_form
msgid "Instructions"
msgstr "Istruzioni"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_is_follower
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_is_follower
#: model:ir.model.fields,field_description:quality.field_quality_check__message_is_follower
#: model:ir.model.fields,field_description:quality.field_quality_point__message_is_follower
msgid "Is Follower"
msgstr "Sta seguendo"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert____last_update
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage____last_update
#: model:ir.model.fields,field_description:quality.field_quality_alert_team____last_update
#: model:ir.model.fields,field_description:quality.field_quality_check____last_update
#: model:ir.model.fields,field_description:quality.field_quality_point____last_update
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type____last_update
#: model:ir.model.fields,field_description:quality.field_quality_reason____last_update
#: model:ir.model.fields,field_description:quality.field_quality_tag____last_update
msgid "Last Modified on"
msgstr "Ultima modifica il"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__write_uid
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__write_uid
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__write_uid
#: model:ir.model.fields,field_description:quality.field_quality_check__write_uid
#: model:ir.model.fields,field_description:quality.field_quality_point__write_uid
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type__write_uid
#: model:ir.model.fields,field_description:quality.field_quality_reason__write_uid
#: model:ir.model.fields,field_description:quality.field_quality_tag__write_uid
msgid "Last Updated by"
msgstr "Ultimo aggiornamento di"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__write_date
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__write_date
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__write_date
#: model:ir.model.fields,field_description:quality.field_quality_check__write_date
#: model:ir.model.fields,field_description:quality.field_quality_point__write_date
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type__write_date
#: model:ir.model.fields,field_description:quality.field_quality_reason__write_date
#: model:ir.model.fields,field_description:quality.field_quality_tag__write_date
msgid "Last Updated on"
msgstr "Ultimo aggiornamento il"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Late Activities"
msgstr "Attività in ritardo"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert__email_cc
msgid "List of cc from incoming emails."
msgstr "Elenco di CC dalle e-mail in arrivo."

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__lot_id
msgid "Lot"
msgstr "Lotto"

#. module: quality
#: model:ir.model.fields.selection,name:quality.selection__quality_alert__priority__1
msgid "Low"
msgstr "Bassa"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_main_attachment_id
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_main_attachment_id
#: model:ir.model.fields,field_description:quality.field_quality_check__message_main_attachment_id
#: model:ir.model.fields,field_description:quality.field_quality_point__message_main_attachment_id
msgid "Main Attachment"
msgstr "Allegato principale"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_has_error
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_has_error
#: model:ir.model.fields,field_description:quality.field_quality_check__message_has_error
#: model:ir.model.fields,field_description:quality.field_quality_point__message_has_error
msgid "Message Delivery error"
msgstr "Errore di consegna messaggio"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_ids
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_ids
#: model:ir.model.fields,field_description:quality.field_quality_check__message_ids
#: model:ir.model.fields,field_description:quality.field_quality_point__message_ids
msgid "Messages"
msgstr "Messaggi"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "My Alerts"
msgstr "I miei avvisi"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__name
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__name
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__name
#: model:ir.model.fields,field_description:quality.field_quality_check__name
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type__name
#: model:ir.model.fields,field_description:quality.field_quality_reason__name
msgid "Name"
msgstr "Nome"

#. module: quality
#: code:addons/quality/models/quality.py:0
#: code:addons/quality/models/quality.py:0
#: code:addons/quality/models/quality.py:0
#: code:addons/quality/models/quality.py:0
#: code:addons/quality/models/quality.py:0
#: code:addons/quality/models/quality.py:0
#: code:addons/quality/models/quality.py:0
#: code:addons/quality/models/quality.py:0
#: code:addons/quality/models/quality.py:0
#: model:quality.alert.stage,name:quality.quality_alert_stage_0
#, python-format
msgid "New"
msgstr "Nuovo"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_date_deadline
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Scadenza prossima attività"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_summary
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_summary
msgid "Next Activity Summary"
msgstr "Riepilogo prossima attività"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_type_id
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_type_id
msgid "Next Activity Type"
msgstr "Tipologia prossima attività"

#. module: quality
#: code:addons/quality/models/quality.py:0
#, python-format
msgid ""
"No quality team found for this company.\n"
"Please go to configuration and create one first."
msgstr ""
"Nessun team qualità trovato per l'azienda.\n"
"Andare nella configurazione e per prima cosa crearne uno."

#. module: quality
#: model:ir.model.fields.selection,name:quality.selection__quality_alert__priority__0
msgid "Normal"
msgstr "Normale"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__note
#: model:ir.model.fields,field_description:quality.field_quality_point__note
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_form
msgid "Note"
msgstr "Nota"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_form
msgid "Notes"
msgstr "Note"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_needaction_counter
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_needaction_counter
#: model:ir.model.fields,field_description:quality.field_quality_check__message_needaction_counter
#: model:ir.model.fields,field_description:quality.field_quality_point__message_needaction_counter
msgid "Number of Actions"
msgstr "Numero di azioni"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_has_error_counter
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_has_error_counter
#: model:ir.model.fields,field_description:quality.field_quality_check__message_has_error_counter
#: model:ir.model.fields,field_description:quality.field_quality_point__message_has_error_counter
msgid "Number of errors"
msgstr "Numero di errori"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert__message_needaction_counter
#: model:ir.model.fields,help:quality.field_quality_alert_team__message_needaction_counter
#: model:ir.model.fields,help:quality.field_quality_check__message_needaction_counter
#: model:ir.model.fields,help:quality.field_quality_point__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Numero di messaggi che richiedono un'azione"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert__message_has_error_counter
#: model:ir.model.fields,help:quality.field_quality_alert_team__message_has_error_counter
#: model:ir.model.fields,help:quality.field_quality_check__message_has_error_counter
#: model:ir.model.fields,help:quality.field_quality_point__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Numero di messaggi con errore di consegna"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert__message_unread_counter
#: model:ir.model.fields,help:quality.field_quality_alert_team__message_unread_counter
#: model:ir.model.fields,help:quality.field_quality_check__message_unread_counter
#: model:ir.model.fields,help:quality.field_quality_point__message_unread_counter
msgid "Number of unread messages"
msgstr "Numero di messaggi non letti"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_point__picking_type_ids
msgid "Operation Types"
msgstr "Tipi di operazione"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_form
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_tree
msgid "Operations"
msgstr "Operazioni"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"ID opzionale di una discussione (record) alla quale vengono allegati tutti i"
" messaggi in arrivo, non solo le risposte. Se impostato, viene disabilitata "
"in modo completo la creazione di nuovi record."

#. module: quality
#: model:quality.reason,name:quality.reason_other
msgid "Others"
msgstr "Altre"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_user_id
msgid "Owner"
msgstr "Proprietario"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_parent_model_id
msgid "Parent Model"
msgstr "Modello primario"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "ID discussione record primario"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"Modello primario contenente l'alias. Il modello che contiene il riferimento "
"alias non è necessariamente quello fornito da alias_model_id (esempio: "
"progetto (parent_model) e lavoro (model))"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__partner_id
msgid "Partner"
msgstr "Partner"

#. module: quality
#: model:quality.reason,name:quality.reason_parts
msgid "Parts Quality"
msgstr "Qualità componenti"

#. module: quality
#: model:ir.model.fields.selection,name:quality.selection__quality_check__quality_state__pass
msgid "Passed"
msgstr "Superato"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__picking_id
#: model:ir.model.fields,field_description:quality.field_quality_check__picking_id
msgid "Picking"
msgstr "Prelievo"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__picture
msgid "Picture"
msgstr "Immagine"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"Politica di pubblicazione messaggi su documenti usando il gateway di posta.\n"
"- tutti: tutti possono pubblicare\n"
"- partner: solo partner autenticati\n"
"- chi sta seguendo: solo chi segue il documento collegato o gli iscritti ai relativi canali\n"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__action_preventive
msgid "Preventive Action"
msgstr "Azione preventiva"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__priority
msgid "Priority"
msgstr "Priorità"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__product_tmpl_id
#: model:ir.model.fields,field_description:quality.field_quality_check__product_id
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Product"
msgstr "Prodotto"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__product_id
msgid "Product Variant"
msgstr "Variante prodotto"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_point__product_ids
msgid "Products"
msgstr "Prodotti"

#. module: quality
#: model:ir.model,name:quality.model_quality_alert
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Quality Alert"
msgstr "Avviso qualità"

#. module: quality
#: model:ir.model,name:quality.model_quality_alert_stage
msgid "Quality Alert Stage"
msgstr "Fase avviso qualità"

#. module: quality
#: model:ir.model,name:quality.model_quality_alert_team
msgid "Quality Alert Team"
msgstr "Team avvisi qualità"

#. module: quality
#: model:ir.model,name:quality.model_quality_check
msgid "Quality Check"
msgstr "Controllo qualità"

#. module: quality
#: model:ir.model,name:quality.model_quality_point
msgid "Quality Control Point"
msgstr "Punto controllo qualità"

#. module: quality
#: model:ir.model,name:quality.model_quality_point_test_type
msgid "Quality Control Test Type"
msgstr "Tipo di prova controllo qualità"

#. module: quality
#: model:ir.model,name:quality.model_quality_tag
msgid "Quality Tag"
msgstr "Etichetta qualità"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Quality Team"
msgstr "Team qualità"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_force_thread_id
msgid "Record Thread ID"
msgstr "ID record discussione"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_point__name
msgid "Reference"
msgstr "Riferimento"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__user_id
#: model:ir.model.fields,field_description:quality.field_quality_check__user_id
#: model:ir.model.fields,field_description:quality.field_quality_point__user_id
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Responsible"
msgstr "Responsabile"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_user_id
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_user_id
msgid "Responsible User"
msgstr "Utente responsabile"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__reason_id
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Root Cause"
msgstr "Causa originaria"

#. module: quality
#: model:ir.model,name:quality.model_quality_reason
msgid "Root Cause for Quality Failure"
msgstr "Causa originaria per mancanza di qualità"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_has_sms_error
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_has_sms_error
#: model:ir.model.fields,field_description:quality.field_quality_check__message_has_sms_error
#: model:ir.model.fields,field_description:quality.field_quality_point__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Errore di consegna SMS"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__sequence
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__sequence
#: model:ir.model.fields,field_description:quality.field_quality_point__sequence
msgid "Sequence"
msgstr "Sequenza"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Show all records which has next action date is before today"
msgstr "Mostra tutti i record con data prossima azione precedente a oggi"

#. module: quality
#: model:quality.alert.stage,name:quality.quality_alert_stage_3
msgid "Solved"
msgstr "Risolto"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__stage_id
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Stage"
msgstr "Fase"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__quality_state
msgid "Status"
msgstr "Stato"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert__activity_state
#: model:ir.model.fields,help:quality.field_quality_check__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Stato basato sulle attività\n"
"In ritardo: scadenza già superata\n"
"Oggi: attività in data odierna\n"
"Pianificato: attività future."

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_tag__name
msgid "Tag Name"
msgstr "Nome etichetta"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__tag_ids
msgid "Tags"
msgstr "Etichette"

#. module: quality
#. openerp-web
#: code:addons/quality/static/src/xml/widget_template.xml:0
#, python-format
msgid "Take a Picture"
msgstr "Scatto di una foto"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__team_id
#: model:ir.model.fields,field_description:quality.field_quality_check__team_id
#: model:ir.model.fields,field_description:quality.field_quality_point__team_id
msgid "Team"
msgstr "Team"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__team_ids
msgid "Teams"
msgstr "Team"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__test_type
#: model:ir.model.fields,field_description:quality.field_quality_point__test_type
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type__technical_name
msgid "Technical name"
msgstr "Nome tecnico"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__test_type_id
#: model:ir.model.fields,field_description:quality.field_quality_point__test_type_id
msgid "Test Type"
msgstr "Tipo di prova"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"Il modello (tipo di documento Odoo) a cui corrisponde questo alias. Le "
"e-mail in arrivo che non rispondono a un record esistente attivano la "
"creazione di un nuovo record per questo modello (es. lavoro di un progetto)"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"Il nome dell’alias e-mail, es. “lavori” per intercettare le e-mail a "
"<<EMAIL>>"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_user_id
msgid ""
"The owner of records created upon receiving emails on this alias. If this "
"field is not set the system will attempt to find the right owner based on "
"the sender (From) address, or will use the Administrator account if no "
"system user is found for that address."
msgstr ""
"Proprietario dei record creati al ricevimento di e-mail per l'alias. Se il "
"campo non è impostato, il sistema prova a cercare il proprietario in base al"
" mittente (campo \"Da\"). Se all'indirizzo non è associato alcun utente di "
"sistema, viene usato l'account amministratore."

#. module: quality
#: model:res.groups,comment:quality.group_quality_manager
msgid "The quality manager manages the quality process"
msgstr "Il responsabile della qualità gestisce il processo di qualità"

#. module: quality
#: model:res.groups,comment:quality.group_quality_user
msgid "The quality user uses the quality process"
msgstr "L'utente della qualità utilizza il processo di qualità"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__title
#: model:ir.model.fields,field_description:quality.field_quality_point__title
msgid "Title"
msgstr "Titolo"

#. module: quality
#: model:ir.model.fields.selection,name:quality.selection__quality_check__quality_state__none
msgid "To do"
msgstr "Da fare"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Today Activities"
msgstr "Attività odierne"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_form
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_tree
msgid "Type"
msgstr "Tipologia"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert__activity_exception_decoration
#: model:ir.model.fields,help:quality.field_quality_check__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipo di attività eccezione sul record."

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_unread
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_unread
#: model:ir.model.fields,field_description:quality.field_quality_check__message_unread
#: model:ir.model.fields,field_description:quality.field_quality_point__message_unread
msgid "Unread Messages"
msgstr "Messaggi non letti"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_unread_counter
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_unread_counter
#: model:ir.model.fields,field_description:quality.field_quality_check__message_unread_counter
#: model:ir.model.fields,field_description:quality.field_quality_point__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Numero messaggi non letti"

#. module: quality
#. openerp-web
#: code:addons/quality/static/src/xml/widget_template.xml:0
#, python-format
msgid "Uploading..."
msgstr "Caricamento..."

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_tag__color
msgid "Used in the kanban view"
msgstr "Usato nella vista kanban"

#. module: quality
#: model:res.groups,name:quality.group_quality_user
msgid "User"
msgstr "Utente"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__partner_id
msgid "Vendor"
msgstr "Fornitore"

#. module: quality
#: model:ir.model.fields.selection,name:quality.selection__quality_alert__priority__3
msgid "Very High"
msgstr "Molto alta"

#. module: quality
#. openerp-web
#: code:addons/quality/static/src/xml/widget_template.xml:0
#, python-format
msgid "Viewer"
msgstr "Visualizzatore"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__website_message_ids
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__website_message_ids
#: model:ir.model.fields,field_description:quality.field_quality_check__website_message_ids
#: model:ir.model.fields,field_description:quality.field_quality_point__website_message_ids
msgid "Website Messages"
msgstr "Messaggi sito web"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert__website_message_ids
#: model:ir.model.fields,help:quality.field_quality_alert_team__website_message_ids
#: model:ir.model.fields,help:quality.field_quality_check__website_message_ids
#: model:ir.model.fields,help:quality.field_quality_point__website_message_ids
msgid "Website communication history"
msgstr "Cronologia comunicazioni sito web"

#. module: quality
#: model:quality.reason,name:quality.reason_wo
msgid "Work Operation"
msgstr "Operazione di lavoro"

#. module: quality
#: model:quality.reason,name:quality.reason_workcenter
msgid "Workcenter Failure"
msgstr "Guasto centro di lavoro"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type__active
msgid "active"
msgstr "attivo"
