# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * quality
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-10-02 11:50+0000\n"
"PO-Revision-Date: 2017-10-02 11:50+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Albanian (https://www.transifex.com/odoo/teams/41243/sq/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sq\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team_alert_count
#: model:ir.model.fields,field_description:quality.field_quality_check_alert_count
msgid "# Quality Alerts"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team_check_count
msgid "# Quality Checks"
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_point_view_form
msgid "% of the operation"
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_alert_team_dashboard_view_kanban
msgid "<i class=\"fa fa-envelope-o\"/>&amp;nbsp;"
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_point_view_form
msgid ""
"<span attrs=\"{'invisible': [('measure_frequency_type', '=', "
"'all')]}\">Every </span>"
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.stock_picking_view_form_inherit_quality
msgid ""
"<span class=\"o_stat_text text-success\" attrs=\"{'invisible': [('quality_check_fail', '=', True)]}\">Quality Checks</span>\n"
"                    <span class=\"o_stat_text text-danger\" attrs=\"{'invisible': [('quality_check_fail', '!=', True)]}\">Quality Checks</span>"
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_point_view_form
msgid ""
"<span class=\"o_stat_text\">AVG:</span>\n"
"                                <span class=\"o_stat_text\">STD:</span>"
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.stock_picking_view_form_inherit_quality
msgid "<span class=\"o_stat_text\">Quality Alert</span>"
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_alert_view_form
msgid "<span class=\"o_stat_text\">Quality Check</span>"
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_point_view_form
msgid "<span>from </span>"
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_point_view_form
msgid "<span>to </span>"
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.view_quality_point_kanban
msgid "<strong>Operation :</strong>"
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.view_quality_point_kanban
msgid "<strong>Product :</strong>"
msgstr ""

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team_alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_alert_team_view_form
msgid "Accept Emails From"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_point_active
msgid "Active"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage_done
msgid "Alert Processed"
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_check_view_form
msgid "Alert(s)"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check_alert_ids
#: model:ir.model.fields,field_description:quality.field_stock_picking_quality_alert_ids
#: model:ir.ui.menu,name:quality.menu_quality_alerts
msgid "Alerts"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team_alias_id
msgid "Alias"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team_alias_contact
msgid "Alias Contact Security"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team_alias_name
msgid "Alias Name"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team_alias_domain
msgid "Alias domain"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team_alias_model_id
msgid "Aliased Model"
msgstr ""

#. module: quality
#: selection:quality.point,measure_frequency_type:0
msgid "All Operations"
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_point_view_search
msgid "Archived"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_point_average
msgid "Average"
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_check_view_form_small
msgid "Cancel"
msgstr "Anullo"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_check_id
#: model:ir.model.fields,field_description:quality.field_quality_point_check_ids
msgid "Check"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_point_check_count
msgid "Check Count"
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_check_view_tree
msgid "Checked By"
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_check_view_tree
msgid "Checked Date"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_stock_picking_check_ids
msgid "Checks"
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_alert_team_dashboard_view_kanban
msgid "Checks In Progress"
msgstr ""

#. module: quality
#: model:ir.actions.act_window,help:quality.quality_point_action
msgid "Click here to add a new Quality Control Points"
msgstr ""

#. module: quality
#: model:ir.actions.act_window,help:quality.quality_tag_action
msgid "Click to add a new tag."
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team_color
msgid "Color"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_tag_color
msgid "Color Index"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_company_id
#: model:ir.model.fields,field_description:quality.field_quality_alert_team_company_id
#: model:ir.model.fields,field_description:quality.field_quality_check_company_id
#: model:ir.model.fields,field_description:quality.field_quality_point_company_id
msgid "Company"
msgstr "Kompani"

#. module: quality
#: model:ir.ui.menu,name:quality.menu_quality_configuration
msgid "Configuration"
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_check_view_form_failure
msgid "Confirm Measure"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check_control_date
msgid "Control Date"
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_point_view_form
msgid "Control Frequency"
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_check_view_form
msgid "Control Person"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check_point_id
msgid "Control Point"
msgstr ""

#. module: quality
#: model:ir.ui.menu,name:quality.menu_quality_control_points
msgid "Control Points"
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_point_view_form
msgid "Control Type"
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_check_view_form_failure
msgid "Correct Measure"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_action_corrective
msgid "Corrective Action"
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_alert_view_form
msgid "Corrective Actions"
msgstr ""

#. module: quality
#: model:ir.actions.act_window,help:quality.quality_alert_action_check
msgid "Create Quality Alerts."
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_create_uid
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage_create_uid
#: model:ir.model.fields,field_description:quality.field_quality_alert_team_create_uid
#: model:ir.model.fields,field_description:quality.field_quality_check_create_uid
#: model:ir.model.fields,field_description:quality.field_quality_point_create_uid
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type_create_uid
#: model:ir.model.fields,field_description:quality.field_quality_reason_create_uid
#: model:ir.model.fields,field_description:quality.field_quality_tag_create_uid
msgid "Created by"
msgstr "Krijuar nga"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_create_date
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage_create_date
#: model:ir.model.fields,field_description:quality.field_quality_alert_team_create_date
#: model:ir.model.fields,field_description:quality.field_quality_check_create_date
#: model:ir.model.fields,field_description:quality.field_quality_point_create_date
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type_create_date
#: model:ir.model.fields,field_description:quality.field_quality_reason_create_date
#: model:ir.model.fields,field_description:quality.field_quality_tag_create_date
msgid "Created on"
msgstr "Krijuar me"

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Creation Date"
msgstr ""

#. module: quality
#: model:ir.ui.menu,name:quality.menu_quality_dashboard
msgid "Dashboard"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_date_assign
msgid "Date Assigned"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_date_close
msgid "Date Closed"
msgstr ""

#. module: quality
#: selection:quality.point,measure_frequency_unit:0
msgid "Day(s)"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team_alias_defaults
msgid "Default Values"
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_alert_view_form
msgid "Describe the corrective actions you did..."
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_alert_view_form
msgid "Describe the preventive actions you did..."
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_point_view_form
msgid "Describe the quality check to do..."
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_point_view_form
msgid "Describe why you need to perform this quality check..."
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_description
#: model:ir.ui.view,arch_db:quality.quality_alert_view_form
msgid "Description"
msgstr "Përshkrimi"

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_alert_view_form
msgid "Description of the issue..."
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_display_name
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage_display_name
#: model:ir.model.fields,field_description:quality.field_quality_alert_team_display_name
#: model:ir.model.fields,field_description:quality.field_quality_check_display_name
#: model:ir.model.fields,field_description:quality.field_quality_point_display_name
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type_display_name
#: model:ir.model.fields,field_description:quality.field_quality_reason_display_name
#: model:ir.model.fields,field_description:quality.field_quality_tag_display_name
msgid "Display Name"
msgstr "Emri i paraqitur"

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Done"
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_alert_team_view_form
msgid "Email Alias"
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_check_view_form
#: model:ir.ui.view,arch_db:quality.quality_check_view_form_small
#: selection:quality.check,measure_success:0
msgid "Fail"
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_check_view_search
#: selection:quality.check,quality_state:0
msgid "Failed"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check_failure_message
#: model:ir.model.fields,field_description:quality.field_quality_point_failure_message
msgid "Failure Message"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage_folded
msgid "Folded"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_point_measure_frequency_type
#: model:ir.model.fields,field_description:quality.field_quality_point_measure_frequency_unit_value
msgid "Frequency"
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_alert_view_search
#: model:ir.ui.view,arch_db:quality.quality_check_view_search
msgid "Group By"
msgstr "Grupo Nga"

#. module: quality
#: model:ir.module.category,description:quality.module_category_quality
msgid "Helps you manage your quality alerts and quality checks."
msgstr ""

#. module: quality
#: selection:quality.alert,priority:0
msgid "High"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_id
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage_id
#: model:ir.model.fields,field_description:quality.field_quality_alert_team_id
#: model:ir.model.fields,field_description:quality.field_quality_check_id
#: model:ir.model.fields,field_description:quality.field_quality_point_id
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type_id
#: model:ir.model.fields,field_description:quality.field_quality_reason_id
#: model:ir.model.fields,field_description:quality.field_quality_tag_id
msgid "ID"
msgstr "ID"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team_alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_check_view_search
msgid "In Progress"
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "In progress"
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_point_view_form
msgid "Instructions"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert___last_update
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage___last_update
#: model:ir.model.fields,field_description:quality.field_quality_alert_team___last_update
#: model:ir.model.fields,field_description:quality.field_quality_check___last_update
#: model:ir.model.fields,field_description:quality.field_quality_point___last_update
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type___last_update
#: model:ir.model.fields,field_description:quality.field_quality_reason___last_update
#: model:ir.model.fields,field_description:quality.field_quality_tag___last_update
msgid "Last Modified on"
msgstr "Modifikimi i fundit në"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage_write_uid
#: model:ir.model.fields,field_description:quality.field_quality_alert_team_write_uid
#: model:ir.model.fields,field_description:quality.field_quality_alert_write_uid
#: model:ir.model.fields,field_description:quality.field_quality_check_write_uid
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type_write_uid
#: model:ir.model.fields,field_description:quality.field_quality_point_write_uid
#: model:ir.model.fields,field_description:quality.field_quality_reason_write_uid
#: model:ir.model.fields,field_description:quality.field_quality_tag_write_uid
msgid "Last Updated by"
msgstr "Modifikuar per here te fundit nga"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage_write_date
#: model:ir.model.fields,field_description:quality.field_quality_alert_team_write_date
#: model:ir.model.fields,field_description:quality.field_quality_alert_write_date
#: model:ir.model.fields,field_description:quality.field_quality_check_write_date
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type_write_date
#: model:ir.model.fields,field_description:quality.field_quality_point_write_date
#: model:ir.model.fields,field_description:quality.field_quality_reason_write_date
#: model:ir.model.fields,field_description:quality.field_quality_tag_write_date
msgid "Last Updated on"
msgstr "Modifikuar per here te fundit me"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_lot_id
#: model:ir.model.fields,field_description:quality.field_quality_check_lot_id
msgid "Lot"
msgstr ""

#. module: quality
#: selection:quality.alert,priority:0
msgid "Low"
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_check_view_form
msgid "Make Alert"
msgstr ""

#. module: quality
#: model:res.groups,name:quality.group_quality_manager
msgid "Manager"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check_tolerance_max
#: model:ir.model.fields,field_description:quality.field_quality_point_tolerance_max
msgid "Max Tolerance"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check_measure
msgid "Measure"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_point_measure_frequency_unit
msgid "Measure Frequency Unit"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check_measure_success
msgid "Measure Success"
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_point_view_form
msgid "Message If Failure"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check_tolerance_min
#: model:ir.model.fields,field_description:quality.field_quality_point_tolerance_min
msgid "Min Tolerance"
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_alert_view_form
msgid "Miscellaneous"
msgstr ""

#. module: quality
#: selection:quality.point,measure_frequency_unit:0
msgid "Month(s)"
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "My Alerts"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_name
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage_name
#: model:ir.model.fields,field_description:quality.field_quality_alert_team_name
#: model:ir.model.fields,field_description:quality.field_quality_check_name
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type_name
#: model:ir.model.fields,field_description:quality.field_quality_reason_name
#: model:ir.model.fields,field_description:quality.field_quality_tag_name
msgid "Name"
msgstr ""

#. module: quality
#: code:addons/quality/models/quality.py:30
#: code:addons/quality/models/quality.py:116
#: code:addons/quality/models/quality.py:117
#: code:addons/quality/models/quality.py:230
#: code:addons/quality/models/quality.py:297
#: code:addons/quality/models/quality.py:298
#: code:addons/quality/models/quality.py:402
#: code:addons/quality/models/quality.py:437
#: code:addons/quality/models/quality.py:438
#: code:addons/quality/models/quality.py:478
#, python-format
msgid "New"
msgstr ""

#. module: quality
#: selection:quality.check,measure_success:0
msgid "No measure"
msgstr ""

#. module: quality
#: model:ir.actions.act_window,help:quality.quality_alert_action_report
msgid "No quality alerts."
msgstr ""

#. module: quality
#: model:ir.actions.act_window,help:quality.quality_check_action_report
msgid "No quality checks."
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_point_norm
msgid "Norm"
msgstr ""

#. module: quality
#: selection:quality.alert,priority:0
msgid "Normal"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check_note
#: model:ir.model.fields,field_description:quality.field_quality_point_note
#: model:ir.model.fields,field_description:quality.field_quality_point_reason
msgid "Note"
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_check_view_form
#: model:ir.ui.view,arch_db:quality.quality_point_view_form
msgid "Notes"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_picking_id
#: model:ir.model.fields,field_description:quality.field_quality_check_picking_id
#: model:ir.ui.view,arch_db:quality.quality_point_view_form
msgid "Operation"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_point_picking_type_id
msgid "Operation Type"
msgstr ""

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team_alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team_alias_user_id
msgid "Owner"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team_alias_parent_model_id
msgid "Parent Model"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team_alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr ""

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team_alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_check_view_form
#: model:ir.ui.view,arch_db:quality.quality_check_view_form_small
#: selection:quality.check,measure_success:0
msgid "Pass"
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_check_view_search
#: selection:quality.check,quality_state:0
msgid "Passed"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_stock_picking_quality_check_todo
msgid "Pending checks"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_point_measure_frequency_value
msgid "Percentage"
msgstr ""

#. module: quality
#: selection:quality.point,measure_frequency_type:0
msgid "Periodically"
msgstr ""

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team_alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_action_preventive
msgid "Preventive Action"
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_alert_view_form
msgid "Preventive Actions"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_priority
msgid "Priority"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_product_tmpl_id
#: model:ir.model.fields,field_description:quality.field_quality_check_product_id
#: model:ir.model.fields,field_description:quality.field_quality_point_product_tmpl_id
msgid "Product"
msgstr "Produkti"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_product_id
#: model:ir.model.fields,field_description:quality.field_quality_point_product_id
msgid "Product Variant"
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_alert_team_view_form
msgid "Project Name"
msgstr ""

#. module: quality
#: model:ir.module.category,name:quality.module_category_quality
#: model:ir.ui.menu,name:quality.menu_quality_root
msgid "Quality"
msgstr ""

#. module: quality
#: code:addons/quality/models/quality.py:360
#: code:addons/quality/models/quality.py:373
#: model:ir.model,name:quality.model_quality_alert
#: model:ir.ui.view,arch_db:quality.quality_alert_view_calendar
#: model:ir.ui.view,arch_db:quality.stock_picking_view_form_inherit_quality
#, python-format
msgid "Quality Alert"
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_alert_view_graph
#: model:ir.ui.view,arch_db:quality.quality_alert_view_pivot
msgid "Quality Alert Analysis"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_stock_picking_quality_alert_count
msgid "Quality Alert Count"
msgstr ""

#. module: quality
#: model:ir.model,name:quality.model_quality_alert_stage
msgid "Quality Alert Stage"
msgstr ""

#. module: quality
#: model:ir.actions.act_window,name:quality.quality_alert_stage_action
#: model:ir.ui.menu,name:quality.menu_quality_config_alert_stage
msgid "Quality Alert Stages"
msgstr ""

#. module: quality
#: model:ir.model,name:quality.model_quality_alert_team
msgid "Quality Alert Team"
msgstr ""

#. module: quality
#: model:ir.ui.menu,name:quality.menu_quality_config_alert_team
msgid "Quality Alert Teams"
msgstr ""

#. module: quality
#: model:ir.actions.act_window,help:quality.quality_alert_stage_action
msgid ""
"Quality Alert stages define the different steps a quality alert should go "
"through."
msgstr ""

#. module: quality
#: model:ir.actions.act_window,name:quality.quality_alert_action_check
#: model:ir.actions.act_window,name:quality.quality_alert_action_report
#: model:ir.actions.act_window,name:quality.quality_alert_action_team
#: model:ir.ui.menu,name:quality.menu_quality_alert
#: model:ir.ui.menu,name:quality.menu_quality_alert_report
#: model:ir.ui.view,arch_db:quality.quality_alert_team_dashboard_view_kanban
msgid "Quality Alerts"
msgstr ""

#. module: quality
#: code:addons/quality/models/quality.py:455
#: model:ir.model,name:quality.model_quality_check
#, python-format
msgid "Quality Check"
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_check_view_graph
#: model:ir.ui.view,arch_db:quality.quality_check_view_pivot
msgid "Quality Check Analysis"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_stock_picking_quality_check_fail
msgid "Quality Check Fail"
msgstr ""

#. module: quality
#: code:addons/quality/models/quality.py:321
#: model:ir.ui.view,arch_db:quality.quality_check_view_form_failure
#, python-format
msgid "Quality Check Failed"
msgstr ""

#. module: quality
#: model:ir.actions.act_window,help:quality.quality_check_action_main
#: model:ir.actions.act_window,help:quality.quality_check_action_team
msgid "Quality Check is the execution of a quality control in some point"
msgstr ""

#. module: quality
#: code:addons/quality/models/quality.py:337
#: model:ir.actions.act_window,name:quality.quality_check_action_main
#: model:ir.actions.act_window,name:quality.quality_check_action_picking
#: model:ir.actions.act_window,name:quality.quality_check_action_report
#: model:ir.actions.act_window,name:quality.quality_check_action_small
#: model:ir.actions.act_window,name:quality.quality_check_action_team
#: model:ir.ui.menu,name:quality.menu_quality_check_report
#: model:ir.ui.menu,name:quality.menu_quality_checks
#: model:ir.ui.view,arch_db:quality.quality_check_view_form_small
#: model:ir.ui.view,arch_db:quality.quality_point_view_form
#: model:ir.ui.view,arch_db:quality.stock_picking_view_form_inherit_quality
#, python-format
msgid "Quality Checks"
msgstr ""

#. module: quality
#: model:ir.actions.act_window,name:quality.quality_check_action_spc
msgid "Quality Checks SPC"
msgstr ""

#. module: quality
#: model:ir.ui.menu,name:quality.menu_quality_control
msgid "Quality Control"
msgstr ""

#. module: quality
#: model:ir.actions.act_window,name:quality.quality_point_action
msgid "Quality Control Points"
msgstr ""

#. module: quality
#: model:ir.actions.act_window,help:quality.quality_point_action
msgid ""
"Quality Control Points are places in your logistics process\n"
"                where you want to do quality control."
msgstr ""

#. module: quality
#: model:ir.ui.menu,name:quality.menu_quality_controls
msgid "Quality Controls"
msgstr ""

#. module: quality
#: model:ir.actions.act_window,name:quality.quality_alert_team_action
msgid "Quality Dashboard"
msgstr ""

#. module: quality
#: model:ir.model,name:quality.model_quality_point
msgid "Quality Point"
msgstr ""

#. module: quality
#: model:ir.model,name:quality.model_quality_reason
msgid "Quality Reason"
msgstr ""

#. module: quality
#: model:ir.model,name:quality.model_quality_tag
msgid "Quality Tag"
msgstr ""

#. module: quality
#: model:ir.actions.act_window,name:quality.quality_tag_action
#: model:ir.ui.menu,name:quality.menu_config_quality_tags
msgid "Quality Tags"
msgstr ""

#. module: quality
#: model:ir.actions.act_window,name:quality.quality_alert_team_action_config
msgid "Quality Teams"
msgstr ""

#. module: quality
#: model:ir.actions.act_window,help:quality.quality_alert_team_action
msgid ""
"Quality Teams group the different quality alerts/checks\n"
"                according to the roles (teams) that need them."
msgstr ""

#. module: quality
#: selection:quality.point,measure_frequency_type:0
msgid "Randomly"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team_alias_force_thread_id
msgid "Record Thread ID"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_point_name
msgid "Reference"
msgstr ""

#. module: quality
#: model:ir.ui.menu,name:quality.menu_quality_reporting
msgid "Reporting"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_user_id
#: model:ir.model.fields,field_description:quality.field_quality_check_user_id
#: model:ir.model.fields,field_description:quality.field_quality_point_user_id
#: model:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Responsible"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_reason_id
#: model:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Root Cause"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage_sequence
#: model:ir.model.fields,field_description:quality.field_quality_alert_team_sequence
#: model:ir.model.fields,field_description:quality.field_quality_point_sequence
msgid "Sequence"
msgstr "Sekuencë"

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_alert_team_dashboard_view_kanban
msgid "Settings"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage_id_8974
#: model:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Stage"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_point_standard_deviation
msgid "Standard Deviation"
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_check_view_search
msgid "State"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check_quality_state
msgid "Status"
msgstr "Statusi"

#. module: quality
#: model:ir.model,name:quality.model_stock_move
msgid "Stock Move"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_tag_ids
#: model:ir.ui.view,arch_db:quality.quality_tag_view_search
#: model:ir.ui.view,arch_db:quality.quality_tag_view_tree
msgid "Tags"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team_id_8984
#: model:ir.model.fields,field_description:quality.field_quality_check_team_id
#: model:ir.model.fields,field_description:quality.field_quality_point_team_id
msgid "Team"
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_alert_team_view_tree
msgid "Teams"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check_test_type
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type_technical_name
msgid "Technical name"
msgstr ""

#. module: quality
#: model:ir.model,name:quality.model_quality_point_test_type
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type_id_8842
msgid "Test Type"
msgstr ""

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team_alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team_alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team_alias_user_id
msgid ""
"The owner of records created upon receiving emails on this alias. If this "
"field is not set the system will attempt to find the right owner based on "
"the sender (From) address, or will use the Administrator account if no "
"system user is found for that address."
msgstr ""

#. module: quality
#: model:res.groups,comment:quality.group_quality_manager
msgid "The quality manager manages the quality process"
msgstr ""

#. module: quality
#: model:res.groups,comment:quality.group_quality_user
msgid "The quality user uses the quality process"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_point_title
msgid "Title"
msgstr ""

#. module: quality
#: selection:quality.check,quality_state:0
msgid "To do"
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_point_view_form
msgid "Tolerance"
msgstr ""

#. module: quality
#: model:ir.model,name:quality.model_stock_picking
msgid "Transfer"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check_norm_unit
#: model:ir.model.fields,field_description:quality.field_quality_point_norm_unit
msgid "Unit of Measure"
msgstr ""

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_tag_color
msgid "Used in the kanban view"
msgstr ""

#. module: quality
#: model:res.groups,name:quality.group_quality_user
msgid "User"
msgstr ""

#. module: quality
#: model:ir.ui.view,arch_db:quality.quality_check_view_form_small
msgid "Validate"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_partner_id
msgid "Vendor"
msgstr ""

#. module: quality
#: selection:quality.alert,priority:0
msgid "Very High"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check_warning_message
msgid "Warning Message"
msgstr ""

#. module: quality
#: selection:quality.point,measure_frequency_unit:0
msgid "Week(s)"
msgstr ""

#. module: quality
#: code:addons/quality/models/quality.py:273
#, python-format
msgid "You measured %.2f %s and it should be between %.2f and %.2f %s."
msgstr ""

#. module: quality
#: code:addons/quality/models/stock_picking.py:63
#, python-format
msgid "You still need to do the quality checks!"
msgstr ""
