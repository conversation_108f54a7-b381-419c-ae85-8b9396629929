# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* calendar
# 
# Translators:
# <PERSON><PERSON><PERSON>IL<PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# 8bbc36517816c0507c9ca350a0e523fd_9e68207 <90a3963e59612d1681dec64fbc906ae0_331927>, 2020
# <AUTHOR> <EMAIL>, 2020
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2020
# F<PERSON>rat <PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# Martin <PERSON>, 2020
# <PERSON>ent <PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# <AUTHOR> <EMAIL>, 2020
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# Um<PERSON> Akın <<EMAIL>>, 2020
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-11-16 13:33+0000\n"
"PO-Revision-Date: 2020-09-07 08:11+0000\n"
"Last-Translator: Ertuğrul Güreş <<EMAIL>>, 2022\n"
"Language-Team: Turkish (https://www.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: calendar
#: model:mail.template,subject:calendar.calendar_template_meeting_reminder
msgid "${object.event_id.name} - Reminder"
msgstr "${object.event_id.name} - Hatırlatma"

#. module: calendar
#: model:mail.template,subject:calendar.calendar_template_meeting_changedate
msgid "${object.event_id.name}: Date updated"
msgstr "${object.event_id.name}: Tarih güncellendi"

#. module: calendar
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid ""
"%(date_start)s at %(time_start)s To\n"
" %(date_end)s at %(time_end)s (%(timezone)s)"
msgstr ""
"%(date_start)s at %(time_start)s To\n"
" %(date_end)s at %(time_end)s (%(timezone)s)"

#. module: calendar
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid "%(day)s at (%(start)s To %(end)s) (%(timezone)s)"
msgstr "%(day)s at (%(start)s To %(end)s) (%(timezone)s)"

#. module: calendar
#: code:addons/calendar/models/calendar_attendee.py:0
#, python-format
msgid "%s has accepted invitation"
msgstr "%s daveti kabul etti."

#. module: calendar
#: code:addons/calendar/models/calendar_attendee.py:0
#, python-format
msgid "%s has declined invitation"
msgstr "%s daveti kabul etmedi"

#. module: calendar
#: model:mail.template,body_html:calendar.calendar_template_meeting_invitation
msgid ""
"<div>\n"
"    % set colors = ctx.get('colors', {})\n"
"    % set recurrent = object.recurrence_id and not ctx['ignore_recurrence']\n"
"    <p>\n"
"        Hello ${object.common_name},<br/><br/>\n"
"        ${object.event_id.user_id.partner_id.name} invited you to the ${object.event_id.name} meeting of ${object.event_id.user_id.company_id.name}.\n"
"    </p>\n"
"    <div style=\"text-align: center; margin: 16px 0px 16px 0px;\">\n"
"        % set target = 'recurrence' if recurrent else 'meeting'\n"
"        <a href=\"/calendar/${target}/accept?token=${object.access_token}&amp;id=${object.event_id.id}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Accept</a>\n"
"        <a href=\"/calendar/${target}/decline?token=${object.access_token}&amp;id=${object.event_id.id}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Decline</a>\n"
"        <a href=\"/calendar/meeting/view?token=${object.access_token}&amp;id=${object.event_id.id}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            View</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        % if not recurrent:\n"
"        <td width=\"130px;\">\n"
"            <div style=\"border-top-left-radius: 3px; border-top-right-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                ${object.event_id.get_interval('dayname', tz=object.partner_id.tz if not object.event_id.allday else None)}\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                ${object.event_id.get_interval('day', tz=object.partner_id.tz if not object.event_id.allday else None)}\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                ${object.event_id.get_interval('month', tz=object.partner_id.tz if not object.event_id.allday else None)}\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-right-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-left-radius: 3px;\">\n"
"                ${not object.event_id.allday and object.event_id.get_interval('time', tz=object.partner_id.tz) or ''}\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"/>\n"
"        % endif\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>Details of the event</strong></p>\n"
"            <ul>\n"
"                % if object.event_id.location:\n"
"                    <li>Location: ${object.event_id.location}\n"
"                        (<a target=\"_blank\" href=\"http://maps.google.com/maps?oi=map&amp;q=${object.event_id.location}\">View Map</a>)\n"
"                    </li>\n"
"                % endif\n"
"                % if object.event_id.description :\n"
"                    <li>Description: ${object.event_id.description}</li>\n"
"                % endif\n"
"                % if recurrent:\n"
"                    <li>When: ${object.recurrence_id.name}</li>\n"
"                % endif\n"
"                % if not object.event_id.allday and object.event_id.duration\n"
"                    <li>Duration: ${('%dH%02d' % (object.event_id.duration,round(object.event_id.duration*60)%60))}</li>\n"
"                % endif\n"
"                <li>Attendees\n"
"                <ul>\n"
"                % for attendee in object.event_id.attendee_ids:\n"
"                    <li>\n"
"                        <div style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:${colors[attendee.state] or 'white'};\"> </div>\n"
"                        % if attendee.common_name != object.common_name:\n"
"                            <span style=\"margin-left:5px\">${attendee.common_name}</span>\n"
"                        % else:\n"
"                            <span style=\"margin-left:5px\">You</span>\n"
"                        % endif\n"
"                    </li>\n"
"                % endfor\n"
"                </ul></li>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br/>\n"
"    Thank you,\n"
"    % if object.event_id.user_id.signature:\n"
"        <br/>\n"
"        ${object.event_id.user_id.signature | safe}\n"
"    % endif\n"
"</div>\n"
"            "
msgstr ""

#. module: calendar
#: model:mail.template,body_html:calendar.calendar_template_meeting_changedate
msgid ""
"<div>\n"
"    % set colors = ctx.get('colors', {})\n"
"    % set recurrent = object.recurrence_id and not ctx['ignore_recurrence']\n"
"    <p>\n"
"        Hello ${object.common_name},<br/><br/>\n"
"        The date of the meeting has been updated. The meeting ${object.event_id.name} created by ${object.event_id.user_id.partner_id.name} is now scheduled for ${object.event_id.get_display_time_tz(tz=object.partner_id.tz)}.\n"
"    </p>\n"
"    <div style=\"text-align: center; margin: 16px 0px 16px 0px;\">\n"
"        % set target = 'recurrence' if recurrent else 'meeting'\n"
"        <a href=\"/calendar/${target}/accept?token=${object.access_token}&amp;id=${object.event_id.id}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Accept</a>\n"
"        <a href=\"/calendar/${target}/decline?token=${object.access_token}&amp;id=${object.event_id.id}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Decline</a>\n"
"        <a href=\"/calendar/meeting/view?token=${object.access_token}&amp;id=${object.event_id.id}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            View</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        % if not recurrent:\n"
"        <td width=\"130px;\">\n"
"            <div style=\"border-top-left-radius: 3px; border-top-right-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                ${object.event_id.get_interval('dayname', tz=object.partner_id.tz if not object.event_id.allday else None)}\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                ${object.event_id.get_interval('day', tz=object.partner_id.tz if not object.event_id.allday else None)}\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                ${object.event_id.get_interval('month', tz=object.partner_id.tz if not object.event_id.allday else None)}\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-right-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-left-radius: 3px;\">\n"
"                ${not object.event_id.allday and object.event_id.get_interval('time', tz=object.partner_id.tz) or ''}\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"/>\n"
"        % endif\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>Details of the event</strong></p>\n"
"            <ul>\n"
"                % if object.event_id.location:\n"
"                    <li>Location: ${object.event_id.location}\n"
"                        (<a target=\"_blank\" href=\"http://maps.google.com/maps?oi=map&amp;q=${object.event_id.location}\">View Map</a>)\n"
"                    </li>\n"
"                % endif\n"
"                % if object.event_id.description :\n"
"                    <li>Description: ${object.event_id.description}</li>\n"
"                % endif\n"
"                % if recurrent:\n"
"                    <li>When: ${object.recurrence_id.name}</li>\n"
"                % endif\n"
"                % if not object.event_id.allday and object.event_id.duration\n"
"                    <li>Duration: ${('%dH%02d' % (object.event_id.duration,round(object.event_id.duration*60)%60))}</li>\n"
"                % endif\n"
"                <li>Attendees\n"
"                <ul>\n"
"                % for attendee in object.event_id.attendee_ids:\n"
"                    <li>\n"
"                        <div style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background: ${colors[attendee.state] or 'white'};\"> </div>\n"
"                        % if attendee.common_name != object.common_name:\n"
"                            <span style=\"margin-left:5px\">${attendee.common_name}</span>\n"
"                        % else:\n"
"                            <span style=\"margin-left:5px\">You</span>\n"
"                        % endif\n"
"                    </li>\n"
"                % endfor\n"
"                </ul></li>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br/>\n"
"    Thank you,\n"
"    % if object.event_id.user_id.signature:\n"
"        <br/>\n"
"        ${object.event_id.user_id.signature | safe}\n"
"    % endif\n"
"</div>\n"
"            "
msgstr ""
"<div>\n"
"    % set colors = ctx.get('colors', {})\n"
"    % set recurrent = object.recurrence_id and not ctx['ignore_recurrence']\n"
"    <p>\n"
"        Hello ${object.common_name},<br/><br/>\n"
"        The date of the meeting has been updated. The meeting ${object.event_id.name} created by ${object.event_id.user_id.partner_id.name} is now scheduled for ${object.event_id.get_display_time_tz(tz=object.partner_id.tz)}.\n"
"    </p>\n"
"    <div style=\"text-align: center; margin: 16px 0px 16px 0px;\">\n"
"        % set target = 'recurrence' if recurrent else 'meeting'\n"
"        <a href=\"/calendar/${target}/accept?token=${object.access_token}&amp;id=${object.event_id.id}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Accept</a>\n"
"        <a href=\"/calendar/${target}/decline?token=${object.access_token}&amp;id=${object.event_id.id}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Decline</a>\n"
"        <a href=\"/calendar/meeting/view?token=${object.access_token}&amp;id=${object.event_id.id}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            View</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        % if not recurrent:\n"
"        <td width=\"130px;\">\n"
"            <div style=\"border-top-left-radius: 3px; border-top-right-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                ${object.event_id.get_interval('dayname', tz=object.partner_id.tz if not object.event_id.allday else None)}\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                ${object.event_id.get_interval('day', tz=object.partner_id.tz if not object.event_id.allday else None)}\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                ${object.event_id.get_interval('month', tz=object.partner_id.tz if not object.event_id.allday else None)}\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-right-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-left-radius: 3px;\">\n"
"                ${not object.event_id.allday and object.event_id.get_interval('time', tz=object.partner_id.tz) or ''}\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"/>\n"
"        % endif\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>Details of the event</strong></p>\n"
"            <ul>\n"
"                % if object.event_id.location:\n"
"                    <li>Location: ${object.event_id.location}\n"
"                        (<a target=\"_blank\" href=\"http://maps.google.com/maps?oi=map&amp;q=${object.event_id.location}\">View Map</a>)\n"
"                    </li>\n"
"                % endif\n"
"                % if object.event_id.description :\n"
"                    <li>Description: ${object.event_id.description}</li>\n"
"                % endif\n"
"                % if recurrent:\n"
"                    <li>When: ${object.recurrence_id.name}</li>\n"
"                % endif\n"
"                % if not object.event_id.allday and object.event_id.duration\n"
"                    <li>Duration: ${('%dH%02d' % (object.event_id.duration,round(object.event_id.duration*60)%60))}</li>\n"
"                % endif\n"
"                <li>Attendees\n"
"                <ul>\n"
"                % for attendee in object.event_id.attendee_ids:\n"
"                    <li>\n"
"                        <div style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background: ${colors[attendee.state] or 'white'};\"> </div>\n"
"                        % if attendee.common_name != object.common_name:\n"
"                            <span style=\"margin-left:5px\">${attendee.common_name}</span>\n"
"                        % else:\n"
"                            <span style=\"margin-left:5px\">You</span>\n"
"                        % endif\n"
"                    </li>\n"
"                % endfor\n"
"                </ul></li>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br/>\n"
"    Thank you,\n"
"    % if object.event_id.user_id.signature:\n"
"        <br/>\n"
"        ${object.event_id.user_id.signature | safe}\n"
"    % endif\n"
"</div>\n"
"            "

#. module: calendar
#: model:mail.template,body_html:calendar.calendar_template_meeting_reminder
msgid ""
"<div>\n"
"    % set colors = {'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00',  'declined': 'red'}\n"
"    <!--\n"
"        In a recurring event case, the object.event_id is always the first event\n"
"        This makes the event date (and a lot of other information) incorrect\n"
"    -->\n"
"    % set event_id = ctx.get('force_event_id') or object.event_id\n"
"    <p>\n"
"        Hello ${object.common_name},<br/><br/>\n"
"        This is a reminder for the below event :\n"
"    </p>\n"
"    <div style=\"text-align: center; margin: 16px 0px 16px 0px;\">\n"
"        <a href=\"/calendar/meeting/accept?token=${object.access_token}&amp;id=${object.event_id.id}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Accept</a>\n"
"        <a href=\"/calendar/meeting/decline?token=${object.access_token}&amp;id=${object.event_id.id}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Decline</a>\n"
"        <a href=\"/calendar/meeting/view?token=${object.access_token}&amp;id=${object.event_id.id}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            View</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\">\n"
"            <div style=\"border-top-left-radius: 3px; border-top-right-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                ${event_id.get_interval('dayname', tz=object.partner_id.tz if not event_id.allday else None)}\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                ${event_id.get_interval('day', tz=object.partner_id.tz if not event_id.allday else None)}\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                ${event_id.get_interval('month', tz=object.partner_id.tz if not event_id.allday else None)}\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-right-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-left-radius: 3px;\">\n"
"                ${not event_id.allday and event_id.get_interval('time', tz=object.partner_id.tz) or ''}\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"/>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>Details of the event</strong></p>\n"
"            <ul>\n"
"                % if object.event_id.location:\n"
"                    <li>Location: ${object.event_id.location}\n"
"                        (<a target=\"_blank\" href=\"http://maps.google.com/maps?oi=map&amp;q=${object.event_id.location}\">View Map</a>)\n"
"                    </li>\n"
"                % endif\n"
"                % if object.event_id.description :\n"
"                    <li>Description: ${object.event_id.description}</li>\n"
"                % endif\n"
"                % if not object.event_id.allday and object.event_id.duration\n"
"                    <li>Duration: ${('%dH%02d' % (object.event_id.duration,(object.event_id.duration*60)%60))}</li>\n"
"                % endif\n"
"                <li>Attendees\n"
"                <ul>\n"
"                % for attendee in object.event_id.attendee_ids:\n"
"                    <li>\n"
"                        <div style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:${colors[attendee.state] or 'white'};\"> </div>\n"
"                        % if attendee.common_name != object.common_name:\n"
"                            <span style=\"margin-left:5px\">${attendee.common_name}</span>\n"
"                        % else:\n"
"                            <span style=\"margin-left:5px\">You</span>\n"
"                        % endif\n"
"                    </li>\n"
"                % endfor\n"
"                </ul></li>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br/>\n"
"    Thank you,\n"
"    % if object.event_id.user_id.signature:\n"
"        <br/>\n"
"        ${object.event_id.user_id.signature | safe}\n"
"    % endif\n"
"</div>\n"
"            "
msgstr ""

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "<span> hours</span>"
msgstr "<span> saat</span>"

#. module: calendar
#: model:ir.model.constraint,message:calendar.constraint_calendar_contacts_user_id_partner_id_unique
msgid "A user cannot have the same contact twice."
msgstr "Bir kullanıcı aynı temasa iki kez sahip olamaz."

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/xml/base_calendar.xml:0
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#, python-format
msgid "Accept"
msgstr "Kabul Et"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_attendee__state__accepted
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__attendee_status__accepted
msgid "Accepted"
msgstr "Kabul Edildi"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_needaction
msgid "Action Needed"
msgstr "Eylem Gerekiyor"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_mail_activity_type__category
msgid "Action to Perform"
msgstr "Gerçekleştirilecek Eylem"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr ""
"Eylemler, takvim görünümünü açma gibi belirli davranışları tetikleyebilir "
"veya bir belge yüklendiği zaman otomatik olarak yapılmış olarak "
"işaretleyebilir"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_contacts__active
#: model:ir.model.fields,field_description:calendar.field_calendar_event__active
msgid "Active"
msgstr "Etkin"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__activity_ids
msgid "Activities"
msgstr "Aktiviteler"

#. module: calendar
#: model:ir.model,name:calendar.model_mail_activity
msgid "Activity"
msgstr "Aktivite"

#. module: calendar
#: model:ir.model,name:calendar.model_mail_activity_type
msgid "Activity Type"
msgstr "Aktivite Türü"

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/xml/base_calendar.xml:0
#: model:ir.model.fields,field_description:calendar.field_calendar_event__allday
#, python-format
msgid "All Day"
msgstr "Tüm Gün"

#. module: calendar
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid "All Day, %(day)s"
msgstr "Tüm Gün, %(day)s"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__recurrence_update__all_events
msgid "All events"
msgstr "All events"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Archived"
msgstr "Arşivlendi"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_attachment_count
msgid "Attachment Count"
msgstr "Ek Sayısı"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__attendee_status
msgid "Attendee Status"
msgstr "Katılımcı Durumu"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__partner_ids
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Attendees"
msgstr "Katılımcılar"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Availability"
msgstr "Kullanılabilirlik"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__base_event_id
msgid "Base Event"
msgstr "Base Event"

#. module: calendar
#: code:addons/calendar/models/calendar_event.py:0
#: model:ir.model.fields.selection,name:calendar.selection__calendar_attendee__availability__busy
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__show_as__busy
#, python-format
msgid "Busy"
msgstr "Meşgul"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__byday
msgid "By day"
msgstr "Gündüz"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__byday
msgid "Byday"
msgstr "Byday"

#. module: calendar
#: model:ir.ui.menu,name:calendar.mail_menu_calendar
#: model:ir.ui.menu,name:calendar.menu_calendar_configuration
msgid "Calendar"
msgstr "Takvim"

#. module: calendar
#: model:ir.actions.act_window,name:calendar.action_calendar_alarm
#: model:ir.ui.menu,name:calendar.menu_calendar_alarm
#: model_terms:ir.ui.view,arch_db:calendar.calendar_alarm_view_form
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_alarm_tree
msgid "Calendar Alarm"
msgstr "Takvim Alarmı"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_attendee
msgid "Calendar Attendee Information"
msgstr "Katılımcı Bilgisini Planlayın"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_contacts
msgid "Calendar Contacts"
msgstr "Kontakları Planla"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_event
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__calendar_event_ids
msgid "Calendar Event"
msgstr "Takvim Etkinliği"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Calendar Invitation"
msgstr "Takvim Daveti"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_mail_activity__calendar_event_id
msgid "Calendar Meeting"
msgstr "Takvim Toplantısı"

#. module: calendar
#: model:ir.actions.server,name:calendar.ir_cron_scheduler_alarm_ir_actions_server
#: model:ir.cron,cron_name:calendar.ir_cron_scheduler_alarm
#: model:ir.cron,name:calendar.ir_cron_scheduler_alarm
msgid "Calendar: Event Reminder"
msgstr "Takvim: Etkinlik Hatırlatıcı"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__recurrence_update
msgid ""
"Choose what to do with other events in the recurrence. Updating All Events "
"is not allowed when dates or time is modified"
msgstr ""
"Choose what to do with other events in the recurrence. Updating All Events "
"is not allowed when dates or time is modified"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__common_name
msgid "Common name"
msgstr "Genel ad"

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/js/calendar_controller.js:0
#, python-format
msgid "Confirm"
msgstr "Onayla"

#. module: calendar
#: model:ir.model,name:calendar.model_res_partner
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__partner_id
msgid "Contact"
msgstr "Temas"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__count
msgid "Count"
msgstr "Sayı"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_contacts__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_event__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__create_uid
msgid "Created by"
msgstr "Oluşturan"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_contacts__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_event__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__create_date
msgid "Created on"
msgstr "Oluşturulma"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Date"
msgstr "Tarih"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__day
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__month_by__date
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__month_by__date
msgid "Date of month"
msgstr "Ayın Günü"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__day
msgid "Day"
msgstr "Gün"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Day of Month"
msgstr "Ayın Günü"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__month_by__day
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__month_by__day
msgid "Day of month"
msgstr "Ayın günü"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_alarm__interval__days
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type__daily
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__rrule_type__daily
msgid "Days"
msgstr "Gün"

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/xml/base_calendar.xml:0
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#, python-format
msgid "Decline"
msgstr "Reddet"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_attendee__state__declined
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__attendee_status__declined
msgid "Declined"
msgstr "Reddedildi"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__description
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Description"
msgstr "Açıklama"

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/xml/notification_calendar.xml:0
#, python-format
msgid "Details"
msgstr "Detaylar"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm_manager__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_contacts__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_event__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__display_name
#: model:ir.model.fields,field_description:calendar.field_ir_http__display_name
#: model:ir.model.fields,field_description:calendar.field_mail_activity__display_name
#: model:ir.model.fields,field_description:calendar.field_mail_activity_type__display_name
#: model:ir.model.fields,field_description:calendar.field_res_partner__display_name
#: model:ir.model.fields,field_description:calendar.field_res_users__display_name
msgid "Display Name"
msgstr "Görünüm Adı"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Document"
msgstr "Belge"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__res_id
msgid "Document ID"
msgstr "Belge ID"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__res_model_id
msgid "Document Model"
msgstr "Belge Modeli"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__res_model
msgid "Document Model Name"
msgstr "Belge Model Adı"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__dtstart
msgid "Dtstart"
msgstr "Dtstart"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__duration
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Duration"
msgstr "Süre"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__duration_minutes
#: model:ir.model.fields,help:calendar.field_calendar_alarm__duration_minutes
msgid "Duration in minutes"
msgstr "Dakika cinsinden süre"

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/js/calendar_controller.js:0
#, python-format
msgid "Edit Recurrent event"
msgstr "Edit Recurrent event"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Edit recurring event"
msgstr "Edit recurring event"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__email
#: model:ir.model.fields.selection,name:calendar.selection__calendar_alarm__alarm_type__email
msgid "Email"
msgstr "E-Posta"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_mail_1
msgid "Email - 3 Hours"
msgstr "Eposta - 3 Saat"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_mail_2
msgid "Email - 6 Hours"
msgstr "Eposta - 6 Saat"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_attendee__email
msgid "Email of Invited Person"
msgstr "Davetli Kişinin Epostası"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_contacts__partner_id
msgid "Employee"
msgstr "Personel"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__stop_date
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_tree
msgid "End Date"
msgstr "Bitiş Tarihi"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__end_type
msgid "End Type"
msgstr "End Type"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__end_type__end_date
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__end_type__end_date
msgid "End date"
msgstr "Bitiş tarihi"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Ending at"
msgstr "Bitiş"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_alarm
msgid "Event Alarm"
msgstr "Etkinlik Alarmı"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_alarm_manager
msgid "Event Alarm Manager"
msgstr "Etkinlik Alarmı Yöneticisi"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_event_type
msgid "Event Meeting Type"
msgstr "Etkinlik Toplantısı Türü"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_recurrence
msgid "Event Recurrence Rule"
msgstr "Event Recurrence Rule"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__display_time
msgid "Event Time"
msgstr "Olay Zamanı"

#. module: calendar
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "Every %(count)s %(period)s"
msgstr "Her %(count)s %(period)s"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__privacy__public
msgid "Everyone"
msgstr "Herkes"

#. module: calendar
#: code:addons/calendar/models/mail_activity.py:0
#, python-format
msgid "Feedback: "
msgstr "Geribildirim:"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__byday__1
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__byday__1
msgid "First"
msgstr "İlk"

#. module: calendar
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid "First you have to specify the date of the invitation."
msgstr "İlk davet tarihini belirtmeniz gerekir."

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__follow_recurrence
msgid "Follow Recurrence"
msgstr "Follow Recurrence"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_follower_ids
msgid "Followers"
msgstr "Takipçiler"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_channel_ids
msgid "Followers (Channels)"
msgstr "Takipçiler (Kanallar)"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_partner_ids
msgid "Followers (Partners)"
msgstr "Takipçiler (İş ortakları)"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__end_type__forever
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__end_type__forever
msgid "Forever"
msgstr "Sonsuza dek"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__byday__4
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__byday__4
msgid "Fourth"
msgstr "Dördüncü"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__fr
msgid "Fr"
msgstr "Fr"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_attendee__availability__free
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__show_as__free
msgid "Free"
msgstr "Uygun"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__availability
msgid "Free/Busy"
msgstr "Uygun/Meşgul"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__fr
msgid "Fri"
msgstr "Cum"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__fr
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__fr
msgid "Friday"
msgstr "Cuma"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Group By"
msgstr "Grupla"

#. module: calendar
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid "Grouping by %s is not allowed."
msgstr "Grouping by %s is not allowed."

#. module: calendar
#: model:ir.model,name:calendar.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP Yönlendirme"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_alarm__interval__hours
msgid "Hours"
msgstr "Saat"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__id
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm_manager__id
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__id
#: model:ir.model.fields,field_description:calendar.field_calendar_contacts__id
#: model:ir.model.fields,field_description:calendar.field_calendar_event__id
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__id
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__id
#: model:ir.model.fields,field_description:calendar.field_ir_http__id
#: model:ir.model.fields,field_description:calendar.field_mail_activity__id
#: model:ir.model.fields,field_description:calendar.field_mail_activity_type__id
#: model:ir.model.fields,field_description:calendar.field_res_partner__id
#: model:ir.model.fields,field_description:calendar.field_res_users__id
msgid "ID"
msgstr "ID"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__message_needaction
#: model:ir.model.fields,help:calendar.field_calendar_event__message_unread
msgid "If checked, new messages require your attention."
msgstr "İşaretliyse, yeni mesajlar dikkatinize sunulacak."

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "İşaretliyse,bazı mesajlar gönderi hatası içermektedir."

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__active
msgid ""
"If the active field is set to false, it will allow you to hide the event "
"alarm information without removing it."
msgstr ""
"Şayet aktif alanı etkin değil olarak ayarlanırsa, etkinlik alarmını "
"etkinliği kaldırmanıza gerek kalmadan kapayabilirsiniz."

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__interval
msgid "Interval"
msgstr "Aralık"

#. module: calendar
#: model:mail.message.subtype,name:calendar.subtype_invitation
msgid "Invitation"
msgstr "Davet"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__access_token
msgid "Invitation Token"
msgstr "Davet Jetonu"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Invitation details"
msgstr "Davet Detayları"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Invitation for"
msgstr "Davetliler"

#. module: calendar
#: model:mail.template,subject:calendar.calendar_template_meeting_invitation
msgid "Invitation to ${object.event_id.name}"
msgstr "Davetli olarak ${object.event_id.name}"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Invitations"
msgstr "Davetler"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_is_follower
msgid "Is Follower"
msgstr "Takipçi mi"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__is_highlighted
msgid "Is the Event Highlighted"
msgstr "Etkinlik vurgulandı mı?"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__byday__-1
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__byday__-1
msgid "Last"
msgstr "Sonuncu"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm____last_update
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm_manager____last_update
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee____last_update
#: model:ir.model.fields,field_description:calendar.field_calendar_contacts____last_update
#: model:ir.model.fields,field_description:calendar.field_calendar_event____last_update
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type____last_update
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence____last_update
#: model:ir.model.fields,field_description:calendar.field_ir_http____last_update
#: model:ir.model.fields,field_description:calendar.field_mail_activity____last_update
#: model:ir.model.fields,field_description:calendar.field_mail_activity_type____last_update
#: model:ir.model.fields,field_description:calendar.field_res_partner____last_update
#: model:ir.model.fields,field_description:calendar.field_res_users____last_update
msgid "Last Modified on"
msgstr "Son Düzenleme"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_contacts__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_event__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__write_uid
msgid "Last Updated by"
msgstr "Son Güncelleyen"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_contacts__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_event__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__write_date
msgid "Last Updated on"
msgstr "Son Güncelleme"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_res_partner__calendar_last_notif_ack
#: model:ir.model.fields,field_description:calendar.field_res_users__calendar_last_notif_ack
msgid "Last notification marked as read from base Calendar"
msgstr "Temel takvimden okundu olarak işaretlenen son bildirim"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__rrule_type
msgid "Let the event automatically repeat at that interval"
msgstr "Belirli aralıklarla olayın kendiliğinden tekrarlamasına izin ver"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__location
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Location"
msgstr "Konum"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__location
msgid "Location of Event"
msgstr "Etkinlik Konumu"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Logo"
msgstr "Logo"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_main_attachment_id
msgid "Main Attachment"
msgstr "Ana Ek"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_contacts__user_id
msgid "Me"
msgstr "Bana"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__mail_activity_type__category__meeting
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Meeting"
msgstr "Toplantı"

#. module: calendar
#: code:addons/calendar/models/calendar_event.py:0
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid ""
"Meeting '%(name)s' starts '%(start_datetime)s' and ends '%(end_datetime)s'"
msgstr ""
"Meeting '%(name)s' starts '%(start_datetime)s' and ends '%(end_datetime)s'"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Meeting Details"
msgstr "Toplantı Ayrıntıları"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__name
msgid "Meeting Subject"
msgstr "Toplantı Konusu"

#. module: calendar
#: model:ir.actions.act_window,name:calendar.action_calendar_event_type
#: model:ir.ui.menu,name:calendar.menu_calendar_event_type
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_type_tree
msgid "Meeting Types"
msgstr "Toplantı Türü"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__event_id
msgid "Meeting linked"
msgstr "Toplantı bağlantılandı"

#. module: calendar
#: model:ir.actions.act_window,name:calendar.action_calendar_event
#: model:ir.actions.act_window,name:calendar.action_calendar_event_notify
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_tree
msgid "Meetings"
msgstr "Toplantılar"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_has_error
msgid "Message Delivery error"
msgstr "Mesaj Teslim hatası"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_ids
msgid "Messages"
msgstr "Mesajlar"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_alarm__interval__minutes
msgid "Minutes"
msgstr "Dakika "

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Misc"
msgstr "Diğer"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__mo
msgid "Mo"
msgstr "Mo"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__mo
msgid "Mon"
msgstr "Pzt"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__mo
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__mo
msgid "Monday"
msgstr "Pazartesi"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__month_by
msgid "Month By"
msgstr "Month By"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type__monthly
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__rrule_type__monthly
msgid "Months"
msgstr "Ay"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "My Meetings"
msgstr "Toplantılarım"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__name
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__name
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__name
msgid "Name"
msgstr "Adı"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_attendee__state__needsaction
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__attendee_status__needsaction
msgid "Needs Action"
msgstr "Eylem Gerektiriyor"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "No I'm not going."
msgstr "Hayır ben gitmiyorum."

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "No feedback yet"
msgstr "No feedback yet"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_alarm__alarm_type__notification
msgid "Notification"
msgstr "Bildirimler"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_notif_5
msgid "Notification - 1 Days"
msgstr "Bildirim - 1 Gün"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_notif_3
msgid "Notification - 1 Hours"
msgstr "Bildirim - 1 Saat"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_notif_1
msgid "Notification - 15 Minutes"
msgstr "Bildirim - 15 Dakika"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_notif_4
msgid "Notification - 2 Hours"
msgstr "Bildirim - 2 Saat"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_notif_2
msgid "Notification - 30 Minutes"
msgstr "Bildirim - 30 Dakika"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_needaction_counter
msgid "Number of Actions"
msgstr "Eylemlerin Sayısı"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_has_error_counter
msgid "Number of errors"
msgstr "Hata sayısı"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Eylem gerektiren mesaj sayısı"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Teslimat hatası olan mesaj sayısı"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__end_type__count
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__end_type__count
msgid "Number of repetitions"
msgstr "Tekrarlama Sayısı"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__message_unread_counter
msgid "Number of unread messages"
msgstr "Okunmamış mesaj sayısı"

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/xml/notification_calendar.xml:0
#, python-format
msgid "OK"
msgstr "Tamam"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__privacy__confidential
msgid "Only internal users"
msgstr "Sadece dahili kullanıcılar"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__privacy__private
msgid "Only me"
msgstr "Sadece ben"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.mail_activity_view_form_popup
msgid "Open Calendar"
msgstr "Takvimi Aç"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__month_by
msgid "Option"
msgstr "Seçenek"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Options"
msgstr "Seçenekler"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__attendee_ids
msgid "Participant"
msgstr "Katılımcı"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__partner_id
msgid "Partner-related data of the user"
msgstr "Kullanıcının İş Ortağı ilgili veriler"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__privacy
msgid "Privacy"
msgstr "Özel"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__rrule_type
msgid "Recurrence"
msgstr "Periyot"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__recurrence_id
#: model:ir.model.fields,field_description:calendar.field_calendar_event__recurrence_id
msgid "Recurrence Rule"
msgstr "Recurrence Rule"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__end_type
msgid "Recurrence Termination"
msgstr "Tekrarlama Sonlandırma"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__recurrence_update
msgid "Recurrence Update"
msgstr "Recurrence Update"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__recurrency
msgid "Recurrent"
msgstr "Yinelenen"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__recurrency
msgid "Recurrent Event"
msgstr "Recurrent Event"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__rrule
msgid "Recurrent Rule"
msgstr "Yinelenen Kural"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__duration
msgid "Remind Before"
msgstr "Öncesinde Hatırlat : "

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__alarm_ids
msgid "Reminders"
msgstr "Hatırlatmalar"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__count
msgid "Repeat"
msgstr "Tekrarla"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__interval
msgid "Repeat Every"
msgstr "Tekrarlama Durumu"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__until
msgid "Repeat Until"
msgstr "Buna Kadar Tekrarla"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__interval
msgid "Repeat every (Days/Week/Month/Year)"
msgstr "Tekrarlama Durumu (Gün/Hafta/Ay/Yıl)"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__count
msgid "Repeat x times"
msgstr "x kere tekrarla"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__user_id
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Responsible"
msgstr "Sorumlu"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__partner_id
msgid "Responsible Contact"
msgstr "Responsible Contact"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__rrule
msgid "Rrule"
msgstr "Rrule"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__rrule_type
msgid "Rrule Type"
msgstr "Rrule Type"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__sa
msgid "Sa"
msgstr "Sa"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__sa
msgid "Sat"
msgstr "Cts"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__sa
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__sa
msgid "Saturday"
msgstr "Cumartesi"

#. module: calendar
#: model_terms:ir.actions.act_window,help:calendar.action_calendar_event
msgid "Schedule a new meeting"
msgstr "Yeni bir toplantı planla"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Search Meetings"
msgstr "Toplantı Ara"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__byday__2
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__byday__2
msgid "Second"
msgstr "Saniye"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Select attendees..."
msgstr "Katılımcıları seçiniz..."

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Send mail"
msgstr "Mail Gönder"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__show_as
msgid "Show Time as"
msgstr "Saat Görünümü"

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/xml/notification_calendar.xml:0
#, python-format
msgid "Snooze"
msgstr "Erteleme"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__start
msgid "Start"
msgstr "Başla"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__start_date
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_tree
msgid "Start Date"
msgstr "Başlama Tarihi"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__start
msgid "Start date of an event, without time for full days events"
msgstr "Etkinlik başlangıç günü, tüm gün etinlikleri için zaman olmadan"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Starting at"
msgstr "Başlangıç"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__state
msgid "Status"
msgstr "Durumu"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_attendee__state
msgid "Status of the attendee's participation"
msgstr "Katılımcının iştirak durumu"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Status:"
msgstr "Durumu:"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__stop
msgid "Stop"
msgstr "Durdur"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__stop
msgid "Stop date of an event, without time for full days events"
msgstr "Etkinlik bitiş günü, tüm gün etkinlikler için zaman olmadan"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__su
msgid "Su"
msgstr "Su"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_tree
msgid "Subject"
msgstr "Konu"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__su
msgid "Sun"
msgstr "Paz"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__su
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__su
msgid "Sunday"
msgstr "Pazar"

#. module: calendar
#: model:ir.model.constraint,message:calendar.constraint_calendar_event_type_name_uniq
msgid "Tag name already exists !"
msgstr "Etiket adı halihazırda mevcut !"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__categ_ids
msgid "Tags"
msgstr "Etiketler"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Tentative"
msgstr "Tentative"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__th
msgid "Th"
msgstr "Th"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "The"
msgstr "Bu"

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/js/mail_activity.js:0
#, python-format
msgid ""
"The activity is linked to a meeting. Deleting it will remove the meeting as "
"well. Do you want to proceed ?"
msgstr ""
"Bu aktivite toplantı ile bağlıdır. Aktiviteyi silmeniz ile toplantıda "
"silinecektir. Buna devam etmek istiyor musunuz?"

#. module: calendar
#: model_terms:ir.actions.act_window,help:calendar.action_calendar_event
msgid ""
"The calendar is shared between employees and fully integrated with\n"
"            other applications such as the employee leaves or the business\n"
"            opportunities."
msgstr ""
"Bu takvim personel arasında paylaşılır ve personel çıkışı ya da iş fırsatı "
"gibi diğer uygulamalar ile   tam entegre olarak çalışır."

#. module: calendar
#: model:ir.model.constraint,message:calendar.constraint_calendar_recurrence_month_day
msgid "The day must be between 1 and 31"
msgstr "The day must be between 1 and 31"

#. module: calendar
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid ""
"The ending date and time cannot be earlier than the starting date and time."
msgstr ""
"Bitiş tarihi ve saati, başlangıç tarihi ve saatinden daha önce olamaz."

#. module: calendar
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid "The ending date cannot be earlier than the starting date."
msgstr "Bitiş tarihi, başlangıç tarihinden önce olamaz."

#. module: calendar
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "The interval cannot be negative."
msgstr "Aralık negatif olamaz."

#. module: calendar
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "The number of repetitions cannot be negative."
msgstr "The number of repetitions cannot be negative."

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__byday__3
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__byday__3
msgid "Third"
msgstr "Üçüncü"

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/xml/base_calendar.xml:0
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__recurrence_update__future_events
#, python-format
msgid "This and following events"
msgstr "This and following events"

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/xml/base_calendar.xml:0
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__recurrence_update__self_only
#, python-format
msgid "This event"
msgstr "This event"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__th
msgid "Thu"
msgstr "Per"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__th
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__th
msgid "Thursday"
msgstr "Perşembe"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__event_tz
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__event_tz
msgid "Timezone"
msgstr "Saat Dilimi"

#. module: calendar
#: code:addons/calendar/models/res_users.py:0
#, python-format
msgid "Today's Meetings"
msgstr "Bugünün Toplantıları"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__tu
msgid "Tu"
msgstr "Tu"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__tu
msgid "Tue"
msgstr "Sal"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__tu
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__tu
msgid "Tuesday"
msgstr "Salı"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__alarm_type
msgid "Type"
msgstr "Tür"

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/xml/base_calendar.xml:0
#: model:ir.model.fields.selection,name:calendar.selection__calendar_attendee__state__tentative
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__attendee_status__tentative
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#, python-format
msgid "Uncertain"
msgstr "Belirsiz"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__interval
msgid "Unit"
msgstr "Adet"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_unread
msgid "Unread Messages"
msgstr "Okunmamış Mesajlar"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Okunmamış Mesaj Sayacı"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__until
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Until"
msgstr "Bitiş"

#. module: calendar
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid ""
"Updating All Events is not allowed when dates or time is modified. You can "
"only update one particular event and following events."
msgstr ""
"Updating All Events is not allowed when dates or time is modified. You can "
"only update one particular event and following events."

#. module: calendar
#: model:ir.model,name:calendar.model_res_users
msgid "Users"
msgstr "Kullanıcılar"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__we
msgid "We"
msgstr "We"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__we
msgid "Wed"
msgstr "Çar"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__we
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__we
msgid "Wednesday"
msgstr "Çarşamba"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__weekday
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__weekday
msgid "Weekday"
msgstr "Hafta Günü"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type__weekly
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__rrule_type__weekly
msgid "Weeks"
msgstr "Hafta"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type__yearly
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__rrule_type__yearly
msgid "Years"
msgstr "Yıl"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Yes I'm going."
msgstr "Evet ben gidiyorum"

#. module: calendar
#: code:addons/calendar/models/calendar_attendee.py:0
#, python-format
msgid "You cannot duplicate a calendar attendee."
msgstr "Takvim katılımcısını çoğaltamazsınız."

#. module: calendar
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "You have to choose at least one day in the week"
msgstr "You have to choose at least one day in the week"

#. module: calendar
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "day %s"
msgstr "gün %s"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "e.g. Business Lunch"
msgstr "ör. İş Yemeği"

#. module: calendar
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "for %s events"
msgstr "for %s events"

#. module: calendar
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "on %s"
msgstr "üzerinde %s"

#. module: calendar
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "on the %(position)s %(weekday)s"
msgstr "üzerinde %(position)s %(weekday)s"

#. module: calendar
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "until %s"
msgstr "until %s"
