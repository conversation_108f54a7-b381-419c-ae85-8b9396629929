# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mail_mobile
# 
# Translators:
# <PERSON>, 2020
# <PERSON> <igor.shelud<PERSON>@gmail.com>, 2020
# <PERSON><PERSON><PERSON><PERSON>, 2020
# <PERSON> <<EMAIL>>, 2020
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~13.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-09-01 07:39+0000\n"
"PO-Revision-Date: 2020-09-07 08:21+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2020\n"
"Language-Team: Bulgarian (https://www.transifex.com/odoo/teams/41243/bg/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bg\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: mail_mobile
#: model:ir.model.fields,help:mail_mobile.field_res_config_settings__disable_redirect_firebase_dynamic_link
msgid ""
"Check this if dynamic mobile-app detection links cause problems for your "
"installation. This will stop the automatic wrapping of links inside outbound"
" emails. The links will always open in a normal browser, even for users who "
"have the Android/iOS app installed."
msgstr ""

#. module: mail_mobile
#: model:ir.model,name:mail_mobile.model_res_config_settings
msgid "Config Settings"
msgstr "Настройки конфигурация"

#. module: mail_mobile
#: model:ir.model,name:mail_mobile.model_res_partner
msgid "Contact"
msgstr "Контакт"

#. module: mail_mobile
#: model:ir.model.fields,field_description:mail_mobile.field_res_config_settings__disable_redirect_firebase_dynamic_link
msgid "Disable link redirection to mobile app"
msgstr ""

#. module: mail_mobile
#: model:ir.model.fields,field_description:mail_mobile.field_ir_http__display_name
#: model:ir.model.fields,field_description:mail_mobile.field_mail_thread__display_name
#: model:ir.model.fields,field_description:mail_mobile.field_res_config_settings__display_name
#: model:ir.model.fields,field_description:mail_mobile.field_res_partner__display_name
msgid "Display Name"
msgstr "Име за показване"

#. module: mail_mobile
#: model:ir.model,name:mail_mobile.model_mail_thread
msgid "Email Thread"
msgstr "Имейл поредица"

#. module: mail_mobile
#: model_terms:ir.ui.view,arch_db:mail_mobile.res_config_settings_view_form
msgid "Get notifications on Android and iOS application"
msgstr ""

#. module: mail_mobile
#: model:ir.model,name:mail_mobile.model_ir_http
msgid "HTTP Routing"
msgstr ""

#. module: mail_mobile
#: model:ir.model.fields,field_description:mail_mobile.field_ir_http__id
#: model:ir.model.fields,field_description:mail_mobile.field_mail_thread__id
#: model:ir.model.fields,field_description:mail_mobile.field_res_config_settings__id
#: model:ir.model.fields,field_description:mail_mobile.field_res_partner__id
msgid "ID"
msgstr "ID"

#. module: mail_mobile
#: model_terms:ir.ui.view,arch_db:mail_mobile.res_config_settings_view_form
msgid ""
"If disabled, you won't be able to open external URL's in the Android/iOS "
"mobile app (e.g. \"View Task\" button in email)."
msgstr ""

#. module: mail_mobile
#: model:ir.model.fields,field_description:mail_mobile.field_ir_http____last_update
#: model:ir.model.fields,field_description:mail_mobile.field_mail_thread____last_update
#: model:ir.model.fields,field_description:mail_mobile.field_res_config_settings____last_update
#: model:ir.model.fields,field_description:mail_mobile.field_res_partner____last_update
msgid "Last Modified on"
msgstr "Последно променено на"

#. module: mail_mobile
#: model_terms:ir.ui.view,arch_db:mail_mobile.res_config_settings_view_form
msgid "Mobile"
msgstr "Мобилен"

#. module: mail_mobile
#: model:ir.model.fields,field_description:mail_mobile.field_res_partner__ocn_token
#: model:ir.model.fields,field_description:mail_mobile.field_res_users__ocn_token
msgid "OCN Token"
msgstr ""

#. module: mail_mobile
#: model:ir.model.fields,field_description:mail_mobile.field_res_config_settings__enable_ocn
msgid "Push Notifications"
msgstr ""

#. module: mail_mobile
#: model:ir.model.fields,help:mail_mobile.field_res_partner__ocn_token
#: model:ir.model.fields,help:mail_mobile.field_res_users__ocn_token
msgid "Used for sending notification to registered devices"
msgstr ""
