# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mail_mobile
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2020
# <AUTHOR> <EMAIL>, 2020
# <PERSON><PERSON><PERSON> <osama<PERSON><PERSON><EMAIL>>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~13.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-09-01 07:39+0000\n"
"PO-Revision-Date: 2020-09-07 08:21+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Arabic (https://www.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: mail_mobile
#: model:ir.model.fields,help:mail_mobile.field_res_config_settings__disable_redirect_firebase_dynamic_link
msgid ""
"Check this if dynamic mobile-app detection links cause problems for your "
"installation. This will stop the automatic wrapping of links inside outbound"
" emails. The links will always open in a normal browser, even for users who "
"have the Android/iOS app installed."
msgstr ""
"قم بالتحديد إذا كانت روابط رصد تطبيق الهاتف الديناميكية تتسبب في مشاكل أثناء"
" التثبيت. سوف يوقف ذلك الجمع التلقائي للروابط داخل رسائل البريد الإلكتروني "
"الصادرة. ستفتح الروابط دائماً في المتصفحات العادية، حتى للمستخدمين الذين "
"يستخدمون تطبيق Android/iOS. "

#. module: mail_mobile
#: model:ir.model,name:mail_mobile.model_res_config_settings
msgid "Config Settings"
msgstr "ضبط الاعدادات"

#. module: mail_mobile
#: model:ir.model,name:mail_mobile.model_res_partner
msgid "Contact"
msgstr "جهة الاتصال"

#. module: mail_mobile
#: model:ir.model.fields,field_description:mail_mobile.field_res_config_settings__disable_redirect_firebase_dynamic_link
msgid "Disable link redirection to mobile app"
msgstr "تعطيل إعادة توجيه الرابط إلى تطبيق الهاتف المحمول "

#. module: mail_mobile
#: model:ir.model.fields,field_description:mail_mobile.field_ir_http__display_name
#: model:ir.model.fields,field_description:mail_mobile.field_mail_thread__display_name
#: model:ir.model.fields,field_description:mail_mobile.field_res_config_settings__display_name
#: model:ir.model.fields,field_description:mail_mobile.field_res_partner__display_name
msgid "Display Name"
msgstr "الاسم المعروض"

#. module: mail_mobile
#: model:ir.model,name:mail_mobile.model_mail_thread
msgid "Email Thread"
msgstr "المحادثة البريدية"

#. module: mail_mobile
#: model_terms:ir.ui.view,arch_db:mail_mobile.res_config_settings_view_form
msgid "Get notifications on Android and iOS application"
msgstr "ستصلك الإشعارات على تطبيقنا على أندرويد أو iOS"

#. module: mail_mobile
#: model:ir.model,name:mail_mobile.model_ir_http
msgid "HTTP Routing"
msgstr "مسار HTTP"

#. module: mail_mobile
#: model:ir.model.fields,field_description:mail_mobile.field_ir_http__id
#: model:ir.model.fields,field_description:mail_mobile.field_mail_thread__id
#: model:ir.model.fields,field_description:mail_mobile.field_res_config_settings__id
#: model:ir.model.fields,field_description:mail_mobile.field_res_partner__id
msgid "ID"
msgstr "المُعرف"

#. module: mail_mobile
#: model_terms:ir.ui.view,arch_db:mail_mobile.res_config_settings_view_form
msgid ""
"If disabled, you won't be able to open external URL's in the Android/iOS "
"mobile app (e.g. \"View Task\" button in email)."
msgstr ""
"إذا كان معطلاً، لن تتمكن من فتح روابط URL الخارجية في تطبيق Android/iOS "
"(مثال: رز \"عرض المهمة\" في البريد الإلكتروني). "

#. module: mail_mobile
#: model:ir.model.fields,field_description:mail_mobile.field_ir_http____last_update
#: model:ir.model.fields,field_description:mail_mobile.field_mail_thread____last_update
#: model:ir.model.fields,field_description:mail_mobile.field_res_config_settings____last_update
#: model:ir.model.fields,field_description:mail_mobile.field_res_partner____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: mail_mobile
#: model_terms:ir.ui.view,arch_db:mail_mobile.res_config_settings_view_form
msgid "Mobile"
msgstr "الهاتف المحمول"

#. module: mail_mobile
#: model:ir.model.fields,field_description:mail_mobile.field_res_partner__ocn_token
#: model:ir.model.fields,field_description:mail_mobile.field_res_users__ocn_token
msgid "OCN Token"
msgstr "كلمة سر إشعارات أودو السحابية"

#. module: mail_mobile
#: model:ir.model.fields,field_description:mail_mobile.field_res_config_settings__enable_ocn
msgid "Push Notifications"
msgstr "إظهار إشعارات"

#. module: mail_mobile
#: model:ir.model.fields,help:mail_mobile.field_res_partner__ocn_token
#: model:ir.model.fields,help:mail_mobile.field_res_users__ocn_token
msgid "Used for sending notification to registered devices"
msgstr "يستخدم لإرسال الإشعارات للأجهزة المسجلة"
