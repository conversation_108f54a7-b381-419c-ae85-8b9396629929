# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_edi
# 
# Translators:
# krnkris, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-11-27 14:12+0000\n"
"PO-Revision-Date: 2020-09-07 08:09+0000\n"
"Last-Translator: <PERSON>, 2022\n"
"Language-Team: Hungarian (https://www.transifex.com/odoo/teams/41243/hu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_edi
#: code:addons/account_edi/models/account_move.py:0
#, python-format
msgid "A cancellation of the EDI has been requested."
msgstr ""

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_invoice_send
msgid "Account Invoice Send"
msgstr ""

#. module: account_edi
#: model:ir.model,name:account_edi.model_ir_attachment
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__attachment_id
msgid "Attachment"
msgstr "Melléklet"

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__state__cancelled
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_state__cancelled
msgid "Cancelled"
msgstr "Törölve"

#. module: account_edi
#: code:addons/account_edi/models/account_journal.py:0
#, python-format
msgid ""
"Cannot deactivate (%s) on this journal because not all documents are "
"synchronized"
msgstr ""

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__code
msgid "Code"
msgstr "Kód"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_journal__compatible_edi_ids
msgid "Compatible Edi"
msgstr ""

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__create_uid
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__create_uid
msgid "Created by"
msgstr "Létrehozta"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__create_date
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__create_date
msgid "Created on"
msgstr "Létrehozva"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__display_name
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__display_name
#: model:ir.model.fields,field_description:account_edi.field_account_invoice_send__display_name
#: model:ir.model.fields,field_description:account_edi.field_account_journal__display_name
#: model:ir.model.fields,field_description:account_edi.field_account_move__display_name
#: model:ir.model.fields,field_description:account_edi.field_account_move_line__display_name
#: model:ir.model.fields,field_description:account_edi.field_account_payment__display_name
#: model:ir.model.fields,field_description:account_edi.field_ir_actions_report__display_name
#: model:ir.model.fields,field_description:account_edi.field_ir_attachment__display_name
#: model:ir.model.fields,field_description:account_edi.field_mail_template__display_name
msgid "Display Name"
msgstr "Név megjelenítése"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
#: model_terms:ir.ui.view,arch_db:account_edi.view_payment_form_inherit
msgid "Download"
msgstr "Letöltés"

#. module: account_edi
#: model:ir.actions.server,name:account_edi.ir_cron_edi_network_ir_actions_server
#: model:ir.cron,cron_name:account_edi.ir_cron_edi_network
#: model:ir.cron,name:account_edi.ir_cron_edi_network
msgid "EDI : Perform web services operations"
msgstr ""

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
#: model_terms:ir.ui.view,arch_db:account_edi.view_payment_form_inherit
msgid "EDI Documents"
msgstr ""

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_edi_format
msgid "EDI format"
msgstr ""

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_journal__compatible_edi_ids
msgid "EDI format that support moves in this journal"
msgstr ""

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__edi_content
msgid "Edi Content"
msgstr ""

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_document_ids
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_document_ids
#: model:ir.model.fields,field_description:account_edi.field_account_payment__edi_document_ids
msgid "Edi Document"
msgstr ""

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_error_count
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_error_count
#: model:ir.model.fields,field_description:account_edi.field_account_payment__edi_error_count
msgid "Edi Error Count"
msgstr ""

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__edi_format_id
msgid "Edi Format"
msgstr ""

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_show_cancel_button
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_show_cancel_button
#: model:ir.model.fields,field_description:account_edi.field_account_payment__edi_show_cancel_button
msgid "Edi Show Cancel Button"
msgstr ""

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_web_services_to_process
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_web_services_to_process
#: model:ir.model.fields,field_description:account_edi.field_account_payment__edi_web_services_to_process
msgid "Edi Web Services To Process"
msgstr ""

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_account_journal_form_inherited
msgid "Electronic Data Interchange"
msgstr ""

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_edi_document
msgid "Electronic Document for an account.move"
msgstr ""

#. module: account_edi
#: model:ir.actions.act_window,name:account_edi.action_open_edi_documents
#: model:ir.actions.act_window,name:account_edi.action_open_payment_edi_documents
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_state
#: model:ir.model.fields,field_description:account_edi.field_account_invoice_send__edi_format_ids
#: model:ir.model.fields,field_description:account_edi.field_account_journal__edi_format_ids
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_state
#: model:ir.model.fields,field_description:account_edi.field_account_payment__edi_state
msgid "Electronic invoicing"
msgstr ""

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
#: model_terms:ir.ui.view,arch_db:account_edi.view_payment_form_inherit
msgid "Electronic invoicing error(s)"
msgstr ""

#. module: account_edi
#: model:ir.model,name:account_edi.model_mail_template
msgid "Email Templates"
msgstr "E-mail sablonok"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__error
msgid "Error"
msgstr "Hiba"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__edi_format_name
msgid "Format Name"
msgstr ""

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_bank_statement_line__edi_error_count
#: model:ir.model.fields,help:account_edi.field_account_move__edi_error_count
#: model:ir.model.fields,help:account_edi.field_account_payment__edi_error_count
msgid "How many EDIs are in error for this move ?"
msgstr ""

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__id
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__id
#: model:ir.model.fields,field_description:account_edi.field_account_invoice_send__id
#: model:ir.model.fields,field_description:account_edi.field_account_journal__id
#: model:ir.model.fields,field_description:account_edi.field_account_move__id
#: model:ir.model.fields,field_description:account_edi.field_account_move_line__id
#: model:ir.model.fields,field_description:account_edi.field_account_payment__id
#: model:ir.model.fields,field_description:account_edi.field_ir_actions_report__id
#: model:ir.model.fields,field_description:account_edi.field_ir_attachment__id
#: model:ir.model.fields,field_description:account_edi.field_mail_template__id
msgid "ID"
msgstr "Azonosító"

#. module: account_edi
#: code:addons/account_edi/models/account_move.py:0
#, python-format
msgid ""
"Invalid invoice configuration:\n"
"\n"
"%s"
msgstr ""

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_journal
msgid "Journal"
msgstr "Napló"

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_move
msgid "Journal Entry"
msgstr "Könyvelési tétel"

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_move_line
msgid "Journal Item"
msgstr "Napló elem"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document____last_update
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format____last_update
#: model:ir.model.fields,field_description:account_edi.field_account_invoice_send____last_update
#: model:ir.model.fields,field_description:account_edi.field_account_journal____last_update
#: model:ir.model.fields,field_description:account_edi.field_account_move____last_update
#: model:ir.model.fields,field_description:account_edi.field_account_move_line____last_update
#: model:ir.model.fields,field_description:account_edi.field_account_payment____last_update
#: model:ir.model.fields,field_description:account_edi.field_ir_actions_report____last_update
#: model:ir.model.fields,field_description:account_edi.field_ir_attachment____last_update
#: model:ir.model.fields,field_description:account_edi.field_mail_template____last_update
msgid "Last Modified on"
msgstr "Legutóbb módosítva"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__write_uid
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__write_uid
msgid "Last Updated by"
msgstr "Frissítette"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__write_date
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__write_date
msgid "Last Updated on"
msgstr "Frissítve "

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__move_id
msgid "Move"
msgstr "Mozgás"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__name
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__name
msgid "Name"
msgstr "Név"

#. module: account_edi
#: model:ir.model.constraint,message:account_edi.constraint_account_edi_document_unique_edi_document_by_move_by_format
msgid "Only one edi document by move by format"
msgstr ""

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_payment
msgid "Payments"
msgstr "Vevői befizetések"

#. module: account_edi
#: model:ir.model,name:account_edi.model_ir_actions_report
msgid "Report Action"
msgstr "Kimutatás művelet"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid "Request EDI Cancellation"
msgstr ""

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_invoice_send__edi_format_ids
#: model:ir.model.fields,help:account_edi.field_account_journal__edi_format_ids
msgid "Send XML/EDI invoices"
msgstr ""

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
#: model_terms:ir.ui.view,arch_db:account_edi.view_payment_form_inherit
msgid "Send now"
msgstr "Küldés most"

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__state__sent
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_state__sent
msgid "Sent"
msgstr "Elküldött"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__state
msgid "State"
msgstr "Állapot"

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_bank_statement_line__edi_web_services_to_process
#: model:ir.model.fields,help:account_edi.field_account_move__edi_web_services_to_process
#: model:ir.model.fields,help:account_edi.field_account_payment__edi_web_services_to_process
msgid ""
"Technical field to display the documents that will be processed by the CRON"
msgstr ""

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_bank_statement_line__edi_state
#: model:ir.model.fields,help:account_edi.field_account_move__edi_state
#: model:ir.model.fields,help:account_edi.field_account_payment__edi_state
msgid "The aggregated state of all the EDIs of this move"
msgstr ""

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_edi_document__attachment_id
msgid ""
"The file generated by edi_format_id when the invoice is posted (and this "
"document is processed)."
msgstr ""

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid "The invoice will be sent asynchronously to :"
msgstr ""

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_payment_form_inherit
msgid "The payment will be sent asynchronously to :"
msgstr ""

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_edi_document__error
msgid ""
"The text of the last error that happened during Electronic Invoice "
"operation."
msgstr ""

#. module: account_edi
#: model:ir.model.constraint,message:account_edi.constraint_account_edi_format_unique_code
msgid "This code already exists"
msgstr ""

#. module: account_edi
#: code:addons/account_edi/models/account_edi_document.py:0
#, python-format
msgid "This document is being sent by another process already. "
msgstr ""

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__state__to_cancel
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_state__to_cancel
msgid "To Cancel"
msgstr ""

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__state__to_send
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_state__to_send
msgid "To Send"
msgstr "Elküldendő"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.account_invoice_send_inherit_account_wizard_form
msgid "You can set default electronic invoicing formats on the journal"
msgstr ""

#. module: account_edi
#: code:addons/account_edi/models/account_move.py:0
#, python-format
msgid ""
"You can't edit the following journal entry %s because an electronic document"
" has already been sent. Please use the 'Request EDI Cancellation' button "
"instead."
msgstr ""

#. module: account_edi
#: code:addons/account_edi/models/ir_attachment.py:0
#, python-format
msgid ""
"You can't unlink an attachment being an EDI document sent to the government."
msgstr ""

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
#: model_terms:ir.ui.view,arch_db:account_edi.view_payment_form_inherit
msgid "⇒ See errors"
msgstr ""
