# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mail_enterprise
# 
# Translators:
# <AUTHOR> <EMAIL>, 2020
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-11-27 13:48+0000\n"
"PO-Revision-Date: 2020-09-07 08:21+0000\n"
"Last-Translator: 敬雲 林 <<EMAIL>>, 2020\n"
"Language-Team: Chinese (Taiwan) (https://www.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: mail_enterprise
#: model:ir.model.fields,field_description:mail_enterprise.field_publisher_warranty_contract__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: mail_enterprise
#: model:ir.model.fields,field_description:mail_enterprise.field_publisher_warranty_contract__id
msgid "ID"
msgstr "ID"

#. module: mail_enterprise
#: model:ir.model.fields,field_description:mail_enterprise.field_publisher_warranty_contract____last_update
msgid "Last Modified on"
msgstr "最後修改於"

#. module: mail_enterprise
#: model:ir.model,name:mail_enterprise.model_publisher_warranty_contract
msgid "Publisher Warranty Contract For IoT Box"
msgstr ""

#. module: mail_enterprise
#. openerp-web
#: code:addons/mail_enterprise/static/src/bugfix/bugfix.xml:0
#, python-format
msgid "{ 'o-isInFormSheetBg': props.isInFormSheetBg }"
msgstr ""
