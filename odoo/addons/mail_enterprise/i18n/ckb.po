# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mail_enterprise
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2020
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-11-27 13:48+0000\n"
"PO-Revision-Date: 2020-09-07 08:21+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2020\n"
"Language-Team: Central Kurdish (https://www.transifex.com/odoo/teams/41243/ckb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ckb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: mail_enterprise
#: model:ir.model.fields,field_description:mail_enterprise.field_publisher_warranty_contract__display_name
msgid "Display Name"
msgstr "پیشاندانی ناو"

#. module: mail_enterprise
#: model:ir.model.fields,field_description:mail_enterprise.field_publisher_warranty_contract__id
msgid "ID"
msgstr "ناسنامە"

#. module: mail_enterprise
#: model:ir.model.fields,field_description:mail_enterprise.field_publisher_warranty_contract____last_update
msgid "Last Modified on"
msgstr "دواین دەستکاری لە"

#. module: mail_enterprise
#: model:ir.model,name:mail_enterprise.model_publisher_warranty_contract
msgid "Publisher Warranty Contract For IoT Box"
msgstr "گرێبەستی دڵنیایی بڵاوکەرەوە بۆ سندوقی IoT"

#. module: mail_enterprise
#. openerp-web
#: code:addons/mail_enterprise/static/src/bugfix/bugfix.xml:0
#, python-format
msgid "{ 'o-isInFormSheetBg': props.isInFormSheetBg }"
msgstr ""
