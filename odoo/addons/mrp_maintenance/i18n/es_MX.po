# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_maintenance
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-09-29 14:04+0000\n"
"PO-Revision-Date: 2020-09-07 08:22+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021\n"
"Language-Team: Spanish (Mexico) (https://www.transifex.com/odoo/teams/41243/es_MX/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_MX\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: mrp_maintenance
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.mrp_production_view_form_inherit_maintenance
msgid "<span class=\"o_stat_text\">Maintenance</span>"
msgstr "<span class=\"o_stat_text\">Mantenimiento</span>"

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_maintenance_request__company_id
msgid "Company"
msgstr "Empresa"

#. module: mrp_maintenance
#: model:ir.model.fields,help:mrp_maintenance.field_maintenance_equipment__estimated_next_failure
msgid "Computed as Latest Failure Date + MTBF"
msgstr ""
"Calculado como fecha de ultimo fallo + MTBF (tiempo medio entre fallos)"

#. module: mrp_maintenance
#: model:maintenance.equipment,name:mrp_maintenance.equipment_furniture1
msgid "Crosscut Saw: 8 ppi."
msgstr "Sierra de corte transversal: 8 ppi."

#. module: mrp_maintenance
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.maintenance_request_view_form_inherit_mrp
msgid "Discard"
msgstr "Descartar"

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_maintenance_equipment__display_name
#: model:ir.model.fields,field_description:mrp_maintenance.field_maintenance_request__display_name
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_production__display_name
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_workcenter__display_name
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_workorder__display_name
msgid "Display Name"
msgstr "Nombre en pantalla"

#. module: mrp_maintenance
#: model:maintenance.equipment,name:mrp_maintenance.equipment_furniture3
msgid "Drill Machine"
msgstr "Taladro"

#. module: mrp_maintenance
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.mrp_workcenter_view_form_inherit_maintenance
msgid "Equipment"
msgstr "Equipo"

#. module: mrp_maintenance
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.mrp_workcenter_view_form_inherit_maintenance
msgid "Equipments"
msgstr "Equipos"

#. module: mrp_maintenance
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.mrp_workcenter_view_form_inherit_maintenance
msgid "Est. Next Failure"
msgstr "Próximo fallo estimado"

#. module: mrp_maintenance
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.maintenance_equipment_view_form_inherit_mrp
msgid "Estimated Next Failure"
msgstr "Próximo fallo estimado"

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_maintenance_equipment__estimated_next_failure
msgid "Estimated time before next failure (in days)"
msgstr "Tiempo estimado antes del próximo fallo (en días)"

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_maintenance_equipment__expected_mtbf
msgid "Expected MTBF"
msgstr "Tiempo medio estimado entre fallos "

#. module: mrp_maintenance
#: model:ir.model.fields,help:mrp_maintenance.field_maintenance_equipment__expected_mtbf
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.maintenance_equipment_view_form_inherit_mrp
msgid "Expected Mean Time Between Failure"
msgstr "Tiempo medio estimado entre fallos"

#. module: mrp_maintenance
#: model:maintenance.equipment.category,name:mrp_maintenance.equipment_furniture_tools
msgid "Furniture Tools"
msgstr "Herramientas para muebles"

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_maintenance_equipment__id
#: model:ir.model.fields,field_description:mrp_maintenance.field_maintenance_request__id
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_production__id
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_workcenter__id
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_workorder__id
msgid "ID"
msgstr "ID"

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_maintenance_equipment____last_update
#: model:ir.model.fields,field_description:mrp_maintenance.field_maintenance_request____last_update
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_production____last_update
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_workcenter____last_update
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_workorder____last_update
msgid "Last Modified on"
msgstr "Última modificación el"

#. module: mrp_maintenance
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.maintenance_equipment_view_form_inherit_mrp
msgid "Latest Failure"
msgstr "Último fallo"

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_maintenance_equipment__latest_failure_date
msgid "Latest Failure Date"
msgstr "Fecha del último fallo"

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_maintenance_equipment__mtbf
msgid "MTBF"
msgstr "Tiempo medio entre fallos"

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_maintenance_equipment__mttr
msgid "MTTR"
msgstr "Tiempo medio de reparación"

#. module: mrp_maintenance
#: model:ir.ui.menu,name:mrp_maintenance.menu_equipment_dashboard
msgid "Machines & Tools"
msgstr "Máquinas y herramientas"

#. module: mrp_maintenance
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.mrp_workcenter_view_kanban_inherit_maintenance
msgid "Maintenance"
msgstr "Mantenimiento"

#. module: mrp_maintenance
#: model:ir.model,name:mrp_maintenance.model_maintenance_equipment
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_workcenter__equipment_ids
msgid "Maintenance Equipment"
msgstr "Equipo de mantenimiento"

#. module: mrp_maintenance
#: model:ir.model,name:mrp_maintenance.model_maintenance_request
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.mrp_production_view_form_inherit_maintenance
msgid "Maintenance Request"
msgstr "Solicitud de mantenimiento"

#. module: mrp_maintenance
#: code:addons/mrp_maintenance/models/mrp_maintenance.py:0
#, python-format
msgid "Maintenance Requests"
msgstr "Solicitudes de mantenimiento"

#. module: mrp_maintenance
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.mrp_workorder_tablet_view_form_inherit_maintenance
msgid "Maintenance request"
msgstr "Solicitud de mantenimiento"

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_maintenance_request__production_id
msgid "Manufacturing Order"
msgstr "Orden de fabricación"

#. module: mrp_maintenance
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.maintenance_equipment_view_form_inherit_mrp
msgid "Mean Time Between Failure"
msgstr "Tiempo medio entre fallos"

#. module: mrp_maintenance
#: model:ir.model.fields,help:mrp_maintenance.field_maintenance_equipment__mtbf
msgid ""
"Mean Time Between Failure, computed based on done corrective maintenances."
msgstr ""
"Tiempo medio entre fallos, calculado según los mantenimientos correctivos "
"realizados."

#. module: mrp_maintenance
#: model:ir.model.fields,help:mrp_maintenance.field_maintenance_equipment__mttr
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.maintenance_equipment_view_form_inherit_mrp
msgid "Mean Time To Repair"
msgstr "Tiempo medio de reparación"

#. module: mrp_maintenance
#: code:addons/mrp_maintenance/models/mrp_maintenance.py:0
#: code:addons/mrp_maintenance/models/mrp_maintenance.py:0
#, python-format
msgid "New Maintenance Request"
msgstr "Nueva solicitud de mantenimiento"

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_production__maintenance_count
msgid "Number of maintenance requests"
msgstr "Número de solicitudes de mantenimiento"

#. module: mrp_maintenance
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.maintenance_request_view_search_inherit_mrp
msgid "Operation"
msgstr "Operación"

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_maintenance_request__production_company_id
msgid "Production Company"
msgstr "Empresa de producción"

#. module: mrp_maintenance
#: model:ir.model,name:mrp_maintenance.model_mrp_production
msgid "Production Order"
msgstr "Orden de producción"

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_production__request_ids
msgid "Request"
msgstr "Solicitud"

#. module: mrp_maintenance
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.maintenance_request_view_form_inherit_mrp
msgid "Save"
msgstr "Guardar"

#. module: mrp_maintenance
#: model:maintenance.equipment,name:mrp_maintenance.equipment_furniture2
msgid "Scrub Plane"
msgstr "Cepillo de madera"

#. module: mrp_maintenance
#: model:ir.model,name:mrp_maintenance.model_mrp_workcenter
#: model:ir.model.fields,field_description:mrp_maintenance.field_maintenance_equipment__workcenter_id
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.maintenance_equipment_view_form_inherit_mrp
msgid "Work Center"
msgstr "Centro de trabajo"

#. module: mrp_maintenance
#: model:ir.ui.menu,name:mrp_maintenance.menu_workcenter_tree
msgid "Work Centers"
msgstr "Centros de trabajo"

#. module: mrp_maintenance
#: model:ir.model,name:mrp_maintenance.model_mrp_workorder
#: model:ir.model.fields,field_description:mrp_maintenance.field_maintenance_request__workorder_id
msgid "Work Order"
msgstr "Orden de trabajo"

#. module: mrp_maintenance
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.maintenance_equipment_view_form_inherit_mrp
msgid "days"
msgstr "días"

#. module: mrp_maintenance
#: code:addons/mrp_maintenance/models/mrp_maintenance.py:0
#, python-format
msgid "work centers"
msgstr "centros de trabajo"
