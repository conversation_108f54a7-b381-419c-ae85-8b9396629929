# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment
# 
# Translators:
# <AUTHOR> <EMAIL>, 2020
# <PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON><PERSON><PERSON> <aurel<PERSON>pille<PERSON><EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2020
# C<PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON>, 2020
# <PERSON> <<EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2020
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2020
# <PERSON> Luba <<EMAIL>>, 2020
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2020
# Martin Trigaux, 2020
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# Rémi CAZENAVE, 2021
# RHTodoo, 2021
# <AUTHOR> <EMAIL>, 2022
# Jolien De Paepe, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-11-27 14:12+0000\n"
"PO-Revision-Date: 2020-09-07 08:15+0000\n"
"Last-Translator: Jolien De Paepe, 2022\n"
"Language-Team: French (https://www.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard___data_fetched
msgid " Data Fetched"
msgstr "Données Récupérées"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__invoice_ids_nbr
msgid "# of Invoices"
msgstr "Nb. de factures"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__html_3ds
msgid "3D Secure HTML"
msgstr "3D Secure HTML"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "<b>Amount:</b>"
msgstr "<b>Montant:</b>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_confirmation_status
msgid "<b>Communication: </b>"
msgstr "<b>Communication : </b>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "<b>Reference:</b>"
msgstr "<b>Référence:</b>"

#. module: payment
#: code:addons/payment/wizards/payment_acquirer_onboarding_wizard.py:0
#, python-format
msgid ""
"<h3>Please make a payment to: </h3><ul><li>Bank: %s</li><li>Account Number: "
"%s</li><li>Account Holder: %s</li></ul>"
msgstr ""
"<h3>Veuillez effectuer un paiement à l'adresse suivante:</h3><ul><li> "
"Banque: %s</li><li> Numéro de compte: %s</li><li>Titulaire du compte: "
"%s</li></ul>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "<i class=\"fa fa-arrow-circle-right\"/> Back to My Account"
msgstr "<i class=\"fa fa-arrow-circle-right\"/> Retour à mon compte"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_tokens_list
msgid ""
"<i class=\"fa fa-check text-muted\" title=\"This payment method has not been"
" verified by our system.\" role=\"img\" aria-label=\"Not verified\"/>"
msgstr ""
"<i class=\"fa fa-check text-muted\" title=\"Ce mode de paiement n'a pas été "
"vérifié par notre système.\" role=\"img\" aria-label=\"non vérifié\"/>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_tokens_list
msgid ""
"<i class=\"fa fa-check text-success\" title=\"This payment method is "
"verified by our system.\" role=\"img\" aria-label=\"Ok\"/>"
msgstr ""
"<i class=\"fa fa-close text-danger\" title=\"Ce mode de paiement n'a pas été"
" vérifié par notre système.\"/>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay_methods
msgid "<i class=\"fa fa-home\" role=\"img\" aria-label=\"Home\" title=\"Home\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-calendar\" aria-label=\"Réunions\" role=\"img\" "
"title=\"Réunions\"/>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_tokens_list
msgid "<i class=\"fa fa-lock\"/> Pay"
msgstr "<i class=\"fa fa-lock\"/> Payer"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_tokens_list
msgid "<i class=\"fa fa-plus-circle\"/> Add new card"
msgstr "<i class=\"fa fa-plus-circle\"/>Ajouter une nouvelle carte "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_tokens_list
msgid "<i class=\"fa fa-trash\"/> Delete"
msgstr "<i class=\"fa fa-trash\"/>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_tokens_list
msgid "<small class=\"text-muted\">(Some fees may apply)</small>"
msgstr ""
"<small class=\"text-muted\">(Certaines charges peuvent être "
"appliquées)</small>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_kanban
msgid ""
"<span class=\"badge badge-primary oe_inline "
"o_enterprise_label\">Enterprise</span>"
msgstr ""
"<span class=\"badge badge-primary oe_inline "
"o_enterprise_label\">Entreprise</span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid "<span class=\"fa fa-arrow-right\"> Get my Stripe keys</span>"
msgstr "<span class=\"fa fa-arrow-right\"> Obtenir mes clés Stripe</span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid ""
"<span class=\"fa fa-arrow-right\"> How to configure your PayPal "
"account</span>"
msgstr ""
"<span class=\"fa fa-arrow-right\">Comment configurer votre compte "
"PayPal</span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.view_partners_form_payment_defaultcreditcard
msgid "<span class=\"o_stat_text\">Credit Cards</span>"
msgstr "<span class=\"o_stat_text\">Cartes de Crédit</span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid ""
"<span>Start selling directly without an account; an email will be sent by "
"Paypal to create your new account and collect your payments.</span>"
msgstr ""
"<span>Commencez à vendre directement sans compte; un email sera envoyé par "
"Paypal pour créer votre nouveau compte et encaisser vos paiements.</span>"

#. module: payment
#: code:addons/payment/models/account_invoice.py:0
#, python-format
msgid "A journal must be specified for the acquirer %s."
msgstr "Un journal doit être configuré pour cet intermédiaire %s."

#. module: payment
#: code:addons/payment/models/account_invoice.py:0
#, python-format
msgid "A payment acquirer is required to create a transaction."
msgstr ""
"Un intermédiaire de paiement est nécessaire pour exécuter une transaction"

#. module: payment
#: code:addons/payment/models/account_payment.py:0
#, python-format
msgid "A payment transaction already exists."
msgstr "Une transaction de paiement existe déjà."

#. module: payment
#: code:addons/payment/models/account_payment.py:0
#, python-format
msgid "A token is required to create a new payment transaction."
msgstr "Un token est requis pour créer une nouvelle transaction de paiement."

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#, python-format
msgid "A transaction %s with %s initiated using %s credit card."
msgstr ""
"Une transaction %s avec %s a été initiée utilisant la carte de crédit %s."

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#, python-format
msgid "A transaction %s with %s initiated."
msgstr "Une transaction %s avec %s a été initiée."

#. module: payment
#: code:addons/payment/models/account_invoice.py:0
#, python-format
msgid "A transaction can't be linked to invoices having different currencies."
msgstr ""
"Une transcation ne peut pas être liée à des factures utilisant des devises "
"différences."

#. module: payment
#: code:addons/payment/models/account_invoice.py:0
#, python-format
msgid "A transaction can't be linked to invoices having different partners."
msgstr ""
"Une transaction ne peut pas être liée à des factures ayant des partenaires "
"différents."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__access_token
msgid "Access Token"
msgstr "Clé d'accès"

#. module: payment
#: model:ir.model,name:payment.model_account_chart_template
msgid "Account Chart Template"
msgstr "Modèle de plan comptable"

#. module: payment
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_transfer
msgid "Account Holder:"
msgstr "Titulaire de compte:"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__acc_number
msgid "Account Number"
msgstr "Numéro de compte"

#. module: payment
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_transfer
msgid "Account Number:"
msgstr "Numéro de compte:"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__acquirer_id
#: model_terms:ir.ui.view,arch_db:payment.acquirer_kanban
#: model_terms:ir.ui.view,arch_db:payment.acquirer_search
msgid "Acquirer"
msgstr "L'acquéreur"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__acquirer_id
msgid "Acquirer Account"
msgstr "Compte de l'intermédiaire"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__acquirer_ref
msgid "Acquirer Ref."
msgstr "La référence de l'intermédiaire "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__acquirer_reference
msgid "Acquirer Reference"
msgstr "La référence de l'intermédiaire"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_icon__acquirer_ids
msgid "Acquirers"
msgstr "Acquéreurs"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_icon_form_view
msgid "Acquirers list"
msgstr "Liste des acquéreurs"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_kanban
msgid "Activate"
msgstr "Activer"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__active
msgid "Active"
msgstr "Actif"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__fees_active
msgid "Add Extra Fees"
msgstr "Ajoutez des frais additionnels"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_address
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid "Address"
msgstr "Adresse"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_adyen
msgid "Adyen"
msgstr "Adyen"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_alipay
msgid "Alipay"
msgstr "Alipay"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer__save_token__always
msgid "Always"
msgstr "Toujours"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__amount
#: model:ir.model.fields,field_description:payment.field_payment_transaction__amount
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "Amount"
msgstr "Montant"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__amount_max
msgid "Amount Max"
msgstr "Montant maximum"

#. module: payment
#: code:addons/payment/models/account_journal.py:0
#, python-format
msgid ""
"An acquirer is using this journal. Only bank and cash types are allowed."
msgstr ""
"Un fournisseur utilise ce journal. Seuls les types banque et espèces sont "
"autorisés."

#. module: payment
#: code:addons/payment/controllers/portal.py:0
#, python-format
msgid "An error occured during the processing of this payment"
msgstr "Une erreur est survenue lors du traitement de ce payement "

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:0
#, python-format
msgid "An error occured during the processing of this payment."
msgstr "Une erreur est survenue lors du traitement du payement "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid "Apply"
msgstr "Appliquer"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_token_view_search
msgid "Archived"
msgstr "Archivé"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.account_invoice_view_form_inherit_payment
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid ""
"Are you sure you want to void the authorized transaction? This action can't "
"be undone."
msgstr ""
"Êtes-vous sûr de vouloir annuler la transaction autorisée? Cette action ne "
"peut pas être annulée."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__authorize_implemented
msgid "Authorize Mechanism Supported"
msgstr "Mécanisme d'autorisation pris en charge"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__auth_msg
msgid "Authorize Message"
msgstr "Message autorisé"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_authorize
msgid "Authorize.Net"
msgstr "Authorize.Net"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__authorized
msgid "Authorized"
msgstr "Autorisé"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_bank_statement_line__authorized_transaction_ids
#: model:ir.model.fields,field_description:payment.field_account_move__authorized_transaction_ids
#: model:ir.model.fields,field_description:payment.field_account_payment__authorized_transaction_ids
msgid "Authorized Transactions"
msgstr "Transactions autorisées"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "Availability"
msgstr "Disponibilité"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__journal_name
msgid "Bank Name"
msgstr "Nom de la banque"

#. module: payment
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_transfer
msgid "Bank:"
msgstr "Banque: "

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_buckaroo
msgid "Buckaroo"
msgstr "Buckaroo"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__callback_res_id
msgid "Callback Document ID"
msgstr "ID du document de rappel"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__callback_model_id
msgid "Callback Document Model"
msgstr "Modèle du document de rappel"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__callback_hash
msgid "Callback Hash"
msgstr "Hachage de rappel"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__callback_method
msgid "Callback Method"
msgstr "Méthode de rappel"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:0
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
#, python-format
msgid "Cancel"
msgstr "Annuler"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__cancel_msg
msgid "Cancel Message"
msgstr "Annuler le message"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__cancel
msgid "Canceled"
msgstr "Annulé"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:0
#, python-format
msgid "Cancelled payments"
msgstr "Paiements annulés"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:0
#, python-format
msgid "Cannot setup the payment"
msgstr "Impossible d'installer le paiement."

#. module: payment
#: code:addons/payment/controllers/portal.py:0
#, python-format
msgid "Cannot setup the payment."
msgstr "Impossible d'installer le paiement."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__capture_manually
msgid "Capture Amount Manually"
msgstr "Capturer le montant manuellement"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.account_invoice_view_form_inherit_payment
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid "Capture Transaction"
msgstr "Capturer la transaction"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__capture_manually
msgid "Capture the amount from Odoo, when the delivery is completed."
msgstr "Capturer le montant depuis Odoo une fois l'acheminement effectué."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid "Check here"
msgstr "Vérifier ici"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_open_payment_onboarding_payment_acquirer_wizard
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid "Choose a payment method"
msgstr "Choisissez une méthode de paiement"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.onboarding_payment_acquirer_step
msgid "Choose your default customer payment method."
msgstr "Choisissez votre méthode de paiement client par défaut."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_city
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid "City"
msgstr "Ville"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:0
#, python-format
msgid "Click here to be redirected to the confirmation page."
msgstr "Cliquez ici pour être redirigé vers la page de confirmation."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_confirmation_status
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid "Close"
msgstr "Fermer"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__color
msgid "Color"
msgstr "Couleur"

#. module: payment
#: model:ir.model,name:payment.model_res_company
msgid "Companies"
msgstr "Sociétés"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__company_id
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__company_id
#: model:ir.model.fields,field_description:payment.field_payment_token__company_id
msgid "Company"
msgstr "Société"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "Configuration"
msgstr "Configuration"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_kanban
msgid "Configure"
msgstr "Configurer"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:0
#, python-format
msgid "Confirm Deletion"
msgstr "Confirmer la suppression"

#. module: payment
#: model:ir.model,name:payment.model_res_partner
msgid "Contact"
msgstr "Contact"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__module_id
msgid "Corresponding Module"
msgstr "Module correspondant"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_res_partner__payment_token_count
#: model:ir.model.fields,field_description:payment.field_res_users__payment_token_count
msgid "Count Payment Token"
msgstr "Nombre de méthodes de paiement"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__country_ids
msgid "Countries"
msgstr "Pays"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_country_id
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid "Country"
msgstr "Pays"

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_acquirer
msgid "Create a new payment acquirer"
msgstr "Créez un nouvel intermédiaire de paiement."

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_transaction
msgid "Create a new payment transaction"
msgstr "Créez une nouvelle transaction de paiement"

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_icon
msgid "Create a payment icon"
msgstr "Créez une icône de paiement"

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.payment_token_action
msgid "Create a saved payment data"
msgstr "Créer une donnée de paiement sauvegardée"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_icon__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_token__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_transaction__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__create_date
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__create_date
#: model:ir.model.fields,field_description:payment.field_payment_icon__create_date
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__create_date
#: model:ir.model.fields,field_description:payment.field_payment_token__create_date
#: model:ir.model.fields,field_description:payment.field_payment_transaction__create_date
msgid "Created on"
msgstr "Créé le"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "Credentials"
msgstr "Identifiants"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_adyen
msgid "Credit Card (powered by Adyen)"
msgstr "Carte de crédit (fourni par Adyen)"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_alipay
msgid "Credit Card (powered by Alipay)"
msgstr "Carte de crédit (fourni par Alipay)"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_authorize
msgid "Credit Card (powered by Authorize)"
msgstr "Carte de crédit (fourni par Authorize)"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_buckaroo
msgid "Credit Card (powered by Buckaroo)"
msgstr "Carte de crédit (fourni par Buckaroo)"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_ingenico
msgid "Credit Card (powered by Ingenico)"
msgstr "Carte de crédit (fourni par Ingenico)"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_odoo_by_adyen
msgid "Credit Card (powered by Odoo Payments)"
msgstr "Carte de crédit (généré par Odoo Paiements)"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_payulatam
msgid "Credit Card (powered by PayU Latam)"
msgstr "Carte de crédit (fourni par PayU Latam)"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_payu
msgid "Credit Card (powered by PayUmoney)"
msgstr "Carte de crédit (fourni par PayUmoney)"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_sips
msgid "Credit Card (powered by Sips)"
msgstr "Carte de crédit (fourni par Sips)"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_stripe
msgid "Credit Card (powered by Stripe)"
msgstr "Carte de crédit (fourni par Stripe)"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer_onboarding_wizard__payment_method__stripe
msgid "Credit card (via Stripe)"
msgstr "Carte de crédit (via Stripe)"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__currency_id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__currency_id
msgid "Currency"
msgstr "Devise"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer__provider__manual
msgid "Custom Payment Form"
msgstr "Formulaire de paiement personnalisé"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer_onboarding_wizard__payment_method__manual
msgid "Custom payment instructions"
msgstr "Instructions de paiement"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_id
msgid "Customer"
msgstr "Client"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__description
msgid "Description"
msgstr "Description"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__sequence
msgid "Determine the display order"
msgstr "Détermine l'ordre d'affichage"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer__state__disabled
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "Disabled"
msgstr "Désactivé"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_confirmation_status
msgid "Dismiss"
msgstr "Annuler"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_chart_template__display_name
#: model:ir.model.fields,field_description:payment.field_account_move__display_name
#: model:ir.model.fields,field_description:payment.field_account_payment__display_name
#: model:ir.model.fields,field_description:payment.field_ir_http__display_name
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__display_name
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__display_name
#: model:ir.model.fields,field_description:payment.field_payment_icon__display_name
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__display_name
#: model:ir.model.fields,field_description:payment.field_payment_token__display_name
#: model:ir.model.fields,field_description:payment.field_payment_transaction__display_name
#: model:ir.model.fields,field_description:payment.field_res_company__display_name
#: model:ir.model.fields,field_description:payment.field_res_partner__display_name
msgid "Display Name"
msgstr "Nom affiché"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__display_as
msgid "Displayed as"
msgstr "Affiché comme"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__done
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_acquirer_onboarding_state__done
msgid "Done"
msgstr "Fait"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__done_msg
msgid "Done Message"
msgstr "Message effectué"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__draft
msgid "Draft"
msgstr "Brouillon"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid "E-mail"
msgstr "Email"

#. module: payment
#: model:account.payment.method,name:payment.account_payment_method_electronic_in
msgid "Electronic"
msgstr "Electronique"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__paypal_email_account
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__partner_email
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_email
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid "Email"
msgstr "Email"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__qr_code
msgid "Enable QR Codes"
msgstr "Activer les codes QR"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__qr_code
msgid "Enable the use of QR-codes for payments made on this provider."
msgstr ""
"Activer l'utilisation des codes QR pour les paiements effectués sur ce "
"fournisseur."

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer__state__enabled
msgid "Enabled"
msgstr "Activé"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__error
msgid "Error"
msgstr "Erreur"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:0
#, python-format
msgid "Error: "
msgstr "Erreur :"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_tokens_list
msgid "Fee"
msgstr "Frais"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__fees
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "Fees"
msgstr "Frais"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__fees_implemented
msgid "Fees Computation Supported"
msgstr "Calcul des frais pris en charge"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__fees
msgid "Fees amount; set by the system because depends on the acquirer"
msgstr ""
"Montant des commissions; fixé par le système car dépendant de "
"l'intermédiaire"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__state_message
msgid "Field used to store error and/or validation messages for information"
msgstr ""
"Champ utilisé pour enregistré des erreurs et/ou des messages de validation "
"pour information"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__fees_dom_fixed
msgid "Fixed domestic fees"
msgstr "Frais fixes domestiques"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__fees_int_fixed
msgid "Fixed international fees"
msgstr "Frais fixes internationales "

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__type__form
msgid "Form"
msgstr "Formulaire"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__view_template_id
msgid "Form Button Template"
msgstr "Modèle de bouton de formulaire"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__type__form_save
msgid "Form with tokenization"
msgstr "Formulaire avec tokenisation"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "From"
msgstr "De"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid "Generate Payment Link"
msgstr "Générer un lien de paiement"

#. module: payment
#: model:ir.model,name:payment.model_payment_link_wizard
msgid "Generate Sales Payment Link"
msgstr "Générer le lien de paiement des ventes"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_invoice_order_generate_link
msgid "Generate a Payment Link"
msgstr "Générer un lien de paiement"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_search
msgid "Group By"
msgstr "Regrouper par"

#. module: payment
#: model:ir.model,name:payment.model_ir_http
msgid "HTTP Routing"
msgstr "Routage HTTP"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__is_processed
msgid "Has the payment been post processed"
msgstr "Le paiement a-t-il été post-traité"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__pre_msg
msgid "Help Message"
msgstr "Message d'aide"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__display_as
msgid "How the acquirer is displayed to the customers."
msgstr "Comment l'acquéreur est présenté aux clients."

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer_onboarding_wizard__paypal_user_type__new_user
msgid "I don't have a Paypal account"
msgstr "Je n'ai pas de compte Paypal"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer_onboarding_wizard__paypal_user_type__existing_user
msgid "I have a Paypal account"
msgstr "J'ai un compte Paypal"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_chart_template__id
#: model:ir.model.fields,field_description:payment.field_account_move__id
#: model:ir.model.fields,field_description:payment.field_account_payment__id
#: model:ir.model.fields,field_description:payment.field_ir_http__id
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__id
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__id
#: model:ir.model.fields,field_description:payment.field_payment_icon__id
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__id
#: model:ir.model.fields,field_description:payment.field_payment_token__id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__id
#: model:ir.model.fields,field_description:payment.field_res_company__id
#: model:ir.model.fields,field_description:payment.field_res_partner__id
msgid "ID"
msgstr "ID"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "If not defined, the acquirer name will be used."
msgstr "S'il n'est pas défini, le nom de l'acquéreur sera utilisé."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:0
#, python-format
msgid "If the payment hasn't been confirmed you can contact us."
msgstr "Contactez-nous, si le paiement n'a pas été confirmé."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__image_128
#: model:ir.model.fields,field_description:payment.field_payment_icon__image
msgid "Image"
msgstr "Image"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_icon__image_payment_form
msgid "Image displayed on the payment form"
msgstr "Image affichée dans le formulaire de paiement"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__state
msgid ""
"In test mode, a fake payment is processed through a test\n"
"             payment interface. This mode is advised when setting up the\n"
"             acquirer. Watch out, test and production modes require\n"
"             different credentials."
msgstr ""
"En mode test, un faux paiement est traité via un test\n"
"               interface de paiement. Ce mode est conseillé lors de la configuration du\n"
"             acquéreur. Attention, les modes de test et de production nécessitent\n"
"             pouvoirs différents."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__inbound_payment_method_ids
msgid "Inbound Payment Methods"
msgstr "Méthodes de paiement entrant"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_ingenico
msgid "Ingenico"
msgstr "Ingenico"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
#: model_terms:ir.ui.view,arch_db:payment.acquirer_kanban
msgid "Install"
msgstr "Installer"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__module_state
msgid "Installation State"
msgstr "État de l'installation"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_search
msgid "Installed"
msgstr "Installé"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__reference
msgid "Internal reference of the TX"
msgstr "Référence interne de la transaction"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:0
#, python-format
msgid "Internal server error"
msgstr "Erreur interne du serveur"

#. module: payment
#: code:addons/payment/models/account_invoice.py:0
#, python-format
msgid "Invalid token found! Token acquirer %s != %s"
msgstr "Token trouvé invalid! Acquéreur du token %s != %s"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid "Invoice(s)"
msgstr "Facture(s)"

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#: model:ir.model.fields,field_description:payment.field_payment_transaction__invoice_ids
#, python-format
msgid "Invoices"
msgstr "Factures"

#. module: payment
#: model:ir.model,name:payment.model_account_move
msgid "Journal Entry"
msgstr "Pièce comptable"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__journal_id
msgid "Journal where the successful transactions will be posted"
msgstr "Journal où les transactions réussies seront affichées"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_acquirer_onboarding_state__just_done
msgid "Just done"
msgstr "Fait à l'instant"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_lang
msgid "Language"
msgstr "Langue"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_chart_template____last_update
#: model:ir.model.fields,field_description:payment.field_account_move____last_update
#: model:ir.model.fields,field_description:payment.field_account_payment____last_update
#: model:ir.model.fields,field_description:payment.field_ir_http____last_update
#: model:ir.model.fields,field_description:payment.field_payment_acquirer____last_update
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard____last_update
#: model:ir.model.fields,field_description:payment.field_payment_icon____last_update
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard____last_update
#: model:ir.model.fields,field_description:payment.field_payment_token____last_update
#: model:ir.model.fields,field_description:payment.field_payment_transaction____last_update
#: model:ir.model.fields,field_description:payment.field_res_company____last_update
#: model:ir.model.fields,field_description:payment.field_res_partner____last_update
msgid "Last Modified on"
msgstr "Dernière modification le"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_icon__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_token__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_transaction__write_uid
msgid "Last Updated by"
msgstr "Dernière mise à jour par"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__write_date
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__write_date
#: model:ir.model.fields,field_description:payment.field_payment_icon__write_date
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__write_date
#: model:ir.model.fields,field_description:payment.field_payment_token__write_date
#: model:ir.model.fields,field_description:payment.field_payment_transaction__write_date
msgid "Last Updated on"
msgstr "Dernière mise à jour le"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer__save_token__ask
msgid "Let the customer decide"
msgstr "Laissez le client choisir"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_icon__acquirer_ids
msgid "List of Acquirers supporting this payment icon."
msgstr "Liste des acquéreurs prenant en charge cette icône de paiement."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay_methods
msgid "Manage Payment Methods"
msgstr "Gérer les méthodes de paiement"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay_meth_link
msgid "Manage payment methods"
msgstr "Gérer les moyens de paiement"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_tokens_list
msgid "Manage your payment methods"
msgstr "Gérer vos méthodes de paiements"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__manual
msgid "Manual"
msgstr "Manuel"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__inbound_payment_method_ids
msgid ""
"Manual: Get paid by cash, check or any other method outside of Odoo.\n"
"Electronic: Get paid automatically through a payment acquirer by requesting a transaction on a card saved by the customer when buying or subscribing online (payment token).\n"
"Batch Deposit: Encase several customer checks at once by generating a batch deposit to submit to your bank. When encoding the bank statement in Odoo,you are suggested to reconcile the transaction with the batch deposit. Enable this option from the settings."
msgstr ""
"Paiement manuel : soyez payé en espèces, par chèque ou par tout autre moyen externe à Odoo.\n"
"Paiement électronique : soyez payé automatiquement par un acquéreur en demandant une transaction sur une carte enregistrée par le client lors de l'achat ou de l'inscription en ligne (jeton de paiement).\n"
"Dépôt groupé : encaissez plusieurs chèques clients à la fois en générant un dépôt groupé à envoyer à votre banque. Lors de l'encodage du relevé bancaire dans Odoo, vous pouvez rapprocher la transaction et le dépôt groupé. Activez cette option dans les paramètres."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__paypal_seller_account
msgid "Merchant Account ID"
msgstr "Identifiant du compte marchand"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__state_message
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid "Message"
msgstr "Message"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__auth_msg
msgid "Message displayed if payment is authorized."
msgstr "Message affiché si le paiement est autorisé."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__pre_msg
msgid "Message displayed to explain and help the payment process."
msgstr ""
"Message affiché pour donner des explications et de l'aide lors du processus "
"de paiement."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__cancel_msg
msgid "Message displayed, if order is cancel during the payment process."
msgstr ""
"Message affiché, si la commande est annulée pendant le processus de "
"paiement."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__done_msg
msgid ""
"Message displayed, if order is done successfully after having done the "
"payment process."
msgstr ""
"Message affiché, si la commande est réussie après avoir effectué le "
"processus de paiement."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__pending_msg
msgid ""
"Message displayed, if order is in pending state after having done the "
"payment process."
msgstr ""
"Message affiché, si la commande est en attente après avoir effectué le "
"processus de paiement."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "Messages"
msgstr "Messages"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__manual_name
msgid "Method"
msgstr "Méthode"

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#, python-format
msgid "Missing partner reference when trying to create a new payment token"
msgstr ""
"Référence partenaire manquante lors de la tentative de création d'un jeton "
"de paiement"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__name
#: model:ir.model.fields,field_description:payment.field_payment_icon__name
#: model:ir.model.fields,field_description:payment.field_payment_token__name
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
#: model_terms:ir.ui.view,arch_db:payment.payment_icon_form_view
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid "Name"
msgstr "Nom"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_token__name
msgid "Name of the payment token"
msgstr "Nom du jeton de paiement"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer__save_token__none
msgid "Never"
msgstr "Jamais"

#. module: payment
#: code:addons/payment/wizards/payment_acquirer_onboarding_wizard.py:0
#, python-format
msgid ""
"No manual payment method could be found for this company. Please create one "
"from the Payment Acquirer menu."
msgstr ""
"Aucun mode de paiement manuel n'a pu être trouvé pour cette entreprise. "
"Veuillez en créer un dans le menu Acquéreur de paiement."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "No payment acquirer found."
msgstr "Pas d'intermédiaires de paiement trouvés."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:0
#, python-format
msgid "No payment has been processed."
msgstr "Aucun paiement n'a été traité."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:0
#: code:addons/payment/static/src/js/payment_form.js:0
#, python-format
msgid "No payment method selected"
msgstr "Aucune méthode de paiement sélectionnée"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_acquirer_onboarding_state__not_done
msgid "Not done"
msgstr "Pas fait"

#. module: payment
#: model:ir.model.fields,help:payment.field_account_payment__payment_token_id
msgid ""
"Note that tokens from acquirers set to only authorize transactions (instead "
"of capturing the amount) are not available."
msgstr ""
"Notez que les jetons issus d'acquéreurs pouvant uniquement autoriser les "
"transactions (et non capturer le montant) ne sont pas disponibles."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__payment_flow
msgid ""
"Note: Subscriptions does not take this field in account, it uses server to "
"server by default."
msgstr ""
"Remarque : la fonction Abonnements ne prend pas ce champ en compte. Par "
"défaut, elle utilise l'option serveur à serveur."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__module_to_buy
msgid "Odoo Enterprise Module"
msgstr "Module Enterprise Odoo"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_odoo_by_adyen
msgid "Odoo Payments by Adyen"
msgstr "Paiements Odoo par Adyen"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:0
#, python-format
msgid "Ok"
msgstr "Ok"

#. module: payment
#: code:addons/payment/wizards/payment_acquirer_onboarding_wizard.py:0
#, python-format
msgid "Only administrators can access this data."
msgstr "Seul les administrateurs peuvent accéder à ces données."

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#, python-format
msgid "Only transactions having the authorized status can be captured."
msgstr ""
"Seules les transactions ayant le statut autorisé peuvent être saisies."

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#, python-format
msgid "Only transactions having the capture status can be voided."
msgstr ""
"Seules les transactions ayant le statut de capture peuvent être annulées."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.confirm
#: model_terms:ir.ui.view,arch_db:payment.payment_confirmation_status
msgid "Or scan me with your banking app."
msgstr "Ou scannez-moi avec votre application bancaire."

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__other
msgid "Other"
msgstr "Autre"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer_onboarding_wizard__payment_method__other
msgid "Other payment acquirer"
msgstr "Autre intermédiaire de Paiement"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__paypal_pdt_token
msgid "PDT Identity Token"
msgstr "Jeton d'Identité PDT"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__partner_id
#: model:ir.model.fields,field_description:payment.field_payment_token__partner_id
msgid "Partner"
msgstr "Partenaire"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_name
msgid "Partner Name"
msgstr "Nom du partenaire"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer_onboarding_wizard__payment_method__paypal
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__paypal
msgid "PayPal"
msgstr "PayPal"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_payulatam
msgid "PayU Latam"
msgstr "PayU Latam"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_payu
msgid "PayUmoney"
msgstr "PayUmoney"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__payment_id
#: model_terms:ir.ui.view,arch_db:payment.confirm
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "Payment"
msgstr "Paiement"

#. module: payment
#: model:ir.model,name:payment.model_payment_acquirer
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "Payment Acquirer"
msgstr "Intermédiaire de paiement"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_acquirer
#: model:ir.ui.menu,name:payment.payment_acquirer_menu
#: model_terms:ir.ui.view,arch_db:payment.acquirer_list
msgid "Payment Acquirers"
msgstr "Intermédiaires de paiement"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__payment_flow
msgid "Payment Flow"
msgstr "Flux de règlement"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "Payment Followup"
msgstr "Suivi du Paiement"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "Payment Form"
msgstr "Formulaire de paiement"

#. module: payment
#: model:ir.model,name:payment.model_payment_icon
#: model_terms:ir.ui.view,arch_db:payment.payment_icon_form_view
msgid "Payment Icon"
msgstr "Icône de paiement"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_icon
#: model:ir.ui.menu,name:payment.payment_icon_menu
msgid "Payment Icons"
msgstr "Icônes de paiement"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__manual_post_msg
msgid "Payment Instructions"
msgstr "Instructions de paiement"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__journal_id
msgid "Payment Journal"
msgstr "Journal des paiements"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__link
msgid "Payment Link"
msgstr "Lien de paiement"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__payment_method
#: model_terms:ir.ui.view,arch_db:payment.onboarding_payment_acquirer_step
msgid "Payment Method"
msgstr "Moyen de paiement"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay_methods
msgid "Payment Methods"
msgstr "Méthodes de paiements"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__description
msgid "Payment Ref"
msgstr "Réf. paiement"

#. module: payment
#: model:ir.model,name:payment.model_payment_token
#: model:ir.model.fields,field_description:payment.field_payment_transaction__payment_token_id
msgid "Payment Token"
msgstr "Jeton de paiement"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_res_partner__payment_token_ids
#: model:ir.model.fields,field_description:payment.field_res_users__payment_token_ids
#: model_terms:ir.ui.view,arch_db:payment.payment_token_form_view
#: model_terms:ir.ui.view,arch_db:payment.payment_token_tree_view
#: model_terms:ir.ui.view,arch_db:payment.payment_token_view_search
msgid "Payment Tokens"
msgstr "Tokens de Paiement"

#. module: payment
#: model:ir.model,name:payment.model_payment_transaction
#: model:ir.model.fields,field_description:payment.field_account_payment__payment_transaction_id
msgid "Payment Transaction"
msgstr "Transaction"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_transaction
#: model:ir.actions.act_window,name:payment.action_payment_tx_ids
#: model:ir.model.fields,field_description:payment.field_payment_token__payment_ids
#: model:ir.ui.menu,name:payment.payment_transaction_menu
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
#: model_terms:ir.ui.view,arch_db:payment.transaction_list
msgid "Payment Transactions"
msgstr "Transactions de paiement"

#. module: payment
#: model:ir.model,name:payment.model_payment_acquirer_onboarding_wizard
msgid "Payment acquire onboarding wizard"
msgstr "Assistant d'acquisition de paiement"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer__payment_flow__s2s
msgid "Payment from Odoo"
msgstr "Paiement d'Odoo"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.onboarding_payment_acquirer_step
msgid "Payment method set!"
msgstr "Methode de paiement définie!"

#. module: payment
#: model:ir.model,name:payment.model_account_payment
#: model_terms:ir.ui.view,arch_db:payment.payment_token_form_view
msgid "Payments"
msgstr "Paiements"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:0
#, python-format
msgid "Payments failed"
msgstr "Les paiements ont échoué"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:0
#, python-format
msgid "Payments received"
msgstr "Paiement reçu"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_paypal
msgid "Paypal"
msgstr "Paypal"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__paypal_user_type
msgid "Paypal User Type"
msgstr "Type d'utilisateur Paypal"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__pending
msgid "Pending"
msgstr "En attente"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__pending_msg
msgid "Pending Message"
msgstr "Message en attente"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_phone
msgid "Phone"
msgstr "Téléphone"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "Please configure a payment acquirer."
msgstr "Veuillez configurer un intermédiaire de paiement."

#. module: payment
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_transfer
msgid "Please make a payment to:"
msgstr "Veuillez effectuer un paiement à:"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:0
#, python-format
msgid "Please select a payment method."
msgstr "Veuillez sélectionner une méthode de paiement."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:0
#, python-format
msgid "Please select the option to add a new payment method."
msgstr ""
"Veuillez sélectionner l'option d'ajouter une nouvelle méthode de paiement."

#. module: payment
#: code:addons/payment/wizards/payment_link_wizard.py:0
#, python-format
msgid "Please set an amount smaller than %s."
msgstr "Veuillez définir un montant inférieur à %s."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:0
#, python-format
msgid "Please wait ..."
msgstr "Veuillez patienter..."

#. module: payment
#: model:ir.actions.server,name:payment.cron_post_process_payment_tx_ir_actions_server
#: model:ir.cron,cron_name:payment.cron_post_process_payment_tx
#: model:ir.cron,name:payment.cron_post_process_payment_tx
msgid "Post process payment transactions"
msgstr "Opérations de paiement post-traitement"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "Processed by"
msgstr "Traité par"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__provider
#: model:ir.model.fields,field_description:payment.field_payment_transaction__provider
#: model_terms:ir.ui.view,arch_db:payment.acquirer_search
msgid "Provider"
msgstr "Fournisseur"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:0
#, python-format
msgid "Reason:"
msgstr "Motif:"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer__payment_flow__form
msgid "Redirection to the acquirer website"
msgstr "Redirection vers le site de l'intermédiaire"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__reference
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "Reference"
msgstr "Référence"

#. module: payment
#: model:ir.model.constraint,message:payment.constraint_payment_transaction_reference_uniq
msgid "Reference must be unique!"
msgstr "La référence doit être unique!"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__acquirer_reference
msgid "Reference of the TX as stored in the acquirer database"
msgstr ""
"Référence de transaction tel que stockée dans la base de donnée de "
"l'acquéreur."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__res_id
msgid "Related Document ID"
msgstr "ID du document associé"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__res_model
msgid "Related Document Model"
msgstr "Modèle de document concerné"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment__related_partner_ids
msgid "Related Partner"
msgstr "Partenaire associé"

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#, python-format
msgid "Required fields not filled: %s"
msgstr "Champs obligatoires non renseignés: %s"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__return_url
msgid "Return URL after payment"
msgstr "URL de retour après paiement"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__registration_view_template_id
msgid "S2S Form Template"
msgstr "Modèle de formulaire S2S"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_sepa_direct_debit
msgid "SEPA Direct Debit"
msgstr "Prélèvement automatique SEPA"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__save_token
msgid "Save Cards"
msgstr "Enregistrer les cartes"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_tokens_list
msgid "Save my payment data"
msgstr "Enregistrer mes informations de règlement"

#. module: payment
#: model:ir.actions.act_window,name:payment.payment_token_action
#: model:ir.ui.menu,name:payment.payment_token_menu
msgid "Saved Payment Data"
msgstr "Sauvez les données de paiement"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment__payment_token_id
msgid "Saved payment token"
msgstr "Jeton de paiement enregistré"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__token_implemented
msgid "Saving Card Data supported"
msgstr "Enregistrement des données de la carte pris en charge"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "Select countries. Leave empty to use everywhere."
msgstr "Choisir les pays. Laisser vide pour utiliser partout."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_res_company__payment_onboarding_payment_method
msgid "Selected onboarding payment method"
msgstr "Mode de paiement d'intégration sélectionné"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__sequence
msgid "Sequence"
msgstr "Séquence"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:0
#: code:addons/payment/static/src/js/payment_form.js:0
#: code:addons/payment/static/src/js/payment_form.js:0
#: code:addons/payment/static/src/js/payment_form.js:0
#: code:addons/payment/static/src/js/payment_form.js:0
#: code:addons/payment/static/src/js/payment_form.js:0
#: code:addons/payment/static/src/js/payment_form.js:0
#, python-format
msgid "Server Error"
msgstr "Erreur serveur"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__type__server2server
msgid "Server To Server"
msgstr "Serveur vers serveur"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:0
#, python-format
msgid "Server error"
msgstr "Erreur serveur"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:0
#, python-format
msgid "Server error:"
msgstr "Erreur du serveur:"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.onboarding_payment_acquirer_step
msgid "Set payments"
msgstr "Configurer"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__short_name
msgid "Short name"
msgstr "Nom court"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_sips
msgid "Sips"
msgstr "Sips"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__state
#: model_terms:ir.ui.view,arch_db:payment.acquirer_search
msgid "State"
msgstr "État"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_res_company__payment_acquirer_onboarding_state
msgid "State of the onboarding payment acquirer step"
msgstr "État de l'étape d'acquéreur du paiement d'intégration"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__state
msgid "Status"
msgstr "Statut"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__stripe
#: model:payment.acquirer,name:payment.payment_acquirer_stripe
msgid "Stripe"
msgstr "Stripe"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__stripe_publishable_key
msgid "Stripe Publishable Key"
msgstr "Clé publique Stripe"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__stripe_secret_key
msgid "Stripe Secret Key"
msgstr "Clé secrète Stripe"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__payment_icon_ids
msgid "Supported Payment Icons"
msgstr "Icônes de paiement prises en charge"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__registration_view_template_id
msgid "Template for method registration"
msgstr "Modèle pour une méthode d'enregistrement"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer__state__test
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
#: model_terms:ir.ui.view,arch_db:payment.payment_tokens_list
msgid "Test Mode"
msgstr "Mode test"

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#, python-format
msgid "The %s payment acquirers are not allowed to manual capture mode!"
msgstr ""
"Les acquéreurs de paiement %s ne sont pas autorisés à utiliser la capture "
"manuelle."

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#, python-format
msgid "The customer has selected %s to pay this document."
msgstr "Le client a sélectionné %s pour payer ce document."

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#, python-format
msgid ""
"The transaction %s with %s for %s has been authorized. Waiting for "
"capture..."
msgstr ""
"La transaction %s avec %s pour %s a été autorisée. En attente de capture..."

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#, python-format
msgid ""
"The transaction %s with %s for %s has been cancelled with the following "
"message: %s"
msgstr ""
"La transaction %s avec %s pour %s a été annulée avec le message suivant: %s"

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#, python-format
msgid "The transaction %s with %s for %s has been cancelled."
msgstr "La transaction %s avec %s pour %s a été annulée."

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#, python-format
msgid ""
"The transaction %s with %s for %s has been confirmed. The related payment is"
" posted: %s"
msgstr ""
"La transaction %s avec %s pour %s a été confirmée. Le paiement suivant est "
"posté: %s"

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#, python-format
msgid ""
"The transaction %s with %s for %s has return failed with the following error"
" message: %s"
msgstr ""
"La transaction %s avec %s pour %s a échoué avec le message d'erreur suivant "
": %s"

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#, python-format
msgid "The transaction %s with %s for %s is pending."
msgstr "La transaction %s avec %s pour %s est en attente."

#. module: payment
#: code:addons/payment/models/account_invoice.py:0
#, python-format
msgid ""
"The transaction was aborted because you are not the customer of this "
"invoice. Log in as %s to be able to use this payment method."
msgstr ""
"La transaction a été interrompue, car vous n'êtes pas le client de cette "
"facture. Connectez-vous en tant que %s pour pouvoir utiliser ce mode de "
"paiement."

#. module: payment
#: code:addons/payment/wizards/payment_link_wizard.py:0
#, python-format
msgid "The value of the payment amount must be positive."
msgstr "La valeur du paiement doit être positive."

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#, python-format
msgid ""
"This Transaction was automatically processed & refunded in order to validate"
" a new credit card."
msgstr ""
"Cette transaction a été automatiquement traitée et remboursée afin de "
"valider une nouvelle carte de crédit."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:0
#, python-format
msgid "This card is currently linked to the following records:"
msgstr "Cette carte est actuellement liée aux enregistrements suivants :"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_icon__image
msgid ""
"This field holds the image used for this payment icon, limited to "
"1024x1024px"
msgstr ""
"Ce champ contient l'image utilisée pour cette icône de paiement, limitée à "
"1024 x 1024 px."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__save_token
msgid ""
"This option allows customers to save their credit card as a payment token "
"and to reuse it for a later purchase. If you manage subscriptions (recurring"
" invoicing), you need it to automatically charge the customer when you issue"
" an invoice."
msgstr ""
"Cette option permet aux clients d'enregistrer leur carte de crédit en tant "
"que moyen de paiement et de l'utiliser pour un achat ultérieur. Si vous "
"gérez des abonnements (facturation récurrente), vous en avez besoin pour "
"facturer automatiquement le client lorsque vous émettez une facture."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid ""
"This partner has no email, which may cause issues with some payment "
"acquirers. Setting an email for this partner is advised."
msgstr ""
"Ce partenaire n'a pas d'adresse email, ce qui pourrait causer des erreurs "
"avec certains intermédiaire de paiement. Il est conseillé de définir une "
"adresse email pour ce partenaire."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__country_ids
msgid ""
"This payment gateway is available for selected countries. If none is "
"selected it is available for all countries."
msgstr ""
"Cette passerelle de paiement est uniquement disponible dans les pays "
"sélectionnés. Si aucun pays n'est sélectionné, elle est disponible dans tous"
" les pays."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__view_template_id
msgid ""
"This template renders the acquirer button with all necessary values.\n"
"It is rendered with qWeb with the following evaluation context:\n"
"tx_url: transaction URL to post the form\n"
"acquirer: payment.acquirer browse record\n"
"user: current user browse record\n"
"reference: the transaction reference number\n"
"currency: the transaction currency browse record\n"
"amount: the transaction amount, a float\n"
"partner: the buyer partner browse record, not necessarily set\n"
"partner_values: specific values about the buyer, for example coming from a shipping form\n"
"tx_values: transaction values\n"
"context: the current context dictionary"
msgstr ""
"Ce modèle rend le bouton acquéreur avec toutes les valeurs nécessaires.\n"
"Il est rendu avec qWeb avec le contexte d'évaluation suivant:\n"
"tx_url : URL de transaction pour poster le formulaire\n"
"acquéreur : payment.acquirer parcourir l'enregistrement\n"
"utilisateur : enregistrement de navigation de l'utilisateur actuel\n"
"référence : le numéro de référence de la transaction\n"
"devise : l'enregistrement de navigation des devises de transaction\n"
"montant : le montant de la transaction, un flottant\n"
"partenaire : l'enregistrement de navigation du partenaire acheteur, pas nécessairement défini\n"
"valeurs_partenaire : valeurs spécifiques concernant l'acheteur, provenant par exemple d'un formulaire d'expédition\n"
"tx_values : valeurs de transaction\n"
"contexte : le dictionnaire de contexte actuel"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:0
#, python-format
msgid "This transaction has been cancelled."
msgstr "Cette transaction a été annulée."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_bank_statement_line__transaction_ids
#: model:ir.model.fields,field_description:payment.field_account_move__transaction_ids
#: model:ir.model.fields,field_description:payment.field_account_payment__transaction_ids
msgid "Transactions"
msgstr "Transactions"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__check_validity
msgid ""
"Trigger a transaction of 1 currency unit and its refund to check the validity of new credit cards entered in the customer portal.\n"
"        Without this check, the validity will be verified at the very first transaction."
msgstr ""
"Déclenche une transaction de 1 unité monétaire et son remboursement pour vérifier la validité des nouvelles cartes de crédit saisies sur le portail client.\n"
"Sans cette vérification, la validité sera vérifiée à la toute première transaction."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__type
msgid "Type"
msgstr "Type"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:0
#, python-format
msgid "Unable to contact the Odoo server."
msgstr "Impossible de contacter le serveur Odoo."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
#: model_terms:ir.ui.view,arch_db:payment.acquirer_kanban
msgid "Upgrade"
msgstr "Mettre à jour"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__date
msgid "Validation Date"
msgstr "Date de validation"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__type__validation
msgid "Validation of the bank card"
msgstr "Validation de la carte bancaire"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__fees_dom_var
msgid "Variable domestic fees (in percents)"
msgstr "Frais de port national variables (en pourcentage)"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__fees_int_var
msgid "Variable international fees (in percents)"
msgstr "Frais de port international variables (en pourcentage)"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__verified
msgid "Verified"
msgstr "Vérifiée"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__check_validity
msgid "Verify Card Validity"
msgstr "Vérifier la validité de la carte"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.account_invoice_view_form_inherit_payment
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid "Void Transaction"
msgstr "Annuler la transaction"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:0
#, python-format
msgid "Waiting for payment"
msgstr "En attente de paiement"

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#, python-format
msgid "Waiting for payment confirmation..."
msgstr "En attente de la confirmation de paiement..."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:0
#, python-format
msgid "Warning!"
msgstr "Avertissement!"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:0
#: code:addons/payment/static/src/js/payment_form.js:0
#, python-format
msgid "We are not able to add your payment method at the moment."
msgstr "Nous ne pouvons ajouter votre méthode de paiement pour le moment."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:0
#: code:addons/payment/static/src/js/payment_form.js:0
#, python-format
msgid "We are not able to delete your payment method at the moment."
msgstr ""
"Nous ne sommes pas en mesure de supprimer votre méthode de paiement pour le "
"moment."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:0
#, python-format
msgid "We are not able to find your payment, but don't worry."
msgstr ""
"Nous ne sommes pas en mesure de trouver votre paiement, mais ne vous "
"inquiétez pas."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:0
#: code:addons/payment/static/src/js/payment_form.js:0
#, python-format
msgid "We are not able to redirect you to the payment form."
msgstr "Nous ne pouvons pas vous rediriger vers le formulaire de paiement."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_processing.js:0
#, python-format
msgid "We are processing your payment, please wait ..."
msgstr "Nous traitons votre paiement, veuillez patienter ..."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:0
#, python-format
msgid "We are waiting for the payment acquirer to confirm the payment."
msgstr "Nous attendons que l'acquéreur du paiement confirme le paiement."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:0
#, python-format
msgid "We're unable to process your payment."
msgstr "Impossible de traiter votre paiement."

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_transfer
msgid "Wire Transfer"
msgstr "Virement bancaire"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:0
#, python-format
msgid "You can click here to be redirected to the confirmation page."
msgstr ""
"Vous pouvez cliquer ici pour être redirigé vers la page de confirmation."

#. module: payment
#: code:addons/payment/wizards/payment_acquirer_onboarding_wizard.py:0
#, python-format
msgid "You have to set a journal for your payment acquirer %s."
msgstr "Vous devez définir un journal pour votre acquéreur de paiement %s."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:0
#, python-format
msgid "You should receive an email confirming your payment in a few minutes."
msgstr ""
"Vous devriez recevoir un email confirmant votre paiement dans quelques "
"minutes."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:0
#, python-format
msgid "You will be notified when the payment is confirmed."
msgstr "Vous serez averti lorsque le paiement sera confirmé."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:0
#, python-format
msgid "You will be notified when the payment is fully confirmed."
msgstr "Vous serez averti lorsque le paiement sera entièrement confirmé."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:0
#, python-format
msgid "Your order has been processed."
msgstr "Votre commande a été traitée."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:0
#, python-format
msgid "Your order is being processed, please wait ..."
msgstr "Votre commande est en cours de traitement, veuillez patienter."

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_adyen
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_alipay
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_authorize
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_buckaroo
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_ingenico
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_odoo_by_adyen
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_paypal
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_payu
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_payulatam
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_sepa_direct_debit
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_sips
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_stripe
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_transfer
#, python-format
msgid "Your payment has been authorized."
msgstr "Votre paiement a été autorisé."

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_adyen
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_alipay
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_authorize
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_buckaroo
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_ingenico
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_odoo_by_adyen
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_paypal
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_payu
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_payulatam
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_sepa_direct_debit
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_sips
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_stripe
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_transfer
#, python-format
msgid "Your payment has been cancelled."
msgstr "Votre paiement a été annulé."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:0
#, python-format
msgid "Your payment has been received but need to be confirmed manually."
msgstr ""
"Votre payement a été reçu mais doit encore être confirmée manuellement."

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_adyen
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_alipay
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_authorize
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_buckaroo
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_ingenico
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_odoo_by_adyen
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_paypal
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_payu
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_payulatam
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_sepa_direct_debit
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_sips
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_stripe
#, python-format
msgid ""
"Your payment has been successfully processed but is waiting for approval."
msgstr ""
"Votre paiement a été traité avec succès mais est en attente de validation."

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_adyen
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_alipay
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_authorize
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_buckaroo
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_ingenico
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_odoo_by_adyen
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_paypal
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_payu
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_payulatam
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_sepa_direct_debit
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_sips
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_stripe
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_transfer
#, python-format
msgid "Your payment has been successfully processed. Thank you!"
msgstr "Votre paiement a été traité avec succès. Merci!"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:0
#, python-format
msgid "Your payment is in pending state."
msgstr "Votre payement est en attente"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid "ZIP"
msgstr "Code postal"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_zip
msgid "Zip"
msgstr "Code postal"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_tokens_list
msgid "and more"
msgstr "et plus"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:0
#: code:addons/payment/static/src/js/payment_form.js:0
#, python-format
msgid "e.g. Your credit card details are wrong. Please verify."
msgstr ""
"e.g. Les détails de votre carte de crédit sont faux. Veuillez vérifier."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid "to choose another payment method."
msgstr "pour choisir une autre méthode de paiement."
