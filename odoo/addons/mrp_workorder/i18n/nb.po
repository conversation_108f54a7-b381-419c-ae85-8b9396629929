# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_workorder
# 
# Translators:
# <PERSON>, 2020
# <PERSON><PERSON><PERSON> <PERSON><PERSON>, 2020
# <PERSON> <<EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-11-27 13:47+0000\n"
"PO-Revision-Date: 2020-09-07 08:22+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2022\n"
"Language-Team: <PERSON>k<PERSON>l (https://www.transifex.com/odoo/teams/41243/nb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid ""
"<i class=\"fa fa-clock-o\" attrs=\"{'invisible': [('is_user_working', '=', False)]}\" title=\"User is not working\"/>\n"
"                                <i class=\"fa fa-clock-o text-warning\" attrs=\"{'invisible': [('is_user_working', '=', True)]}\" title=\"User is working\"/>"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.res_config_settings_view_form
msgid "<i class=\"fa fa-print\"/> Print barcode commands"
msgstr ""

#. module: mrp_workorder
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
#: model_terms:ir.ui.view,arch_db:mrp_workorder.view_mrp_workorder_additional_product_wizard
#, python-format
msgid "Add By-Product"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet_menu
msgid "Add By-product"
msgstr ""

#. module: mrp_workorder
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet_menu
#: model_terms:ir.ui.view,arch_db:mrp_workorder.view_mrp_workorder_additional_product_wizard
#, python-format
msgid "Add Component"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.view_mrp_workorder_additional_product_wizard
msgid "Add product"
msgstr ""

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_mrp_workorder_additional_product
msgid "Additional Product"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__allow_producing_quantity_change
msgid "Allow Changes to Producing Quantity"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point_test_type__allow_registration
msgid "Allow Registration"
msgstr "Tillat registrering"

#. module: mrp_workorder
#. openerp-web
#: code:addons/mrp_workorder/static/src/xml/mrp_workorder_barcode.xml:0
#, python-format
msgid "And barcode"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.view_routing_steps_search
msgid "Archived"
msgstr "Arkivert"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_production_tree_view_planning
msgid "Availability"
msgstr "Lagerstatus"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder___barcode_scanned
msgid "Barcode Scanned"
msgstr "Barcode Skannet"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__bom_id
msgid "Bill of Material"
msgstr "Stykkliste"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet_menu
msgid "Block"
msgstr "Blokker"

#. module: mrp_workorder
#: model:ir.model.fields.selection,name:mrp_workorder.selection__mrp_workorder_additional_product__type__byproduct
msgid "By-Product"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "By-product"
msgstr "Biprodukt"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "CONTINUE CONSUMPTION"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "CONTINUE PRODUCTION"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder_additional_product__product_uom_category_id
msgid "Category"
msgstr "Kategori"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_change_production_qty
msgid "Change Production Qty"
msgstr "Endre produksjons kvantum"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__check_ids
#: model:ir.model.fields,field_description:mrp_workorder.field_stock_move_line__quality_check_ids
msgid "Check"
msgstr "Kontroll"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production__check_ids
msgid "Checks"
msgstr "Kontroller"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder_additional_product__company_id
msgid "Company"
msgstr "Firma"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__component_id
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__component_id
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__component_ids
#: model:ir.model.fields.selection,name:mrp_workorder.selection__mrp_workorder_additional_product__type__component
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "Component"
msgstr "Komponent"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__lot_id
msgid "Component Lot/Serial"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__component_qty_to_do
msgid "Component Qty To Do"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__component_uom_id
msgid "Component UoM"
msgstr ""

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurasjonsinnstillinger"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "Continue"
msgstr "Fortsette"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__control_date
msgid "Control Date"
msgstr "Kontrolldato"

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_mrp_workorder_additional_product__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"Konvertering mellom enheter fungerer bare når de tilhører samme kategori. "
"Konverteringen gjøres basert på forholdet mellom enhetene."

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder_additional_product__create_uid
msgid "Created by"
msgstr "Opprettet av"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder_additional_product__create_date
msgid "Created on"
msgstr "Opprettet"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "Creates a new serial/lot number"
msgstr "Opprett et nytt serie/lot nummer"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__current_quality_check_id
msgid "Current Quality Check"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_inherit_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "Date"
msgstr "Dato"

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_quality_point__test_type_id
msgid "Defines the type of the quality control point."
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.view_mrp_workorder_additional_product_wizard
msgid "Discard"
msgstr "Forkast"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_change_production_qty__display_name
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production__display_name
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_routing_workcenter__display_name
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workcenter__display_name
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__display_name
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder_additional_product__display_name
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_alert__display_name
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__display_name
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__display_name
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point_test_type__display_name
#: model:ir.model.fields,field_description:mrp_workorder.field_res_config_settings__display_name
#: model:ir.model.fields,field_description:mrp_workorder.field_stock_move__display_name
#: model:ir.model.fields,field_description:mrp_workorder.field_stock_move_line__display_name
msgid "Display Name"
msgstr "Visningsnavn"

#. module: mrp_workorder
#: model:ir.model.fields.selection,name:mrp_workorder.selection__quality_point__worksheet__noupdate
msgid "Do not update page"
msgstr "Ikke oppdater siden"

#. module: mrp_workorder
#: code:addons/mrp_workorder/models/quality.py:0
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__qty_done
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__qty_done
#, python-format
msgid "Done"
msgstr "Fullført"

#. module: mrp_workorder
#. openerp-web
#: code:addons/mrp_workorder/static/src/xml/mrp_workorder_barcode.xml:0
#: code:addons/mrp_workorder/static/src/xml/mrp_workorder_barcode.xml:0
#, python-format
msgid "Download"
msgstr "Last ned"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "Dropdown menu"
msgstr "Nedtrekksmeny"

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_mrp_workorder__component_tracking
#: model:ir.model.fields,help:mrp_workorder.field_mrp_workorder_additional_product__product_tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr "Sikre sporbarhet for produkter i ditt lager."

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "Finish steps"
msgstr "Fullfør steg"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__finished_lot_id
msgid "Finished Lot/Serial"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_inherit_workorder
msgid "Finished Lot/Serial Number"
msgstr "Lot/serienummer for ferdig produkt"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__finished_product_check_ids
msgid "Finished Product Check"
msgstr "Fullført produktsjekk"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__finished_product_sequence
msgid "Finished Product Sequence Number"
msgstr "Ferdig produktsekvensnummer"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_inherit_workorder
msgid "Finished Steps"
msgstr "Fullførte steg"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_change_production_qty__id
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production__id
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_routing_workcenter__id
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workcenter__id
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__id
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder_additional_product__id
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_alert__id
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__id
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__id
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point_test_type__id
#: model:ir.model.fields,field_description:mrp_workorder.field_res_config_settings__id
#: model:ir.model.fields,field_description:mrp_workorder.field_stock_move__id
#: model:ir.model.fields,field_description:mrp_workorder.field_stock_move_line__id
msgid "ID"
msgstr "ID"

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_mrp_workorder__move_line_ids
msgid ""
"Inventory moves for which you must scan a lot number at this work order"
msgstr "Lagerpostering der du må scanne et lot nummer på denne arbeidssordren"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__component_tracking
msgid "Is Component Tracked"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__is_first_step
msgid "Is First Step"
msgstr "Er første steg"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__is_last_step
msgid "Is Last Step"
msgstr "Er siste steg"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__is_last_unfinished_wo
msgid "Is Last Work Order To Process"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__is_last_lot
msgid "Is Last lot"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__is_first_started_wo
msgid "Is The first Work Order"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__is_workorder_step
msgid "Is Workorder Step"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_change_production_qty____last_update
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production____last_update
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_routing_workcenter____last_update
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workcenter____last_update
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder____last_update
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder_additional_product____last_update
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_alert____last_update
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check____last_update
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point____last_update
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point_test_type____last_update
#: model:ir.model.fields,field_description:mrp_workorder.field_res_config_settings____last_update
#: model:ir.model.fields,field_description:mrp_workorder.field_stock_move____last_update
#: model:ir.model.fields,field_description:mrp_workorder.field_stock_move_line____last_update
msgid "Last Modified on"
msgstr "Sist endret"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder_additional_product__write_uid
msgid "Last Updated by"
msgstr "Sist oppdatert av"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder_additional_product__write_date
msgid "Last Updated on"
msgstr "Sist oppdatert"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "Lot Number"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "Lot Number:"
msgstr ""

#. module: mrp_workorder
#: model:res.groups,name:mrp_workorder.group_mrp_wo_tablet_timer
msgid "Manage Work Order timer on Tablet View"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_production_tree_view_planning
msgid "Manufacturing Orders"
msgstr "Produksjonsordre"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "Mark as Done"
msgstr "Marker som fullført"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "Mark as Done and Close MO"
msgstr ""

#. module: mrp_workorder
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
#, python-format
msgid "Menu"
msgstr "Meny"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__move_line_ids
msgid "Moves to Track"
msgstr "Forflytninger å spore"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "Next"
msgstr "Neste"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__next_check_id
msgid "Next Check"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.actions.act_window,help:mrp_workorder.action_mrp_workorder_show_steps
msgid "No manufacturing steps defined yet!"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.actions.act_window,help:mrp_workorder.mrp_workorder_action_tablet
msgid "No work orders to do!"
msgstr "Ingen arbeidsordre å utføre"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__note
msgid "Note"
msgstr "Notat"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_alert__workorder_id
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__workorder_id
msgid "Operation"
msgstr "Operasjon"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_inherit_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "Operator"
msgstr "Operatør"

#. module: mrp_workorder
#: model:ir.ui.menu,name:mrp_workorder.menu_mrp_dashboard
msgid "Overview"
msgstr "Oversikt"

#. module: mrp_workorder
#: model:ir.model.fields.selection,name:mrp_workorder.selection__quality_point__test_report_type__pdf
msgid "PDF"
msgstr "PDF"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "Pause"
msgstr "Pause"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__picture
msgid "Picture"
msgstr "Bilde"

#. module: mrp_workorder
#: model:ir.ui.menu,name:mrp_workorder.menu_mrp_workorder_production
msgid "Planning by Production"
msgstr ""

#. module: mrp_workorder
#: model:ir.ui.menu,name:mrp_workorder.menu_mrp_workorder_workcenter
msgid "Planning by Workcenter"
msgstr ""

#. module: mrp_workorder
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
#, python-format
msgid "Please ensure the quantity to produce is greater than 0."
msgstr ""

#. module: mrp_workorder
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
#, python-format
msgid "Please enter a Lot/SN."
msgstr ""

#. module: mrp_workorder
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
#, python-format
msgid "Please enter a positive quantity."
msgstr "Vennligst legg inn et positivt antall."

#. module: mrp_workorder
#: code:addons/mrp_workorder/models/mrp_production.py:0
#, python-format
msgid ""
"Please go in the Operations tab and perform the following work orders and "
"their quality checks:\n"
msgstr ""

#. module: mrp_workorder
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
#, python-format
msgid ""
"Please set the quantity you are currently producing. It should be different "
"from zero."
msgstr ""

#. module: mrp_workorder
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
#, python-format
msgid "Please upload a picture."
msgstr "Last opp et bilde."

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "Previous"
msgstr "Tilbake"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__previous_check_id
msgid "Previous Check"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "Print Labels"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder_additional_product__product_id
msgid "Product"
msgstr "Produkt"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__component_id
msgid "Product To Register"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder_additional_product__product_uom_id
msgid "Product Uom"
msgstr ""

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_mrp_production
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_alert__production_id
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__production_id
msgid "Production Order"
msgstr "Produksjonsordre"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "Production Workcenter"
msgstr ""

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_quality_alert
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__quality_alert_ids
msgid "Quality Alert"
msgstr "Kvalitetsvarsel"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__quality_alert_count
msgid "Quality Alert Count"
msgstr "Antall kvalitetsvarsler"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_quality_check
msgid "Quality Check"
msgstr "Kvalitetskontroll"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__quality_check_fail
msgid "Quality Check Fail"
msgstr "Kvalitetskontroll ikke bestått"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__quality_check_todo
msgid "Quality Check Todo"
msgstr "Kvalitetskontroll å utføre"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_quality_point
msgid "Quality Control Point"
msgstr "Kvalitetskontrollpunkt"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_quality_point_test_type
msgid "Quality Control Test Type"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_routing_workcenter__quality_point_ids
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__quality_point_ids
msgid "Quality Point"
msgstr "Kvalitetspunkt"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.view_routing_steps_search
msgid "Quality Point Steps"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__quality_state
msgid "Quality State"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder_additional_product__product_qty
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_production_tree_view_planning
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
#: model_terms:ir.ui.view,arch_db:mrp_workorder.view_mrp_workorder_additional_product_wizard
msgid "Quantity"
msgstr "Antall"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "Record production"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__additional
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__additional
msgid "Register additional product"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__component_remaining_qty
msgid "Remaining Quantity for Component"
msgstr "Gjenstående antall for komponent"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__test_report_type
msgid "Report Type"
msgstr "Rapport-type"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__user_id
msgid "Responsible"
msgstr "Ansvarlig"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__result
msgid "Result"
msgstr "Resultat"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet_menu
msgid "Scrap"
msgstr "Vrak"

#. module: mrp_workorder
#: model:ir.model.fields.selection,name:mrp_workorder.selection__quality_point__worksheet__scroll
msgid "Scroll to specific page"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "Serial Number"
msgstr "Serienummer"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "Serial Number:"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.res_config_settings_view_form
msgid "Show the timer on the work order screen"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "Skip"
msgstr "Hopp over"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__skip_completed_checks
msgid "Skip Completed Checks"
msgstr "Hopp over fullførte kontroller"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__skipped_check_ids
msgid "Skipped Check"
msgstr "Hoppet over kontroll"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "Status"
msgstr "Status"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__quality_state_for_summary
msgid "Status Summary"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__operation_id
msgid "Step"
msgstr "Steg"

#. module: mrp_workorder
#: model:ir.actions.act_window,name:mrp_workorder.action_mrp_workorder_show_steps
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_routing_workcenter__quality_point_count
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__quality_point_count
msgid "Steps"
msgstr "Steg"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_stock_move
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__move_id
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__move_id
msgid "Stock Move"
msgstr "Lagerbevegelse"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__move_line_id
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__move_line_id
msgid "Stock Move Line"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__test_type
msgid "Technical name"
msgstr "Teknisk navn"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__test_type_id
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__test_type_id
msgid "Test Type"
msgstr "Testtype"

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_quality_point__bom_id
msgid "The Bill of Material this operation is linked to"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_res_config_settings__group_mrp_wo_tablet_timer
msgid "Timer"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__title
msgid "Title"
msgstr "Tittel"

#. module: mrp_workorder
#: code:addons/mrp_workorder/models/quality.py:0
#, python-format
msgid "To Do"
msgstr "Å gjøre"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_production_tree_view_planning
msgid "Total Qty"
msgstr "Totalt antall"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder_additional_product__product_tracking
msgid "Tracking"
msgstr "Sporing"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder_additional_product__type
msgid "Type"
msgstr "Type"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet_menu
msgid "Unblock"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__component_uom_id
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_production_tree_view_planning
msgid "Unit of Measure"
msgstr "Enhet"

#. module: mrp_workorder
#: model:ir.actions.server,name:mrp_workorder.production_order_unplan_server_action
msgid "Unplan orders"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.actions.act_window,help:mrp_workorder.action_mrp_workorder_show_steps
msgid ""
"Use steps to show instructions on a worksheet to operators, or trigger "
"quality checks at specific steps of the work order."
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.actions.act_window,help:mrp_workorder.mrp_workorder_action_tablet
msgid ""
"Use the table work center control panel to register operations in the shop floor directly.\n"
"            The tablet provides worksheets for your workers and allow them to scrap products, track time,\n"
"            launch a maintenance request, perform quality tests, etc."
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "VALIDATE"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_mrp_workorder___barcode_scanned
msgid "Value of the last barcode scanned."
msgstr "Verdien fra siste avleste strekkode."

#. module: mrp_workorder
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
#, python-format
msgid "Warning"
msgstr "Advarsel"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_mrp_workcenter
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_alert__workcenter_id
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__workcenter_id
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_alert_view_search_inherit_mrp_workorder
msgid "Work Center"
msgstr "Arbeidssenter"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_mrp_routing_workcenter
msgid "Work Center Usage"
msgstr "Arbeidssenter bruk"

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_mrp_workorder
msgid "Work Order"
msgstr "Arbeidsordre"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_point_view_form_inherit_mrp
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_point_view_tree
msgid "Work Order Operation"
msgstr ""

#. module: mrp_workorder
#: model:ir.actions.act_window,name:mrp_workorder.mrp_workorder_action_tablet
#: model:ir.ui.menu,name:mrp_workorder.mrp_workorder_menu_planning
msgid "Work Orders"
msgstr "Arbeidsordrer"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workcenter_view_kanban_inherit_workorder
msgid "Work orders"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.actions.act_window,help:mrp_workorder.mrp_workorder_action_tablet
msgid ""
"Work orders are operations to do as part of a manufacturing order.\n"
"            Operations are defined in the bill of materials or added in the manufacturing order directly."
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder_additional_product__workorder_id
msgid "Workorder"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet_menu
msgid "Workorder Actions"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__worksheet
msgid "Worksheet"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__worksheet_page
msgid "Worksheet Page"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__worksheet_page
msgid "Worksheet page"
msgstr ""

#. module: mrp_workorder
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
#, python-format
msgid ""
"You are using components from another lot. \n"
"Please validate the components from the first lot before using another lot."
msgstr ""

#. module: mrp_workorder
#: code:addons/mrp_workorder/wizard/change_production_qty.py:0
#, python-format
msgid ""
"You cannot update the quantity to do of an ongoing manufacturing order for "
"which quality checks have been performed."
msgstr ""

#. module: mrp_workorder
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
#, python-format
msgid "You did not set a lot/serial number for the final product"
msgstr ""

#. module: mrp_workorder
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
#, python-format
msgid "You should provide a lot/serial number for the final product"
msgstr ""

#. module: mrp_workorder
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
#, python-format
msgid "You still need to do the quality checks!"
msgstr "Du må fortsatt utføre kvalitetskontrollene!"

#. module: mrp_workorder
#: model:ir.model.fields.selection,name:mrp_workorder.selection__quality_point__test_report_type__zpl
msgid "ZPL"
msgstr "ZPL"

#. module: mrp_workorder
#. openerp-web
#: code:addons/mrp_workorder/static/src/xml/mrp_workorder_barcode.xml:0
#, python-format
msgid "commands for Manufacturing"
msgstr ""
