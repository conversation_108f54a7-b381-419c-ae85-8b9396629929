# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* fetchmail
# 
# Translators:
# <PERSON>, 2020
# <PERSON> <olujic.vlad<PERSON><EMAIL>>, 2020
# <PERSON><PERSON><PERSON> <i<PERSON><PERSON>.<PERSON><PERSON>@storm.hr>, 2020
# <PERSON><PERSON><PERSON> <karol<PERSON>.<EMAIL>>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~13.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-09-01 07:29+0000\n"
"PO-Revision-Date: 2020-09-07 08:12+0000\n"
"Last-Translator: <PERSON><PERSON>, 2022\n"
"Language-Team: Croatian (https://www.transifex.com/odoo/teams/41243/hr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hr\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Actions to Perform on Incoming Mails"
msgstr "Akcije za izvoditi na dolaznim porukama"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__active
msgid "Active"
msgstr "Aktivan"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Advanced"
msgstr "Napredno"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Advanced Options"
msgstr "Napredne mogućnosti"

#. module: fetchmail
#: code:addons/fetchmail/models/fetchmail.py:0
#, python-format
msgid ""
"An SSL exception occurred. Check SSL/TLS configuration on server port.\n"
" %s"
msgstr ""

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_search
msgid "Archived"
msgstr "Arhivirano"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__configuration
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Configuration"
msgstr "Postava"

#. module: fetchmail
#: model:ir.model.fields.selection,name:fetchmail.selection__fetchmail_server__state__done
msgid "Confirmed"
msgstr "Potvrđeno"

#. module: fetchmail
#: code:addons/fetchmail/models/fetchmail.py:0
#, python-format
msgid "Connection test failed: %s"
msgstr "Greška pri testiranju povezivanja: %s"

#. module: fetchmail
#: model:ir.model.fields,help:fetchmail.field_fetchmail_server__is_ssl
msgid ""
"Connections are encrypted with SSL/TLS through a dedicated port (default: "
"IMAPS=993, POP3S=995)"
msgstr ""
"Konekcije su enkriptirane sa SSL/TLS kroz dedicirani port (default: "
"IMAPS=993, POP3S=995)"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__object_id
msgid "Create a New Record"
msgstr "Kreiraj novi zapis"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: fetchmail
#: model:ir.model.fields,help:fetchmail.field_fetchmail_server__priority
msgid "Defines the order of processing, lower values mean higher priority"
msgstr ""
"Definira redoslijed procesiranja, niže vrijednosti podrazumijevaju veći "
"prioritet"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__display_name
#: model:ir.model.fields,field_description:fetchmail.field_mail_mail__display_name
msgid "Display Name"
msgstr "Naziv"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_tree
msgid "Email Count"
msgstr "Broj poruka"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Fetch Now"
msgstr "Dohvati poruke"

#. module: fetchmail
#: model:ir.model.fields,help:fetchmail.field_fetchmail_server__server
msgid "Hostname or IP of the mail server"
msgstr "Ime ili IP adresa mail poslužitelja"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__id
#: model:ir.model.fields,field_description:fetchmail.field_mail_mail__id
msgid "ID"
msgstr "ID"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_search
msgid "IMAP"
msgstr "IMAP"

#. module: fetchmail
#: model:ir.model.fields.selection,name:fetchmail.selection__fetchmail_server__server_type__imap
msgid "IMAP Server"
msgstr "IMAP poslužitelj"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_search
msgid "If SSL required."
msgstr "Ako je SSL obavezan"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_mail_mail__fetchmail_server_id
msgid "Inbound Mail Server"
msgstr "Poslužitelj dolaznih mail poruka"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.res_config_settings_view_form
msgid "Incoming Email Servers"
msgstr ""

#. module: fetchmail
#: model:ir.model,name:fetchmail.model_fetchmail_server
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_search
msgid "Incoming Mail Server"
msgstr "Poslužitelj dolaznih poruka"

#. module: fetchmail
#: model:ir.actions.act_window,name:fetchmail.action_email_server_tree
#: model:ir.ui.menu,name:fetchmail.menu_action_fetchmail_server_tree
msgid "Incoming Mail Servers"
msgstr "Poslužitelj dolazne pošte"

#. module: fetchmail
#: code:addons/fetchmail/models/fetchmail.py:0
#, python-format
msgid ""
"Invalid server name !\n"
" %s"
msgstr ""
"Naziv poslužitelja nije valjan !\n"
" %s"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__attach
msgid "Keep Attachments"
msgstr "Zadrži privitke"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__original
msgid "Keep Original"
msgstr "Zadrži original"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__date
msgid "Last Fetch Date"
msgstr "Zadnji datum ažuriranja poruka"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server____last_update
#: model:ir.model.fields,field_description:fetchmail.field_mail_mail____last_update
msgid "Last Modified on"
msgstr "Zadnja promjena"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__write_uid
msgid "Last Updated by"
msgstr "Promijenio"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__write_date
msgid "Last Updated on"
msgstr "Vrijeme promjene"

#. module: fetchmail
#: model:ir.model.fields.selection,name:fetchmail.selection__fetchmail_server__server_type__local
msgid "Local Server"
msgstr "Lokalni poslužitelj"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Login Information"
msgstr "Podaci o prijavi"

#. module: fetchmail
#: model:ir.actions.server,name:fetchmail.ir_cron_mail_gateway_action_ir_actions_server
#: model:ir.cron,cron_name:fetchmail.ir_cron_mail_gateway_action
#: model:ir.cron,name:fetchmail.ir_cron_mail_gateway_action
msgid "Mail: Fetchmail Service"
msgstr ""

#. module: fetchmail
#: model:ir.actions.act_window,name:fetchmail.act_server_history
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__message_ids
msgid "Messages"
msgstr "Poruke"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__name
msgid "Name"
msgstr "Naziv"

#. module: fetchmail
#: code:addons/fetchmail/models/fetchmail.py:0
#, python-format
msgid ""
"No response received. Check server information.\n"
" %s"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields.selection,name:fetchmail.selection__fetchmail_server__state__draft
msgid "Not Confirmed"
msgstr "Nepotvrđeno"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.res_config_settings_view_form
msgid "Outgoing Email Servers"
msgstr ""

#. module: fetchmail
#: model:ir.model,name:fetchmail.model_mail_mail
msgid "Outgoing Mails"
msgstr "Odlazni mailovi"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_search
msgid "POP"
msgstr "POP"

#. module: fetchmail
#: model:ir.model.fields.selection,name:fetchmail.selection__fetchmail_server__server_type__pop
msgid "POP Server"
msgstr "POP poslužitelj"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_tree
msgid "POP/IMAP Servers"
msgstr "POP/IMAP Servers"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__password
msgid "Password"
msgstr "Zaporka"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__port
msgid "Port"
msgstr "Port"

#. module: fetchmail
#: model:ir.model.fields,help:fetchmail.field_fetchmail_server__object_id
msgid ""
"Process each incoming mail as part of a conversation corresponding to this "
"document type. This will create new documents for new conversations, or "
"attach follow-up emails to the existing conversations (documents)."
msgstr ""
"Procesiraj svaku dolaznu poruku kao dio konvrzacije koja pripada ovom tipa "
"dokumenta. To će kreirati nove dokumente za nove konverzacije, ili priloži "
"follow-up poruka na postojeću konverzaciju."

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Reset Confirmation"
msgstr "Resetiraj potvrdu"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_search
msgid "SSL"
msgstr "SSL"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__is_ssl
msgid "SSL/TLS"
msgstr "SSL/TLS"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__script
msgid "Script"
msgstr "Skripta"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_search
msgid "Search Incoming Mail Servers"
msgstr "Pretraži poslužitelje dolaznih mail poruka"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Server & Login"
msgstr "Poslužitelj i prijava"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Server Information"
msgstr "Informacije o poslužitelju"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__server
msgid "Server Name"
msgstr "Ime Servera"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__priority
msgid "Server Priority"
msgstr "Prioritet poslužitelja"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__server_type
msgid "Server Type"
msgstr "Tip poslužitelja"

#. module: fetchmail
#: code:addons/fetchmail/models/fetchmail.py:0
#, python-format
msgid ""
"Server replied with following exception:\n"
" %s"
msgstr ""

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_search
msgid "Server type IMAP."
msgstr "Tip servera IMAP"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_search
msgid "Server type POP."
msgstr "POP tip poslužitelja"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__state
msgid "Status"
msgstr "Status"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Test & Confirm"
msgstr "Testiraj i potvrdi"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__user
msgid "Username"
msgstr "Korisničko ime"

#. module: fetchmail
#: model:ir.model.fields,help:fetchmail.field_fetchmail_server__original
msgid ""
"Whether a full original copy of each email should be kept for reference and "
"attached to each processed message. This will usually double the size of "
"your message database."
msgstr ""
"Treba li kompletan original poruke čuvati kao referencu pridruženu svakoj "
"obrađenoj poruci. Ovo obično uzrokuje značajnu povećanje baze poruka."

#. module: fetchmail
#: model:ir.model.fields,help:fetchmail.field_fetchmail_server__attach
msgid ""
"Whether attachments should be downloaded. If not enabled, incoming emails "
"will be stripped of any attachments before being processed"
msgstr ""
"Privitci poruka trebali bi biti skinuti. Ako nije omogućeno, dolaznim "
"porukema biti će maknuti privitci prije procesiranja"
