# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_unsplash
# 
# Translators:
# <PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-09-29 13:46+0000\n"
"PO-Revision-Date: 2020-09-07 08:20+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Italian (https://www.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: web_unsplash
#: model_terms:ir.ui.view,arch_db:web_unsplash.res_config_settings_view_form
msgid "<i class=\"fa fa-arrow-right\"/> Generate an Access Key"
msgstr "<i class=\"fa fa-arrow-right\"/> Genera una chiave di accesso"

#. module: web_unsplash
#: model:ir.model.fields,field_description:web_unsplash.field_res_config_settings__unsplash_access_key
msgid "Access Key"
msgstr "Chiave di accesso"

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Apply"
msgstr "Applica"

#. module: web_unsplash
#: model:ir.model,name:web_unsplash.model_res_config_settings
msgid "Config Settings"
msgstr "Impostazioni di configurazione"

#. module: web_unsplash
#: model:ir.model.fields,field_description:web_unsplash.field_ir_qweb_field_image__display_name
#: model:ir.model.fields,field_description:web_unsplash.field_res_config_settings__display_name
#: model:ir.model.fields,field_description:web_unsplash.field_res_users__display_name
msgid "Display Name"
msgstr "Nome visualizzato"

#. module: web_unsplash
#: code:addons/web_unsplash/controllers/main.py:0
#, python-format
msgid "ERROR: Unknown Unsplash URL!"
msgstr "ERRORE: URL Unsplash sconosciuto."

#. module: web_unsplash
#: code:addons/web_unsplash/controllers/main.py:0
#, python-format
msgid "ERROR: Unknown Unsplash notify URL!"
msgstr "ERRORE: URL di notifica Unsplash sconosciuto."

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Generate an access key"
msgstr "Genera una chiave di accesso"

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "How to find my Unsplash Application ID?"
msgstr "Come trovare l'ID applicazione Unsplash"

#. module: web_unsplash
#: model:ir.model.fields,field_description:web_unsplash.field_ir_qweb_field_image__id
#: model:ir.model.fields,field_description:web_unsplash.field_res_config_settings__id
#: model:ir.model.fields,field_description:web_unsplash.field_res_users__id
msgid "ID"
msgstr "ID"

#. module: web_unsplash
#: model:ir.model.fields,field_description:web_unsplash.field_ir_qweb_field_image____last_update
#: model:ir.model.fields,field_description:web_unsplash.field_res_config_settings____last_update
#: model:ir.model.fields,field_description:web_unsplash.field_res_users____last_update
msgid "Last Modified on"
msgstr "Ultima modifica il"

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Paste your access key here"
msgstr "Incolla qui la chiave di accesso"

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Paste your application ID here"
msgstr "Incolla qui l'ID applicazione"

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Photos (via Unsplash)"
msgstr "Foto (via Unsplash)"

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Please check your Unsplash access key and application ID."
msgstr "Controllare la chiave di accesso e l'ID applicazione Unsplash."

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Please check your internet connection or contact administrator."
msgstr "Controllare la connessione internet o contattare l'amministratore."

#. module: web_unsplash
#: model:ir.model,name:web_unsplash.model_ir_qweb_field_image
msgid "Qweb Field Image"
msgstr "Campo QWeb immagine"

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Search is temporarily unavailable"
msgstr "Ricerca temporaneamente non disponibile"

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Something went wrong"
msgstr "Qualcosa è andato storto"

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid ""
"The max number of searches is exceeded. Please retry in an hour or extend to"
" a better account."
msgstr ""
"È stato superato il numero massimo di ricerche. Riprovare tra un'ora o "
"passare a un account superiore."

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Unauthorized Key"
msgstr "Chiave non autorizzata"

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Unsplash requires an access key and an application ID"
msgstr "Unsplash richiede una chiave di accesso e un ID applicazione"

#. module: web_unsplash
#: model:ir.model,name:web_unsplash.model_res_users
msgid "Users"
msgstr "Utenti"
