# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_unsplash
# 
# Translators:
# <PERSON>, 2020
# <PERSON> <igor.shelud<PERSON>@gmail.com>, 2020
# <PERSON><PERSON><PERSON><PERSON>, 2020
# <PERSON> <<EMAIL>>, 2020
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-09-29 13:46+0000\n"
"PO-Revision-Date: 2020-09-07 08:20+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2020\n"
"Language-Team: Bulgarian (https://www.transifex.com/odoo/teams/41243/bg/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bg\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: web_unsplash
#: model_terms:ir.ui.view,arch_db:web_unsplash.res_config_settings_view_form
msgid "<i class=\"fa fa-arrow-right\"/> Generate an Access Key"
msgstr ""

#. module: web_unsplash
#: model:ir.model.fields,field_description:web_unsplash.field_res_config_settings__unsplash_access_key
msgid "Access Key"
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Apply"
msgstr "Прилагайте"

#. module: web_unsplash
#: model:ir.model,name:web_unsplash.model_res_config_settings
msgid "Config Settings"
msgstr "Настройки конфигурация"

#. module: web_unsplash
#: model:ir.model.fields,field_description:web_unsplash.field_ir_qweb_field_image__display_name
#: model:ir.model.fields,field_description:web_unsplash.field_res_config_settings__display_name
#: model:ir.model.fields,field_description:web_unsplash.field_res_users__display_name
msgid "Display Name"
msgstr "Име за показване"

#. module: web_unsplash
#: code:addons/web_unsplash/controllers/main.py:0
#, python-format
msgid "ERROR: Unknown Unsplash URL!"
msgstr ""

#. module: web_unsplash
#: code:addons/web_unsplash/controllers/main.py:0
#, python-format
msgid "ERROR: Unknown Unsplash notify URL!"
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Generate an access key"
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "How to find my Unsplash Application ID?"
msgstr ""

#. module: web_unsplash
#: model:ir.model.fields,field_description:web_unsplash.field_ir_qweb_field_image__id
#: model:ir.model.fields,field_description:web_unsplash.field_res_config_settings__id
#: model:ir.model.fields,field_description:web_unsplash.field_res_users__id
msgid "ID"
msgstr "ID"

#. module: web_unsplash
#: model:ir.model.fields,field_description:web_unsplash.field_ir_qweb_field_image____last_update
#: model:ir.model.fields,field_description:web_unsplash.field_res_config_settings____last_update
#: model:ir.model.fields,field_description:web_unsplash.field_res_users____last_update
msgid "Last Modified on"
msgstr "Последно променено на"

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Paste your access key here"
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Paste your application ID here"
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Photos (via Unsplash)"
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Please check your Unsplash access key and application ID."
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Please check your internet connection or contact administrator."
msgstr ""

#. module: web_unsplash
#: model:ir.model,name:web_unsplash.model_ir_qweb_field_image
msgid "Qweb Field Image"
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Search is temporarily unavailable"
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Something went wrong"
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid ""
"The max number of searches is exceeded. Please retry in an hour or extend to"
" a better account."
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Unauthorized Key"
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Unsplash requires an access key and an application ID"
msgstr ""

#. module: web_unsplash
#: model:ir.model,name:web_unsplash.model_res_users
msgid "Users"
msgstr "Потребители"
