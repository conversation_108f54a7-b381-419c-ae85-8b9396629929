# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_tour
# 
# Translators:
# <PERSON>, 2020
# <PERSON><PERSON>, 2020
# me<PERSON> <mehs<PERSON><PERSON>@gmail.com>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~13.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-09-01 07:29+0000\n"
"PO-Revision-Date: 2020-09-07 08:20+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Persian (https://www.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/js/tour_manager.js:0
#, python-format
msgid ""
"<strong><b>Good job!</b> You went through all steps of this tour.</strong>"
msgstr "<strong><b>بسیار عالی!</b> شما تمام این تور را مرور کردید.</strong>"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/js/tip.js:0
#, python-format
msgid "Click here to go to the next step."
msgstr ""

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/js/tour_step_utils.js:0
#, python-format
msgid "Click on the <i>Home icon</i> to navigate across apps."
msgstr "بر روی <i>آیکون خانه</i> کلیک کنید تا بین برنامه های بگردید."

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__user_id
msgid "Consumed by"
msgstr "دیده شده توسط"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/xml/debug_manager.xml:0
#, python-format
msgid "Disable Tours"
msgstr "غیر فعال کردن تورها"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_ir_http__display_name
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__display_name
msgid "Display Name"
msgstr "نام نمایشی"

#. module: web_tour
#: model:ir.model,name:web_tour.model_ir_http
msgid "HTTP Routing"
msgstr "مسیریابی HTTP"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_ir_http__id
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__id
msgid "ID"
msgstr "شناسه"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_ir_http____last_update
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour____last_update
msgid "Last Modified on"
msgstr "آخرین تغییر در"

#. module: web_tour
#: model_terms:ir.ui.view,arch_db:web_tour.edit_tour_list
msgid "Menu"
msgstr "منو"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/xml/debug_manager.xml:0
#, python-format
msgid "Name"
msgstr "نام"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/js/tour_step_utils.js:0
#, python-format
msgid "Open bugger menu."
msgstr ""

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/xml/debug_manager.xml:0
#, python-format
msgid "Path"
msgstr "مسیر"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/js/tip.js:0
#, python-format
msgid "Scroll to reach the next step."
msgstr "اسکرول کن تا به مرحله بعد بروی."

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/xml/debug_manager.xml:0
#, python-format
msgid "Sequence"
msgstr "دنباله"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/js/tour_manager.js:0
#, python-format
msgid "Skip tour"
msgstr "برو تور بعدی"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/xml/debug_manager.xml:0
#, python-format
msgid "Start"
msgstr "آغاز"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/xml/debug_manager.xml:0
#, python-format
msgid "Start Tour"
msgstr "شروع تور"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/xml/debug_manager.xml:0
#: code:addons/web_tour/static/src/xml/debug_manager.xml:0
#, python-format
msgid "Start tour"
msgstr ""

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/xml/debug_manager.xml:0
#, python-format
msgid "Test"
msgstr "آزمایش"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/xml/debug_manager.xml:0
#: code:addons/web_tour/static/src/xml/debug_manager.xml:0
#, python-format
msgid "Test tour"
msgstr ""

#. module: web_tour
#: model_terms:ir.ui.view,arch_db:web_tour.edit_tour_search
msgid "Tip"
msgstr "انعام"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__name
msgid "Tour name"
msgstr "نام تور"

#. module: web_tour
#: model:ir.actions.act_window,name:web_tour.edit_tour_action
#: model:ir.model,name:web_tour.model_web_tour_tour
#: model:ir.ui.menu,name:web_tour.menu_tour_action
msgid "Tours"
msgstr "تورها"
