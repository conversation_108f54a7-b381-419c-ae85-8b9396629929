# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_tour
# 
# Translators:
# <PERSON>, 2020
# <PERSON> <oluji<PERSON>.vlad<PERSON><EMAIL>>, 2020
# <PERSON><PERSON><PERSON><PERSON> <durdi<PERSON>.<PERSON><PERSON><PERSON><PERSON>@storm.hr>, 2020
# <PERSON><PERSON><PERSON> <ivic<PERSON>.<PERSON><PERSON><PERSON><PERSON>@storm.hr>, 2020
# <PERSON><PERSON><PERSON> <karol<PERSON>.<EMAIL>>, 2020
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~13.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-09-01 07:29+0000\n"
"PO-Revision-Date: 2020-09-07 08:20+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2020\n"
"Language-Team: Croatian (https://www.transifex.com/odoo/teams/41243/hr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hr\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/js/tour_manager.js:0
#, python-format
msgid ""
"<strong><b>Good job!</b> You went through all steps of this tour.</strong>"
msgstr ""

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/js/tip.js:0
#, python-format
msgid "Click here to go to the next step."
msgstr ""

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/js/tour_step_utils.js:0
#, python-format
msgid "Click on the <i>Home icon</i> to navigate across apps."
msgstr ""

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__user_id
msgid "Consumed by"
msgstr "Koristili"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/xml/debug_manager.xml:0
#, python-format
msgid "Disable Tours"
msgstr ""

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_ir_http__display_name
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__display_name
msgid "Display Name"
msgstr "Naziv"

#. module: web_tour
#: model:ir.model,name:web_tour.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP usmjeravanje"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_ir_http__id
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__id
msgid "ID"
msgstr "ID"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_ir_http____last_update
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour____last_update
msgid "Last Modified on"
msgstr "Zadnja promjena"

#. module: web_tour
#: model_terms:ir.ui.view,arch_db:web_tour.edit_tour_list
msgid "Menu"
msgstr "Izbornik"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/xml/debug_manager.xml:0
#, python-format
msgid "Name"
msgstr "Naziv"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/js/tour_step_utils.js:0
#, python-format
msgid "Open bugger menu."
msgstr ""

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/xml/debug_manager.xml:0
#, python-format
msgid "Path"
msgstr "Putanja"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/js/tip.js:0
#, python-format
msgid "Scroll to reach the next step."
msgstr ""

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/xml/debug_manager.xml:0
#, python-format
msgid "Sequence"
msgstr "Sekvenca"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/js/tour_manager.js:0
#, python-format
msgid "Skip tour"
msgstr ""

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/xml/debug_manager.xml:0
#, python-format
msgid "Start"
msgstr "Start"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/xml/debug_manager.xml:0
#, python-format
msgid "Start Tour"
msgstr ""

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/xml/debug_manager.xml:0
#: code:addons/web_tour/static/src/xml/debug_manager.xml:0
#, python-format
msgid "Start tour"
msgstr ""

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/xml/debug_manager.xml:0
#, python-format
msgid "Test"
msgstr "Test"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/xml/debug_manager.xml:0
#: code:addons/web_tour/static/src/xml/debug_manager.xml:0
#, python-format
msgid "Test tour"
msgstr ""

#. module: web_tour
#: model_terms:ir.ui.view,arch_db:web_tour.edit_tour_search
msgid "Tip"
msgstr "Savjet"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__name
msgid "Tour name"
msgstr ""

#. module: web_tour
#: model:ir.actions.act_window,name:web_tour.edit_tour_action
#: model:ir.model,name:web_tour.model_web_tour_tour
#: model:ir.ui.menu,name:web_tour.menu_tour_action
msgid "Tours"
msgstr ""
