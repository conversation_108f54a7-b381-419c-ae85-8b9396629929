# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_tour
# 
# Translators:
# <PERSON>, 2020
# Khwunch<PERSON>awa<PERSON> <<EMAIL>>, 2020
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2020
# Wichanon Jamwutthipreecha, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~13.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-09-01 07:29+0000\n"
"PO-Revision-Date: 2020-09-07 08:20+0000\n"
"Last-Translator: Wichanon Jamwutthipreecha, 2022\n"
"Language-Team: Thai (https://www.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/js/tour_manager.js:0
#, python-format
msgid ""
"<strong><b>Good job!</b> You went through all steps of this tour.</strong>"
msgstr "<strong><b>ยอดเยี่ยม!</b> คุณผ่านทุกขั้นตอนของทัวร์นี้แล้ว</strong>"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/js/tip.js:0
#, python-format
msgid "Click here to go to the next step."
msgstr ""

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/js/tour_step_utils.js:0
#, python-format
msgid "Click on the <i>Home icon</i> to navigate across apps."
msgstr "คลิกที่ <i> ไอคอนหน้าแรก </i> เพื่อไปยังแอปต่างๆ"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__user_id
msgid "Consumed by"
msgstr "บริโภคโดย"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/xml/debug_manager.xml:0
#, python-format
msgid "Disable Tours"
msgstr "ปิดการใช้งานทัวร์"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_ir_http__display_name
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__display_name
msgid "Display Name"
msgstr "ชื่อที่ใช้แสดง"

#. module: web_tour
#: model:ir.model,name:web_tour.model_ir_http
msgid "HTTP Routing"
msgstr "See http://openerp.com"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_ir_http__id
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__id
msgid "ID"
msgstr "รหัส"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_ir_http____last_update
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour____last_update
msgid "Last Modified on"
msgstr "แก้ไขครั้งสุดท้ายเมื่อ"

#. module: web_tour
#: model_terms:ir.ui.view,arch_db:web_tour.edit_tour_list
msgid "Menu"
msgstr "เมนู"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/xml/debug_manager.xml:0
#, python-format
msgid "Name"
msgstr "ชื่อ"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/js/tour_step_utils.js:0
#, python-format
msgid "Open bugger menu."
msgstr "เปิดเมนูบักเกอร์"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/xml/debug_manager.xml:0
#, python-format
msgid "Path"
msgstr "Path"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/js/tip.js:0
#, python-format
msgid "Scroll to reach the next step."
msgstr "เลื่อนเพื่อไปยังขั้นตอนต่อไป"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/xml/debug_manager.xml:0
#, python-format
msgid "Sequence"
msgstr "ลำดับ"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/js/tour_manager.js:0
#, python-format
msgid "Skip tour"
msgstr "ข้ามทัวร์"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/xml/debug_manager.xml:0
#, python-format
msgid "Start"
msgstr "เริ่ม"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/xml/debug_manager.xml:0
#, python-format
msgid "Start Tour"
msgstr "เริ่มทัวร์"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/xml/debug_manager.xml:0
#: code:addons/web_tour/static/src/xml/debug_manager.xml:0
#, python-format
msgid "Start tour"
msgstr "เริ่มทัวร์"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/xml/debug_manager.xml:0
#, python-format
msgid "Test"
msgstr "ทดสอบ"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/xml/debug_manager.xml:0
#: code:addons/web_tour/static/src/xml/debug_manager.xml:0
#, python-format
msgid "Test tour"
msgstr "ทดสอบทัวร์"

#. module: web_tour
#: model_terms:ir.ui.view,arch_db:web_tour.edit_tour_search
msgid "Tip"
msgstr "ิทิป"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__name
msgid "Tour name"
msgstr "ชื่อทัวร์"

#. module: web_tour
#: model:ir.actions.act_window,name:web_tour.edit_tour_action
#: model:ir.model,name:web_tour.model_web_tour_tour
#: model:ir.ui.menu,name:web_tour.menu_tour_action
msgid "Tours"
msgstr "ทัวร์"
