<?xml version="1.0" encoding="UTF-8"?>
<odoo>
<data>
    <record model='l10n_latam.identification.type' id='l10n_latam_base.it_fid'>
        <field name='l10n_ar_afip_code'>91</field>
    </record>
    <record model='l10n_latam.identification.type' id='l10n_latam_base.it_pass'>
        <field name='l10n_ar_afip_code'>94</field>
    </record>
    <record model='l10n_latam.identification.type' id='it_cuit'>
        <field name='name'>CUIT</field>
        <field name='description'>Código Único de Identificación Tributaria</field>
        <field name='country_id' ref='base.ar'/>
        <field name='is_vat' eval='True'/>
        <field name='l10n_ar_afip_code'>80</field>
        <field name='sequence'>10</field>
    </record>
    <record model='l10n_latam.identification.type' id='it_dni'>
        <field name='name'>DNI</field>
        <field name='description'>Documento Nacional de Identidad</field>
        <field name='country_id' ref='base.ar'/>
        <field name='l10n_ar_afip_code'>96</field>
        <field name='sequence'>20</field>
    </record>
    <record model='l10n_latam.identification.type' id='it_CUIL'>
        <field name='name'>CUIL</field>
        <field name='description'>Código Único de Identificación Laboral</field>
        <field name='country_id' ref='base.ar'/>
        <field name='l10n_ar_afip_code'>86</field>
        <field name='sequence'>30</field>
    </record>
    <record model='l10n_latam.identification.type' id='it_Sigd'>
        <field name='name'>Sigd</field>
        <field name='description'>Sin identificar/venta global diaria</field>
        <field name='country_id' ref='base.ar'/>
        <field name='l10n_ar_afip_code'>99</field>
        <field name='sequence'>110</field>
    </record>
    <record model='l10n_latam.identification.type' id='it_CPF'>
        <field name='name'>CPF</field>
        <field name='description'>CI Policía Federal</field>
        <field name='country_id' ref='base.ar'/>
        <field name='l10n_ar_afip_code'>0</field>
    </record>
    <record model='l10n_latam.identification.type' id='it_CBA'>
        <field name='name'>CBA</field>
        <field name='description'>CI Buenos Aires</field>
        <field name='country_id' ref='base.ar'/>
        <field name='l10n_ar_afip_code'>1</field>
    </record>
    <record model='l10n_latam.identification.type' id='it_CCat'>
        <field name='name'>CCat</field>
        <field name='description'>CI Catamarca</field>
        <field name='country_id' ref='base.ar'/>
        <field name='l10n_ar_afip_code'>2</field>
    </record>
    <record model='l10n_latam.identification.type' id='it_CCor'>
        <field name='name'>CCor</field>
        <field name='description'>CI Córdoba</field>
        <field name='country_id' ref='base.ar'/>
        <field name='l10n_ar_afip_code'>3</field>
    </record>
    <record model='l10n_latam.identification.type' id='it_CCorr'>
        <field name='name'>CCorr</field>
        <field name='description'>CI Corrientes</field>
        <field name='country_id' ref='base.ar'/>
        <field name='l10n_ar_afip_code'>4</field>
    </record>
    <record model='l10n_latam.identification.type' id='it_CIER'>
        <field name='name'>CIER</field>
        <field name='description'>CI Entre Ríos</field>
        <field name='country_id' ref='base.ar'/>
        <field name='l10n_ar_afip_code'>5</field>
    </record>
    <record model='l10n_latam.identification.type' id='it_CIJ'>
        <field name='name'>CIJ</field>
        <field name='description'>CI Jujuy</field>
        <field name='country_id' ref='base.ar'/>
        <field name='l10n_ar_afip_code'>6</field>
    </record>
    <record model='l10n_latam.identification.type' id='it_CIMen'>
        <field name='name'>CIMen</field>
        <field name='description'>CI Mendoza</field>
        <field name='country_id' ref='base.ar'/>
        <field name='l10n_ar_afip_code'>7</field>
    </record>
    <record model='l10n_latam.identification.type' id='it_CILR'>
        <field name='name'>CILR</field>
        <field name='description'>CI La Rioja</field>
        <field name='country_id' ref='base.ar'/>
        <field name='l10n_ar_afip_code'>8</field>
    </record>
    <record model='l10n_latam.identification.type' id='it_CIS'>
        <field name='name'>CIS</field>
        <field name='description'>CI Salta</field>
        <field name='country_id' ref='base.ar'/>
        <field name='l10n_ar_afip_code'>9</field>
    </record>
    <record model='l10n_latam.identification.type' id='it_CISJ'>
        <field name='name'>CISJ</field>
        <field name='description'>CI San Juan</field>
        <field name='country_id' ref='base.ar'/>
        <field name='l10n_ar_afip_code'>10</field>
    </record>
    <record model='l10n_latam.identification.type' id='it_CISL'>
        <field name='name'>CISL</field>
        <field name='description'>CI San Luis</field>
        <field name='country_id' ref='base.ar'/>
        <field name='l10n_ar_afip_code'>11</field>
    </record>
    <record model='l10n_latam.identification.type' id='it_CISF'>
        <field name='name'>CISF</field>
        <field name='description'>CI Santa Fe</field>
        <field name='country_id' ref='base.ar'/>
        <field name='l10n_ar_afip_code'>12</field>
    </record>
    <record model='l10n_latam.identification.type' id='it_CISdE'>
        <field name='name'>CISdE</field>
        <field name='description'>CI Santiago del Estero</field>
        <field name='country_id' ref='base.ar'/>
        <field name='l10n_ar_afip_code'>13</field>
    </record>
    <record model='l10n_latam.identification.type' id='it_CIT'>
        <field name='name'>CIT</field>
        <field name='description'>CI Tucumán</field>
        <field name='country_id' ref='base.ar'/>
        <field name='l10n_ar_afip_code'>14</field>
    </record>
    <record model='l10n_latam.identification.type' id='it_CICha'>
        <field name='name'>CICha</field>
        <field name='description'>CI Chaco</field>
        <field name='country_id' ref='base.ar'/>
        <field name='l10n_ar_afip_code'>16</field>
    </record>
    <record model='l10n_latam.identification.type' id='it_CIChu'>
        <field name='name'>CIChu</field>
        <field name='description'>CI Chubut</field>
        <field name='country_id' ref='base.ar'/>
        <field name='l10n_ar_afip_code'>17</field>
    </record>
    <record model='l10n_latam.identification.type' id='it_CIF'>
        <field name='name'>CIF</field>
        <field name='description'>CI Formosa</field>
        <field name='country_id' ref='base.ar'/>
        <field name='l10n_ar_afip_code'>18</field>
    </record>
    <record model='l10n_latam.identification.type' id='it_CIMis'>
        <field name='name'>CIMis</field>
        <field name='description'>CI Misiones</field>
        <field name='country_id' ref='base.ar'/>
        <field name='l10n_ar_afip_code'>19</field>
    </record>
    <record model='l10n_latam.identification.type' id='it_CIN'>
        <field name='name'>CIN</field>
        <field name='description'>CI Neuquén</field>
        <field name='country_id' ref='base.ar'/>
        <field name='l10n_ar_afip_code'>20</field>
    </record>
    <record model='l10n_latam.identification.type' id='it_CILP'>
        <field name='name'>CILP</field>
        <field name='description'>CI La Pampa</field>
        <field name='country_id' ref='base.ar'/>
        <field name='l10n_ar_afip_code'>21</field>
    </record>
    <record model='l10n_latam.identification.type' id='it_CIRN'>
        <field name='name'>CIRN</field>
        <field name='description'>CI Río Negro</field>
        <field name='country_id' ref='base.ar'/>
        <field name='l10n_ar_afip_code'>22</field>
    </record>
    <record model='l10n_latam.identification.type' id='it_CISC'>
        <field name='name'>CISC</field>
        <field name='description'>CI Santa Cruz</field>
        <field name='country_id' ref='base.ar'/>
        <field name='l10n_ar_afip_code'>23</field>
    </record>
    <record model='l10n_latam.identification.type' id='it_CITdF'>
        <field name='name'>CITdF</field>
        <field name='description'>CI Tierra del Fuego</field>
        <field name='country_id' ref='base.ar'/>
        <field name='l10n_ar_afip_code'>24</field>
    </record>
    <record model='l10n_latam.identification.type' id='it_CDI'>
        <field name='name'>CDI</field>
        <field name='description'>CDI</field>
        <field name='country_id' ref='base.ar'/>
        <field name='l10n_ar_afip_code'>87</field>
    </record>
    <record model='l10n_latam.identification.type' id='it_LE'>
        <field name='name'>LE</field>
        <field name='description'>LE</field>
        <field name='country_id' ref='base.ar'/>
        <field name='l10n_ar_afip_code'>89</field>
    </record>
    <record model='l10n_latam.identification.type' id='it_LC'>
        <field name='name'>LC</field>
        <field name='description'>LC</field>
        <field name='country_id' ref='base.ar'/>
        <field name='l10n_ar_afip_code'>90</field>
    </record>
    <record model='l10n_latam.identification.type' id='it_ET'>
        <field name='name'>ET</field>
        <field name='description'>en trámite</field>
        <field name='country_id' ref='base.ar'/>
        <field name='l10n_ar_afip_code'>92</field>
    </record>
    <record model='l10n_latam.identification.type' id='it_AN'>
        <field name='name'>AN</field>
        <field name='description'>Acta nacimiento</field>
        <field name='country_id' ref='base.ar'/>
        <field name='l10n_ar_afip_code'>93</field>
    </record>
    <record model='l10n_latam.identification.type' id='it_CIBAR'>
        <field name='name'>CIBAR</field>
        <field name='description'>CI Bs. As. RNP</field>
        <field name='country_id' ref='base.ar'/>
        <field name='l10n_ar_afip_code'>95</field>
    </record>
    <record model='l10n_latam.identification.type' id='it_CdM'>
        <field name='name'>CdM</field>
        <field name='description'>Certificado de Migración</field>
        <field name='country_id' ref='base.ar'/>
        <field name='l10n_ar_afip_code'>30</field>
    </record>
    <record model='l10n_latam.identification.type' id='it_UpApP'>
        <field name='name'>UpApP</field>
        <field name='description'>Usado por Anses para Padrón</field>
        <field name='country_id' ref='base.ar'/>
        <field name='l10n_ar_afip_code'>88</field>
    </record>
</data>
<data noupdate="True">
    <record model='l10n_latam.identification.type' id='it_CBA'>
        <field name='active' eval='False'/>
    </record>
    <record model='l10n_latam.identification.type' id='it_CCat'>
        <field name='active' eval='False'/>
    </record>
    <record model='l10n_latam.identification.type' id='it_CCor'>
        <field name='active' eval='False'/>
    </record>
    <record model='l10n_latam.identification.type' id='it_CCorr'>
        <field name='active' eval='False'/>
    </record>
    <record model='l10n_latam.identification.type' id='it_CIER'>
        <field name='active' eval='False'/>
    </record>
    <record model='l10n_latam.identification.type' id='it_CIJ'>
        <field name='active' eval='False'/>
    </record>
    <record model='l10n_latam.identification.type' id='it_CIMen'>
        <field name='active' eval='False'/>
    </record>
    <record model='l10n_latam.identification.type' id='it_CILR'>
        <field name='active' eval='False'/>
    </record>
    <record model='l10n_latam.identification.type' id='it_CIS'>
        <field name='active' eval='False'/>
    </record>
    <record model='l10n_latam.identification.type' id='it_CISJ'>
        <field name='active' eval='False'/>
    </record>
    <record model='l10n_latam.identification.type' id='it_CISL'>
        <field name='active' eval='False'/>
    </record>
    <record model='l10n_latam.identification.type' id='it_CISF'>
        <field name='active' eval='False'/>
    </record>
    <record model='l10n_latam.identification.type' id='it_CISdE'>
        <field name='active' eval='False'/>
    </record>
    <record model='l10n_latam.identification.type' id='it_CIT'>
        <field name='active' eval='False'/>
    </record>
    <record model='l10n_latam.identification.type' id='it_CICha'>
        <field name='active' eval='False'/>
    </record>
    <record model='l10n_latam.identification.type' id='it_CIChu'>
        <field name='active' eval='False'/>
    </record>
    <record model='l10n_latam.identification.type' id='it_CIF'>
        <field name='active' eval='False'/>
    </record>
    <record model='l10n_latam.identification.type' id='it_CIMis'>
        <field name='active' eval='False'/>
    </record>
    <record model='l10n_latam.identification.type' id='it_CIN'>
        <field name='active' eval='False'/>
    </record>
    <record model='l10n_latam.identification.type' id='it_CILP'>
        <field name='active' eval='False'/>
    </record>
    <record model='l10n_latam.identification.type' id='it_CIRN'>
        <field name='active' eval='False'/>
    </record>
    <record model='l10n_latam.identification.type' id='it_CISC'>
        <field name='active' eval='False'/>
    </record>
    <record model='l10n_latam.identification.type' id='it_CITdF'>
        <field name='active' eval='False'/>
    </record>
    <record model='l10n_latam.identification.type' id='it_CDI'>
        <field name='active' eval='False'/>
    </record>
    <record model='l10n_latam.identification.type' id='it_LE'>
        <field name='active' eval='False'/>
    </record>
    <record model='l10n_latam.identification.type' id='it_LC'>
        <field name='active' eval='False'/>
    </record>
    <record model='l10n_latam.identification.type' id='it_ET'>
        <field name='active' eval='False'/>
    </record>
    <record model='l10n_latam.identification.type' id='it_AN'>
        <field name='active' eval='False'/>
    </record>
    <record model='l10n_latam.identification.type' id='it_CIBAR'>
        <field name='active' eval='False'/>
    </record>
    <record model='l10n_latam.identification.type' id='it_CdM'>
        <field name='active' eval='False'/>
    </record>
    <record model='l10n_latam.identification.type' id='it_UpApP'>
        <field name='active' eval='False'/>
    </record>
    <record model='l10n_latam.identification.type' id='it_Sigd'>
        <field name='active' eval='True'/>
    </record>
    <record model='l10n_latam.identification.type' id='it_CPF'>
        <field name='active' eval='False'/>
    </record>
</data>
</odoo>
