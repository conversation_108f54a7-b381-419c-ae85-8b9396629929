# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_fleet
# 
# Translators:
# <PERSON>, 2020
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2020
# P<PERSON>cilla (prs) Odoo <<EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~13.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-09-01 07:29+0000\n"
"PO-Revision-Date: 2020-09-07 08:12+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2022\n"
"Language-Team: French (https://www.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: hr_fleet
#: code:addons/hr_fleet/controllers/main.py:0
#, python-format
msgid "%(car_name)s (driven from: %(date_start)s to %(date_end)s)"
msgstr "%(car_name)s (conduite du: %(date_start)s au %(date_end)s)"

#. module: hr_fleet
#: model_terms:ir.ui.view,arch_db:hr_fleet.fleet_vehicle_assignation_log_view_list
msgid "Attachments"
msgstr "Pièces jointes"

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_hr_employee__employee_cars_count
#: model:ir.model.fields,field_description:hr_fleet.field_res_users__employee_cars_count
msgid "Cars"
msgstr "Voitures"

#. module: hr_fleet
#: model_terms:ir.ui.view,arch_db:hr_fleet.res_users_view_form_preferences
#: model_terms:ir.ui.view,arch_db:hr_fleet.view_employee_form
msgid "Claim Car Report"
msgstr "Réclamer un rapport pour la voiture"

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_hr_departure_wizard
msgid "Departure Wizard"
msgstr "Assistant de départ"

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle__display_name
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle_assignation_log__display_name
#: model:ir.model.fields,field_description:hr_fleet.field_hr_departure_wizard__display_name
#: model:ir.model.fields,field_description:hr_fleet.field_hr_employee__display_name
#: model:ir.model.fields,field_description:hr_fleet.field_hr_employee_public__display_name
#: model:ir.model.fields,field_description:hr_fleet.field_res_users__display_name
msgid "Display Name"
msgstr "Nom affiché"

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_fleet_vehicle_assignation_log
msgid "Drivers history on a vehicle"
msgstr "Historique de conducteurs sur un véhicule"

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_hr_employee
msgid "Employee"
msgstr "Employé"

#. module: hr_fleet
#: model_terms:ir.ui.view,arch_db:hr_fleet.view_employee_form
msgid "Fleet"
msgstr "Parc Automobile"

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle__id
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle_assignation_log__id
#: model:ir.model.fields,field_description:hr_fleet.field_hr_departure_wizard__id
#: model:ir.model.fields,field_description:hr_fleet.field_hr_employee__id
#: model:ir.model.fields,field_description:hr_fleet.field_hr_employee_public__id
#: model:ir.model.fields,field_description:hr_fleet.field_res_users__id
msgid "ID"
msgstr "ID"

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle____last_update
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle_assignation_log____last_update
#: model:ir.model.fields,field_description:hr_fleet.field_hr_departure_wizard____last_update
#: model:ir.model.fields,field_description:hr_fleet.field_hr_employee____last_update
#: model:ir.model.fields,field_description:hr_fleet.field_hr_employee_public____last_update
#: model:ir.model.fields,field_description:hr_fleet.field_res_users____last_update
msgid "Last Modified on"
msgstr "Dernière modification le"

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle__mobility_card
#: model:ir.model.fields,field_description:hr_fleet.field_hr_employee__mobility_card
#: model:ir.model.fields,field_description:hr_fleet.field_hr_employee_public__mobility_card
msgid "Mobility Card"
msgstr "Carte Mobilité"

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle_assignation_log__attachment_number
msgid "Number of Attachments"
msgstr "Nombre de documents joints"

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_hr_employee_public
msgid "Public Employee"
msgstr "Employé public"

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_hr_departure_wizard__release_campany_car
msgid "Release Company Car"
msgstr "Libérer la voiture de société"

#. module: hr_fleet
#: model_terms:ir.ui.view,arch_db:hr_fleet.res_users_view_form_preferences
#: model_terms:ir.ui.view,arch_db:hr_fleet.view_employee_form
msgid ""
"This report will contain only PDF files. If you want all documents, please "
"go on vehicle page. Do you want to proceed?"
msgstr ""
"Ce rapport ne contiendra que des fichiers PDF. Si vous souhaitez tous les "
"documents, rendez-vous sur la page véhicule. Voulez-vous poursuivre?"

#. module: hr_fleet
#: code:addons/hr_fleet/controllers/main.py:0
msgid "There is no pdf attached to generate a claim report."
msgstr "Il n'y a pas de pdf joint pour générer un rapport de réclamation."

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_res_users
msgid "Users"
msgstr "Utilisateurs"

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_fleet_vehicle
msgid "Vehicle"
msgstr "Véhicule"
