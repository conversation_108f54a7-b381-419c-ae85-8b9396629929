// Variables
// ===================================================
$o-social-border-radius-base: 3px;
$o-social-border-radius-bubble: 16px;
$o-social-font-family-reset: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";

// Form Views
// ===================================================
.o_social_post_view_form {
    .o_form_sheet {
        position: relative;

        .o_social_post_empty_preview {
            @include media-breakpoint-down(md) {
                display: none;
            }

            &.o_view_nocontent {
                @include o-position-absolute(0, 0, 0, 55%);
                padding-top: 5%;
            }
        }

        .o_group.o_inner_group.o_social_post_preview_group {
            > tbody > tr > td {
                padding-right: 0;
            }
        }

        .o_social_post_form_attachments .o_attachments,
        .o_social_post_form_attachments .o_attachment {
            width: 250px;
            padding-left: 0;
        }

        .o_social_post_form_content td {
            position: relative;
        }

        .o_social_post_message_wrapper {
            &, & + textarea[disabled] {
                padding-right: 50px;

                @include media-breakpoint-up(sm) {
                    width: 100%!important;
                }
            }

            ~ .o_social_icon_dropdown {
                @include o-position-absolute(0, 30px);

                .dropdown-toggle:after {
                    display: none;
                }
            }

        }
    }

    .o_social_preview {
        font-family: $o-social-font-family-reset;
        font-size: 1.2rem;

        .o_social_preview_icon_wrapper {
            @include size(2em);
        }

        .o_social_stream_post_image img {
            max-height: 120px;

            @media (min-height: 800px) {
                max-height: 200px;
            }
        }
    }
}

// Kanban Views (Both Feed & Posts)
// ===================================================
.o_social_stream_post_message_body {
    .o_social_stream_post_message_text {
        display: -webkit-box;
        max-height: 9.8rem;
        // Doesn't work in all browser but we have no real solutions here
        // Other browsers will just hide the overflow so it's fine anyway
        -webkit-line-clamp: 6;
        -webkit-box-orient: vertical;

        .o_mail_emoji {
            font-size: 1rem;
        }
    }

    &.o_social_stream_post_with_attachments {
        .o_social_stream_post_message_text {
            max-height: 5rem;
            -webkit-line-clamp: 3;
        }
    }
}

// Kanban View (Feed)
// ===================================================
.o_social_stream_post_kanban_new_content {
    display: block;

    @include hover-focus {
        $bg-color: theme-color-level('info', $alert-border-level + 1);
        background: $bg-color;
        color: color-yiq($bg-color);
    }
}

.o_social_stream_post_kanban_before {
    &:empty {
        // Hide entire section if no streams
        display: none;
    }

    &, .o_social_stream_stat_box {
        margin-bottom: -1px;
    }
}

.o_content.o_social_stream_post_kanban_view_wrapper {
    .o_kanban_view {
        background-color: transparent;

        .o_social_stream_post_published_date {
            color: lighten($o-main-text-color, 10%);
            &:hover {
                color: $o-enterprise-primary-color;
            }
        }
    }

    .o_kanban_group {
        border-top: 1px solid $border-color;
        background-color: gray('100');
        padding: 1rem 8px 0 16px;

        &:not(.o_column_folded) {
            width: 310px;
        }

        &.o_column_folded .o_kanban_header_title img {
            display: none;
        }

        + .o_kanban_group {
            padding: 1rem 8px 0;
        }

        &:last-child {
            padding: 1rem 16px 0 8px;
        }
    }
}

.o_social_stream_post_author_name {
    max-width: 190px;
}

.o_stream_post_kanban_refresh_now .fa-spinner {
    margin-left: 5px;
}

// Record Design
.o_social_stream_post_kanban_global {
    border-radius: $o-social-border-radius-base;
    &:hover {
        cursor: pointer;
        box-shadow: 1px 1px 1px 0px #6c757d;
    }
}

// Kanban View (Posts)
// ===================================================
.oe_kanban_global_click {
    .oe_kanban_avatar {
        @include size(1.6em);
    }

    #post-stats {
        div:nth-child(2):last-child {
            margin-left: 2rem;
        }
    }
}

.o_social_post_kanban {
    &.o_social_post_kanban_error {
        border-color: theme-color('danger');
    }
}

// General Components
// ===================================================
.o_social_subtle_btn {
    cursor: pointer;
    color: lighten($o-main-text-color, 10%);

    @include hover-focus {
        outline: none;
        color: darken($o-main-text-color, 20%);
    }
}

.o_social_subtle_btn_disabled {
    color: lighten($o-main-text-color, 10%);
}

.o_social_author_image {
    &:after {
        @include o-position-absolute(0, 0, 0, 0);
        border: 1px solid rgba(black, .1);
        border-radius: 100%;
        content: '';
    }

    img {
        @include size(2em);
    }
}

.o_social_bubble_radius {
    border-radius: $o-social-border-radius-bubble;
}

.o_social_stream_post_link {
    border-radius: $o-social-border-radius-base;

    img {
        @include size(2.6em);
    }

    .o_social_stream_post_link_text {
        min-width: 0; // allow text-truncate on flexbox
        line-height: 1.35;
    }
}

.o_social_stream_post_image {
    img {
        cursor: pointer;
        @include size(100%, auto);
        max-width: 100%;
        max-height: 50px;
        object-fit: cover;
        object-position: center;

        @media (min-height: 800px) {
            max-height: 100px;
        }
    }

    .o_social_stream_post_image_more_overlay {
        cursor: pointer;
        background-color: rgba(black, .2);

        &:hover {
            background-color: rgba(black, .35);
        }
    }
}

// "Fullwidth" images carousel
.o_stream_post_images_carousel {
    height: 85vh;

    .carousel-item {
        transition: none;
        max-width: 80%;

        img {
            max-width: 100%;
            max-height: 73vh;
        }
    }
}

// Modals
// ===================================================

// Add a new stream
.o_social_add_stream_form {
    margin: $modal-inner-padding * -1;
    padding: ($spacer * 1.5);
    padding-bottom: ($spacer * 5);

    .o_social_media {
        background-color: $list-group-bg;
        cursor: pointer;

        @include hover-focus {
            background-color: $list-group-hover-bg;
        }
    }
}


.o_social_live_post_kanban {
    .o_field_widget.badge {
        display: inline-block;
        width: auto;
    }

    .o_social_live_post_kanban_account {
        font-weight: 600;
    }

    .o_social_live_post_kanban_failure_reason {
        max-height: 2rem;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
    }
}

.o_social_account_feed {
    .o_social_media_image {
        position: absolute;
        right: 5px;
    }

    .o_social_feed_stats {
        font-weight: bold;
        font-size: 1.6rem;
        line-height: 1.2rem;
        text-align: center;

        .o_social_feed_stats_item {
            display: inline-flex;
            align-items: center;
            margin-right: 8px;

            div {
                display: inline-block;
                text-align: left;
            }

            .o_social_feed_stat_trend {
                font-size: 1.2rem;
                font-weight: normal;
            }
        }
    }
}

// Comments
.o_social_comments_modal {
    min-height: 280px;
    overflow-x: hidden;
    font-family: $o-social-font-family-reset;

    .o_social_comment_controls, .o_social_manage_comment {
        .dropdown-toggle:after {
            content: none;
        }
    }

    .o_social_add_comment, .o_social_comment_image {
        border-radius: $o-social-border-radius-base;

        &.o_social_bubble_radius {
            border-radius: $o-social-border-radius-bubble;
        }
    }

    .o_social_comment_image {
        max-width: 300px;
        max-height: 300px;
    }

    .o_social_add_comment {
        // Emulate form-control-lg design without adding the class
        // to avoid issues with JS resize code.
        @include font-size($input-font-size-lg);
        line-height: $input-line-height-lg;
        resize: none;
    }

    .o_social_comment_controls {
        @include o-position-absolute(0, 0);
        height: 75%;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;

        .o_mail_add_emoji {
            bottom: auto;
            margin-top: 0;
        }
    }

    .o_social_comment_published_date, .o_social_post_published_date {
        color: lighten($o-main-text-color, 10%);
        &:hover {
            color: $o-enterprise-primary-color;
        }
    }

    .o_social_author_image img {
        @include size(32px);
    }

    .o_social_comment_user_likes{
        font-weight: bold;
    }

    // Original Post
    .o_social_comments_modal_original_post {
        .o_social_author_image img {
            @include size(42px);
        }

        .o_social_original_post_message_text {
            max-height: 160px;
        }

        .o_social_comments_modal_original_post_content {
            min-width: 0; // allow text-truncate on flexbox
        }

        .o_social_stream_post_image img {
            cursor: pointer;

            @media (min-height: 800px) {
                max-height: 200px;
            }
        }

        .o_social_original_post_single {
            @media (min-height: 800px) {
                max-height: 400px;
            }
        }

        .o_social_stream_post_link img {
            @include size(4em);
        }
    }

    .o_social_comment_message {
        .o_social_manage_comment {
            visibility: hidden;
            height: 0;
        }

        .o_social_comment_text {
            overflow: hidden;
        }

        &.o_social_comment_editable:hover .o_social_manage_comment {
            visibility: visible;
        }
    }

    .o_social_load_more_comments {
        display: block;
    }
}

.o_social_comment_delete_modal {
    padding-top: 12.5%;
    text-align: center;

    .modal-dialog {
        display: inline-block;
    }
}

.o_social_media_cards {
    width: min-content;
}
