# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* product_expiry
# 
# Translators:
# <PERSON>, 2020
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON> <jan.ho<PERSON><PERSON>@centrum.cz>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# trendspotter, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~13.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-09-01 07:29+0000\n"
"PO-Revision-Date: 2020-09-07 08:17+0000\n"
"Last-Translator: trendspotter, 2021\n"
"Language-Team: Czech (https://www.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.view_move_form_expiry
msgid ""
"<span class=\"badge badge-danger\" attrs=\"{'invisible': "
"[('product_expiry_alert', '=', False)]}\">Expiration Alert</span>"
msgstr ""

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.view_product_form_expiry
msgid "<span> days</span>"
msgstr "<span> dnů</span>"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_stock_production_lot__alert_date
msgid "Alert Date"
msgstr "Datum výstrahy"

#. module: product_expiry
#: model:mail.activity.type,name:product_expiry.mail_activity_type_alert_date_reached
msgid "Alert Date Reached"
msgstr "Bylo dosaženo výstražného data"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_product_product__alert_time
#: model:ir.model.fields,field_description:product_expiry.field_product_template__alert_time
msgid "Alert Time"
msgstr "Čas výstrahy"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_product_product__use_time
#: model:ir.model.fields,field_description:product_expiry.field_product_template__use_time
msgid "Best Before Time"
msgstr ""

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_stock_production_lot__use_date
msgid "Best before Date"
msgstr "Minimální trvanlivost"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.report_lot_label_expiry
msgid "Best before:"
msgstr ""

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_res_config_settings
msgid "Config Settings"
msgstr "Nastavení konfigurace"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.confirm_expiry_view
msgid "Confirm"
msgstr "Potvrdit"

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_expiry_picking_confirmation
msgid "Confirm Expiry"
msgstr "Potvrďte vypršení platnosti"

#. module: product_expiry
#: code:addons/product_expiry/models/stock_picking.py:0
#: model_terms:ir.ui.view,arch_db:product_expiry.confirm_expiry_view
#, python-format
msgid "Confirmation"
msgstr "Potvrzení"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__create_uid
msgid "Created by"
msgstr "Vytvořeno od"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__create_date
msgid "Created on"
msgstr "Vytvořeno"

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_stock_production_lot__alert_date
msgid ""
"Date to determine the expired lots and serial numbers using the filter "
"\"Expiration Alerts\"."
msgstr ""
"Datum k určení šarží a sériových čísel, jejichž platnost vypršela, pomocí "
"filtru „Upozornění na vypršení platnosti“."

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.view_move_form_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.view_product_form_expiry
msgid "Dates"
msgstr "Data"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__description
msgid "Description"
msgstr "Popis"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.confirm_expiry_view
msgid "Discard"
msgstr "Zrušit"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_res_config_settings__group_expiry_date_on_delivery_slip
msgid "Display Expiration Dates on Delivery Slips"
msgstr "Zobrazit data expirace na dodacích listech"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__display_name
#: model:ir.model.fields,field_description:product_expiry.field_procurement_group__display_name
#: model:ir.model.fields,field_description:product_expiry.field_product_product__display_name
#: model:ir.model.fields,field_description:product_expiry.field_product_template__display_name
#: model:ir.model.fields,field_description:product_expiry.field_res_config_settings__display_name
#: model:ir.model.fields,field_description:product_expiry.field_stock_move__display_name
#: model:ir.model.fields,field_description:product_expiry.field_stock_move_line__display_name
#: model:ir.model.fields,field_description:product_expiry.field_stock_picking__display_name
#: model:ir.model.fields,field_description:product_expiry.field_stock_production_lot__display_name
#: model:ir.model.fields,field_description:product_expiry.field_stock_quant__display_name
msgid "Display Name"
msgstr "Zobrazované jméno"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.quant_search_view_inherit_product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.search_product_lot_filter_inherit_product_expiry
msgid "Expiration Alerts"
msgstr "Výstražné upozornění na expiraci"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_product_product__use_expiration_date
#: model:ir.model.fields,field_description:product_expiry.field_product_template__use_expiration_date
#: model:ir.model.fields,field_description:product_expiry.field_stock_move_line__expiration_date
#: model:ir.model.fields,field_description:product_expiry.field_stock_production_lot__expiration_date
#: model:ir.model.fields,field_description:product_expiry.field_stock_quant__use_expiration_date
#: model_terms:ir.ui.view,arch_db:product_expiry.stock_report_delivery_document_inherit_product_expiry
msgid "Expiration Date"
msgstr "Datum platnosti"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_product_product__expiration_time
#: model:ir.model.fields,field_description:product_expiry.field_product_template__expiration_time
msgid "Expiration Time"
msgstr ""

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.res_config_settings_view_form_stock
msgid "Expiration dates will appear on the delivery slip"
msgstr "Datum expirace bude uvedeno na dodacím listu."

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.confirm_expiry_view
msgid "Expired Lot(s)"
msgstr ""

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_stock_production_lot__product_expiry_reminded
msgid "Expiry has been reminded"
msgstr ""

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__id
#: model:ir.model.fields,field_description:product_expiry.field_procurement_group__id
#: model:ir.model.fields,field_description:product_expiry.field_product_product__id
#: model:ir.model.fields,field_description:product_expiry.field_product_template__id
#: model:ir.model.fields,field_description:product_expiry.field_res_config_settings__id
#: model:ir.model.fields,field_description:product_expiry.field_stock_move__id
#: model:ir.model.fields,field_description:product_expiry.field_stock_move_line__id
#: model:ir.model.fields,field_description:product_expiry.field_stock_picking__id
#: model:ir.model.fields,field_description:product_expiry.field_stock_production_lot__id
#: model:ir.model.fields,field_description:product_expiry.field_stock_quant__id
msgid "ID"
msgstr "ID"

#. module: product_expiry
#: model:res.groups,name:product_expiry.group_expiry_date_on_delivery_slip
msgid "Include expiration dates on delivery slip"
msgstr "Na dodacím listu uvést datum expirace."

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation____last_update
#: model:ir.model.fields,field_description:product_expiry.field_procurement_group____last_update
#: model:ir.model.fields,field_description:product_expiry.field_product_product____last_update
#: model:ir.model.fields,field_description:product_expiry.field_product_template____last_update
#: model:ir.model.fields,field_description:product_expiry.field_res_config_settings____last_update
#: model:ir.model.fields,field_description:product_expiry.field_stock_move____last_update
#: model:ir.model.fields,field_description:product_expiry.field_stock_move_line____last_update
#: model:ir.model.fields,field_description:product_expiry.field_stock_picking____last_update
#: model:ir.model.fields,field_description:product_expiry.field_stock_production_lot____last_update
#: model:ir.model.fields,field_description:product_expiry.field_stock_quant____last_update
msgid "Last Modified on"
msgstr "Naposled změněno"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__write_uid
msgid "Last Updated by"
msgstr "Naposledy upraveno od"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__write_date
msgid "Last Updated on"
msgstr "Naposled upraveno"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__lot_ids
msgid "Lot"
msgstr "Dávka"

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_stock_production_lot
msgid "Lot/Serial"
msgstr "Šarže / seriové číslo"

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_product_product__expiration_time
#: model:ir.model.fields,help:product_expiry.field_product_template__expiration_time
msgid ""
"Number of days after the receipt of the products (from the vendor or in "
"stock after production) after which the goods may become dangerous and must "
"not be consumed. It will be computed on the lot/serial number."
msgstr ""
"Počet dní po přijetí produktů (od prodejce nebo na skladě po výrobě), po "
"kterých se zboží může stát nebezpečným a nesmí být spotřebováno. Bude "
"vypočítáno na šarži / sériovém čísle."

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_product_product__alert_time
#: model:ir.model.fields,help:product_expiry.field_product_template__alert_time
msgid ""
"Number of days before the Expiration Date after which an alert should be "
"raised on the lot/serial number. It will be computed on the lot/serial "
"number."
msgstr ""
"Počet dní před datem vypršení platnosti, po kterém by mělo být upozorněno na"
" šarži / sériové číslo. Bude vypočítáno na šarži / sériovém čísle."

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_product_product__removal_time
#: model:ir.model.fields,help:product_expiry.field_product_template__removal_time
msgid ""
"Number of days before the Expiration Date after which the goods should be "
"removed from the stock. It will be computed on the lot/serial number."
msgstr ""
"Počet dní před datem vypršení platnosti, po kterém by mělo být zboží "
"odebráno ze skladu. Bude vypočítáno na šarži / sériovém čísle."

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_product_product__use_time
#: model:ir.model.fields,help:product_expiry.field_product_template__use_time
msgid ""
"Number of days before the Expiration Date after which the goods starts "
"deteriorating, without being dangerous yet. It will be computed on the "
"lot/serial number."
msgstr ""
"Počet dní před datem vypršení platnosti, po kterém se zboží začne zhoršovat,"
" aniž by to ještě bylo nebezpečné. Bude vypočítáno na šarži / sériovém "
"čísle."

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__picking_ids
msgid "Picking"
msgstr "Dodání"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.confirm_expiry_view
msgid "Proceed except for expired one"
msgstr ""

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_procurement_group
msgid "Procurement Group"
msgstr "Skupina zásobování"

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_product_product
msgid "Product"
msgstr "Produkt"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_stock_production_lot__product_expiry_alert
msgid "Product Expiry Alert"
msgstr ""

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Pohyby produktu (trasa pohybu zboží)"

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_product_template
msgid "Product Template"
msgstr "Šablona produktu"

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_stock_quant
msgid "Quants"
msgstr "Množství"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_stock_production_lot__removal_date
#: model:ir.model.fields,field_description:product_expiry.field_stock_quant__removal_date
msgid "Removal Date"
msgstr "Datum odebrání"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_product_product__removal_time
#: model:ir.model.fields,field_description:product_expiry.field_product_template__removal_time
msgid "Removal Time"
msgstr "Čas odstranění"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__show_lots
msgid "Show Lots"
msgstr ""

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_stock_move
msgid "Stock Move"
msgstr "Pohyb zásob"

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_stock_production_lot__product_expiry_alert
msgid "The Expiration Date has been reached."
msgstr ""

#. module: product_expiry
#: code:addons/product_expiry/models/production_lot.py:0
#, python-format
msgid "The alert date has been reached for this lot/serial number"
msgstr ""

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_stock_move_line__expiration_date
#: model:ir.model.fields,help:product_expiry.field_stock_production_lot__expiration_date
msgid ""
"This is the date on which the goods with this Serial Number may become "
"dangerous and must not be consumed."
msgstr ""
"Datum, kdy se zboží s tímto seriovým číslem může stát nebezpečným a nesmí "
"být požíváno."

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_stock_production_lot__removal_date
#: model:ir.model.fields,help:product_expiry.field_stock_quant__removal_date
msgid ""
"This is the date on which the goods with this Serial Number should be "
"removed from the stock. This date will be used in FEFO removal strategy."
msgstr ""
"Toto je datum, kdy by mělo být zboží s tímto sériovým číslem odebráno ze "
"skladu. Toto datum bude použito ve strategii odstranění FEFO."

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_stock_production_lot__use_date
msgid ""
"This is the date on which the goods with this Serial Number start "
"deteriorating, without being dangerous yet."
msgstr ""
"Datum, kdy se zboží s tímto seriovým číslem začne kazit bez toho, aby ještě "
"bylo nebezpečné."

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_stock_picking
msgid "Transfer"
msgstr "Převod"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_stock_move__use_expiration_date
#: model:ir.model.fields,field_description:product_expiry.field_stock_production_lot__use_expiration_date
msgid "Use Expiration Date"
msgstr ""

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.report_lot_label_expiry
msgid "Use by:"
msgstr ""

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_product_product__use_expiration_date
#: model:ir.model.fields,help:product_expiry.field_product_template__use_expiration_date
#: model:ir.model.fields,help:product_expiry.field_stock_move__use_expiration_date
#: model:ir.model.fields,help:product_expiry.field_stock_production_lot__use_expiration_date
#: model:ir.model.fields,help:product_expiry.field_stock_quant__use_expiration_date
msgid ""
"When this box is ticked, you have the possibility to specify dates to manage"
" product expiration, on the product and on the corresponding lot/serial "
"numbers"
msgstr ""
"Když je toto políčko zaškrtnuto, máte možnost určit data pro správu vypršení"
" platnosti produktu, na produktu a na odpovídajících číslech šarží / "
"sériových čísel"

#. module: product_expiry
#: code:addons/product_expiry/wizard/confirm_expiry.py:0
#, python-format
msgid ""
"You are going to deliver some product expired lots.\n"
"Do you confirm you want to proceed ?"
msgstr ""

#. module: product_expiry
#: code:addons/product_expiry/wizard/confirm_expiry.py:0
#, python-format
msgid ""
"You are going to deliver the product %(product_name)s, %(lot_name)s which is expired.\n"
"Do you confirm you want to proceed ?"
msgstr ""
"Budete dodávat výrobek %(product_name)s, %(lot_name)s který je prošlý.\n"
"Potvrzujete, že chcete pokračovat ?"
