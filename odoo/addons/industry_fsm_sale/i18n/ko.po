# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* industry_fsm_sale
# 
# Translators:
# <PERSON>, 2020
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~13.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-09-01 07:39+0000\n"
"PO-Revision-Date: 2020-09-07 08:21+0000\n"
"Last-Translator: Linkup <<EMAIL>>, 2021\n"
"Language-Team: Korean (https://www.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: industry_fsm_sale
#: code:addons/industry_fsm_sale/models/project_task.py:0
#, python-format
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                            Create a new product\n"
"                        </p><p>\n"
"                            You must define a product for everything you sell or purchase,\n"
"                            whether it's a storable product, a consumable or a service.\n"
"                        </p>"
msgstr ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                            새 상품 만들기\n"
"                        </p><p>\n"
"                            판매 또는 구매하는 모든 제품을 정의해야합니다.\n"
"                            저장 가능한 제품이든 소모품이든 서비스 든 상관 없습니다.\n"
"                        </p>"

#. module: industry_fsm_sale
#: code:addons/industry_fsm_sale/models/project_task.py:0
#: code:addons/industry_fsm_sale/models/project_task.py:0
#, python-format
msgid "A customer should be set on the task to generate a worksheet."
msgstr ""

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
#, python-format
msgid "Add a product by clicking on it."
msgstr "클릭하여 상품을 추가하십시오."

#. module: industry_fsm_sale
#: model:ir.model.constraint,message:industry_fsm_sale.constraint_project_project_fsm_imply_task_rate
msgid "An FSM project must be billed at task rate."
msgstr "FSM 프로젝트는 작업 진행율로 청구해야 합니다."

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_project__allow_billable
msgid "Billable"
msgstr ""

#. module: industry_fsm_sale
#: code:addons/industry_fsm_sale/models/project_task.py:0
#, python-format
msgid "Choose Products"
msgstr "제품 선택"

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
#, python-format
msgid "Choose a name for your product <i>(e.g. Bolts, Screws, Boiler...).</i>"
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_res_config_settings
msgid "Config Settings"
msgstr "설정 구성"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "Create Invoice"
msgstr "청구서 만들기"

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
#, python-format
msgid "Create a new product."
msgstr ""

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_view_form_inherit
msgid "Create new quotations directly from tasks"
msgstr "작업에서 직접 새로운 견적서 만들기"

#. module: industry_fsm_sale
#: model:res.groups,name:industry_fsm_sale.group_fsm_quotation_from_task
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.res_config_settings_view_form
msgid "Create new quotations directly from the tasks"
msgstr "작업에서 직접 새로운 견적서 만들기"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__currency_id
msgid "Currency"
msgstr "환율"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__display_create_invoice_primary
msgid "Display Create Invoice Primary"
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__display_create_invoice_secondary
msgid "Display Create Invoice Secondary"
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_product_product__display_name
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_project__display_name
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__display_name
#: model:ir.model.fields,field_description:industry_fsm_sale.field_res_config_settings__display_name
#: model:ir.model.fields,field_description:industry_fsm_sale.field_sale_order__display_name
#: model:ir.model.fields,field_description:industry_fsm_sale.field_sale_order_line__display_name
msgid "Display Name"
msgstr "이름 표시"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.res_config_settings_view_form
msgid "Documentation"
msgstr "문서화"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_project__allow_quotations
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__allow_quotations
#: model:ir.model.fields,field_description:industry_fsm_sale.field_res_config_settings__group_industry_fsm_quotations
msgid "Extra Quotations"
msgstr "추가 견적"

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/fsm_product_quantity.js:0
#, python-format
msgid "FSM Product Quantity"
msgstr ""

#. module: industry_fsm_sale
#: model:product.product,name:industry_fsm_sale.field_service_product
#: model:product.template,name:industry_fsm_sale.field_service_product_product_template
msgid "Field Service"
msgstr "현장 서비스"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_product_product__id
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_project__id
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__id
#: model:ir.model.fields,field_description:industry_fsm_sale.field_res_config_settings__id
#: model:ir.model.fields,field_description:industry_fsm_sale.field_sale_order__id
#: model:ir.model.fields,field_description:industry_fsm_sale.field_sale_order_line__id
msgid "ID"
msgstr "ID"

#. module: industry_fsm_sale
#: code:addons/industry_fsm_sale/models/project_task.py:0
#, python-format
msgid "Invoice"
msgstr "청구서"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__invoice_status
msgid "Invoice Status"
msgstr "청구서 상태"

#. module: industry_fsm_sale
#: model:ir.model.fields,help:industry_fsm_sale.field_project_project__allow_billable
msgid "Invoice your time and material from tasks."
msgstr ""

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
#, python-format
msgid ""
"Invoice your time and material to your customer or ask for a down payment "
"first."
msgstr ""

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
#, python-format
msgid "Invoice your time and material to your customer."
msgstr ""

#. module: industry_fsm_sale
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.project_task_action_to_invoice_fsm
msgid ""
"Invoice your time and material to your customers once your interventions are"
" completed."
msgstr ""

#. module: industry_fsm_sale
#: code:addons/industry_fsm_sale/models/project_task.py:0
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
#, python-format
msgid "Invoices"
msgstr "청구서"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_product_product____last_update
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_project____last_update
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task____last_update
#: model:ir.model.fields,field_description:industry_fsm_sale.field_res_config_settings____last_update
#: model:ir.model.fields,field_description:industry_fsm_sale.field_sale_order____last_update
#: model:ir.model.fields,field_description:industry_fsm_sale.field_sale_order_line____last_update
msgid "Last Modified on"
msgstr "최근 수정"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__material_line_product_count
msgid "Material Line Product Count"
msgstr "재료 명세 상품 수"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__material_line_total_price
msgid "Material Line Total Price"
msgstr "재료 명세 총 금액"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_product_product__fsm_quantity
msgid "Material Quantity"
msgstr "재료 수량"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "New Quotation"
msgstr "새로운 견적서"

#. module: industry_fsm_sale
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.product_action_fsm
msgid "No products found. Let's create one!"
msgstr ""

#. module: industry_fsm_sale
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.project_task_action_to_invoice_fsm
msgid "No tasks found. Let's create one!"
msgstr "작업이 없습니다."

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/fsm_product_quantity.js:0
#, python-format
msgid "Please enter an integer value"
msgstr "정수를 입력하세요"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_product_product_kanban_material
msgid "Price:"
msgstr "가격 :"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_product_product
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_product_product_kanban_material
msgid "Product"
msgstr "상품"

#. module: industry_fsm_sale
#: model:ir.actions.act_window,name:industry_fsm_sale.product_action_fsm
#: model:ir.ui.menu,name:industry_fsm_sale.fsm_menu_settings_product
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "Products"
msgstr "상품"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_project__allow_material
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__allow_material
msgid "Products on Tasks"
msgstr "작업 제품"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_project_project
msgid "Project"
msgstr "프로젝트"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__quotation_count
msgid "Quotation Count"
msgstr "견적 수"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_task_view_form_quotation
msgid "Quotations"
msgstr "견적서"

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
#, python-format
msgid "Record the material you used for the intervention."
msgstr "중재에 사용한 자재를 기록합니다."

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_sale_order
msgid "Sales Order"
msgstr "판매 주문"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_sale_order_line
msgid "Sales Order Line"
msgstr "판매 주문 명세"

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
#, python-format
msgid "Save your product."
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_project_task
#: model:ir.model.fields,field_description:industry_fsm_sale.field_sale_order__task_id
msgid "Task"
msgstr "작업"

#. module: industry_fsm_sale
#: model:ir.model.fields,help:industry_fsm_sale.field_sale_order__task_id
msgid "Task from which quotation have been created"
msgstr "견적이 생성된 작업"

#. module: industry_fsm_sale
#: model:ir.model.constraint,message:industry_fsm_sale.constraint_project_project_material_imply_billable
msgid "The material can be allowed only when the task can be billed."
msgstr "작업에 대금을 청구할 수 있는 경우에만 자재를 사용할 수 있습니다."

#. module: industry_fsm_sale
#: model:ir.actions.act_window,name:industry_fsm_sale.project_task_action_to_invoice_fsm
#: model:ir.ui.menu,name:industry_fsm_sale.fsm_menu_all_tasks_invoice
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_task_view_search_fsm_inherit_sale
msgid "To Invoice"
msgstr "청구서 발행"

#. module: industry_fsm_sale
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.product_action_fsm
msgid "Track and reinvoice the material used to complete an intervention."
msgstr ""

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_view_form_inherit
msgid "Track the material used to complete tasks"
msgstr "작업을 완료하는 데 사용된 재료 추적"

#. module: industry_fsm_sale
#: model:product.product,uom_name:industry_fsm_sale.field_service_product
#: model:product.template,uom_name:industry_fsm_sale.field_service_product_product_template
msgid "Units"
msgstr "단위"

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
#, python-format
msgid "Use the breadcrumbs to <b>go back to your products list</b>."
msgstr ""

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
#, python-format
msgid "Use the breadcrumbs to <b>go back to your task</b>."
msgstr "사이트 이동 경로를 사용하여 <b>작업으로 돌아갑니다</b>."

#. module: industry_fsm_sale
#: code:addons/industry_fsm_sale/models/product_product.py:0
msgid ""
"You cannot have a quantity inferior to the number of items which have "
"already been delivered."
msgstr ""
