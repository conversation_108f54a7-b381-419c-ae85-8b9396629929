# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_subcontracting
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~13.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-09-01 07:29+0000\n"
"PO-Revision-Date: 2020-09-07 08:15+0000\n"
"Last-Translator: 敬雲 林 <<EMAIL>>, 2021\n"
"Language-Team: Chinese (Taiwan) (https://www.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_mrp_bom
msgid "Bill of Material"
msgstr "物料清單"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_bom__type
msgid "BoM Type"
msgstr "BoM類型"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_subcontracting_product_template_search_view
msgid "Can be Subcontracted"
msgstr "可外包"

#. module: mrp_subcontracting
#: model:ir.model.fields,help:mrp_subcontracting.field_product_supplierinfo__is_subcontractor
msgid ""
"Choose a vendor of type subcontractor if you want to subcontract the product"
msgstr "如果要轉包產品，請選擇類型委外生產商的供應商"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_res_company
msgid "Companies"
msgstr "公司"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_subcontracting_move_tree_view
msgid "Consumed"
msgstr "已投料"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_res_partner
msgid "Contact"
msgstr "聯繫人"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_production_subcontracting_form_view
msgid "Continue"
msgstr "繼續"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_production__move_line_raw_ids
msgid "Detail Component"
msgstr "組件細項"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_production_subcontracting_form_view
msgid "Discard"
msgstr "取消"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_picking__display_action_record_components
msgid "Display Action Record Components"
msgstr "顯示操作紀錄組件"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_bom__display_name
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_production__display_name
#: model:ir.model.fields,field_description:mrp_subcontracting.field_product_supplierinfo__display_name
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_company__display_name
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_partner__display_name
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_move__display_name
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_move_line__display_name
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_picking__display_name
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_return_picking__display_name
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_rule__display_name
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_warehouse__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_bom__id
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_production__id
#: model:ir.model.fields,field_description:mrp_subcontracting.field_product_supplierinfo__id
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_company__id
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_partner__id
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_move__id
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_move_line__id
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_picking__id
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_return_picking__id
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_rule__id
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_warehouse__id
msgid "ID"
msgstr "ID"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_bom____last_update
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_production____last_update
#: model:ir.model.fields,field_description:mrp_subcontracting.field_product_supplierinfo____last_update
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_company____last_update
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_partner____last_update
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_move____last_update
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_move_line____last_update
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_picking____last_update
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_return_picking____last_update
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_rule____last_update
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_warehouse____last_update
msgid "Last Modified on"
msgstr "最後修改於"

#. module: mrp_subcontracting
#: code:addons/mrp_subcontracting/models/stock_warehouse.py:0
#, python-format
msgid "Make To Order"
msgstr "接單生產"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "產品移動(移庫明細)"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_mrp_production
msgid "Production Order"
msgstr "製造訂單"

#. module: mrp_subcontracting
#: code:addons/mrp_subcontracting/models/stock_move.py:0
#, python-format
msgid "Raw Materials for %s"
msgstr "提供給%s的原材料"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_production_subcontracting_form_view
msgid "Record Production"
msgstr "記錄生產"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.stock_picking_form_view
msgid "Record components"
msgstr "記錄組件"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.stock_picking_form_view
msgid "Register components for subcontracted product"
msgstr "紀錄委外生產的組件"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_subcontracting_move_tree_view
msgid "Reserved"
msgstr "已保留"

#. module: mrp_subcontracting
#: code:addons/mrp_subcontracting/models/stock_warehouse.py:0
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_warehouse__subcontracting_route_id
#, python-format
msgid "Resupply Subcontractor"
msgstr "補充生產原料給委外生產商"

#. module: mrp_subcontracting
#: code:addons/mrp_subcontracting/models/stock_warehouse.py:0
#: model:stock.location.route,name:mrp_subcontracting.route_resupply_subcontractor_mto
#, python-format
msgid "Resupply Subcontractor on Order"
msgstr "訂單上再補充給委外生產商"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_warehouse__subcontracting_to_resupply
msgid "Resupply Subcontractors"
msgstr "補充生產原料給委外生產商"

#. module: mrp_subcontracting
#: model:ir.model.fields,help:mrp_subcontracting.field_stock_warehouse__subcontracting_to_resupply
msgid "Resupply subcontractors with components"
msgstr "向委外生產商提供原料"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_return_picking
msgid "Return Picking"
msgstr "退回揀貨"

#. module: mrp_subcontracting
#: code:addons/mrp_subcontracting/models/stock_warehouse.py:0
#, python-format
msgid "Sequence subcontracting"
msgstr "委外生產序列"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_move__show_subcontracting_details_visible
msgid "Show Subcontracting Details Visible"
msgstr "顯示委外生產詳細資訊"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_move
msgid "Stock Move"
msgstr "庫存移動"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_rule
msgid "Stock Rule"
msgstr "庫存規則"

#. module: mrp_subcontracting
#: code:addons/mrp_subcontracting/models/stock_move.py:0
#, python-format
msgid "Subcontract"
msgstr "外包生產一些產品"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_return_picking__subcontract_location_id
msgid "Subcontract Location"
msgstr "外包地點"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_product_supplierinfo__is_subcontractor
msgid "Subcontracted"
msgstr "委外生產"

#. module: mrp_subcontracting
#: code:addons/mrp_subcontracting/models/stock_warehouse.py:0
#: model:ir.model.fields.selection,name:mrp_subcontracting.selection__mrp_bom__type__subcontract
#, python-format
msgid "Subcontracting"
msgstr "委外生產"

#. module: mrp_subcontracting
#: code:addons/mrp_subcontracting/models/res_company.py:0
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_company__subcontracting_location_id
#, python-format
msgid "Subcontracting Location"
msgstr "委外生產位置"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_warehouse__subcontracting_mto_pull_id
msgid "Subcontracting MTO Rule"
msgstr "委外生產MTO規則"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_warehouse__subcontracting_pull_id
msgid "Subcontracting MTS Rule"
msgstr "委外生產MTS規則"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_warehouse__subcontracting_type_id
msgid "Subcontracting Operation Type"
msgstr "委外生產作業類型"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_partner__is_subcontractor
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_users__is_subcontractor
msgid "Subcontractor"
msgstr "分包商"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_partner__property_stock_subcontractor
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_users__property_stock_subcontractor
msgid "Subcontractor Location"
msgstr "委外生產商位置"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_bom__subcontractor_ids
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.view_partner_mrp_subcontracting_filter
msgid "Subcontractors"
msgstr "委外生產商"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_product_supplierinfo
msgid "Supplier Pricelist"
msgstr "供應商價格表"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_move__is_subcontract
msgid "The move is a subcontract receipt"
msgstr "調撥單據是委外生產收據"

#. module: mrp_subcontracting
#: model:ir.model.fields,help:mrp_subcontracting.field_res_partner__property_stock_subcontractor
#: model:ir.model.fields,help:mrp_subcontracting.field_res_users__property_stock_subcontractor
msgid ""
"The stock location used as source and destination when sending        goods "
"to this contact during a subcontracting process."
msgstr "在委外生產過程中向此連絡人發送貨物時用作來源和目的地的庫存位置。"

#. module: mrp_subcontracting
#: code:addons/mrp_subcontracting/models/stock_move.py:0
#, python-format
msgid "To subcontract, use a planned transfer."
msgstr "為委外生產，使用計畫調撥"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_subcontracting_move_form_view
msgid "Total Consumed"
msgstr "總耗料"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_picking
msgid "Transfer"
msgstr "調撥"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_warehouse
msgid "Warehouse"
msgstr "倉庫"

#. module: mrp_subcontracting
#: code:addons/mrp_subcontracting/models/mrp_bom.py:0
#, python-format
msgid "You can not set a Bill of Material with operations as subcontracting."
msgstr ""

#. module: mrp_subcontracting
#: code:addons/mrp_subcontracting/models/mrp_production.py:0
#, python-format
msgid "You must enter a serial number for %s"
msgstr ""

#. module: mrp_subcontracting
#: code:addons/mrp_subcontracting/models/mrp_production.py:0
#, python-format
msgid "You must enter a serial number for each line of %s"
msgstr "您必須為 %s 的每一行輸入一個序號"
