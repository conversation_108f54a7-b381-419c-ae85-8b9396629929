# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_subcontracting
# 
# Translators:
# <PERSON>, 2021
# o<PERSON><PERSON> <oihane<PERSON><PERSON><EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# 61590936fa9bf290362ee306eeabf363_944dd10 <a8bfd5a0b49b9c8455f33fc521764cc3_680674>, 2021
# <PERSON> <blas<PERSON>@binovo.es>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~13.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-09-01 07:29+0000\n"
"PO-Revision-Date: 2020-09-07 08:15+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2021\n"
"Language-Team: Basque (https://www.transifex.com/odoo/teams/41243/eu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: eu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_mrp_bom
msgid "Bill of Material"
msgstr "Materialen zerrenda"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_bom__type
msgid "BoM Type"
msgstr ""

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_subcontracting_product_template_search_view
msgid "Can be Subcontracted"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model.fields,help:mrp_subcontracting.field_product_supplierinfo__is_subcontractor
msgid ""
"Choose a vendor of type subcontractor if you want to subcontract the product"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_res_company
msgid "Companies"
msgstr "Enpresak"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_subcontracting_move_tree_view
msgid "Consumed"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_res_partner
msgid "Contact"
msgstr "Kontaktua"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_production_subcontracting_form_view
msgid "Continue"
msgstr "Jarraitu"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_production__move_line_raw_ids
msgid "Detail Component"
msgstr ""

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_production_subcontracting_form_view
msgid "Discard"
msgstr "Baztertu"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_picking__display_action_record_components
msgid "Display Action Record Components"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_bom__display_name
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_production__display_name
#: model:ir.model.fields,field_description:mrp_subcontracting.field_product_supplierinfo__display_name
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_company__display_name
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_partner__display_name
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_move__display_name
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_move_line__display_name
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_picking__display_name
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_return_picking__display_name
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_rule__display_name
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_warehouse__display_name
msgid "Display Name"
msgstr "Izena erakutsi"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_bom__id
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_production__id
#: model:ir.model.fields,field_description:mrp_subcontracting.field_product_supplierinfo__id
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_company__id
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_partner__id
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_move__id
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_move_line__id
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_picking__id
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_return_picking__id
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_rule__id
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_warehouse__id
msgid "ID"
msgstr "ID"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_bom____last_update
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_production____last_update
#: model:ir.model.fields,field_description:mrp_subcontracting.field_product_supplierinfo____last_update
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_company____last_update
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_partner____last_update
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_move____last_update
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_move_line____last_update
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_picking____last_update
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_return_picking____last_update
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_rule____last_update
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_warehouse____last_update
msgid "Last Modified on"
msgstr "Azken aldaketa"

#. module: mrp_subcontracting
#: code:addons/mrp_subcontracting/models/stock_warehouse.py:0
#, python-format
msgid "Make To Order"
msgstr "Eskariaren arabera egin"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_mrp_production
msgid "Production Order"
msgstr ""

#. module: mrp_subcontracting
#: code:addons/mrp_subcontracting/models/stock_move.py:0
#, python-format
msgid "Raw Materials for %s"
msgstr ""

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_production_subcontracting_form_view
msgid "Record Production"
msgstr ""

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.stock_picking_form_view
msgid "Record components"
msgstr ""

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.stock_picking_form_view
msgid "Register components for subcontracted product"
msgstr ""

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_subcontracting_move_tree_view
msgid "Reserved"
msgstr "Erreserbatua"

#. module: mrp_subcontracting
#: code:addons/mrp_subcontracting/models/stock_warehouse.py:0
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_warehouse__subcontracting_route_id
#, python-format
msgid "Resupply Subcontractor"
msgstr ""

#. module: mrp_subcontracting
#: code:addons/mrp_subcontracting/models/stock_warehouse.py:0
#: model:stock.location.route,name:mrp_subcontracting.route_resupply_subcontractor_mto
#, python-format
msgid "Resupply Subcontractor on Order"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_warehouse__subcontracting_to_resupply
msgid "Resupply Subcontractors"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model.fields,help:mrp_subcontracting.field_stock_warehouse__subcontracting_to_resupply
msgid "Resupply subcontractors with components"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_return_picking
msgid "Return Picking"
msgstr "Itzulpen albarana"

#. module: mrp_subcontracting
#: code:addons/mrp_subcontracting/models/stock_warehouse.py:0
#, python-format
msgid "Sequence subcontracting"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_move__show_subcontracting_details_visible
msgid "Show Subcontracting Details Visible"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_move
msgid "Stock Move"
msgstr "Izakinen mugimendua"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_rule
msgid "Stock Rule"
msgstr ""

#. module: mrp_subcontracting
#: code:addons/mrp_subcontracting/models/stock_move.py:0
#, python-format
msgid "Subcontract"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_return_picking__subcontract_location_id
msgid "Subcontract Location"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_product_supplierinfo__is_subcontractor
msgid "Subcontracted"
msgstr ""

#. module: mrp_subcontracting
#: code:addons/mrp_subcontracting/models/stock_warehouse.py:0
#: model:ir.model.fields.selection,name:mrp_subcontracting.selection__mrp_bom__type__subcontract
#, python-format
msgid "Subcontracting"
msgstr ""

#. module: mrp_subcontracting
#: code:addons/mrp_subcontracting/models/res_company.py:0
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_company__subcontracting_location_id
#, python-format
msgid "Subcontracting Location"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_warehouse__subcontracting_mto_pull_id
msgid "Subcontracting MTO Rule"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_warehouse__subcontracting_pull_id
msgid "Subcontracting MTS Rule"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_warehouse__subcontracting_type_id
msgid "Subcontracting Operation Type"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_partner__is_subcontractor
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_users__is_subcontractor
msgid "Subcontractor"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_partner__property_stock_subcontractor
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_users__property_stock_subcontractor
msgid "Subcontractor Location"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_bom__subcontractor_ids
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.view_partner_mrp_subcontracting_filter
msgid "Subcontractors"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_product_supplierinfo
msgid "Supplier Pricelist"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_move__is_subcontract
msgid "The move is a subcontract receipt"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model.fields,help:mrp_subcontracting.field_res_partner__property_stock_subcontractor
#: model:ir.model.fields,help:mrp_subcontracting.field_res_users__property_stock_subcontractor
msgid ""
"The stock location used as source and destination when sending        goods "
"to this contact during a subcontracting process."
msgstr ""

#. module: mrp_subcontracting
#: code:addons/mrp_subcontracting/models/stock_move.py:0
#, python-format
msgid "To subcontract, use a planned transfer."
msgstr ""

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_subcontracting_move_form_view
msgid "Total Consumed"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_picking
msgid "Transfer"
msgstr "Transferetzia"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_warehouse
msgid "Warehouse"
msgstr "Biltegia"

#. module: mrp_subcontracting
#: code:addons/mrp_subcontracting/models/mrp_bom.py:0
#, python-format
msgid "You can not set a Bill of Material with operations as subcontracting."
msgstr ""

#. module: mrp_subcontracting
#: code:addons/mrp_subcontracting/models/mrp_production.py:0
#, python-format
msgid "You must enter a serial number for %s"
msgstr ""

#. module: mrp_subcontracting
#: code:addons/mrp_subcontracting/models/mrp_production.py:0
#, python-format
msgid "You must enter a serial number for each line of %s"
msgstr ""
