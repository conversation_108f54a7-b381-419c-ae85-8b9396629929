# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_workorder_iot
# 
# Translators:
# <PERSON>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# <AUTHOR> <EMAIL>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON>, 2020
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-09 11:58+0000\n"
"PO-Revision-Date: 2020-09-07 08:22+0000\n"
"Last-Translator: <PERSON><PERSON>, 2020\n"
"Language-Team: Danish (https://www.transifex.com/odoo/teams/41243/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_trigger__action
msgid "Action"
msgstr "Handling"

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_mrp_workorder__boxes
msgid "Boxes"
msgstr "Kasser"

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__cancel
msgid "Cancel"
msgstr "Annullér"

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__clomo
msgid "Close MO"
msgstr "Luk MO"

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__clowo
msgid "Close WO"
msgstr "Luk WO"

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_trigger__create_uid
msgid "Created by"
msgstr "Oprettet af"

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_trigger__create_date
msgid "Created on"
msgstr "Oprettet den"

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_trigger__device_id
msgid "Device"
msgstr "Enhed"

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_mrp_workorder__device_name
msgid "Device Name: "
msgstr "Enhedsnavn:"

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_trigger__display_name
msgid "Display Name"
msgstr "Vis navn"

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_mrp_workorder__ip
msgid "Domain Address"
msgstr "Domæne adresse"

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__finish
msgid "Finish"
msgstr "Afslut"

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_trigger__id
msgid "ID"
msgstr "ID"

#. module: mrp_workorder_iot
#: model:ir.model,name:mrp_workorder_iot.model_iot_device
msgid "IOT Device"
msgstr "IoT Enhed"

#. module: mrp_workorder_iot
#: model:ir.model,name:mrp_workorder_iot.model_iot_trigger
msgid "IOT Trigger"
msgstr "IOT Udløser"

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_mrp_workorder__identifier
msgid "Identifier"
msgstr "Identifikator"

#. module: mrp_workorder_iot
#: model_terms:ir.ui.view,arch_db:mrp_workorder_iot.iot_device_view_form
#: model_terms:ir.ui.view,arch_db:mrp_workorder_iot.mrp_workcenter_view_form_iot
msgid "IoT Triggers"
msgstr "IoT Udløsere"

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_trigger__key
msgid "Key"
msgstr "Nøgle"

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_trigger____last_update
msgid "Last Modified on"
msgstr "Sidst ændret den"

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_trigger__write_uid
msgid "Last Updated by"
msgstr "Sidst opdateret af"

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_trigger__write_date
msgid "Last Updated on"
msgstr "Sidst opdateret den"

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__next
msgid "Next"
msgstr "Næste"

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__pack
msgid "Pack"
msgstr "Pakke"

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__pause
msgid "Pause"
msgstr "Pause"

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__prev
msgid "Previous"
msgstr "Forrige"

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__print-slip
msgid "Print Delivery Slip"
msgstr "Udprint leverings seddel"

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__print
msgid "Print Labels"
msgstr "Print mærkater"

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__print-op
msgid "Print Operation"
msgstr "Print operation"

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__record
msgid "Record Production"
msgstr "Optag produktion"

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__scrap
msgid "Scrap"
msgstr "Skrot"

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_trigger__sequence
msgid "Sequence"
msgstr "Sekvens"

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__skip
msgid "Skip"
msgstr "Spring over"

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__picture
msgid "Take Picture"
msgstr "Tag billede"

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_device__trigger_ids
msgid "Trigger"
msgstr "Trigger"

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_mrp_workcenter__trigger_ids
msgid "Triggers"
msgstr "Udløsere"

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__validate
msgid "Validate"
msgstr "Validér"

#. module: mrp_workorder_iot
#: model:ir.model,name:mrp_workorder_iot.model_mrp_workcenter
msgid "Work Center"
msgstr "Arbejdscenter"

#. module: mrp_workorder_iot
#: model:ir.model,name:mrp_workorder_iot.model_mrp_workorder
msgid "Work Order"
msgstr "Produktionsordre"

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_trigger__workcenter_id
msgid "Workcenter"
msgstr "Workcenter"
