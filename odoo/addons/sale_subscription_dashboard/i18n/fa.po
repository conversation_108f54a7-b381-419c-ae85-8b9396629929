# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_subscription_dashboard
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON>, 2020
# <PERSON><PERSON>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~13.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-09-01 07:40+0000\n"
"PO-Revision-Date: 2020-09-07 08:23+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2021\n"
"Language-Team: Persian (https://www.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: sale_subscription_dashboard
#: code:addons/sale_subscription_dashboard/controllers/stat_types.py:0
#, python-format
msgid "# Subscriptions"
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "# of Subscriptions"
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "1 month ago"
msgstr "1 ماه قبل"

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "12 months ago"
msgstr "12 ماه قبل"

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "3 months ago"
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "30 Days Ago"
msgstr ""

#. module: sale_subscription_dashboard
#: model_terms:ir.ui.view,arch_db:sale_subscription_dashboard.move_line_entries_report_search
msgid "Active invoices"
msgstr ""

#. module: sale_subscription_dashboard
#: code:addons/sale_subscription_dashboard/controllers/stat_types.py:0
#, python-format
msgid "Annual Run-Rate"
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "Apply"
msgstr "اعمال"

#. module: sale_subscription_dashboard
#: code:addons/sale_subscription_dashboard/controllers/stat_types.py:0
#, python-format
msgid ""
"Average revenue of a subscription, obtained by dividing the MRR by the "
"number of subscriptions"
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "Categories"
msgstr "دسته‌ها"

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/js/sale_subscription_dashboard.js:0
#: code:addons/sale_subscription_dashboard/static/src/js/sale_subscription_dashboard.js:0
#, python-format
msgid "Churned MRR"
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "Companies"
msgstr "شرکت‌ها"

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "Company"
msgstr "شرکت"

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "Current"
msgstr "جاری"

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "Current MRR"
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/js/sale_subscription_dashboard.js:0
#: code:addons/sale_subscription_dashboard/static/src/js/sale_subscription_dashboard.js:0
#, python-format
msgid "Current Value: "
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "Custom"
msgstr "دلخواه"

#. module: sale_subscription_dashboard
#: code:addons/sale_subscription_dashboard/controllers/stat_types.py:0
#, python-format
msgid "Customer Churn"
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "Date"
msgstr "تاریخ"

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/js/sale_subscription_dashboard.js:0
#, python-format
msgid "Date cannot be empty"
msgstr "تاریخ نمی‌تواند خالی باشد"

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "Dates"
msgstr "تاریخ ها"

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "Delta"
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "Detailed Analysis"
msgstr ""

#. module: sale_subscription_dashboard
#: model:ir.actions.client,name:sale_subscription_dashboard.action_subscription_dashboard_report_detailed
msgid "Detailed KPI"
msgstr ""

#. module: sale_subscription_dashboard
#: model:ir.model.fields,field_description:sale_subscription_dashboard.field_sale_subscription__display_name
msgid "Display Name"
msgstr "نام نمایشی"

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "Document"
msgstr "سند"

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/js/sale_subscription_dashboard.js:0
#: code:addons/sale_subscription_dashboard/static/src/js/sale_subscription_dashboard.js:0
#, python-format
msgid "Down MRR"
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "End Date :"
msgstr "تاریخ انتها :"

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/js/sale_subscription_dashboard.js:0
#: code:addons/sale_subscription_dashboard/static/src/js/sale_subscription_dashboard.js:0
#, python-format
msgid "Expansion MRR"
msgstr ""

#. module: sale_subscription_dashboard
#: code:addons/sale_subscription_dashboard/controllers/stat_types.py:0
#, python-format
msgid ""
"Expected lifetime revenue of an average subscription; obtained by dividing "
"the average MRR of a subscription by the churn rate (e.g. if your average "
"MRR is $ 100 and your churn rate is 5%, the LTV will be $ 100/5% = $ 2,000)"
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "Exponential"
msgstr ""

#. module: sale_subscription_dashboard
#: code:addons/sale_subscription_dashboard/controllers/stat_types.py:0
#, python-format
msgid "Forecasted Annual MRR Growth"
msgstr ""

#. module: sale_subscription_dashboard
#: code:addons/sale_subscription_dashboard/controllers/stat_types.py:0
#, python-format
msgid "Forecasted Annual Subscriptions Growth"
msgstr ""

#. module: sale_subscription_dashboard
#: model:ir.actions.client,name:sale_subscription_dashboard.action_subscription_dashboard_report_forecast
msgid "Forecasted KPIs"
msgstr ""

#. module: sale_subscription_dashboard
#: model_terms:ir.actions.act_window,help:sale_subscription_dashboard.action_move_line_entries_report
msgid ""
"From this view, have an analysis of your different invoice line entries."
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "From:"
msgstr "از:"

#. module: sale_subscription_dashboard
#: model_terms:ir.ui.view,arch_db:sale_subscription_dashboard.move_line_entries_report_search
msgid "Group By"
msgstr "دسته بندی بر مبنای"

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "Growth Type:"
msgstr ""

#. module: sale_subscription_dashboard
#: model:ir.model.fields,field_description:sale_subscription_dashboard.field_sale_subscription__id
msgid "ID"
msgstr "شناسه"

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "In"
msgstr "در"

#. module: sale_subscription_dashboard
#: model:ir.actions.act_window,name:sale_subscription_dashboard.action_move_line_entries_report
#: model_terms:ir.ui.view,arch_db:sale_subscription_dashboard.move_line_entries_report_pivot
#: model_terms:ir.ui.view,arch_db:sale_subscription_dashboard.move_line_entries_report_search
msgid "Invoice Line Entries Analysis"
msgstr ""

#. module: sale_subscription_dashboard
#: code:addons/sale_subscription_dashboard/controllers/revenue_kpis_dashboard.py:0
#, python-format
msgid ""
"It makes no sense to sum MRR of different currencies. Please select "
"companies with the same currency."
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "Last Financial Year"
msgstr "سال مالی گذشته"

#. module: sale_subscription_dashboard
#: model:ir.model.fields,field_description:sale_subscription_dashboard.field_sale_subscription____last_update
msgid "Last Modified on"
msgstr "آخرین تغییر در"

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "Last Month"
msgstr "ماه گذشته"

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "Last Quarter"
msgstr "فصل گذشته"

#. module: sale_subscription_dashboard
#: code:addons/sale_subscription_dashboard/controllers/stat_types.py:0
#, python-format
msgid "Lifetime Value"
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "Linear"
msgstr "خطی"

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid ""
"List all invoices containing non-recurring sales and the amount of non-recurring\n"
"                    revenues in these invoices for this particular salesperson."
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "Loading..."
msgstr "بارگذاری..."

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/js/sale_subscription_dashboard.js:0
#, python-format
msgid "MRR Growth"
msgstr ""

#. module: sale_subscription_dashboard
#: code:addons/sale_subscription_dashboard/controllers/stat_types.py:0
#, python-format
msgid ""
"MRR for short; total subscription revenue per month (e.g. for an annual "
"subscription of $ 1,200, the MRR is $ 100)"
msgstr ""

#. module: sale_subscription_dashboard
#: code:addons/sale_subscription_dashboard/controllers/stat_types.py:0
#, python-format
msgid "Monthly Recurring Revenue"
msgstr "درآمد مکرر ماهیانه"

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "Monthly Recurring Revenue :"
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "Monthly Recurring Revenue Growth"
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "Months"
msgstr "ماه‌ها"

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/js/sale_subscription_dashboard.js:0
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "NRR"
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/js/sale_subscription_dashboard.js:0
#: code:addons/sale_subscription_dashboard/static/src/js/sale_subscription_dashboard.js:0
#, python-format
msgid "Net New MRR"
msgstr ""

#. module: sale_subscription_dashboard
#: code:addons/sale_subscription_dashboard/controllers/stat_types.py:0
#, python-format
msgid "Net Revenue"
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/js/sale_subscription_dashboard.js:0
#: code:addons/sale_subscription_dashboard/static/src/js/sale_subscription_dashboard.js:0
#, python-format
msgid "New MRR"
msgstr ""

#. module: sale_subscription_dashboard
#: code:addons/sale_subscription_dashboard/controllers/revenue_kpis_dashboard.py:0
#, python-format
msgid "No company selected."
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "Non Recurring invoices"
msgstr ""

#. module: sale_subscription_dashboard
#: code:addons/sale_subscription_dashboard/controllers/stat_types.py:0
#, python-format
msgid "Non-Recurring Revenue"
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "Non-Recurring Revenue :"
msgstr ""

#. module: sale_subscription_dashboard
#: code:addons/sale_subscription_dashboard/controllers/stat_types.py:0
#, python-format
msgid "Number of contracts"
msgstr ""

#. module: sale_subscription_dashboard
#: code:addons/sale_subscription_dashboard/controllers/stat_types.py:0
#, python-format
msgid "Number of subscriptions that gets closed during a period"
msgstr ""

#. module: sale_subscription_dashboard
#: model_terms:ir.ui.view,arch_db:sale_subscription_dashboard.move_line_entries_report_search
msgid "OUT invoices"
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/js/sale_subscription_dashboard.js:0
#, python-format
msgid "Odoo Warning"
msgstr "هشدار اودو"

#. module: sale_subscription_dashboard
#: code:addons/sale_subscription_dashboard/controllers/stat_types.py:0
#, python-format
msgid "One-shot revenue that is not part of a subscription"
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "PRINT PREVIEW"
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "Partner"
msgstr "همکار"

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "Pick a salesman and a period"
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "Plan"
msgstr "پلان"

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "Previous MRR"
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "Projection Time:"
msgstr ""

#. module: sale_subscription_dashboard
#: code:addons/sale_subscription_dashboard/controllers/stat_types.py:0
#, python-format
msgid "Reduction in total MRR over the period"
msgstr ""

#. module: sale_subscription_dashboard
#: code:addons/sale_subscription_dashboard/controllers/stat_types.py:0
#, python-format
msgid "Revenue Churn"
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "Revenue Churn:"
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "Revenue Growth:"
msgstr ""

#. module: sale_subscription_dashboard
#: model:ir.actions.client,name:sale_subscription_dashboard.action_subscription_dashboard_report_main
#: model:ir.ui.menu,name:sale_subscription_dashboard.menu_action_subscription_dashboard_report_main
msgid "Revenue KPIs"
msgstr ""

#. module: sale_subscription_dashboard
#: code:addons/sale_subscription_dashboard/controllers/stat_types.py:0
#, python-format
msgid "Revenue per Subscription"
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "Sales Team"
msgstr "تیم فروش"

#. module: sale_subscription_dashboard
#: model:ir.actions.client,name:sale_subscription_dashboard.action_subscription_dashboard_report_salesman
#: model:ir.ui.menu,name:sale_subscription_dashboard.menu_action_subscription_dashboard_report_salesman
msgid "Salesperson Dashboard"
msgstr ""

#. module: sale_subscription_dashboard
#: code:addons/sale_subscription_dashboard/models/sale_subscription.py:0
#, python-format
msgid "Salesperson report"
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "Sample data"
msgstr "داده نمونه"

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/js/sale_subscription_dashboard.js:0
#, python-format
msgid "Select at least one company."
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "Start Date :"
msgstr "تاریخ شروع:"

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "Starting MRR:"
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "Starting Subscription:"
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#: model:ir.model,name:sale_subscription_dashboard.model_sale_subscription
#, python-format
msgid "Subscription"
msgstr "عضویت"

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "Subscription Modifications"
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid ""
"Subscription modifications aggregate all modifications that happened in selected\n"
"                    period. Note that modifications happening on the same day can be grouped together\n"
"                    (e.g. two upsells occuring on the same day, or a subscription getting closed and re-opened on the\n"
"                    same day)."
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "Subscriptions"
msgstr "اشتراک ها"

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "Subscriptions Churn:"
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "Subscriptions Growth:"
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "Template"
msgstr "قالب"

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid ""
"The current value is measured at the end of the selected period and "
"historical data are relative to this last value."
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid ""
"The following report relies on a previous calculation method. Results may "
"differ from the new method."
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "The following values are measured at the end of the selected period."
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "There is no invoice during this period."
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "There is no subscription modification during this period."
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "This Financial Year"
msgstr "سال مال"

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "This Month"
msgstr "این ماه"

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "This Quarter"
msgstr "این فصل"

#. module: sale_subscription_dashboard
#: code:addons/sale_subscription_dashboard/controllers/stat_types.py:0
#, python-format
msgid "Total net revenue (all invoices emitted during the period)"
msgstr ""

#. module: sale_subscription_dashboard
#: code:addons/sale_subscription_dashboard/controllers/stat_types.py:0
#: code:addons/sale_subscription_dashboard/controllers/stat_types.py:0
#, python-format
msgid ""
"Total subscription revenue per month (e.g. for an annual subscription of $ "
"1,200, the MRR is $ 100)"
msgstr ""

#. module: sale_subscription_dashboard
#: code:addons/sale_subscription_dashboard/controllers/revenue_kpis_dashboard.py:0
#, python-format
msgid "Unknown error"
msgstr "خطای ناشناخته"

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "Update"
msgstr "بروزرسانی"

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/js/sale_subscription_dashboard.js:0
#: code:addons/sale_subscription_dashboard/static/src/js/sale_subscription_dashboard.js:0
#, python-format
msgid "Warning"
msgstr "هشدار"

#. module: sale_subscription_dashboard
#: code:addons/sale_subscription_dashboard/controllers/stat_types.py:0
#, python-format
msgid "Yearly version of the MRR, obtained by multiplying the MRR by 12"
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "churn, your MRR will be"
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "churn, your subscription base will be"
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "growth and"
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "months with"
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "subscription growth and"
msgstr ""

#. module: sale_subscription_dashboard
#. openerp-web
#: code:addons/sale_subscription_dashboard/static/src/xml/sale_subscription_dashboard.xml:0
#, python-format
msgid "to:"
msgstr "به:"
