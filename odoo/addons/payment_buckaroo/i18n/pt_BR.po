# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_buckaroo
# 
# Translators:
# <PERSON> <<EMAIL>>, 2020
# <PERSON>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# <AUTHOR> <EMAIL>, 2020
# <PERSON> <<EMAIL>>, 2020
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~13.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-09-01 07:28+0000\n"
"PO-Revision-Date: 2020-09-07 08:15+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2020\n"
"Language-Team: Portuguese (Brazil) (https://www.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: payment_buckaroo
#: code:addons/payment_buckaroo/models/payment.py:0
#, python-format
msgid "; multiple order found"
msgstr "; múltiplas ordens encontradas"

#. module: payment_buckaroo
#: code:addons/payment_buckaroo/models/payment.py:0
#, python-format
msgid "; no order found"
msgstr "; nenhuma ordem encontrada"

#. module: payment_buckaroo
#: model:ir.model.fields.selection,name:payment_buckaroo.selection__payment_acquirer__provider__buckaroo
msgid "Buckaroo"
msgstr "Buckaroo"

#. module: payment_buckaroo
#: code:addons/payment_buckaroo/models/payment.py:0
#, python-format
msgid "Buckaroo: invalid shasign, received %s, computed %s, for data %s"
msgstr "Buckaroo: shasign inválida, recebido %s, processado %s, para %s"

#. module: payment_buckaroo
#: code:addons/payment_buckaroo/models/payment.py:0
#, python-format
msgid "Buckaroo: received data for reference %s"
msgstr "Buckaroo: dados recebidos para referência %s"

#. module: payment_buckaroo
#: code:addons/payment_buckaroo/models/payment.py:0
#, python-format
msgid ""
"Buckaroo: received data with missing reference (%s) or pay_id (%s) or "
"shasign (%s)"
msgstr ""
"Buckaroo: dados recebidos com referência (%s) faltando ou pay_id (%s) ou "
"shasign (%s)"

#. module: payment_buckaroo
#: model:ir.model.fields,field_description:payment_buckaroo.field_payment_acquirer__display_name
#: model:ir.model.fields,field_description:payment_buckaroo.field_payment_transaction__display_name
msgid "Display Name"
msgstr "Nome exibido"

#. module: payment_buckaroo
#: model:ir.model.fields,field_description:payment_buckaroo.field_payment_acquirer__id
#: model:ir.model.fields,field_description:payment_buckaroo.field_payment_transaction__id
msgid "ID"
msgstr "ID"

#. module: payment_buckaroo
#: model:ir.model.fields,field_description:payment_buckaroo.field_payment_acquirer____last_update
#: model:ir.model.fields,field_description:payment_buckaroo.field_payment_transaction____last_update
msgid "Last Modified on"
msgstr "Última modificação em"

#. module: payment_buckaroo
#: model:ir.model,name:payment_buckaroo.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Método de Pagamento"

#. module: payment_buckaroo
#: model:ir.model,name:payment_buckaroo.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transação do Pagamento"

#. module: payment_buckaroo
#: model:ir.model.fields,field_description:payment_buckaroo.field_payment_acquirer__provider
msgid "Provider"
msgstr "Fornecedor"

#. module: payment_buckaroo
#: model:ir.model.fields,field_description:payment_buckaroo.field_payment_acquirer__brq_secretkey
msgid "SecretKey"
msgstr "SecretKey"

#. module: payment_buckaroo
#: model:ir.model.fields,field_description:payment_buckaroo.field_payment_acquirer__brq_websitekey
msgid "WebsiteKey"
msgstr "WebsiteKey"
