# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_buckaroo
# 
# Translators:
# <PERSON>, 2020
# <AUTHOR> <EMAIL>, 2020
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~13.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-09-01 07:28+0000\n"
"PO-Revision-Date: 2020-09-07 08:15+0000\n"
"Last-Translator: 敬雲 林 <<EMAIL>>, 2020\n"
"Language-Team: Chinese (Taiwan) (https://www.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: payment_buckaroo
#: code:addons/payment_buckaroo/models/payment.py:0
#, python-format
msgid "; multiple order found"
msgstr "; 找到多個訂單"

#. module: payment_buckaroo
#: code:addons/payment_buckaroo/models/payment.py:0
#, python-format
msgid "; no order found"
msgstr "; 沒有找到訂單"

#. module: payment_buckaroo
#: model:ir.model.fields.selection,name:payment_buckaroo.selection__payment_acquirer__provider__buckaroo
msgid "Buckaroo"
msgstr "Buckaroo"

#. module: payment_buckaroo
#: code:addons/payment_buckaroo/models/payment.py:0
#, python-format
msgid "Buckaroo: invalid shasign, received %s, computed %s, for data %s"
msgstr "Buckaroo: 錯誤 shasign，接收 %s, 計算 %s, 數據 %s"

#. module: payment_buckaroo
#: code:addons/payment_buckaroo/models/payment.py:0
#, python-format
msgid "Buckaroo: received data for reference %s"
msgstr "Buckaroo: 接收到的數據供參照 %s"

#. module: payment_buckaroo
#: code:addons/payment_buckaroo/models/payment.py:0
#, python-format
msgid ""
"Buckaroo: received data with missing reference (%s) or pay_id (%s) or "
"shasign (%s)"
msgstr "Buckaroo: 接收到的數據缺失參照 (%s) 或 pay_id (%s) 或 shasign (%s)"

#. module: payment_buckaroo
#: model:ir.model.fields,field_description:payment_buckaroo.field_payment_acquirer__display_name
#: model:ir.model.fields,field_description:payment_buckaroo.field_payment_transaction__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: payment_buckaroo
#: model:ir.model.fields,field_description:payment_buckaroo.field_payment_acquirer__id
#: model:ir.model.fields,field_description:payment_buckaroo.field_payment_transaction__id
msgid "ID"
msgstr "ID"

#. module: payment_buckaroo
#: model:ir.model.fields,field_description:payment_buckaroo.field_payment_acquirer____last_update
#: model:ir.model.fields,field_description:payment_buckaroo.field_payment_transaction____last_update
msgid "Last Modified on"
msgstr "最後修改於"

#. module: payment_buckaroo
#: model:ir.model,name:payment_buckaroo.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "付款收單方"

#. module: payment_buckaroo
#: model:ir.model,name:payment_buckaroo.model_payment_transaction
msgid "Payment Transaction"
msgstr "付款交易"

#. module: payment_buckaroo
#: model:ir.model.fields,field_description:payment_buckaroo.field_payment_acquirer__provider
msgid "Provider"
msgstr "服務商"

#. module: payment_buckaroo
#: model:ir.model.fields,field_description:payment_buckaroo.field_payment_acquirer__brq_secretkey
msgid "SecretKey"
msgstr "安全密鑰"

#. module: payment_buckaroo
#: model:ir.model.fields,field_description:payment_buckaroo.field_payment_acquirer__brq_websitekey
msgid "WebsiteKey"
msgstr "網站密鑰"
