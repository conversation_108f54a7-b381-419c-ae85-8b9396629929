# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_buckaroo
# 
# Translators:
# <PERSON>, 2020
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <AUTHOR> <EMAIL>, 2020
# Ikh<PERSON><PERSON> <<EMAIL>>, 2020
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~13.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-09-01 07:28+0000\n"
"PO-Revision-Date: 2020-09-07 08:15+0000\n"
"Last-Translator: Ikh<PERSON><PERSON> W<PERSON> <<EMAIL>>, 2020\n"
"Language-Team: Indonesian (https://www.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: payment_buckaroo
#: code:addons/payment_buckaroo/models/payment.py:0
#, python-format
msgid "; multiple order found"
msgstr "; beberapa pesanan ditemukan"

#. module: payment_buckaroo
#: code:addons/payment_buckaroo/models/payment.py:0
#, python-format
msgid "; no order found"
msgstr ""

#. module: payment_buckaroo
#: model:ir.model.fields.selection,name:payment_buckaroo.selection__payment_acquirer__provider__buckaroo
msgid "Buckaroo"
msgstr ""

#. module: payment_buckaroo
#: code:addons/payment_buckaroo/models/payment.py:0
#, python-format
msgid "Buckaroo: invalid shasign, received %s, computed %s, for data %s"
msgstr ""

#. module: payment_buckaroo
#: code:addons/payment_buckaroo/models/payment.py:0
#, python-format
msgid "Buckaroo: received data for reference %s"
msgstr ""

#. module: payment_buckaroo
#: code:addons/payment_buckaroo/models/payment.py:0
#, python-format
msgid ""
"Buckaroo: received data with missing reference (%s) or pay_id (%s) or "
"shasign (%s)"
msgstr ""

#. module: payment_buckaroo
#: model:ir.model.fields,field_description:payment_buckaroo.field_payment_acquirer__display_name
#: model:ir.model.fields,field_description:payment_buckaroo.field_payment_transaction__display_name
msgid "Display Name"
msgstr "Nama Tampilan"

#. module: payment_buckaroo
#: model:ir.model.fields,field_description:payment_buckaroo.field_payment_acquirer__id
#: model:ir.model.fields,field_description:payment_buckaroo.field_payment_transaction__id
msgid "ID"
msgstr "ID"

#. module: payment_buckaroo
#: model:ir.model.fields,field_description:payment_buckaroo.field_payment_acquirer____last_update
#: model:ir.model.fields,field_description:payment_buckaroo.field_payment_transaction____last_update
msgid "Last Modified on"
msgstr "Terakhir diubah pada"

#. module: payment_buckaroo
#: model:ir.model,name:payment_buckaroo.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Pemilik Tagihan"

#. module: payment_buckaroo
#: model:ir.model,name:payment_buckaroo.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transaksi Tagihan"

#. module: payment_buckaroo
#: model:ir.model.fields,field_description:payment_buckaroo.field_payment_acquirer__provider
msgid "Provider"
msgstr "Pemberi"

#. module: payment_buckaroo
#: model:ir.model.fields,field_description:payment_buckaroo.field_payment_acquirer__brq_secretkey
msgid "SecretKey"
msgstr ""

#. module: payment_buckaroo
#: model:ir.model.fields,field_description:payment_buckaroo.field_payment_acquirer__brq_websitekey
msgid "WebsiteKey"
msgstr ""
