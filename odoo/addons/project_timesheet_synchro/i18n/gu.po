# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * project_timesheet_synchro
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-08-24 11:28+0000\n"
"PO-Revision-Date: 2018-08-24 11:28+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Gujarati (https://www.transifex.com/odoo/teams/41243/gu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: gu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:170
#, python-format
msgid "&emsp;Start"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:175
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:180
#, python-format
msgid "&emsp;Stop"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:446
#, python-format
msgid "(Last sync was unsuccessful)"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:55
#, python-format
msgid "Action"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:236
#, python-format
msgid "Activity created"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:120
#, python-format
msgid "Add"
msgstr "ઉમેરો"

#. module: project_timesheet_synchro
#: model:ir.model,name:project_timesheet_synchro.model_account_analytic_line
msgid "Analytic Line"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/timesheet_app_backend_template.xml:26
#, python-format
msgid "Apple App Store"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:415
#, python-format
msgid "Are you sure that you want to delete this activity?"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:473
#, python-format
msgid "Are you sure that you want to reset the app?"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/timesheet_app_backend_template.xml:52
#, python-format
msgid "Available for iPhone, Android and Chrome."
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/timesheet_app_backend_template.xml:30
#, python-format
msgid "Blazing Fast"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:157
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:419
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:477
#, python-format
msgid "Cancel"
msgstr "રદ કરો"

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:461
#, python-format
msgid ""
"Connect Timesheets to Odoo to synchronize your activities across all "
"devices."
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:104
#, python-format
msgid "Current activity"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:542
#, python-format
msgid "Database"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:252
#, python-format
msgid "Default project"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:158
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:420
#, python-format
msgid "Delete"
msgstr "કાઢી નાંખો"

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:408
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:409
#, python-format
msgid "Discard"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:490
#, python-format
msgid "Discard data"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:567
#, python-format
msgid ""
"Either you or the Odoo server is offline at the moment. Make sure you do "
"have a connection or try again later."
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/timesheet_app_backend_template.xml:42
#, python-format
msgid "Get Things Done"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/timesheet_app_backend_template.xml:25
#, python-format
msgid "Google Chrome Store"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/timesheet_app_backend_template.xml:24
#, python-format
msgid "Google Play Store"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:491
#, python-format
msgid "Keep data"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:443
#, python-format
msgid "Last sync :"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:136
#, python-format
msgid "Locked"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:437
#, python-format
msgid "Logged in as"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:507
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:514
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:551
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:559
#, python-format
msgid "Login"
msgstr "લોગ ઇન"

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:519
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:540
#, python-format
msgid "Login to an on premise Odoo instance"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:505
#, python-format
msgid "Login with Odoo"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:439
#, python-format
msgid "Logout"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:256
#, python-format
msgid "Minimal duration"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/timesheet_app_backend_template.xml:49
#, python-format
msgid "More info"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:145
#, python-format
msgid "Motivation Text"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:272
#, python-format
msgid "Multiple to round up all durations"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/timesheet_app_backend_template.xml:35
#, python-format
msgid "Never lose track of what you need to do."
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:535
#, python-format
msgid "Next"
msgstr "આગલું"

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:300
#, python-format
msgid "Next week"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:303
#, python-format
msgid "Next week unavailable"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:92
#, python-format
msgid "No task"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:487
#, python-format
msgid ""
"Note that this will create new projects and tasks on your Odoo instance"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/timesheet_app_backend_template.xml:38
#, python-format
msgid "Offline support"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:438
#, python-format
msgid "On server"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:225
#, python-format
msgid ""
"Once you have created or synchronized projects and tasks, they will appear "
"here. This will allow you to plan your day in advance."
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/timesheet_app_backend_template.xml:16
#, python-format
msgid "Open the App"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:511
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:555
#, python-format
msgid "Password"
msgstr "પાસવર્ડ"

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/timesheet_app_backend_template.xml:43
#, python-format
msgid "Plan your day. Focus on important tasks."
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:531
#, python-format
msgid "Please enter a database name:"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:249
#, python-format
msgid "Please enter a valid duration"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:356
#, python-format
msgid "Please select a project first"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:338
#, python-format
msgid "Please select a project."
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:288
#, python-format
msgid "Previous week"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:291
#, python-format
msgid "Previous week unavailable"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:346
#, python-format
msgid "Project"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:478
#, python-format
msgid "RESET"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:499
#, python-format
msgid "Reset"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:116
#, python-format
msgid "Run"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:407
#, python-format
msgid "Save"
msgstr "સંગ્રહો"

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:575
#, python-format
msgid "Select your Odoo instance from the list below :"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:38
#, python-format
msgid "Settings"
msgstr "સુયોજનો"

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:557
#, python-format
msgid "Show password"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:465
#, python-format
msgid "Sign In"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:466
#, python-format
msgid "Sign Up"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:31
#: code:addons/project_timesheet_synchro/static/src/xml/timesheet_app_backend_template.xml:34
#, python-format
msgid "Statistics"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:98
#, python-format
msgid "Subtract"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:441
#, python-format
msgid "Sync Now"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:53
#, python-format
msgid "Sync in progress"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:35
#, python-format
msgid "Synchronize"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:585
#, python-format
msgid "Syncing data, this shouldn't take long"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:351
#, python-format
msgid "Task"
msgstr "કાર્ય"

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:453
#, python-format
msgid ""
"The server you connected to does not support timesheet synchronization. You "
"should contact your administrator in order to install the module "
"''Synchronization with the external timesheet application'' (available in "
"Odoo Enterprise Edition)."
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:24
#, python-format
msgid "This Week"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:384
#, python-format
msgid "Time spent (hh:mm)"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:361
#, python-format
msgid "Time spent (hhmm)"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:265
#, python-format
msgid "Time unit"
msgstr ""

#. module: project_timesheet_synchro
#: model:ir.actions.client,name:project_timesheet_synchro.project_timesheet_synchro_app_action
#: model:ir.ui.menu,name:project_timesheet_synchro.menu_timesheet_app
msgid "Timesheet App"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:21
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:233
#, python-format
msgid "Today"
msgstr "આજે"

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:28
#, python-format
msgid "Today's Plan"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:49
#, python-format
msgid "Toggle action"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:320
#, python-format
msgid "Total :"
msgstr "કુલ:"

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:462
#, python-format
msgid ""
"Use Odoo to organize projects and tasks, forecast resources and invoice time"
" spent on tasks"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:498
#, python-format
msgid "Use an Odoo.com account"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/timesheet_app_backend_template.xml:22
#, python-format
msgid "Use timesheet apps to manage your timesheets."
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:295
#, python-format
msgid "Week"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:396
#, python-format
msgid "Work Summary"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/timesheet_app_backend_template.xml:39
#, python-format
msgid "Work anywhere, anytime."
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/timesheet_app_backend_template.xml:31
#, python-format
msgid "Work in disconnected mode and sync in the background."
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:486
#, python-format
msgid ""
"Would you like the activities, projects and tasks that you created as a "
"guest user to be synchronized as well?"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:153
#, python-format
msgid "Would you like to delete this activity?"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:584
#, python-format
msgid "You are logged in !"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/timesheet_app_backend_template.xml:15
#, python-format
msgid "You can try the app in a new tab:"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:571
#, python-format
msgid ""
"You should have received an email with a link to activate your account. Once"
" it is activated, you'll be able to"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:521
#, python-format
msgid "Your Odoo Server Address"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:570
#, python-format
msgid "Your database and account have been created !"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/timesheet_app_backend_template.xml:21
#, python-format
msgid "Your personal timesheets"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:552
#, python-format
msgid "ex: <EMAIL>"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:523
#, python-format
msgid "http://"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:524
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:525
#, python-format
msgid "https://"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:260
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:269
#, python-format
msgid "minutes"
msgstr ""

#. module: project_timesheet_synchro
#. openerp-web
#: code:addons/project_timesheet_synchro/static/src/xml/project_timesheet.xml:571
#, python-format
msgid "sign in"
msgstr ""
