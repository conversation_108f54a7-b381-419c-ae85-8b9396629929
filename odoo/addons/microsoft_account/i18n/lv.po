# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* microsoft_account
# 
# Translators:
# <PERSON><PERSON><PERSON> <arn<PERSON>@allegro.lv>, 2020
# <AUTHOR> <EMAIL>, 2020
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~13.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-09-01 07:29+0000\n"
"PO-Revision-Date: 2020-09-07 08:14+0000\n"
"Last-Translator: ievaputnina <<EMAIL>>, 2020\n"
"Language-Team: Latvian (https://www.transifex.com/odoo/teams/41243/lv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lv\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n != 0 ? 1 : 2);\n"

#. module: microsoft_account
#: model:ir.model.fields,field_description:microsoft_account.field_microsoft_service__display_name
#: model:ir.model.fields,field_description:microsoft_account.field_res_users__display_name
msgid "Display Name"
msgstr "Attēlotais nosaukums"

#. module: microsoft_account
#: model:ir.model.fields,field_description:microsoft_account.field_microsoft_service__id
#: model:ir.model.fields,field_description:microsoft_account.field_res_users__id
msgid "ID"
msgstr "ID"

#. module: microsoft_account
#: model:ir.model.fields,field_description:microsoft_account.field_microsoft_service____last_update
#: model:ir.model.fields,field_description:microsoft_account.field_res_users____last_update
msgid "Last Modified on"
msgstr "Pēdējoreiz modificēts"

#. module: microsoft_account
#: code:addons/microsoft_account/models/microsoft_service.py:0
#, python-format
msgid "Method not supported [%s] not in [GET, POST, PUT, PATCH or DELETE]!"
msgstr ""

#. module: microsoft_account
#: model:ir.model.fields,field_description:microsoft_account.field_res_users__microsoft_calendar_rtoken
msgid "Microsoft Refresh Token"
msgstr ""

#. module: microsoft_account
#: model:ir.model,name:microsoft_account.model_microsoft_service
msgid "Microsoft Service"
msgstr ""

#. module: microsoft_account
#: model:ir.model.fields,field_description:microsoft_account.field_res_users__microsoft_calendar_token_validity
msgid "Microsoft Token Validity"
msgstr ""

#. module: microsoft_account
#: model:ir.model.fields,field_description:microsoft_account.field_res_users__microsoft_calendar_token
msgid "Microsoft User token"
msgstr ""

#. module: microsoft_account
#: code:addons/microsoft_account/models/microsoft_service.py:0
#, python-format
msgid ""
"Something went wrong during your token generation. Maybe your Authorization "
"Code is invalid"
msgstr ""

#. module: microsoft_account
#: code:addons/microsoft_account/models/microsoft_service.py:0
#, python-format
msgid ""
"Something went wrong during your token generation. Maybe your Authorization "
"Code is invalid or already expired"
msgstr ""

#. module: microsoft_account
#: model:ir.model,name:microsoft_account.model_res_users
msgid "Users"
msgstr "Lietotāji"
