# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_ingenico
# 
# Translators:
# <PERSON>, 2020
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON> <jan.ho<PERSON><PERSON>@centrum.cz>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# trendspotter, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~13.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-09-01 07:28+0000\n"
"PO-Revision-Date: 2020-09-07 08:15+0000\n"
"Last-Translator: trendspotter, 2021\n"
"Language-Team: Czech (https://www.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: payment_ingenico
#: code:addons/payment_ingenico/models/payment.py:0
#, python-format
msgid "; multiple order found"
msgstr "; nalezena vícenásobná objednávka"

#. module: payment_ingenico
#: code:addons/payment_ingenico/models/payment.py:0
#, python-format
msgid "; no order found"
msgstr "; nebyla nalezena žádná objednávka"

#. module: payment_ingenico
#: model:ir.model.fields,field_description:payment_ingenico.field_payment_acquirer__ogone_userid
msgid "API User ID"
msgstr "API User ID"

#. module: payment_ingenico
#: model:ir.model.fields,field_description:payment_ingenico.field_payment_acquirer__ogone_password
msgid "API User Password"
msgstr "Uživatelské heslo API"

#. module: payment_ingenico
#: model:ir.model.fields,field_description:payment_ingenico.field_payment_acquirer__ogone_alias_usage
msgid "Alias Usage"
msgstr "Použití aliasu"

#. module: payment_ingenico
#: model_terms:ir.ui.view,arch_db:payment_ingenico.ogone_s2s_form
msgid "CVC"
msgstr "CVC"

#. module: payment_ingenico
#: model_terms:ir.ui.view,arch_db:payment_ingenico.ogone_s2s_form
msgid "Card number"
msgstr "Číslo platební karty"

#. module: payment_ingenico
#: model_terms:ir.ui.view,arch_db:payment_ingenico.ogone_s2s_form
msgid "Cardholder name"
msgstr "Jméno držitele karty"

#. module: payment_ingenico
#: model:ir.model.fields,field_description:payment_ingenico.field_payment_acquirer__display_name
#: model:ir.model.fields,field_description:payment_ingenico.field_payment_token__display_name
#: model:ir.model.fields,field_description:payment_ingenico.field_payment_transaction__display_name
msgid "Display Name"
msgstr "Zobrazované jméno"

#. module: payment_ingenico
#: model_terms:ir.ui.view,arch_db:payment_ingenico.ogone_s2s_form
msgid "Expires (MM / YY)"
msgstr "Platnost vyprší (MM / RR)"

#. module: payment_ingenico
#: model:ir.model.fields,field_description:payment_ingenico.field_payment_acquirer__id
#: model:ir.model.fields,field_description:payment_ingenico.field_payment_token__id
#: model:ir.model.fields,field_description:payment_ingenico.field_payment_transaction__id
msgid "ID"
msgstr "ID"

#. module: payment_ingenico
#: model:ir.model.fields,help:payment_ingenico.field_payment_acquirer__ogone_alias_usage
msgid ""
"If you want to use Ogone Aliases, this default Alias Usage will be presented"
" to the customer as the reason you want to keep his payment data"
msgstr ""
"Pokud chcete použít Ogone Aliases, bude toto výchozí použití aliasu "
"zákazníkovi představeno jako důvod, proč si chcete zachovat jeho platební "
"údaje."

#. module: payment_ingenico
#: model:ir.model.fields.selection,name:payment_ingenico.selection__payment_acquirer__provider__ogone
msgid "Ingenico"
msgstr "Ingenico"

#. module: payment_ingenico
#: model:ir.model.fields,field_description:payment_ingenico.field_payment_acquirer____last_update
#: model:ir.model.fields,field_description:payment_ingenico.field_payment_token____last_update
#: model:ir.model.fields,field_description:payment_ingenico.field_payment_transaction____last_update
msgid "Last Modified on"
msgstr "Naposled změněno"

#. module: payment_ingenico
#: code:addons/payment_ingenico/models/payment.py:0
#, python-format
msgid "Ogone: invalid shasign, received %s, computed %s, for data %s"
msgstr ""

#. module: payment_ingenico
#: code:addons/payment_ingenico/models/payment.py:0
#, python-format
msgid "Ogone: received data for reference %s"
msgstr ""

#. module: payment_ingenico
#: code:addons/payment_ingenico/models/payment.py:0
#, python-format
msgid ""
"Ogone: received data with missing reference (%s) or pay_id (%s) or shasign "
"(%s)"
msgstr ""

#. module: payment_ingenico
#: model:ir.model.fields,field_description:payment_ingenico.field_payment_acquirer__ogone_pspid
msgid "PSPID"
msgstr "PSPID"

#. module: payment_ingenico
#: model:ir.model,name:payment_ingenico.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Platební brána"

#. module: payment_ingenico
#: model:ir.model,name:payment_ingenico.model_payment_token
msgid "Payment Token"
msgstr "Platební token"

#. module: payment_ingenico
#: model:ir.model,name:payment_ingenico.model_payment_transaction
msgid "Payment Transaction"
msgstr "Platební transakce"

#. module: payment_ingenico
#: model:ir.model.fields,field_description:payment_ingenico.field_payment_acquirer__provider
msgid "Provider"
msgstr "Poskytovatel"

#. module: payment_ingenico
#: model:ir.model.fields,field_description:payment_ingenico.field_payment_acquirer__ogone_shakey_in
msgid "SHA Key IN"
msgstr "SHA Klíč IN"

#. module: payment_ingenico
#: model:ir.model.fields,field_description:payment_ingenico.field_payment_acquirer__ogone_shakey_out
msgid "SHA Key OUT"
msgstr "SHA Klíč OUT"
