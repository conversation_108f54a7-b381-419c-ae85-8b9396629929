# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_odoo_by_adyen
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-11-27 14:12+0000\n"
"PO-Revision-Date: 2021-01-08 12:30+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Latvian (https://www.transifex.com/odoo/teams/41243/lv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lv\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n != 0 ? 1 : 2);\n"

#. module: payment_odoo_by_adyen
#: code:addons/payment_odoo_by_adyen/models/payment.py:0
#, python-format
msgid "; multiple order found"
msgstr ""

#. module: payment_odoo_by_adyen
#: code:addons/payment_odoo_by_adyen/models/payment.py:0
#, python-format
msgid "; no order found"
msgstr ""

#. module: payment_odoo_by_adyen
#: model:ir.model.fields,field_description:payment_odoo_by_adyen.field_payment_acquirer__odoo_adyen_account_id
msgid "Adyen Account"
msgstr ""

#. module: payment_odoo_by_adyen
#: model:ir.model.fields,field_description:payment_odoo_by_adyen.field_payment_acquirer__odoo_adyen_payout_id
msgid "Adyen Payout"
msgstr ""

#. module: payment_odoo_by_adyen
#: code:addons/payment_odoo_by_adyen/models/payment.py:0
#, python-format
msgid "Card No XXXXXXXXXXXX%s"
msgstr ""

#. module: payment_odoo_by_adyen
#: model_terms:ir.ui.view,arch_db:payment_odoo_by_adyen.acquirer_form_odoo_by_adyen
msgid "Create an account in 1 minute"
msgstr ""

#. module: payment_odoo_by_adyen
#: model:ir.model.fields,field_description:payment_odoo_by_adyen.field_payment_acquirer__display_name
#: model:ir.model.fields,field_description:payment_odoo_by_adyen.field_payment_token__display_name
#: model:ir.model.fields,field_description:payment_odoo_by_adyen.field_payment_transaction__display_name
msgid "Display Name"
msgstr "Attēlotais nosaukums"

#. module: payment_odoo_by_adyen
#: model:ir.model.fields,field_description:payment_odoo_by_adyen.field_payment_acquirer__id
#: model:ir.model.fields,field_description:payment_odoo_by_adyen.field_payment_token__id
#: model:ir.model.fields,field_description:payment_odoo_by_adyen.field_payment_transaction__id
msgid "ID"
msgstr "ID"

#. module: payment_odoo_by_adyen
#: model:ir.model.fields,field_description:payment_odoo_by_adyen.field_payment_acquirer____last_update
#: model:ir.model.fields,field_description:payment_odoo_by_adyen.field_payment_token____last_update
#: model:ir.model.fields,field_description:payment_odoo_by_adyen.field_payment_transaction____last_update
msgid "Last Modified on"
msgstr "Pēdējoreiz modificēts"

#. module: payment_odoo_by_adyen
#: code:addons/payment_odoo_by_adyen/models/payment.py:0
#, python-format
msgid "Odoo Payment by Adyen: feedback error"
msgstr ""

#. module: payment_odoo_by_adyen
#: model:ir.model.fields.selection,name:payment_odoo_by_adyen.selection__payment_acquirer__provider__odoo_adyen
msgid "Odoo Payments by Adyen"
msgstr ""

#. module: payment_odoo_by_adyen
#: code:addons/payment_odoo_by_adyen/models/payment.py:0
#, python-format
msgid "Odoo Payments by Adyen is not available in test mode."
msgstr ""

#. module: payment_odoo_by_adyen
#: code:addons/payment_odoo_by_adyen/models/payment.py:0
#, python-format
msgid "Odoo Payments by Adyen: received data for reference %s"
msgstr ""

#. module: payment_odoo_by_adyen
#: code:addons/payment_odoo_by_adyen/models/payment.py:0
#, python-format
msgid "Odoo Payments by Adyen: received data with missing reference (%s)"
msgstr ""

#. module: payment_odoo_by_adyen
#: model:ir.model,name:payment_odoo_by_adyen.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Maksājuma saņēmējs"

#. module: payment_odoo_by_adyen
#: model:ir.model,name:payment_odoo_by_adyen.model_payment_token
msgid "Payment Token"
msgstr ""

#. module: payment_odoo_by_adyen
#: model:ir.model,name:payment_odoo_by_adyen.model_payment_transaction
msgid "Payment Transaction"
msgstr "Maksājuma darījums"

#. module: payment_odoo_by_adyen
#: model:ir.model.fields,field_description:payment_odoo_by_adyen.field_payment_token__odoo_adyen_payment_method_type
msgid "PaymentMethod Type"
msgstr ""

#. module: payment_odoo_by_adyen
#: model:ir.model.fields,field_description:payment_odoo_by_adyen.field_payment_acquirer__provider
msgid "Provider"
msgstr "Sniedzējs"
