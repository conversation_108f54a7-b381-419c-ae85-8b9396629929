# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* purchase_requisition_stock
# 
# Translators:
# <PERSON>, 2020
# <PERSON> <<EMAIL>>, 2020
# <AUTHOR> <EMAIL>, 2020
# <PERSON><PERSON> <man<PERSON><EMAIL>>, 2020
# <PERSON><PERSON><PERSON>, 2020
# jabe<PERSON><PERSON>, 2021
# ma<PERSON><PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~13.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-09-01 07:28+0000\n"
"PO-Revision-Date: 2020-09-07 08:17+0000\n"
"Last-Translator: ma<PERSON><PERSON>, 2022\n"
"Language-Team: Catalan (https://www.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: purchase_requisition_stock
#: model:ir.model.fields,field_description:purchase_requisition_stock.field_purchase_order__display_name
#: model:ir.model.fields,field_description:purchase_requisition_stock.field_purchase_requisition__display_name
#: model:ir.model.fields,field_description:purchase_requisition_stock.field_purchase_requisition_line__display_name
#: model:ir.model.fields,field_description:purchase_requisition_stock.field_stock_move__display_name
#: model:ir.model.fields,field_description:purchase_requisition_stock.field_stock_rule__display_name
#: model:ir.model.fields,field_description:purchase_requisition_stock.field_stock_warehouse_orderpoint__display_name
msgid "Display Name"
msgstr "Nom a mostrar"

#. module: purchase_requisition_stock
#: model:ir.model.fields,field_description:purchase_requisition_stock.field_purchase_requisition_line__move_dest_id
msgid "Downstream Move"
msgstr "Moviment descendent"

#. module: purchase_requisition_stock
#: model:ir.model.fields,field_description:purchase_requisition_stock.field_purchase_order__id
#: model:ir.model.fields,field_description:purchase_requisition_stock.field_purchase_requisition__id
#: model:ir.model.fields,field_description:purchase_requisition_stock.field_purchase_requisition_line__id
#: model:ir.model.fields,field_description:purchase_requisition_stock.field_stock_move__id
#: model:ir.model.fields,field_description:purchase_requisition_stock.field_stock_rule__id
#: model:ir.model.fields,field_description:purchase_requisition_stock.field_stock_warehouse_orderpoint__id
msgid "ID"
msgstr "ID"

#. module: purchase_requisition_stock
#: model:ir.model.fields,field_description:purchase_requisition_stock.field_purchase_order____last_update
#: model:ir.model.fields,field_description:purchase_requisition_stock.field_purchase_requisition____last_update
#: model:ir.model.fields,field_description:purchase_requisition_stock.field_purchase_requisition_line____last_update
#: model:ir.model.fields,field_description:purchase_requisition_stock.field_stock_move____last_update
#: model:ir.model.fields,field_description:purchase_requisition_stock.field_stock_rule____last_update
#: model:ir.model.fields,field_description:purchase_requisition_stock.field_stock_warehouse_orderpoint____last_update
msgid "Last Modified on"
msgstr "Última modificació el "

#. module: purchase_requisition_stock
#: model:ir.model,name:purchase_requisition_stock.model_stock_warehouse_orderpoint
msgid "Minimum Inventory Rule"
msgstr "Regla d'inventari mínim"

#. module: purchase_requisition_stock
#: model:ir.model.fields,field_description:purchase_requisition_stock.field_purchase_requisition__picking_type_id
msgid "Operation Type"
msgstr "Tipus d'operació"

#. module: purchase_requisition_stock
#: model:ir.model,name:purchase_requisition_stock.model_purchase_order
msgid "Purchase Order"
msgstr "Comanda de compra"

#. module: purchase_requisition_stock
#: model:ir.model,name:purchase_requisition_stock.model_purchase_requisition
msgid "Purchase Requisition"
msgstr "Sol·licitud de compra"

#. module: purchase_requisition_stock
#: model:ir.model,name:purchase_requisition_stock.model_purchase_requisition_line
msgid "Purchase Requisition Line"
msgstr "Línia sol·licitud de compra"

#. module: purchase_requisition_stock
#: model:ir.model.fields,field_description:purchase_requisition_stock.field_stock_move__requisition_line_ids
msgid "Requisition Line"
msgstr "Línia de Requisició"

#. module: purchase_requisition_stock
#: model:ir.model,name:purchase_requisition_stock.model_stock_move
msgid "Stock Move"
msgstr "Moviment d'estoc"

#. module: purchase_requisition_stock
#: model:ir.model,name:purchase_requisition_stock.model_stock_rule
msgid "Stock Rule"
msgstr "Regla d'estoc"

#. module: purchase_requisition_stock
#: model:ir.model.fields,field_description:purchase_requisition_stock.field_purchase_requisition__warehouse_id
msgid "Warehouse"
msgstr "Magatzem"
