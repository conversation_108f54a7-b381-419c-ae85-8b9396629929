# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* approvals_purchase_stock
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON>, 2020
# <PERSON><PERSON>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~13.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-09-01 07:39+0000\n"
"PO-Revision-Date: 2020-09-07 08:18+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2020\n"
"Language-Team: Japanese (https://www.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: approvals_purchase_stock
#: model:ir.model,name:approvals_purchase_stock.model_approval_request
msgid "Approval Request"
msgstr "許可リクエスト"

#. module: approvals_purchase_stock
#: model:ir.model.fields,field_description:approvals_purchase_stock.field_approval_product_line__display_name
#: model:ir.model.fields,field_description:approvals_purchase_stock.field_approval_request__display_name
msgid "Display Name"
msgstr "表示名"

#. module: approvals_purchase_stock
#: model:ir.model.fields,field_description:approvals_purchase_stock.field_approval_request__hide_location
msgid "Hide Location"
msgstr "場所を非表示"

#. module: approvals_purchase_stock
#: model:ir.model.fields,field_description:approvals_purchase_stock.field_approval_product_line__id
#: model:ir.model.fields,field_description:approvals_purchase_stock.field_approval_request__id
msgid "ID"
msgstr "ID"

#. module: approvals_purchase_stock
#: model:ir.model.fields,field_description:approvals_purchase_stock.field_approval_product_line____last_update
#: model:ir.model.fields,field_description:approvals_purchase_stock.field_approval_request____last_update
msgid "Last Modified on"
msgstr "最終更新日"

#. module: approvals_purchase_stock
#: model:ir.model,name:approvals_purchase_stock.model_approval_product_line
msgid "Product Line"
msgstr "プロダクトライン"

#. module: approvals_purchase_stock
#: model:ir.model.fields,field_description:approvals_purchase_stock.field_approval_product_line__warehouse_id
msgid "Warehouse"
msgstr "倉庫"
