# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_account_taxcloud
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~13.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-09-01 07:39+0000\n"
"PO-Revision-Date: 2020-09-07 08:23+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Italian (https://www.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: sale_account_taxcloud
#: model:ir.model.fields,field_description:sale_account_taxcloud.field_sale_advance_payment_inv__display_name
#: model:ir.model.fields,field_description:sale_account_taxcloud.field_sale_order__display_name
#: model:ir.model.fields,field_description:sale_account_taxcloud.field_sale_order_line__display_name
msgid "Display Name"
msgstr "Nome visualizzato"

#. module: sale_account_taxcloud
#: model_terms:ir.ui.view,arch_db:sale_account_taxcloud.view_order_form_inherit
msgid "Go to Settings."
msgstr "Andare nelle impostazioni."

#. module: sale_account_taxcloud
#: model:ir.model.fields,field_description:sale_account_taxcloud.field_sale_advance_payment_inv__id
#: model:ir.model.fields,field_description:sale_account_taxcloud.field_sale_order__id
#: model:ir.model.fields,field_description:sale_account_taxcloud.field_sale_order_line__id
msgid "ID"
msgstr "ID"

#. module: sale_account_taxcloud
#: model:ir.model.fields,field_description:sale_account_taxcloud.field_sale_order__is_taxcloud_configured
msgid "Is Taxcloud Configured"
msgstr "TaxCloud è configurato"

#. module: sale_account_taxcloud
#: model:ir.model.fields,field_description:sale_account_taxcloud.field_sale_advance_payment_inv____last_update
#: model:ir.model.fields,field_description:sale_account_taxcloud.field_sale_order____last_update
#: model:ir.model.fields,field_description:sale_account_taxcloud.field_sale_order_line____last_update
msgid "Last Modified on"
msgstr "Ultima modifica il"

#. module: sale_account_taxcloud
#: model_terms:ir.ui.view,arch_db:sale_account_taxcloud.view_order_form_inherit
msgid ""
"Please enter your Taxcloud credentials to compute tax rates automatically."
msgstr ""
"Per calcolare automaticamente le aliquote fiscali inserire le credenziali "
"TaxCloud."

#. module: sale_account_taxcloud
#: model:ir.model,name:sale_account_taxcloud.model_sale_advance_payment_inv
msgid "Sales Advance Payment Invoice"
msgstr "Fattura di vendita con pagamento anticipato"

#. module: sale_account_taxcloud
#: model:ir.model,name:sale_account_taxcloud.model_sale_order
msgid "Sales Order"
msgstr "Ordine di vendita"

#. module: sale_account_taxcloud
#: model:ir.model,name:sale_account_taxcloud.model_sale_order_line
msgid "Sales Order Line"
msgstr "Riga ordine di vendita"

#. module: sale_account_taxcloud
#: model_terms:ir.ui.view,arch_db:sale_account_taxcloud.view_order_form_inherit
msgid "Tax"
msgstr "Imposta"

#. module: sale_account_taxcloud
#: model:ir.model.fields,help:sale_account_taxcloud.field_sale_order__is_taxcloud
msgid "Technical field to determine whether to hide taxes in views or not."
msgstr ""
"Campo tecnico usato per stabilire se le imposte devono essere nascoste nelle"
" viste."

#. module: sale_account_taxcloud
#: code:addons/sale_account_taxcloud/models/sale_order.py:0
#, python-format
msgid "Unable to retrieve taxes from TaxCloud: "
msgstr "Impossibile recuperare le imposte da TaxCloud:"

#. module: sale_account_taxcloud
#: model_terms:ir.ui.view,arch_db:sale_account_taxcloud.view_order_form_inherit
msgid "Update taxes"
msgstr "Aggiorna imposte"

#. module: sale_account_taxcloud
#: model:ir.model.fields,field_description:sale_account_taxcloud.field_sale_order__is_taxcloud
msgid "Use TaxCloud API"
msgstr "Utilizzare API TaxCloud"

#. module: sale_account_taxcloud
#: model:ir.model.fields,help:sale_account_taxcloud.field_sale_order__is_taxcloud_configured
msgid ""
"Used to determine whether or not to warn the user to configure TaxCloud."
msgstr ""
"Usato per stabilire se avvisare l'utente di configurare TaxCloud oppure no."
