# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * web_mobile
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-18 10:05+0000\n"
"PO-Revision-Date: 2018-09-18 10:05+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bs\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: web_mobile
#. openerp-web
#: code:addons/web_mobile/static/src/xml/contact_sync.xml:7
#, python-format
msgid "Add"
msgstr "Dodaj"

#. module: web_mobile
#. openerp-web
#: code:addons/web_mobile/static/src/xml/contact_sync.xml:9
#, python-format
msgid "Add to"
msgstr ""

#. module: web_mobile
#. openerp-web
#: code:addons/web_mobile/static/src/xml/user_menu.xml:6
#, python-format
msgid "Add to Home Screen"
msgstr ""

#. module: web_mobile
#. openerp-web
#: code:addons/web_mobile/static/src/xml/contact_sync.xml:10
#, python-format
msgid "Mobile"
msgstr "Mobitel"

#. module: web_mobile
#. openerp-web
#: code:addons/web_mobile/static/src/js/user_menu.js:58
#, python-format
msgid "No shortcut for Home Menu"
msgstr ""

#. module: web_mobile
#. openerp-web
#: code:addons/web_mobile/static/src/xml/settings_dashboard.xml:14
#, python-format
msgid "On Apple Store"
msgstr ""

#. module: web_mobile
#. openerp-web
#: code:addons/web_mobile/static/src/xml/settings_dashboard.xml:9
#, python-format
msgid "On Google Play"
msgstr ""

#. module: web_mobile
#. openerp-web
#: code:addons/web_mobile/static/src/xml/user_menu.xml:7
#, python-format
msgid "Switch/Add Account"
msgstr ""
