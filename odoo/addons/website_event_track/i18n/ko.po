# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_track
# 
# Translators:
# <PERSON>, 2020
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-11-27 14:13+0000\n"
"PO-Revision-Date: 2020-09-07 08:21+0000\n"
"Last-Translator: JH CHOI <<EMAIL>>, 2021\n"
"Language-Team: Korean (https://www.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_event_track
#: code:addons/website_event_track/models/website.py:0
#, python-format
msgid "\"Events App Name\" field is required."
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__wishlist_visitor_count
#: model:ir.model.fields,field_description:website_event_track.field_website_visitor__event_track_wishlisted_count
msgid "# Wishlisted"
msgstr ""

#. module: website_event_track
#: code:addons/website_event_track/models/website.py:0
#, python-format
msgid "%s Events"
msgstr ""

#. module: website_event_track
#: code:addons/website_event_track/controllers/webmanifest.py:0
#, python-format
msgid "%s Online Events Application"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "&amp;bull;"
msgstr "&amp;bull;"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_17
msgid "10 DIY Furniture Ideas For Absolute Beginners"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_18
msgid "6 Woodworking tips and tricks for beginners"
msgstr ""

#. module: website_event_track
#: model:mail.template,body_html:website_event_track.mail_template_data_track_confirmation
msgid ""
"<?xml version=\"1.0\"?>\n"
"<div>\n"
"    Dear ${object.partner_name or ''}<br/>\n"
"    We are pleased to inform you that your proposal ${object.name} has been accepted and confirmed for the event ${object.event_id.name}.\n"
"    <br/>\n"
"    You will find more details here:\n"
"    <div style=\"margin: 16px 0px 16px 0px;\">\n"
"        <a href=\"/event/${object.event_id.id}/track/${object.id}\" style=\"padding: 8px 16px 8px 16px; font-size: 14px; color: #FFFFFF; text-decoration: none !important; background-color: #875A7B; border: 0px solid #875A7B; border-radius:3px\">\n"
"            View Talk\n"
"        </a>\n"
"    </div>\n"
"    <br/><br/>\n"
"    Thank you,\n"
"    % if user.signature\n"
"        <br/>\n"
"        ${user.signature | safe}\n"
"    % endif\n"
"</div>"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<b>Mail</b>:"
msgstr "<b>메일</b>:"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<b>Phone</b>:"
msgstr "<b>전화번호</b>:"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<b>Proposed By</b>:"
msgstr "<b>제안자</b>:"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<b>Speakers Biography</b>:"
msgstr "<b>연사 이력</b>:"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<b>Talk Introduction</b>:"
msgstr "<b>강연 소개</b>:"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_cards_track
msgid "<i class=\"fa fa-ban mr-2\"/>Unpublished"
msgstr "<i class=\"fa fa-ban mr-2\"/>게시 안함"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_search
msgid "<i class=\"fa fa-bell mr-2 text-muted\"/> Wishlisted Talks"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.session_topbar
msgid "<i class=\"fa fa-folder-open\"/> Favorites"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"<i class=\"fa fa-info-circle\"/>\n"
"                                You can add multiple speakers by separating names, emails and phones with commas."
msgstr ""
"<i class=\"fa fa-info-circle\"/>\n"
"                                이름, 이메일 및 전화를 쉼표로 구분하여 여러 명의 연사를 추가할 수 있습니다."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "<span class=\"d-none d-md-block ml-2\">&amp;bull;</span>"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-"
"specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-"
"specific.\" groups=\"website.group_multi_website\"/>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_topbar
msgid "<span id=\"search_number\" class=\"mr-1\">0</span>Results"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
msgid "<span>&amp;nbsp;at&amp;nbsp;</span>"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "<span>hours</span>"
msgstr "<span>시간</span>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"<strong>Lightning Talks</strong>. These are 30 minutes talks on many\n"
"                                    different topics. Most topics are accepted in lightning talks."
msgstr ""
"<strong>번개 강연</strong>. 이것은 많은 다양한 주제에 대한 \n"
"                                   30분짜리 강연입니다. 대부분의 주제는 번개 강연에서 허용됩니다."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
msgid "<strong>Location:</strong>"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"<strong>Regular Talks</strong>. These are standard talks with slides,\n"
"                                    alocated in slots of 60 minutes."
msgstr ""
"<strong>정기 강연</strong>. 슬라이드가 있는 표준 강연이며, \n"
"                                    60분 간격으로 배치됩니다."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_sponsor_view_form
msgid ">e.g. : https://www.odoo.com"
msgstr ""

#. module: website_event_track
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_2
#: model_terms:event.sponsor,website_description:website_event_track.event_sponsor_2
msgid ""
"A non-profit international educational and scientific\n"
"                    organisation, hosting three departments (aeronautics and\n"
"                    aerospace, environmental and applied fluid dynamics, and\n"
"                    turbomachinery and propulsion)."
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track7
msgid "A technical explanation of how to use computer design apps"
msgstr "컴퓨터 디자인 앱 사용 방법에 대한 기술적 설명"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__kanban_state
msgid ""
"A track's kanban state indicates special situations affecting it:\n"
" * Grey is the default situation\n"
" * Red indicates something is preventing the progress of this track\n"
" * Green indicates the track is ready to be pulled to the next stage"
msgstr ""
"과정 칸반 상태는 그것에 영향을 미치는 특별한 상황을 나타냅니다.\n"
"* 회색은 기본적인 상황입니다\n"
"* 빨간색은 무언가가 과정의 진행을 막고 있음을 나타냅니다\n"
"* 녹색은 과정이 다음 단계로 올라갈 준비가 되었다는 것을 나타냅니다"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__is_accepted
msgid "Accepted Stage"
msgstr "허용된 단계"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__is_accepted
#: model:ir.model.fields,help:website_event_track.field_event_track_stage__is_accepted
msgid "Accepted tracks are displayed in agenda views but not accessible."
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__message_needaction
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_needaction
msgid "Action Needed"
msgstr "필요한 조치"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__active
#: model:ir.model.fields,field_description:website_event_track.field_event_track__active
msgid "Active"
msgstr "활성"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__activity_ids
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_ids
msgid "Activities"
msgstr "활동"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__activity_exception_decoration
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "활동 예외 장식"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__activity_state
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_state
msgid "Activity State"
msgstr "활동 상태"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__activity_type_icon
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_type_icon
msgid "Activity Type Icon"
msgstr "활동 유형 아이콘"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.event_track_stage_action
msgid "Add a new stage in the task pipeline"
msgstr "파이프라인 활동 안에 새로운 단계 추가하기"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track_from_event
msgid "Add a new track"
msgstr "신규 단계 추가하기"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.event_track_visitor_action
msgid "Add a new track visitor"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track19
msgid "Advanced lead management : tips and tricks from the fields"
msgstr "고급 영업제안 관리 : 현장 경험에 의한 팁과 요령"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track13
msgid "Advanced reporting"
msgstr "고급 보고"

#. module: website_event_track
#: code:addons/website_event_track/models/event_event.py:0
#, python-format
msgid "Agenda"
msgstr "의제"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.session_topbar
msgid "All Talks"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_event_view_form
msgid "Allow Track Proposals"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"Allow video and audio recording of their\n"
"                                    presentation, for publishing on our website."
msgstr ""
"웹 사이트에 게시할 수 있도록 \n"
"                                    프레젠테이션의 비디오 및 오디오 녹화를 허용합니다."

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__wishlisted_by_default
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Always Wishlisted"
msgstr ""

#. module: website_event_track
#: model:event.track.stage,name:website_event_track.event_track_stage2
msgid "Announced"
msgstr "발표함"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal_success
msgid "Application"
msgstr "응용 프로그램"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_sponsor_view_form
#: model_terms:ir.ui.view,arch_db:website_event_track.event_sponsor_view_search
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Archived"
msgstr "아카이브됨"

#. module: website_event_track
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_4
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_5
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_6
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_7
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_8
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_9
#: model_terms:event.sponsor,website_description:website_event_track.event_sponsor_1
msgid "As a team, we are happy to contribute to this event."
msgstr "팀으로서 우리는 이 이벤트에 기여하게 되어 기쁩니다."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track_visitor__is_blacklisted
msgid ""
"As key track cannot be un-wishlisted, this field store the partner choice to"
" remove the reminder for key tracks."
msgstr ""

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_17
msgid ""
"As you may have heard before, making your own furniture is actually not as difficult or as complicated as you think.\n"
"    In fact, some projects are so easy anyone could successfully complete them. For example, making a cute stool out of\n"
"    a old tire is a real piece of cake and if you’re in need of a coffee table you can easily put one together using\n"
"    wood crates."
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__message_attachment_count
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_attachment_count
msgid "Attachment Count"
msgstr "첨부 파일 수"

#. module: website_event_track
#: model:event.track.tag.category,name:website_event_track.event_track_tag_category_1
msgid "Audience"
msgstr "청중"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__allowed_track_tag_ids
msgid "Available Track Tags"
msgstr "과정 태그 사용 가능"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "Bandy clamp hack"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_biography
msgid "Biography"
msgstr "이력"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.registration_complete
msgid "Book your seats to the best talks"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "Book your talks"
msgstr ""

#. module: website_event_track
#: model:event.sponsor.type,name:website_event_track.event_sponsor_type1
#: model:ir.model.fields.selection,name:website_event_track.selection__event_sponsor_type__display_ribbon_style__bronze
msgid "Bronze"
msgstr "동"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_14
msgid "Building a DIY cabin from the ground up"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_cta_url
msgid "Button Target URL"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_cta_title
msgid "Button Title"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_cta_delay
msgid "Button appears"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__is_website_cta_live
msgid "CTA button is available"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Call for Proposals"
msgstr "제안 요청"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__can_publish
msgid "Can Publish"
msgstr "게시 가능"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__is_cancel
msgid "Canceled Stage"
msgstr "단계 취소됨"

#. module: website_event_track
#: model:event.track.stage,name:website_event_track.event_track_stage5
msgid "Cancelled"
msgstr "취소 됨"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__category_id
msgid "Category"
msgstr "범주"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_12
msgid "Climate positive"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__color
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__color
msgid "Color"
msgstr "색상"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__color
msgid "Color Index"
msgstr "색상표"

#. module: website_event_track
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_4
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_5
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_6
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_7
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_8
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_9
#: model_terms:event.sponsor,website_description:website_event_track.event_sponsor_1
msgid "Come see us live, we hope to meet you !"
msgstr "생방송으로 보러 오세요, 당신과 만나길 바랍니다!"

#. module: website_event_track
#: code:addons/website_event_track/controllers/event_track.py:0
#, python-format
msgid "Coming soon"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_cards
msgid "Coming soon ..."
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__company_id
msgid "Company"
msgstr "회사"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_company_name
msgid "Company Name"
msgstr "회사명"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_res_config_settings
msgid "Config Settings"
msgstr "설정 구성"

#. module: website_event_track
#: model:mail.template,subject:website_event_track.mail_template_data_track_confirmation
msgid "Confirmation of ${object.name}"
msgstr "${object.name} 확인"

#. module: website_event_track
#: model:event.track.stage,name:website_event_track.event_track_stage1
msgid "Confirmed"
msgstr "확인됨"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_13
#: model_terms:event.track,description:website_event_track.event_7_track_3
msgid ""
"Considering to build a wooden house? Watch this video to find out more "
"information about a construction process and final result. Step by step "
"simple explanation! Interested?"
msgstr ""

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.event_sponsor_action_from_event
msgid "Create a Sponsor / Exhibitor"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_type__create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track__create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__create_uid
msgid "Created by"
msgstr "작성자"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_type__create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track__create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__create_date
msgid "Created on"
msgstr "작성일"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_search
msgid "Customer"
msgstr "고객"

#. module: website_event_track
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_3
#: model_terms:event.sponsor,website_description:website_event_track.event_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_track.event_sponsor_3
msgid "Customer Relationship Management"
msgstr "고객 관계 관리"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_19
msgid "DIY Timber Cladding Project"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Date"
msgstr "날짜"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_11
msgid "Day 2 Wrapup"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_8
msgid "Dealing with OpenWood Furniture"
msgstr ""

#. module: website_event_track
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_3
#: model_terms:event.sponsor,website_description:website_event_track.event_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_track.event_sponsor_3
msgid ""
"Deco Addict designs, develops, integrates and supports HR and Supply\n"
"                Chain processes in order to make our customers more productive,\n"
"                responsive and profitable."
msgstr ""

#. module: website_event_track
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_3
#: model_terms:event.sponsor,website_description:website_event_track.event_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_track.event_sponsor_3
msgid ""
"Deco Addict integrates ERP for Global Companies and supports PME\n"
"                with Open Sources software to manage their businesses. Our\n"
"                consultants are experts in the following areas:"
msgstr ""

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.event_track_stage_action
msgid ""
"Define the steps that will be used in the event from the\n"
"            creation of the track, up to the closing of the track.\n"
"            You will use these stages in order to track the progress in\n"
"            solving an event track."
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_kanban
msgid "Delete"
msgstr "삭제"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__description
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Description"
msgstr "내용"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track25
msgid "Design contest (entire afternoon)"
msgstr "디자인 공모전 (오후 입장)"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track24
msgid "Design contest (entire day)"
msgstr "디자인 공모전 (하루종일)"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track6
msgid "Detailed roadmap of our new products"
msgstr "신제품의 상세 로드맵"

#. module: website_event_track
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_4
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_5
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_6
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_7
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_8
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_9
#: model_terms:event.sponsor,website_description:website_event_track.event_sponsor_1
msgid "Discover more"
msgstr "더 알아보기"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track11
msgid "Discover our new design team"
msgstr "새로운 디자인 팀을 만나보세요"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_type__display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track__display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_type__display_name
#: model:ir.model.fields,field_description:website_event_track.field_res_config_settings__display_name
#: model:ir.model.fields,field_description:website_event_track.field_website__display_name
#: model:ir.model.fields,field_description:website_event_track.field_website_event_menu__display_name
#: model:ir.model.fields,field_description:website_event_track.field_website_menu__display_name
#: model:ir.model.fields,field_description:website_event_track.field_website_visitor__display_name
msgid "Display Name"
msgstr "이름 표시"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_aside_other_track
msgid "Done"
msgstr "완료"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "Dowel Hack"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_kanban
msgid "Dropdown menu"
msgstr "드롭다운 메뉴"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__duration
msgid "Duration"
msgstr "기간"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_3
msgid "Easy Way To Build a Wooden House"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_kanban
msgid "Edit Track"
msgstr "과정 편집하기"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__partner_email
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_email
#: model_terms:ir.ui.view,arch_db:website_event_track.event_sponsor_view_form
msgid "Email"
msgstr "이메일"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__mail_template_id
msgid "Email Template"
msgstr "이메일 서식"

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#, python-format
msgid "Error"
msgstr "오류"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_event
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__event_id
#: model:ir.model.fields,field_description:website_event_track.field_event_track__event_id
#: model_terms:ir.ui.view,arch_db:website_event_track.event_sponsor_view_search
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Event"
msgstr "행사"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_location_tree
msgid "Event Location"
msgstr "행사 위치"

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.action_event_track_location
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_location_form
msgid "Event Locations"
msgstr "행사 위치"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__track_proposal_menu_ids
#: model:ir.model.fields.selection,name:website_event_track.selection__website_event_menu__menu_type__track_proposal
msgid "Event Proposals Menus"
msgstr "행사 제안서 메뉴"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_sponsor
msgid "Event Sponsor"
msgstr "행사 스폰서"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_sponsor_type
#: model_terms:ir.ui.view,arch_db:website_event_track.event_sponsor_type_view_tree
msgid "Event Sponsor Type"
msgstr "행사 스폰서 유형"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_sponsor_type_view_form
msgid "Event Sponsor Types"
msgstr "행사 스폰서 유형"

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.event_sponsor_action_from_event
#: model_terms:ir.ui.view,arch_db:website_event_track.event_sponsor_view_search
msgid "Event Sponsors"
msgstr "행사 스폰서"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_type
msgid "Event Template"
msgstr ""

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_tree
msgid "Event Track"
msgstr "행사 과정"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track_location
msgid "Event Track Location"
msgstr "행사 과정별 위치"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track_stage
msgid "Event Track Stage"
msgstr "행사 과정별 단계"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track_tag
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_tag_form
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_tag_tree
msgid "Event Track Tag"
msgstr "행사 과정 태그"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track_tag_category
msgid "Event Track Tag Category"
msgstr ""

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.action_event_track
#: model:ir.actions.act_window,name:website_event_track.action_event_track_from_event
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_calendar
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Event Tracks"
msgstr "행사 과정"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__track_menu_ids
#: model:ir.model.fields.selection,name:website_event_track.selection__website_event_menu__menu_type__track
msgid "Event Tracks Menus"
msgstr "행사 과정 메뉴"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_21
msgid "Event Wrapup"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_res_config_settings__events_app_name
#: model:ir.model.fields,field_description:website_event_track.field_website__events_app_name
msgid "Events App Name"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.res_config_settings_view_form
msgid "Events PWA"
msgstr ""

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#: model_terms:ir.ui.view,arch_db:website_event_track.track_widget_reminder
#, python-format
msgid "Favorite On"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.session_topbar
msgid "Favorites"
msgstr "즐겨찾기"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Fill this form to propose your talk."
msgstr "강연을 제안하려면 이 양식을 작성하십시오."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_topbar
msgid "Filter Tracks..."
msgstr "필터 과정..."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "Finished"
msgstr "완료됨"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_2
msgid "First Day Wrapup"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__fold
msgid "Folded in Kanban"
msgstr "칸반 화면 접기"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__message_follower_ids
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_follower_ids
msgid "Followers"
msgstr "팔로워"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__message_channel_ids
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_channel_ids
msgid "Followers (Channels)"
msgstr "팔로워 (채널)"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__message_partner_ids
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_partner_ids
msgid "Followers (Partners)"
msgstr "팔로워 (파트너)"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_sponsor__activity_type_icon
#: model:ir.model.fields,help:website_event_track.field_event_track__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "멋진 아이콘 폰트 예 : fa-tasks"

#. module: website_event_track
#: model:event.track.tag.category,name:website_event_track.event_track_tag_category_2
msgid "Format"
msgstr "형식"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Future Activities"
msgstr "향후 활동"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.registration_complete
msgid "Get prepared and"
msgstr ""

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "Glue tip"
msgstr ""

#. module: website_event_track
#: model:event.sponsor.type,name:website_event_track.event_sponsor_type3
#: model:ir.model.fields.selection,name:website_event_track.selection__event_sponsor_type__display_ribbon_style__gold
msgid "Gold"
msgstr "금"

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__kanban_state__done
msgid "Green"
msgstr "녹색"

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__kanban_state__normal
msgid "Grey"
msgstr "회색"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_sponsor_view_search
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_search
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Group By"
msgstr "그룹별"

#. module: website_event_track
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_4
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_5
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_6
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_7
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_8
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_9
#: model_terms:event.sponsor,website_description:website_event_track.event_sponsor_1
msgid "Happy to be Sponsor"
msgstr "스폰서가 되어 기쁩니다"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_l3_11
msgid "Happy with OpenWood"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__priority__2
msgid "High"
msgstr "높음"

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__priority__3
msgid "Highest"
msgstr "가장 높음"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.pwa_offline
msgid "Home page"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track21
msgid "House of World Cultures"
msgstr "세계 문화의 집"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track18
msgid "How to build your marketing strategy within a competitive environment"
msgstr "경쟁 환경에서 마케팅 전략을 세우는 방법"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track15
msgid "How to communicate with your community"
msgstr "커뮤니티와 소통하는 방법"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track1
msgid "How to design a new piece of furniture"
msgstr "새로운 가구를 디자인하는 방법"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track4
msgid "How to develop automated processes"
msgstr "자동화된 프로세스를 개발하는 방법"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track16
msgid "How to follow us on the social media"
msgstr "소셜 미디어에서 우리를 따르는 방법"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track9
msgid "How to improve your quality processes"
msgstr "품질 프로세스를 개선하는 방법"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track2
msgid "How to integrate hardware materials in your pieces of furniture"
msgstr "가구에 하드웨어 재료를 통합하는 방법"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track8
msgid "How to optimize your sales, from leads to sales orders"
msgstr "영업제안에서 판매 주문에 이르기까지 판매를 최적화하는 방법"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__id
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__id
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_type__id
#: model:ir.model.fields,field_description:website_event_track.field_event_track__id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__id
#: model:ir.model.fields,field_description:website_event_track.field_event_type__id
#: model:ir.model.fields,field_description:website_event_track.field_res_config_settings__id
#: model:ir.model.fields,field_description:website_event_track.field_website__id
#: model:ir.model.fields,field_description:website_event_track.field_website_event_menu__id
#: model:ir.model.fields,field_description:website_event_track.field_website_menu__id
#: model:ir.model.fields,field_description:website_event_track.field_website_visitor__id
msgid "ID"
msgstr "ID"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__activity_exception_icon
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_exception_icon
msgid "Icon"
msgstr "아이콘"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_sponsor__activity_exception_icon
#: model:ir.model.fields,help:website_event_track.field_event_track__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "예외 활동을 표시하기 위한 아이콘"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_sponsor__message_needaction
#: model:ir.model.fields,help:website_event_track.field_event_sponsor__message_unread
#: model:ir.model.fields,help:website_event_track.field_event_track__message_needaction
#: model:ir.model.fields,help:website_event_track.field_event_track__message_unread
msgid "If checked, new messages require your attention."
msgstr "만약 선택하였으면, 새 메시지에 주의를 기울여야 합니다."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_sponsor__message_has_error
#: model:ir.model.fields,help:website_event_track.field_event_sponsor__message_has_sms_error
#: model:ir.model.fields,help:website_event_track.field_event_track__message_has_error
#: model:ir.model.fields,help:website_event_track.field_event_track__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "이 옵션을 선택하면 일부 정보가 전달 오류를 생성합니다."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track_stage__mail_template_id
msgid ""
"If set an email will be sent to the customer when the track reaches this "
"step."
msgstr "설정된 경우 과정이 이 단계에 도달하면 고객에게 이메일이 전송됩니다."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__wishlisted_by_default
msgid ""
"If set, the talk will be starred for each attendee registered to the event. "
"The attendee won't be able to un-star this talk."
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__image_128
msgid "Image 128"
msgstr "128 이미지"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__image_256
msgid "Image 256"
msgstr "256 이미지"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__website_image_url
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_image_url
msgid "Image URL"
msgstr "이미지 URL"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_cards_track
msgid "In"
msgstr "In"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_15
msgid "In this video we will see how lumber is made in a sawmill factory."
msgstr ""

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "In this video, I covered 6 tips and tricks to help out beginners:"
msgstr ""

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/xml/website_event_pwa.xml:0
#, python-format
msgid "Install"
msgstr "설치하기"

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/xml/website_event_pwa.xml:0
#, python-format
msgid "Install Application"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Interactivity"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Introduction"
msgstr "소개"

#. module: website_event_track
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_3
#: model_terms:event.sponsor,website_description:website_event_track.event_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_track.event_sponsor_3
msgid "Inventory and Warehouse management"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_accepted
msgid "Is Accepted"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_website_cta_live
msgid "Is CTA Live"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__is_done
msgid "Is Done"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__message_is_follower
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_is_follower
msgid "Is Follower"
msgstr "팔로워입니다"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_published
msgid "Is Published"
msgstr "게시 여부"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_reminder_on
msgid "Is Reminder On"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_track_done
msgid "Is Track Done"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_track_live
msgid "Is Track Live"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_track_soon
msgid "Is Track Soon"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_track_today
msgid "Is Track Today"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_track_upcoming
msgid "Is Track Upcoming"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__is_wishlisted
msgid "Is Wishlisted"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__is_blacklisted
msgid "Is reminder off"
msgstr ""

#. module: website_event_track
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_2
#: model_terms:event.sponsor,website_description:website_event_track.event_sponsor_2
msgid ""
"It provides post-graduate education in fluid dynamics\n"
"                    (research master in fluid dynamics, former \"Diploma\n"
"                    Course\", doctoral program, stagiaire program and lecture\n"
"                    series) and encourages \"training in research through\n"
"                    research\"."
msgstr ""

#. module: website_event_track
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_2
#: model_terms:event.sponsor,website_description:website_event_track.event_sponsor_2
msgid ""
"It undertakes and promotes research in the field of fluid\n"
"                    dynamics. It possesses about fifty different wind tunnels,\n"
"                    turbomachinery and other specialized test facilities, some\n"
"                    of which are unique or the largest in the world. Extensive\n"
"                    research on experimental, computational and theoretical\n"
"                    aspects of gas and liquid flows is carried out under the\n"
"                    direction of the faculty and research engineers, sponsored\n"
"                    mainly by governmental and international agencies as well\n"
"                    as industries."
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_function
msgid "Job Position"
msgstr "직무 영역"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__kanban_state
msgid "Kanban State"
msgstr "칸반 상태"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track23
msgid "Key Success factors selling our furniture"
msgstr "가구를 판매하는 주요 성공 요인"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_9
msgid "Kitchens for the Future"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event____last_update
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor____last_update
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_type____last_update
#: model:ir.model.fields,field_description:website_event_track.field_event_track____last_update
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location____last_update
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage____last_update
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag____last_update
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category____last_update
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor____last_update
#: model:ir.model.fields,field_description:website_event_track.field_event_type____last_update
#: model:ir.model.fields,field_description:website_event_track.field_res_config_settings____last_update
#: model:ir.model.fields,field_description:website_event_track.field_website____last_update
#: model:ir.model.fields,field_description:website_event_track.field_website_event_menu____last_update
#: model:ir.model.fields,field_description:website_event_track.field_website_menu____last_update
#: model:ir.model.fields,field_description:website_event_track.field_website_visitor____last_update
msgid "Last Modified on"
msgstr "최근 수정"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_type__write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track__write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__write_uid
msgid "Last Updated by"
msgstr "최근 갱신한 사람"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_type__write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track__write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__write_date
msgid "Last Updated on"
msgstr "최근 갱신 날짜"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Late Activities"
msgstr "지연된 활동"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track12
msgid "Latest trends"
msgstr "최신 경향"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_26
msgid "Less Furniture is More Furniture"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_sponsor_view_kanban
msgid "Level:"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_4
msgid "Life at Home Around the World: William’s Story"
msgstr ""

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_19
msgid ""
"Link to Q&amp;A here! The time has come to hide those old block walls. Love "
"simple and transformation type projects like this! :)-"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_main_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_aside_other_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "Live"
msgstr "실시간"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_cards
msgid "Live Now"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_l3_10
msgid "Live Testimonial"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_25
msgid "Live Testimonials"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__location_id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__name
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Location"
msgstr "위치"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_13
msgid "Log House Building"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__image_512
msgid "Logo"
msgstr "로고"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_15
msgid "Logs to lumber"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__priority__0
msgid "Low"
msgstr "낮음"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track31
msgid "Lunch"
msgstr "점심"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_cta
msgid "Magic Button"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__message_main_attachment_id
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_main_attachment_id
msgid "Main Attachment"
msgstr "주요 첨부 파일"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "Making a center marking gauge"
msgstr ""

#. module: website_event_track
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_3
#: model_terms:event.sponsor,website_description:website_event_track.event_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_track.event_sponsor_3
msgid "Materials Management"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__priority__1
msgid "Medium"
msgstr "중간"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_website_event_menu__menu_type
msgid "Menu Type"
msgstr "메뉴 유형"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__message_has_error
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_has_error
msgid "Message Delivery error"
msgstr "메시지 전송 오류"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__message_ids
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_ids
msgid "Messages"
msgstr "메시지"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track22
msgid "Minimal but efficient design"
msgstr "최소이지만 효율적인 디자인"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_cta_start_remaining
msgid "Minutes before CTA starts"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__track_start_remaining
msgid "Minutes before track starts"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__track_start_relative
msgid "Minutes compare to track start"
msgstr ""

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "Miter saw tip"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__partner_mobile
#: model_terms:ir.ui.view,arch_db:website_event_track.event_sponsor_view_form
msgid "Mobile"
msgstr "휴대전화"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track30
msgid "Morning break"
msgstr "아침 휴식"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track27
msgid "My Company global presentation"
msgstr "내 회사 글로벌 프리젠테이션"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "My Tracks"
msgstr "나의 과정"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__partner_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__name
#: model_terms:ir.ui.view,arch_db:website_event_track.res_config_settings_view_form
msgid "Name"
msgstr "이름"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.res_config_settings_view_form
msgid "Name of your website's Events Progressive Web Application"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track20
msgid "New Certification Program"
msgstr "새 인증 프로그램"

#. module: website_event_track
#: model:mail.message.subtype,name:website_event_track.mt_event_track
msgid "New Track"
msgstr "새로운 과정"

#. module: website_event_track
#: model:mail.message.subtype,description:website_event_track.mt_event_track
msgid "New Track Created"
msgstr "새 과정 생성함"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "New track proposal"
msgstr "새 과정 제안"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__activity_date_deadline
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "다음 활동 마감일"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__activity_summary
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_summary
msgid "Next Activity Summary"
msgstr "다음 활동 요약"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__activity_type_id
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_type_id
msgid "Next Activity Type"
msgstr "다음 활동 유형"

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_sponsor_type__display_ribbon_style__no_ribbon
msgid "No Ribbon"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_main
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_main
msgid "No track found."
msgstr ""

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.event_track_action_from_visitor
msgid "No track wishlisted by this visitor"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_main_track
msgid "Not Accepted"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track_tag__color
msgid "Note that colorless tags won't be available on the website."
msgstr "웹 사이트에서는 무색 태그를 사용할 수 없습니다."

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__message_needaction_counter
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_needaction_counter
msgid "Number of Actions"
msgstr "작업 수"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__message_has_error_counter
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_has_error_counter
msgid "Number of errors"
msgstr "오류 횟수"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_sponsor__message_needaction_counter
#: model:ir.model.fields,help:website_event_track.field_event_track__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "조치가 필요한 메시지 수"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_sponsor__message_has_error_counter
#: model:ir.model.fields,help:website_event_track.field_event_track__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "전송 오류 메시지 수"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_sponsor__message_unread_counter
#: model:ir.model.fields,help:website_event_track.field_event_track__message_unread_counter
msgid "Number of unread messages"
msgstr "읽지 않은 메시지 수"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_24
msgid "Old is New"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_20
msgid "Our Last Day Together !"
msgstr ""

#. module: website_event_track
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_3
#: model_terms:event.sponsor,website_description:website_event_track.event_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_track.event_sponsor_3
msgid ""
"Our experts invent, imagine and develop solutions which meet\n"
"                your business requirements. They build a new technical\n"
"                environment for your company, but they always take the already\n"
"                installed IT software into account. That is why Idealis\n"
"                Consulting delivers excellence in HR and SC Management."
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__partner_id
#: model_terms:ir.ui.view,arch_db:website_event_track.event_sponsor_view_form
msgid "Partner"
msgstr "협력사"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track14
msgid "Partnership programs"
msgstr "파트너십 프로그램"

#. module: website_event_track
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_3
#: model_terms:event.sponsor,website_description:website_event_track.event_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_track.event_sponsor_3
msgid "Personnel Administration"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__partner_phone
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_phone
#: model_terms:ir.ui.view,arch_db:website_event_track.event_sponsor_view_form
msgid "Phone"
msgstr "전화번호"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "Plan your experience by adding your favorites talks to your wishlist"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track3
msgid "Portfolio presentation"
msgstr "포트폴리오 프리젠테이션"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_16
msgid "Pretty. Ugly. Lovely."
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.pwa_offline
msgid "Previous page"
msgstr "이전 페이지"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__priority
msgid "Priority"
msgstr "우선 순위"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_7
msgid ""
"Probably one of the most asked questions I've gotten is how I got started "
"woodworking! In this video I share with you how/why I started building "
"furniture!"
msgstr ""

#. module: website_event_track
#: model:event.track.stage,name:website_event_track.event_track_stage0
msgid "Proposal"
msgstr "제안"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Proposals are closed!"
msgstr "제안이 마감되었습니다!"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__website_track_proposal
msgid "Proposals on Website"
msgstr "웹사이트에서 제안"

#. module: website_event_track
#: model:event.track.stage,name:website_event_track.event_track_stage3
msgid "Published"
msgstr "게시 됨"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track10
msgid "Raising qualitive insights from your customers"
msgstr "고객의 긍정적인 통찰력 향상"

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__kanban_state__blocked
msgid "Red"
msgstr "빨간색"

#. module: website_event_track
#: model:event.track.stage,name:website_event_track.event_track_stage4
msgid "Refused"
msgstr "반려됨"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__track_start_relative
msgid "Relative time compared to track start (seconds)"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__website_cta_start_remaining
msgid "Remaining time before CTA starts (seconds)"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__track_start_remaining
msgid "Remaining time before track starts (seconds)"
msgstr ""

#. module: website_event_track
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_3
#: model_terms:event.sponsor,website_description:website_event_track.event_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_track.event_sponsor_3
msgid "Reporting"
msgstr "보고"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__user_id
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Responsible"
msgstr "담당자"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__activity_user_id
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_user_id
msgid "Responsible User"
msgstr "담당 사용자"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_23
msgid "Restoring Old Woodworking Tools"
msgstr ""

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_23
msgid "Restoring old woodworking tools"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_type__display_ribbon_style
msgid "Ribbon Style"
msgstr ""

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "Right angle clamp jig"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_seo_optimized
msgid "SEO optimized"
msgstr "SEO 최적화"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__message_has_sms_error
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS 전송 에러"

#. module: website_event_track
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_3
#: model_terms:event.sponsor,website_description:website_event_track.event_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_track.event_sponsor_3
msgid "Sales and Distribution"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_main
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_main
msgid "Schedule some tracks to get started !"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_6
msgid "Securing your Lumber during transport"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__seo_name
msgid "Seo name"
msgstr "Seo 이름"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__sequence
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_type__sequence
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__sequence
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__sequence
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__sequence
msgid "Sequence"
msgstr "순차적"

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#: model_terms:ir.ui.view,arch_db:website_event_track.track_widget_reminder
#, python-format
msgid "Set Favorite"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Show all records which has next action date is before today"
msgstr "다음 행동 날짜가 오늘 이전 인 모든 기록보기"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_event_view_form
msgid "Showcase Tracks"
msgstr ""

#. module: website_event_track
#: model:event.sponsor.type,name:website_event_track.event_sponsor_type2
#: model:ir.model.fields.selection,name:website_event_track.selection__event_sponsor_type__display_ribbon_style__silver
msgid "Silver"
msgstr "은"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_id
msgid "Speaker"
msgstr "연사"

#. module: website_event_track
#: code:addons/website_event_track/models/event_track.py:0
#, python-format
msgid "Speaker Email"
msgstr "연사 이메일"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__image
msgid "Speaker Photo"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Speaker(s) Biography"
msgstr "연사 이력"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Speaker(s) Email"
msgstr "연사 이메일"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Speaker(s) Name"
msgstr "연사 이름"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Speaker(s) Phone"
msgstr "연사 전화번호"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Speaker(s) Picture"
msgstr ""

#. module: website_event_track
#: code:addons/website_event_track/models/event_track.py:0
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
#, python-format
msgid "Speakers"
msgstr "연사"

#. module: website_event_track
#: code:addons/website_event_track/models/event_sponsor.py:0
#, python-format
msgid "Sponsor"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__sponsor_count
msgid "Sponsor Count"
msgstr "스폰서 수"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__email
msgid "Sponsor Email"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__mobile
msgid "Sponsor Mobile"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__name
#: model_terms:ir.ui.view,arch_db:website_event_track.event_sponsor_view_form
msgid "Sponsor Name"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__phone
msgid "Sponsor Phone"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_type__name
msgid "Sponsor Type"
msgstr "스폰서 유형"

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.event_sponsor_type_action
#: model:ir.ui.menu,name:website_event_track.menu_event_sponsor_type
msgid "Sponsor Types"
msgstr "스폰서 유형"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__url
msgid "Sponsor Website"
msgstr "스폰서 웹사이트"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_sponsor_view_kanban
msgid "Sponsor image"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__partner_id
msgid "Sponsor/Customer"
msgstr "스폰서/고객"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__sponsor_type_id
msgid "Sponsoring Type"
msgstr "스폰서 유형"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__sponsor_ids
#: model_terms:ir.ui.view,arch_db:website_event_track.event_event_view_form
msgid "Sponsors"
msgstr "스폰서"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.event_sponsor_action_from_event
msgid ""
"Sponsors might be advertised on your event pages footer.<br>\n"
"    Exhibitors might have a dedicated page with chat room for people to connect with them."
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_sponsor_view_search
msgid "Sponsorship"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__stage_id
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Stage"
msgstr "단계"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__name
msgid "Stage Name"
msgstr "단계명"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
msgid "Starting in"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track28
msgid "Status & Strategy"
msgstr "상태 및 전략"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_sponsor__activity_state
#: model:ir.model.fields,help:website_event_track.field_event_track__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"활동에 기조한 상태입니다\n"
"기한초과: 이미 기한이 지났습니다\n"
"오늘: 활동 날짜가 오늘입니다\n"
"계획: 향후 활동입니다."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Submission Agreement"
msgstr "제출 동의"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Submit Proposal"
msgstr "제안 제출"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__name
msgid "Tag Name"
msgstr "태그 이름"

#. module: website_event_track
#: model:ir.model.constraint,message:website_event_track.constraint_event_track_tag_name_uniq
msgid "Tag name already exists !"
msgstr "동일한 태그명이 존재합니다!"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__tag_ids
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__tag_ids
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_tag_category_view_form
msgid "Tags"
msgstr "태그"

#. module: website_event_track
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_track.event_7_sponsor_3
#: model_terms:event.sponsor,website_description:website_event_track.event_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_track.event_sponsor_3
msgid "Talent Management"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Talk Introduction"
msgstr "강연 소개"

#. module: website_event_track
#: code:addons/website_event_track/models/event_event.py:0
#, python-format
msgid "Talk Proposals"
msgstr "강연 제안"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Talk Title"
msgstr "강연 제목"

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#, python-format
msgid "Talk added to your Favorites"
msgstr ""

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#, python-format
msgid "Talk already in your Favorites"
msgstr ""

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#, python-format
msgid "Talk removed from your Favorites"
msgstr ""

#. module: website_event_track
#: code:addons/website_event_track/models/event_event.py:0
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_aside
#, python-format
msgid "Talks"
msgstr "강연"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Talks Types"
msgstr "강연 유형"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal_success
msgid "Thank you for your proposal."
msgstr "여러분의 제안에 감사드립니다."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__website_url
msgid "The full URL to access the document through the website."
msgstr "웹사이트를 통해 문서에 접근 하는 전체 URL입니다."

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track17
#: model:event.track,name:website_event_track.event_track29
msgid "The new marketing strategy"
msgstr "새로운 마케팅 전략"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track5
msgid "The new way to promote your creations"
msgstr "창작물을 홍보하는 새로운 방법"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_17
msgid ""
"There are a lot of ideas worth exploring so start with the 10 DIY furniture "
"ideas for absolute beginners."
msgstr ""

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_5
msgid ""
"There are several variants of wood is available in the world but we are talking about most expensive\n"
"    ones in the world and keeping to the point we have arranged ten most expensive wood."
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "This event does not accept proposals."
msgstr "이 행사는 제안을 수락하지 않습니다."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_website__app_icon
msgid ""
"This field holds the image used as mobile app icon on the website (PNG "
"format)."
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_res_config_settings__events_app_name
#: model:ir.model.fields,help:website_event_track.field_website__events_app_name
msgid "This fields holds the Event's Progressive Web App name."
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.pwa_offline
msgid ""
"This page hasn't been saved for offline reading yet.<br/>Please check your "
"network connection."
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track_stage__fold
msgid ""
"This stage is folded in the kanban view when there are no records in that "
"stage to display."
msgstr "이 단계에 표시할 레코드가 없으면 칸반 화면은 접혀 있습니다."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"Timely release of presentation material (slides),\n"
"                                    for publishing on our website."
msgstr ""
"웹 사이트에 게시할 프레젠테이션 자료(슬라이드)를 \n"
"                                      시기별로 출시합니다."

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__name
msgid "Title"
msgstr "제목"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Today Activities"
msgstr "오늘 활동"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_22
msgid "Tools for the Woodworking Beginner"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_5
msgid "Top 10 Most Expensive Wood in the World"
msgstr ""

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_5
msgid ""
"Top most expensive wood in the world is quite interesting topic and several people may be surprised\n"
"    that there are hundreds of wood types exist around the globe following different properties and use."
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__track_id
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_search
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Track"
msgstr "과정"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track_visitor
msgid "Track / Visitor Link"
msgstr ""

#. module: website_event_track
#: model:mail.message.subtype,name:website_event_track.mt_track_blocked
msgid "Track Blocked"
msgstr "과정 진행이 막혔습니다"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__track_count
msgid "Track Count"
msgstr "과정 수"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__date
msgid "Track Date"
msgstr "과정 일자"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__date_end
msgid "Track End Date"
msgstr "과정 마감일"

#. module: website_event_track
#: model:ir.ui.menu,name:website_event_track.menu_event_track_location
msgid "Track Locations"
msgstr ""

#. module: website_event_track
#: model:mail.message.subtype,name:website_event_track.mt_track_ready
msgid "Track Ready"
msgstr "과정 시작"

#. module: website_event_track
#: model:mail.message.subtype,description:website_event_track.mt_track_ready
msgid "Track Ready for Next Stage"
msgstr "다음 단계 과정 대기"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_search
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_tree
msgid "Track Stage"
msgstr "과정 단계"

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.event_track_stage_action
#: model:ir.ui.menu,name:website_event_track.event_track_stage_menu
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_search
msgid "Track Stages"
msgstr "과정 단계"

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.event_track_tag_category_action
#: model:ir.ui.menu,name:website_event_track.event_track_tag_category_menu
msgid "Track Tag Categories"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_tag_category_view_form
msgid "Track Tag Category"
msgstr ""

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.action_event_track_tag
#: model:ir.model.fields,field_description:website_event_track.field_event_event__tracks_tag_ids
#: model:ir.ui.menu,name:website_event_track.menu_event_track_tag
msgid "Track Tags"
msgstr "과정 태그"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_tag_category_view_list
msgid "Track Tags Category"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_form
msgid "Track Visitor"
msgstr ""

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.event_track_visitor_action
#: model:ir.model.fields,field_description:website_event_track.field_event_track__event_track_visitor_ids
#: model:ir.model.fields,field_description:website_event_track.field_website_visitor__event_track_visitor_ids
#: model:ir.ui.menu,name:website_event_track.event_track_visitor_menu
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_list
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_search
msgid "Track Visitors"
msgstr ""

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.event_track_visitor_action
msgid ""
"Track Visitors store statistics on track attendance, including\n"
"              wishlist management."
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__is_track_soon
msgid "Track begins soon"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__is_track_today
msgid "Track begins today"
msgstr ""

#. module: website_event_track
#: model:mail.message.subtype,description:website_event_track.mt_track_blocked
msgid "Track blocked"
msgstr "과정 진행이 막힘"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__duration
msgid "Track duration in hours."
msgstr "시간 단위로 추적합니다."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__is_track_live
msgid "Track has started and is ongoing"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__is_track_done
msgid "Track is finished"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__is_track_upcoming
msgid "Track is not yet started"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__track_ids
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__track_ids
#: model:ir.ui.menu,name:website_event_track.menu_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_graph
#: model_terms:ir.ui.view,arch_db:website_event_track.website_visitor_view_form
msgid "Tracks"
msgstr "과정"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_type__website_track_proposal
msgid "Tracks Proposals on Website"
msgstr "웹사이트에서 과정 제안"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track_from_event
msgid ""
"Tracks define the schedule of your event.<br>These can be a talk, a round "
"table, a meeting, etc."
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__website_track
#: model:ir.model.fields,field_description:website_event_track.field_event_type__website_track
msgid "Tracks on Website"
msgstr "웹사이트에서의 과정"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_sponsor__activity_exception_decoration
#: model:ir.model.fields,help:website_event_track.field_event_track__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "레코드에 있는 예외 활동의 유형입니다."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_main_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "Unpublished"
msgstr "게시 안 함"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__message_unread
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_unread
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Unread Messages"
msgstr "읽지 않은 메세지"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__message_unread_counter
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_unread_counter
msgid "Unread Messages Counter"
msgstr "읽지 않은 메세지 수"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_6
msgid ""
"Use these simple steps to easily haul LONG lumber in a short box pickup truck.  A dose of carpenter's\n"
"    ingenuity along with a couple boards, a sturdy strap and a few screws are all I use to easily haul\n"
"    long boards from the lumberyard to the Next Level Carpentry shop or jobsite."
msgstr ""

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_6
msgid ""
"Using a unique wrapping method for a tie down strap (NOT Bungee cords!!!) allows lumber to be\n"
"    cinched securely WITHOUT the need to tie and untie tricky or complicated knots."
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_kanban
msgid "View Track"
msgstr "과정 보기"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_published
msgid "Visible on current website"
msgstr "현재 웹 사이트에 공개"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__visitor_id
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_search
msgid "Visitor"
msgstr "방문자"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__wishlist_visitor_ids
msgid "Visitor Wishlist"
msgstr ""

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.website_visitor_action_from_track
msgid "Visitors Wishlist"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_l3_1
msgid "Voice from Customer"
msgstr ""

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.website_visitor_action_from_track
msgid "Wait for visitors to wishlist your tracks"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_main
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_main
msgid "We did not find any track matching your"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "We require speakers to accept an agreement in which they commit to:"
msgstr "당사에서는 연사가 다음 사항을 이행하는 계약을 수락할 것을 요구합니다."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"We will accept a broad range of\n"
"                                presentations, from reports on academic and\n"
"                                commercial projects to tutorials and case\n"
"                                studies. As long as the presentation is\n"
"                                interesting and potentially useful to the\n"
"                                audience, it will be considered for\n"
"                                inclusion in the programme."
msgstr ""
"우리는 학술 및 상업 프로젝트에 대한 보고서에서 \n"
"                                자습서 및 사례 연구에 이르기까지 \n"
"                                광범위한 프레젠테이션을 수락합니다.\n"
"                                프레젠테이션이 흥미롭고 \n"
"                                잠재 고객에게 유용 할 수 있는 한 \n"
"                                프로그램에 포함되는 것으로 간주됩니다."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal_success
msgid "We will evaluate your proposition and get back to you shortly."
msgstr "우리는 당신의 제안을 평가하고 곧 다시 연락을 드릴 것입니다."

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_website
#: model_terms:ir.ui.view,arch_db:website_event_track.event_sponsor_view_form
#: model_terms:ir.ui.view,arch_db:website_event_track.event_sponsor_view_tree
msgid "Website"
msgstr "웹사이트"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_website__app_icon
msgid "Website App Icon"
msgstr ""

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_website_event_menu
msgid "Website Event Menu"
msgstr "웹사이트 행사 메뉴"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_image
msgid "Website Image"
msgstr ""

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_website_menu
msgid "Website Menu"
msgstr "웹사이트 메뉴"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__website_message_ids
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_message_ids
msgid "Website Messages"
msgstr "웹사이트 메시지"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_url
msgid "Website URL"
msgstr "웹 사이트 URL"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_website_visitor
msgid "Website Visitor"
msgstr "웹사이트 방문자"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_sponsor__website_message_ids
#: model:ir.model.fields,help:website_event_track.field_event_track__website_message_ids
msgid "Website communication history"
msgstr "웹사이트 대화 이력"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_meta_description
msgid "Website meta description"
msgstr "웹사이트 메타 설명"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_meta_keywords
msgid "Website meta keywords"
msgstr "웹사이트 메타 핵심어"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_meta_title
msgid "Website meta title"
msgstr "웹사이트 메타 제목"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_meta_og_img
msgid "Website opengraph image"
msgstr "웹사이트 오픈그래프 이미지"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_10
msgid "Welcome to Day 2"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_1
msgid "What This Event Is All About"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_l3_2
msgid "Who's OpenWood anyway ?"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Wishlisted By"
msgstr ""

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.event_track_action_from_visitor
#: model:ir.model.fields,field_description:website_event_track.field_website_visitor__event_track_wishlisted_ids
#: model_terms:ir.ui.view,arch_db:website_event_track.website_visitor_view_kanban
msgid "Wishlisted Tracks"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_7
msgid "Woodworking: How I got started!"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.pwa_offline
msgid "You're offline!"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_cards_track
msgid "ago"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_sponsor_view_form
msgid "e.g. : OpenWood Decoration"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_sponsor_view_form
msgid "e.g. : <EMAIL>"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "e.g. Get Yours Now !"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "e.g. Inspiring Business Talk"
msgstr "예 : 감동적인 비즈니스 강연"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "e.g. http://www.example.com"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_kanban
msgid "hours"
msgstr "시간"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "minutes after talk start"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.registration_complete
msgid "register to your favorites talks now."
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_main
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_main
msgid "search."
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
msgid "starts in"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
msgid "starts on"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_main
msgid "tracks"
msgstr ""
