# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * l10n_us_check_printing
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-28 12:15+0000\n"
"PO-Revision-Date: 2015-09-08 05:48+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Spanish (Paraguay) (http://www.transifex.com/odoo/odoo-9/language/es_PY/)\n"
"Language: es_PY\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: l10n_us_check_printing
#: model_terms:ir.ui.view,arch_db:l10n_us_check_printing.ckus_stub
msgid "Check Amount:"
msgstr ""

#. module: l10n_us_check_printing
#: model_terms:ir.ui.view,arch_db:l10n_us_check_printing.ckus_stub
msgid "Description"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_us_check_printing
#: model_terms:ir.ui.view,arch_db:l10n_us_check_printing.ckus_stub
msgid "Due Date"
msgstr "Fecha de Vencimiento"

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment__id
msgid "ID"
msgstr ""

#. module: l10n_us_check_printing
#: model_terms:ir.ui.view,arch_db:l10n_us_check_printing.ckus_stub
msgid "Invoice Amount"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.model.fields,field_description:l10n_us_check_printing.field_account_payment____last_update
msgid "Last Modified on"
msgstr ""

#. module: l10n_us_check_printing
#: model_terms:ir.ui.view,arch_db:l10n_us_check_printing.ckus_stub
msgid "Payment"
msgstr "Pago"

#. module: l10n_us_check_printing
#: model:ir.model,name:l10n_us_check_printing.model_account_payment
msgid "Payments"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.actions.report,name:l10n_us_check_printing.action_print_check_bottom
msgid "Print Check (Bottom)"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.actions.report,name:l10n_us_check_printing.action_print_check_middle
msgid "Print Check (Middle)"
msgstr ""

#. module: l10n_us_check_printing
#: model:ir.actions.report,name:l10n_us_check_printing.action_print_check_top
msgid "Print Check (Top)"
msgstr ""

#~ msgid "Action Needed"
#~ msgstr ""

#~ msgid "Amount in Words"
#~ msgstr ""

#~ msgid "Attachment Count"
#~ msgstr ""

#~ msgid "Balance Due"
#~ msgstr ""

#~ msgid "Cancelled"
#~ msgstr ""

#~ msgid "Change label of the counterpart that will hold the payment difference"
#~ msgstr ""

#~ msgid "Check Number"
#~ msgstr ""

#~ msgid "Check this option if your pre-printed checks are not numbered."
#~ msgstr ""

#~ msgid "Code"
#~ msgstr ""

#~ msgid "Company"
#~ msgstr ""

#~ msgid "Company related to this journal"
#~ msgstr ""

#~ msgid "Created by"
#~ msgstr ""

#~ msgid "Created on"
#~ msgstr ""

#~ msgid "Currency"
#~ msgstr ""

#~ msgid "Customer"
#~ msgstr ""

#~ msgid "Destination Account"
#~ msgstr ""

#~ msgid "Difference Account"
#~ msgstr ""

#~ msgid "Draft"
#~ msgstr ""

#~ msgid "Followers"
#~ msgstr ""

#~ msgid "Followers (Channels)"
#~ msgstr ""

#~ msgid "Followers (Partners)"
#~ msgstr ""

#~ msgid "Has Invoices"
#~ msgstr ""

#~ msgid "Hide Payment Method"
#~ msgstr ""

#~ msgid "If checked new messages require your attention."
#~ msgstr ""

#~ msgid "If checked, new messages require your attention."
#~ msgstr ""

#~ msgid "If checked, some messages have a delivery error."
#~ msgstr ""

#~ msgid "Internal Transfer"
#~ msgstr ""

#~ msgid "Invoices"
#~ msgstr ""

#~ msgid "Invoices whose journal items have been reconciled with this payment's."
#~ msgstr ""

#~ msgid "Is Follower"
#~ msgstr ""

#~ msgid "Journal Entry Name"
#~ msgstr ""

#~ msgid "Journal Item Label"
#~ msgstr ""

#~ msgid "Keep open"
#~ msgstr ""

#~ msgid "Last Updated by"
#~ msgstr ""

#~ msgid "Last Updated on"
#~ msgstr ""

#~ msgid "Main Attachment"
#~ msgstr ""

#~ msgid "Manual Numbering"
#~ msgstr ""

#~ msgid ""
#~ "Manual: Get paid by cash, check or any other method outside of Odoo.\n"
#~ "Electronic: Get paid automatically through a payment acquirer by requesting a transaction on a card saved by the customer when buying or subscribing online (payment token).\n"
#~ "Check: Pay bill by check and print it from Odoo.\n"
#~ "Batch Deposit: Encase several customer checks at once by generating a batch deposit to submit to your bank. When encoding the bank statement in Odoo, you are suggested to reconcile the transaction with the batch deposit.To enable batch deposit, module account_batch_payment must be installed.\n"
#~ "SEPA Credit Transfer: Pay bill from a SEPA Credit Transfer file you submit to your bank. To enable sepa credit transfer, module account_sepa must be installed "
#~ msgstr ""

#~ msgid "Mark invoice as fully paid"
#~ msgstr ""

#~ msgid "Memo"
#~ msgstr ""

#~ msgid "Message Delivery error"
#~ msgstr ""

#~ msgid "Messages"
#~ msgstr ""

#~ msgid "Move Line"
#~ msgstr ""

#~ msgid "Move Reconciled"
#~ msgstr ""

#~ msgid "Multi"
#~ msgstr ""

#~ msgid "Name"
#~ msgstr ""

#~ msgid ""
#~ "Note that tokens from acquirers set to only authorize transactions (instead "
#~ "of capturing the amount) are not available."
#~ msgstr ""

#~ msgid "Number of Actions"
#~ msgstr ""

#~ msgid "Number of error"
#~ msgstr ""

#~ msgid "Number of messages which requires an action"
#~ msgstr ""

#~ msgid "Number of messages with delivery error"
#~ msgstr ""

#~ msgid "Number of unread messages"
#~ msgstr ""

#~ msgid "Partner"
#~ msgstr ""

#~ msgid "Partner Type"
#~ msgstr ""

#~ msgid "Payment Amount"
#~ msgstr ""

#~ msgid "Payment Date"
#~ msgstr ""

#~ msgid "Payment Difference"
#~ msgstr ""

#~ msgid "Payment Difference Handling"
#~ msgstr ""

#~ msgid "Payment Journal"
#~ msgstr ""

#~ msgid "Payment Method Type"
#~ msgstr ""

#~ msgid "Payment Reference"
#~ msgstr ""

#~ msgid "Payment Transaction"
#~ msgstr ""

#~ msgid "Payment Type"
#~ msgstr ""

#~ msgid "Posted"
#~ msgstr ""

#~ msgid "Receive Money"
#~ msgstr ""

#~ msgid "Recipient Bank Account"
#~ msgstr ""

#~ msgid "Reconciled"
#~ msgstr ""

#~ msgid "Reconciled Invoices"
#~ msgstr ""

#~ msgid ""
#~ "Reference of the document used to issue this payment. Eg. check number, file"
#~ " name, etc."
#~ msgstr ""

#~ msgid "Saved payment token"
#~ msgstr ""

#~ msgid "Send Money"
#~ msgstr ""

#~ msgid "Sent"
#~ msgstr ""

#~ msgid "Show Partner Bank Account"
#~ msgstr ""

#~ msgid "Status"
#~ msgstr ""

#~ msgid ""
#~ "Technical field containing the invoices for which the payment has been generated.\n"
#~ "                                                                                                                                                                       This does not especially correspond to the invoices reconciled with the payment,\n"
#~ "                                                                                                                                                                       as it can have been generated first, and reconciled later"
#~ msgstr ""

#~ msgid ""
#~ "Technical field holding the number given to the journal entry, automatically"
#~ " set when the statement line is reconciled then stored to set the same "
#~ "number again if the line is cancelled, set to draft and re-processed again."
#~ msgstr ""

#~ msgid ""
#~ "Technical field indicating if the user selected invoices from multiple "
#~ "partners or from different types."
#~ msgstr ""

#~ msgid "Technical field used for usability purposes"
#~ msgstr ""

#~ msgid ""
#~ "Technical field used to adapt the interface to the payment type selected."
#~ msgstr ""

#~ msgid ""
#~ "Technical field used to hide the payment method if the selected journal has "
#~ "only one available which is 'manual'"
#~ msgstr ""

#~ msgid ""
#~ "Technical field used to know whether the field `partner_bank_account_id` "
#~ "needs to be displayed or not in the payments form views"
#~ msgstr ""

#~ msgid ""
#~ "The selected journal is configured to print check numbers. If your pre-"
#~ "printed check paper already has numbers or if the current numbering is "
#~ "wrong, you can change it in the journal configuration page."
#~ msgstr ""

#~ msgid "Transfer To"
#~ msgstr ""

#~ msgid "Unread Messages"
#~ msgstr ""

#~ msgid "Unread Messages Counter"
#~ msgstr ""

#~ msgid "VOID"
#~ msgstr ""

#~ msgid "Vendor"
#~ msgstr ""

#~ msgid "Website Messages"
#~ msgstr ""

#~ msgid "Website communication history"
#~ msgstr ""
