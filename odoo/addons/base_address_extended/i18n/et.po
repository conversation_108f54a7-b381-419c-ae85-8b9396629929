# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_address_extended
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON>, 2020
# <PERSON>eli Õigus <<EMAIL>>, 2020
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~13.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-09-01 07:28+0000\n"
"PO-Revision-Date: 2020-09-07 08:10+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: Estonian (https://www.transifex.com/odoo/teams/41243/et/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: et\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: base_address_extended
#: model_terms:ir.ui.view,arch_db:base_address_extended.view_res_country_extended_form
msgid ""
"Change how the system computes the full street field based on the different "
"street subfields"
msgstr ""
"Muutke, kuidas süsteem koostab täispika tänava välja erinevatest tänava "
"alamväljadest."

#. module: base_address_extended
#: model:ir.model,name:base_address_extended.model_res_company
msgid "Companies"
msgstr "Ettevõtted"

#. module: base_address_extended
#: model:ir.model,name:base_address_extended.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: base_address_extended
#: model:ir.model,name:base_address_extended.model_res_country
msgid "Country"
msgstr "Riik"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_company__display_name
#: model:ir.model.fields,field_description:base_address_extended.field_res_country__display_name
#: model:ir.model.fields,field_description:base_address_extended.field_res_partner__display_name
msgid "Display Name"
msgstr "Kuva nimi"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_partner__street_number2
#: model:ir.model.fields,field_description:base_address_extended.field_res_users__street_number2
msgid "Door"
msgstr "Uks"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_company__street_number2
msgid "Door Number"
msgstr "Ukse number"

#. module: base_address_extended
#: model:ir.model.fields,help:base_address_extended.field_res_country__street_format
msgid ""
"Format to use for streets belonging to this country.\n"
"\n"
"You can use the python-style string pattern with all the fields of the street (for example, use '%(street_name)s, %(street_number)s' if you want to display the street name, followed by a comma and the house number)\n"
"%(street_name)s: the name of the street\n"
"%(street_number)s: the house number\n"
"%(street_number2)s: the door number"
msgstr ""
"Selle riigi tänavatele mõeldud vorming.\n"
"\n"
"Võite kasutada pythoni stiilis sõne mustrit koos kõigi tänava väljadega (näiteks, kasuta '%(street_name)s, %(street_number)s', kui soovid näidata tänavanime, millele järgneb koma ja maja number)\n"
"%(street_name)s: tänavanimi\n"
"%(street_number)s: maja number\n"
"%(street_number2)s: ukse number"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_partner__street_number
#: model:ir.model.fields,field_description:base_address_extended.field_res_users__street_number
msgid "House"
msgstr "Maja"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_company__street_number
msgid "House Number"
msgstr "Maja number"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_company__id
#: model:ir.model.fields,field_description:base_address_extended.field_res_country__id
#: model:ir.model.fields,field_description:base_address_extended.field_res_partner__id
msgid "ID"
msgstr "ID"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_company____last_update
#: model:ir.model.fields,field_description:base_address_extended.field_res_country____last_update
#: model:ir.model.fields,field_description:base_address_extended.field_res_partner____last_update
msgid "Last Modified on"
msgstr "Viimati muudetud (millal)"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_country__street_format
msgid "Street Format"
msgstr "Tänava formaat"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_company__street_name
#: model:ir.model.fields,field_description:base_address_extended.field_res_partner__street_name
#: model:ir.model.fields,field_description:base_address_extended.field_res_users__street_name
msgid "Street Name"
msgstr "Tänava nimi"

#. module: base_address_extended
#: model_terms:ir.ui.view,arch_db:base_address_extended.view_partner_address_structured_form
#: model_terms:ir.ui.view,arch_db:base_address_extended.view_partner_structured_form
#: model_terms:ir.ui.view,arch_db:base_address_extended.view_res_company_extended_form
msgid "Street Name..."
msgstr "Tänava nimi ..."

#. module: base_address_extended
#: model_terms:ir.ui.view,arch_db:base_address_extended.view_res_country_extended_form
msgid "Street format..."
msgstr "Täna formaat ..."

#. module: base_address_extended
#: code:addons/base_address_extended/models/res_partner.py:0
#: code:addons/base_address_extended/models/res_partner.py:0
#, python-format
msgid "Unrecognized field %s in street format."
msgstr "Tänava formaadis on väli %s, mida ei õnnestu tuvastada."
