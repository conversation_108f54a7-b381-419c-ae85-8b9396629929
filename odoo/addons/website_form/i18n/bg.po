# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_form
# 
# Translators:
# <PERSON>, 2020
# <PERSON> <<EMAIL>>, 2020
# ale<PERSON><PERSON><PERSON> <PERSON>, 2020
# <PERSON><PERSON><PERSON> <alben<PERSON>_viche<PERSON>@abv.bg>, 2020
# <PERSON> <<EMAIL>>, 2020
# <AUTHOR> <EMAIL>, 2020
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-11-27 14:13+0000\n"
"PO-Revision-Date: 2020-09-07 08:21+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2020\n"
"Language-Team: Bulgarian (https://www.transifex.com/odoo/teams/41243/bg/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bg\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/snippets/s_website_form/000.js:0
#, python-format
msgid "'%s' is not a correct date"
msgstr "\"%s\" не е правилна дата"

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/snippets/s_website_form/000.js:0
#, python-format
msgid "'%s' is not a correct datetime"
msgstr "\"%s\" не е правилна дата и час"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid ", .s_website_form"
msgstr ""

#. module: website_form
#: code:addons/website_form/controllers/main.py:0
#, python-format
msgid "<p>Attached files : </p>"
msgstr "<p>Прикачи файлове: </p>"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.contactus_thanks_ir_ui_view
#: model_terms:website.page,arch_db:website_form.contactus_thanks
msgid ""
"<span class=\"fa fa-check-circle\"/>\n"
"                                            <span>Your message has been sent <b>successfully</b></span>"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.contactus_form
msgid "<span class=\"s_website_form_label_content\">Email To</span>"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.contactus_form
msgid ""
"<span class=\"s_website_form_label_content\">Email</span>\n"
"                                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.contactus_form
msgid "<span class=\"s_website_form_label_content\">Phone Number</span>"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.contactus_form
msgid ""
"<span class=\"s_website_form_label_content\">Subject</span>\n"
"                                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.contactus_form
msgid "<span class=\"s_website_form_label_content\">Your Company</span>"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.contactus_form
msgid ""
"<span class=\"s_website_form_label_content\">Your Name</span>\n"
"                                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.contactus_form
msgid "<span class=\"s_website_form_label_content\">Your Question</span>"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid "Add a new field after this one"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid "Add a new field at the end"
msgstr ""

#. module: website_form
#: model:ir.model.fields,field_description:website_form.field_ir_model__website_form_access
msgid "Allowed to use in forms"
msgstr "Позволено да се използва във форми"

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/snippets/s_website_form/000.js:0
#, python-format
msgid "An error has occured, the form has not been sent."
msgstr "Възникна грешка, формата не беше изпратена."

#. module: website_form
#: model:ir.model.fields,help:website_form.field_ir_model_fields__website_form_blacklisted
msgid "Blacklist this field for web forms"
msgstr "Поставете това поле за уебформи в черен списък"

#. module: website_form
#: model:ir.model.fields,field_description:website_form.field_ir_model_fields__website_form_blacklisted
msgid "Blacklisted in web forms"
msgstr "Вписан в черен списък в уебформи"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid "Button Position"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid "Center"
msgstr "Център"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid "Checkbox"
msgstr "Поле за отметка"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid "Custom field"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid "Date"
msgstr "Дата"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid "Date &amp; Time"
msgstr "Дата &amp; час"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid "Decimal Number"
msgstr "Десетично число"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid "Display"
msgstr "Покажете"

#. module: website_form
#: model:ir.model.fields,field_description:website_form.field_ir_model__display_name
#: model:ir.model.fields,field_description:website_form.field_ir_model_fields__display_name
#: model:ir.model.fields,field_description:website_form.field_website__display_name
msgid "Display Name"
msgstr "Име за показване"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid "Edit Message"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid "Email"
msgstr "Имейл"

#. module: website_form
#: model:ir.model.fields,help:website_form.field_ir_model__website_form_access
msgid "Enable the form builder feature for this model."
msgstr "Активирайте функцията за съставяне на форми за този модел."

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/xml/website_form.xml:0
#: code:addons/website_form/static/src/xml/website_form.xml:0
#, python-format
msgid "Error"
msgstr "Грешка"

#. module: website_form
#: model:ir.model.fields,field_description:website_form.field_ir_model__website_form_default_field_id
msgid "Field for custom form data"
msgstr "Поле за персонализирани данни от форма"

#. module: website_form
#: model:ir.model,name:website_form.model_ir_model_fields
msgid "Fields"
msgstr "Полета"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid "File Upload"
msgstr "Качване на файл в системата"

#. module: website_form
#: model:ir.model.fields,help:website_form.field_ir_model__website_form_label
msgid ""
"Form action label. Ex: crm.lead could be 'Send an e-mail' and project.issue "
"could be 'Create an Issue'."
msgstr ""
"Етикет за форма на действие. Напр: crm.lead би могло да бъде 'Изпратете "
"имейл', а project.issue би могло да бъде 'Регистрирайте грешка'."

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid "Height"
msgstr "Височина"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid "Hidden"
msgstr "Скрит"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid "Hide"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid "Horizontal"
msgstr "Хоризонтален"

#. module: website_form
#: model:ir.model.fields,field_description:website_form.field_ir_model__id
#: model:ir.model.fields,field_description:website_form.field_ir_model_fields__id
#: model:ir.model.fields,field_description:website_form.field_website__id
msgid "ID"
msgstr "ID"

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "In the meantime we invite you to visit our"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid "Input Aligned"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid "Input Placeholder"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid "Input Type"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid "Label Name"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid "Label Position"
msgstr ""

#. module: website_form
#: model:ir.model.fields,field_description:website_form.field_ir_model__website_form_label
msgid "Label for form action"
msgstr "Етикет за действие на формата"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid "Labels Width"
msgstr ""

#. module: website_form
#: model:ir.model.fields,field_description:website_form.field_ir_model____last_update
#: model:ir.model.fields,field_description:website_form.field_ir_model_fields____last_update
#: model:ir.model.fields,field_description:website_form.field_website____last_update
msgid "Last Modified on"
msgstr "Последно променено на"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid "Left"
msgstr "Ляв"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid "Long Text"
msgstr "Дълъг текст"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid "Mark Text"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid "Marked Fields"
msgstr ""

#. module: website_form
#: code:addons/website_form/controllers/main.py:0
#, python-format
msgid "Metadata"
msgstr "Метаданни"

#. module: website_form
#: model:ir.model,name:website_form.model_ir_model
msgid "Models"
msgstr "Модели"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid "Multiple Checkboxes"
msgstr "Няколко полета за отметка"

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/xml/website_form_editor.xml:0
#: code:addons/website_form/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "No matching record !"
msgstr "Липсва съвпадащ запис !"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid "None"
msgstr "Никакъв"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid "Nothing"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid "Number"
msgstr "Номер"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid "On Success"
msgstr ""

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/snippets/s_website_form/options.js:0
#: code:addons/website_form/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Option 1"
msgstr ""

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/snippets/s_website_form/options.js:0
#: code:addons/website_form/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Option 2"
msgstr ""

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/snippets/s_website_form/options.js:0
#: code:addons/website_form/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Option 3"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid "Optional"
msgstr "Опция"

#. module: website_form
#: code:addons/website_form/controllers/main.py:0
#, python-format
msgid "Other Information:"
msgstr ""

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "Our team will message you back as soon as possible."
msgstr ""

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/snippets/s_website_form/000.js:0
#, python-format
msgid "Please fill in the form correctly."
msgstr "Моля, попълнете правилно формата."

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid "Radio Buttons"
msgstr "Радио бутони"

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/js/website_form_editor_registry.js:0
#, python-format
msgid "Recipient Email"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid "Redirect"
msgstr "Пренасочен"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid "Required"
msgstr "Изискуем"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid "Right"
msgstr "Дясно"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid "Selection"
msgstr "Селекция"

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "Separate email addresses with a comma."
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid "Show Message"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid "Show reCaptcha Policy"
msgstr ""

#. module: website_form
#: model:ir.model.fields,help:website_form.field_ir_model__website_form_default_field_id
msgid ""
"Specify the field which will contain meta and custom form fields datas."
msgstr ""
"Посочете полето, което ще съдържа полевите данни за мета- и персонализирани "
"формуляри."

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.contactus_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form
msgid "Submit"
msgstr "Изпращане"

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/xml/website_form.xml:0
#: code:addons/website_form/static/src/xml/website_form.xml:0
#, python-format
msgid "Success"
msgstr "Успех"

#. module: website_form
#: code:addons/website_form/controllers/main.py:0
#, python-format
msgid "Suspicious activity detected by Google reCaptcha."
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid "Telephone"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid "Text"
msgstr "Текст"

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "Thank You For Your Feedback"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.contactus_thanks_ir_ui_view
#: model_terms:website.page,arch_db:website_form.contactus_thanks
msgid "Thank You!"
msgstr ""

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/xml/website_form.xml:0
#, python-format
msgid "The form has been sent successfully."
msgstr "Формата е изпратена успешно."

#. module: website_form
#: code:addons/website_form/controllers/main.py:0
#, python-format
msgid "The form's specified model does not exist"
msgstr ""

#. module: website_form
#: code:addons/website_form/controllers/main.py:0
#, python-format
msgid "This message has been posted on your website!"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid "Top"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid "Type"
msgstr "Вид"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid "URL"
msgstr "URL адрес"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid "Url"
msgstr "URL адрес"

#. module: website_form
#: model:ir.model.fields,help:website_form.field_ir_model__website_form_key
msgid "Used in FormBuilder Registry"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid "Vertical"
msgstr "Вертикален"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.contactus_thanks_ir_ui_view
#: model_terms:website.page,arch_db:website_form.contactus_thanks
msgid "We will get back to you shortly."
msgstr "Скоро ще се свържем с вас."

#. module: website_form
#: model:ir.model,name:website_form.model_website
msgid "Website"
msgstr "Уебсайт"

#. module: website_form
#: model:ir.model.fields,field_description:website_form.field_ir_model__website_form_key
msgid "Website Form Key"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.ir_model_view
msgid "Website Forms"
msgstr ""

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "You can't duplicate a model field."
msgstr ""

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "You can't duplicate the submit button of the form."
msgstr ""

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "You can't remove a field that is required by the model itself."
msgstr ""

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "You can't remove the submit button of the form"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form_options
msgid "rows"
msgstr ""

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "website"
msgstr "уебсайт"
