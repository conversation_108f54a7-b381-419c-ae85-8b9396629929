# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_form
#
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2016-07-12 11:13+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Macedonian (http://www.transifex.com/odoo/odoo-9/language/"
"mk/)\n"
"Language: mk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n % 10 == 1 && n % 100 != 11) ? 0 : 1;\n"

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/js/website_form.js:209
#, python-format
msgid "'%s' is not a correct date"
msgstr ""

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/js/website_form.js:204
#, python-format
msgid "'%s' is not a correct datetime"
msgstr ""

#. module: website_form
#: code:addons/website_form/controllers/main.py:221
#, python-format
msgid "<p>Attached files : </p>"
msgstr ""

#. module: website_form
#: model:ir.model.fields,field_description:website_form.field_ir_model_website_form_access
msgid "Allowed to use in forms"
msgstr ""

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/xml/website_form.xml:21
#, python-format
msgid "An error has occured, the form has not been sent."
msgstr ""

#. module: website_form
#: model:ir.model.fields,help:website_form.field_ir_model_fields_website_form_blacklisted
msgid "Blacklist this field for web forms"
msgstr ""

#. module: website_form
#: model:ir.model.fields,field_description:website_form.field_ir_model_fields_website_form_blacklisted
msgid "Blacklisted in web forms"
msgstr ""

#. module: website_form
#: code:addons/website_form/controllers/main.py:49
#, python-format
msgid "Custom infos"
msgstr ""

#. module: website_form
#: model:ir.model.fields,help:website_form.field_ir_model_website_form_access
msgid "Enable the form builder feature for this model."
msgstr ""

#. module: website_form
#: model:ir.model.fields,help:website_form.field_website_website_form_enable_metadata
msgid "Enable writing metadata on form submit."
msgstr ""

#. module: website_form
#: model:ir.model.fields,field_description:website_form.field_ir_model_website_form_default_field_id
msgid "Field for custom form data"
msgstr ""

#. module: website_form
#: model:ir.model,name:website_form.model_ir_model_fields
msgid "Fields"
msgstr "Полиња"

#. module: website_form
#: model:ir.model.fields,help:website_form.field_ir_model_website_form_label
msgid ""
"Form action label. Ex: crm.lead could be 'Send an e-mail' and project.issue "
"could be 'Create an Issue'."
msgstr ""

#. module: website_form
#: model:ir.model.fields,field_description:website_form.field_ir_model_website_form_label
msgid "Label for form action"
msgstr ""

#. module: website_form
#: code:addons/website_form/controllers/main.py:50
#, python-format
msgid "Metadata"
msgstr ""

#. module: website_form
#: model:ir.model,name:website_form.model_ir_model
msgid "Models"
msgstr "Модели"

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/xml/website_form.xml:14
#, python-format
msgid "Please fill in the form correctly."
msgstr "Ве молиме пополнете го формуларот коректно."

#. module: website_form
#: model:ir.model.fields,help:website_form.field_ir_model_website_form_default_field_id
msgid "Specify the field which will contain meta and custom form fields datas."
msgstr ""

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/xml/website_form.xml:7
#, python-format
msgid "The form has been sent successfully."
msgstr ""

#. module: website_form
#: model:ir.model,name:website_form.model_website
msgid "Website"
msgstr "Вебсајт"

#. module: website_form
#: model:ir.model.fields,field_description:website_form.field_website_website_form_enable_metadata
msgid "Write metadata"
msgstr ""
