# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_hr
# 
# Translators:
# <PERSON>, 2020
# <PERSON> <<EMAIL>>, 2020
# <AUTHOR> <EMAIL>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# Du<PERSON> <<EMAIL>>, 2020
# Trần <PERSON> <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~13.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-09-01 07:29+0000\n"
"PO-Revision-Date: 2020-09-07 08:16+0000\n"
"Last-Translator: Tr<PERSON><PERSON> <<EMAIL>>, 2021\n"
"Language-Team: Vietnamese (https://www.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: pos_hr
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_config_form_view_inherit
msgid "<span class=\"o_form_label oe_edit_only\">Allowed Employees </span>"
msgstr "<span class=\"o_form_label oe_edit_only\">Allowed Employees </span>"

#. module: pos_hr
#: model:ir.model.fields,field_description:pos_hr.field_pos_order__cashier
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_order_form_inherit
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_order_list_select_inherit
msgid "Cashier"
msgstr "Thu ngân"

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/js/useSelectEmployee.js:0
#, python-format
msgid "Change Cashier"
msgstr "Đổi thu ngân"

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/xml/LoginScreen.xml:0
#, python-format
msgid "Close session"
msgstr "Close session"

#. module: pos_hr
#: model:ir.model.fields,field_description:pos_hr.field_hr_employee__display_name
#: model:ir.model.fields,field_description:pos_hr.field_pos_config__display_name
#: model:ir.model.fields,field_description:pos_hr.field_pos_order__display_name
#: model:ir.model.fields,field_description:pos_hr.field_report_pos_order__display_name
msgid "Display Name"
msgstr "Tên hiển thị"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_hr_employee
#: model:ir.model.fields,field_description:pos_hr.field_pos_order__employee_id
#: model:ir.model.fields,field_description:pos_hr.field_report_pos_order__employee_id
#: model_terms:ir.ui.view,arch_db:pos_hr.view_report_pos_order_search_inherit
msgid "Employee"
msgstr "Nhân viên"

#. module: pos_hr
#: code:addons/pos_hr/models/hr_employee.py:0
#, python-format
msgid "Employee: %s - PoS Config(s): %s \n"
msgstr "Employee: %s - PoS Config(s): %s \n"

#. module: pos_hr
#: model:ir.model.fields,field_description:pos_hr.field_pos_config__employee_ids
msgid "Employees with access"
msgstr "Employees with access"

#. module: pos_hr
#: model:ir.model.fields,field_description:pos_hr.field_hr_employee__id
#: model:ir.model.fields,field_description:pos_hr.field_pos_config__id
#: model:ir.model.fields,field_description:pos_hr.field_pos_order__id
#: model:ir.model.fields,field_description:pos_hr.field_report_pos_order__id
msgid "ID"
msgstr "ID"

#. module: pos_hr
#: model:ir.model.fields,help:pos_hr.field_pos_config__employee_ids
msgid "If left empty, all employees can log in to the PoS session"
msgstr "If left empty, all employees can log in to the PoS session"

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/js/useSelectEmployee.js:0
#, python-format
msgid "Incorrect Password"
msgstr "Mật khẩu Không hợp lệ"

#. module: pos_hr
#: model:ir.model.fields,field_description:pos_hr.field_hr_employee____last_update
#: model:ir.model.fields,field_description:pos_hr.field_pos_config____last_update
#: model:ir.model.fields,field_description:pos_hr.field_pos_order____last_update
#: model:ir.model.fields,field_description:pos_hr.field_report_pos_order____last_update
msgid "Last Modified on"
msgstr "Sửa lần cuối vào"

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/xml/LoginScreen.xml:0
#, python-format
msgid "Log in to"
msgstr "Log in to"

#. module: pos_hr
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_config_form_view_inherit
msgid ""
"Only users with Manager access rights for PoS app can modify the product "
"prices on orders."
msgstr ""
"Chỉ những người dùng có quyền truy cập trình quản lý cho ứng dụng PoS mới có"
" thể sửa đổi giá sản phẩm trên đơn đặt hàng."

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/js/useSelectEmployee.js:0
#, python-format
msgid "Password ?"
msgstr "Mật khẩu?"

#. module: pos_hr
#: model:ir.model.fields,help:pos_hr.field_pos_order__employee_id
msgid ""
"Person who uses the cash register. It can be a reliever, a student or an "
"interim employee."
msgstr ""
"Người sử dụng máy tính tiền. Nó có thể là người hỗ trợ, sinh viên hoặc nhân "
"viên tạm thời."

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Cấu hình điểm bán lẻ"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_pos_order
msgid "Point of Sale Orders"
msgstr "Đơn bán lẻ"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_report_pos_order
msgid "Point of Sale Orders Report"
msgstr "Báo cáo đơn hàng bán lẻ"

#. module: pos_hr
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_config_form_view_inherit
msgid "Price Control"
msgstr "Kiểm soát giá"

#. module: pos_hr
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_config_form_view_inherit
msgid "Restrict price modification to managers"
msgstr "Hạn chế việc sửa giá đến cấp quản lý"

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/xml/LoginScreen.xml:0
#, python-format
msgid "Scan your badge"
msgstr "Quét thẻ"

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/xml/LoginScreen.xml:0
#, python-format
msgid "Select Cashier"
msgstr "Select Cashier"

#. module: pos_hr
#: code:addons/pos_hr/models/hr_employee.py:0
#, python-format
msgid ""
"You cannot delete an employee that may be used in an active PoS session, "
"close the session(s) first: \n"
msgstr ""
"You cannot delete an employee that may be used in an active PoS session, "
"close the session(s) first: \n"

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/xml/LoginScreen.xml:0
#, python-format
msgid "or"
msgstr "hoặc"

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/xml/CashierName.xml:0
#, python-format
msgid "selectCashier"
msgstr "selectCashier"
