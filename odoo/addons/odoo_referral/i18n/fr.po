# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* odoo_referral
# 
# Translators:
# <PERSON>, 2020
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON><PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-09-29 13:45+0000\n"
"PO-Revision-Date: 2020-10-05 06:33+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2022\n"
"Language-Team: French (https://www.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: odoo_referral
#. openerp-web
#: code:addons/odoo_referral/static/src/js/systray.js:0
#, python-format
msgid ""
"A popup window has been blocked. You may need to change your browser "
"settings to allow popup windows for this page."
msgstr ""
"Une fenêtre contextuelle contenant votre rapport a été bloquée. Vous devez "
"peut-être autoriser les fenêtres contextuelles pour cette page dans les "
"paramètres de votre navigateur."

#. module: odoo_referral
#: model:ir.model.fields,field_description:odoo_referral.field_res_users__display_name
msgid "Display Name"
msgstr "Nom affiché"

#. module: odoo_referral
#: model:ir.model.fields,field_description:odoo_referral.field_res_users__id
msgid "ID"
msgstr "ID"

#. module: odoo_referral
#: model:ir.model.fields,field_description:odoo_referral.field_res_users____last_update
msgid "Last Modified on"
msgstr "Dernière modification le"

#. module: odoo_referral
#. openerp-web
#: code:addons/odoo_referral/static/src/xml/systray.xml:0
#: code:addons/odoo_referral/static/src/xml/systray.xml:0
#, python-format
msgid "Odoo Referral Program"
msgstr "Programme de recommandation d'Odoo"

#. module: odoo_referral
#: model:ir.model,name:odoo_referral.model_res_users
msgid "Users"
msgstr "Utilisateurs"
