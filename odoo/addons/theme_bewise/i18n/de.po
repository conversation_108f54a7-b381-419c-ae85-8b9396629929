# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* theme_bewise
# 
# Translators:
# <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2021
# Friederi<PERSON> Fasterling-Nesselbosch, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-12-13 07:05+0000\n"
"PO-Revision-Date: 2021-12-13 07:13+0000\n"
"Last-Translator: Friederike Fasterling-Nesselbosch, 2021\n"
"Language-Team: German (https://www.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: theme_bewise
#: model_terms:theme.ir.ui.view,arch:theme_bewise.s_call_to_action
msgid "<b>3,000 students</b> graduate each year find a job within 2 months"
msgstr ""

#. module: theme_bewise
#: model_terms:theme.ir.ui.view,arch:theme_bewise.s_cover
msgid ""
"<font style=\"font-size: 62px;font-weight: bold;\">Always at the top</font>"
msgstr ""

#. module: theme_bewise
#: model_terms:theme.ir.ui.view,arch:theme_bewise.s_quotes_carousel
msgid ""
"<span class=\"s_blockquote_author\"><b>Iris DOE</b> • Graduated in "
"2019</span>"
msgstr ""

#. module: theme_bewise
#: model_terms:theme.ir.ui.view,arch:theme_bewise.s_quotes_carousel
msgid ""
"<span class=\"s_blockquote_author\"><b>Jane DOE</b> • Graduated in "
"2017</span>"
msgstr ""

#. module: theme_bewise
#: model_terms:theme.ir.ui.view,arch:theme_bewise.s_quotes_carousel
msgid ""
"<span class=\"s_blockquote_author\"><b>John DOE</b> • Graduated in "
"2016</span>"
msgstr ""

#. module: theme_bewise
#: model_terms:theme.ir.ui.view,arch:theme_bewise.s_numbers
msgid "<span class=\"s_number display-4\">16,456</span>"
msgstr ""

#. module: theme_bewise
#: model_terms:theme.ir.ui.view,arch:theme_bewise.s_company_team
msgid "Aline Turner, <small><b>Law professor</b></small>"
msgstr ""

#. module: theme_bewise
#: model_terms:theme.ir.ui.view,arch:theme_bewise.s_image_text
msgid "All students benefit from individualised support."
msgstr ""

#. module: theme_bewise
#. openerp-web
#: code:addons/theme_bewise/static/src/js/tour.js:0
#: code:addons/theme_bewise/static/src/js/tour.js:0
#, python-format
msgid "Background Shape"
msgstr "Hintergrundform"

#. module: theme_bewise
#: model:ir.model.fields,field_description:theme_bewise.field_theme_utils__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: theme_bewise
#: model_terms:theme.ir.ui.view,arch:theme_bewise.s_text_image
msgid "Driven by Excellence"
msgstr ""

#. module: theme_bewise
#: model_terms:theme.ir.ui.view,arch:theme_bewise.s_numbers
msgid "Faculties"
msgstr ""

#. module: theme_bewise
#: model_terms:theme.ir.ui.view,arch:theme_bewise.s_title
msgid "Faculty Team"
msgstr ""

#. module: theme_bewise
#: model_terms:theme.ir.ui.view,arch:theme_bewise.s_quotes_carousel
msgid ""
"Great support and quality courses! A mentor helps you move forward and can "
"be contacted through flexible schedules. Everything you need to start your "
"career on the web."
msgstr ""

#. module: theme_bewise
#: model_terms:theme.ir.ui.view,arch:theme_bewise.s_company_team
msgid ""
"He is professor in the Institute of Mechanics, Materials and Civil "
"Engineering since 2000. He lectures in mechanical drawing and mechanical "
"design for undergraduate and graduate students.   He is active in Problem "
"and Project based learning. He is the promoter of 8 doctoral theses."
msgstr ""

#. module: theme_bewise
#: model_terms:theme.ir.ui.view,arch:theme_bewise.s_quotes_carousel
msgid ""
"I have learned a lot, and I have realised professional projects thanks to "
"the support of the mentors! The team is always at the top and always looking"
" for solutions to problems, I felt accompanied and supported! It's not easy "
"every day, but it's a great adventure!"
msgstr ""

#. module: theme_bewise
#: model:ir.model.fields,field_description:theme_bewise.field_theme_utils__id
msgid "ID"
msgstr "ID"

#. module: theme_bewise
#: model_terms:theme.ir.ui.view,arch:theme_bewise.s_company_team
msgid "Iris Joe, <small><b>team leader professor</b></small>"
msgstr ""

#. module: theme_bewise
#: model_terms:theme.ir.ui.view,arch:theme_bewise.s_call_to_action
msgid "Join them and increase your chances to get hired."
msgstr ""

#. module: theme_bewise
#: model_terms:theme.ir.ui.view,arch:theme_bewise.s_text_image
msgid ""
"Journalists, directors, developers, etc. working effectively in their fields"
" of excellence."
msgstr ""

#. module: theme_bewise
#: model:ir.model.fields,field_description:theme_bewise.field_theme_utils____last_update
msgid "Last Modified on"
msgstr "Zuletzt geändert am"

#. module: theme_bewise
#: model_terms:theme.ir.ui.view,arch:theme_bewise.s_numbers
msgid "Libraries"
msgstr "Bibliotheken"

#. module: theme_bewise
#: model_terms:theme.ir.ui.view,arch:theme_bewise.s_company_team
msgid "Mich Stark, <small><b>IT Officer</b></small>"
msgstr ""

#. module: theme_bewise
#: model_terms:theme.ir.ui.view,arch:theme_bewise.s_numbers
msgid "Nationalities"
msgstr ""

#. module: theme_bewise
#: model_terms:theme.ir.ui.view,arch:theme_bewise.s_cover
msgid ""
"Our university has topped the The World University<br/>Rankings for a fifth "
"consecutive year."
msgstr ""

#. module: theme_bewise
#: model_terms:theme.ir.ui.view,arch:theme_bewise.s_company_team
msgid ""
"She has been practicing law at the French-speaking Brussels Bar since 2006. "
"She has worked in various major law firms based in Brussels, as member and "
"then head of their litigation/arbitration practice groups."
msgstr ""

#. module: theme_bewise
#: model_terms:theme.ir.ui.view,arch:theme_bewise.s_numbers
msgid "Students"
msgstr ""

#. module: theme_bewise
#: model_terms:theme.ir.ui.view,arch:theme_bewise.s_text_image
msgid ""
"Teachers are selected among experts of their domains. They are not just "
"professors, they are professionals in their field of expertise."
msgstr ""

#. module: theme_bewise
#: model_terms:theme.ir.ui.view,arch:theme_bewise.s_quotes_carousel
msgid ""
"Thanks to its innovative system, the school offers a diplomatic training "
"during which we are followed by a \"mentor\", a sort of \"tutor\"!"
msgstr ""

#. module: theme_bewise
#: model:ir.model,name:theme_bewise.model_theme_utils
msgid "Theme Utils"
msgstr "Thema-Werkzeuge"

#. module: theme_bewise
#: model_terms:theme.ir.ui.view,arch:theme_bewise.s_company_team
msgid "Tony Fred, <small><b>Faculty Head of IT</b></small>"
msgstr ""

#. module: theme_bewise
#: model_terms:theme.ir.ui.view,arch:theme_bewise.s_company_team
msgid ""
"Tony received a degree in Electrical and Mechanical Engineering and a Ph D. "
"degree in 1998 and 2004. After a post-doctoral experience  he joined the "
"school as professor of mechatronics in 2006. In 2010, he became Head of IT."
msgstr ""

#. module: theme_bewise
#: model_terms:theme.ir.ui.view,arch:theme_bewise.s_image_text
msgid ""
"We make sure that everyone blossoms. We keep a human size in a warm and "
"modern setting. All students benefit from individualised support."
msgstr ""

#. module: theme_bewise
#: model_terms:theme.ir.ui.view,arch:theme_bewise.s_image_text
msgid "What makes us different?"
msgstr ""
