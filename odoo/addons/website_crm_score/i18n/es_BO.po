# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_crm_score
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-10-24 09:24+0000\n"
"PO-Revision-Date: 2017-10-24 09:24+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Spanish (Bolivia) (https://www.transifex.com/odoo/teams/41243/es_BO/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_BO\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_crm_score
#: model:ir.model.fields,field_description:website_crm_score.field_crm_lead_pageviews_count
msgid "# Page Views"
msgstr ""

#. module: website_crm_score
#: model:ir.model.fields,field_description:website_crm_score.field_website_crm_score_running
msgid "Active"
msgstr ""

#. module: website_crm_score
#: model:ir.model.fields,help:website_crm_score.field_ir_ui_view_track
#: model:ir.model.fields,help:website_crm_score.field_website_page_track
msgid "Allow to specify for one page of the website to be trackable or not"
msgstr ""

#. module: website_crm_score
#: selection:website.crm.score,rule_type:0
msgid "Archive"
msgstr ""

#. module: website_crm_score
#: model:ir.model.fields,field_description:website_crm_score.field_crm_lead_assign_date
msgid "Assign Date"
msgstr ""

#. module: website_crm_score
#: model:ir.ui.view,arch_db:website_crm_score.sales_team_form_view_assign
msgid "Assignation"
msgstr ""

#. module: website_crm_score
#: model:ir.model.fields,field_description:website_crm_score.field_team_user_leads_count
msgid "Assigned Leads"
msgstr ""

#. module: website_crm_score
#: model:ir.model.fields,field_description:website_crm_score.field_crm_team_assigned_leads_count
msgid "Assigned Leads Count"
msgstr ""

#. module: website_crm_score
#: model:ir.model.fields,help:website_crm_score.field_team_user_leads_count
msgid "Assigned Leads this last month"
msgstr ""

#. module: website_crm_score
#: model:ir.model.fields,field_description:website_crm_score.field_crm_team_capacity
msgid "Capacity"
msgstr ""

#. module: website_crm_score
#: model:ir.actions.act_window,help:website_crm_score.team_action
msgid "Click here to define a new sales channel."
msgstr ""

#. module: website_crm_score
#: model:ir.actions.act_window,help:website_crm_score.team_user_action
msgid "Click to create a salesmen."
msgstr ""

#. module: website_crm_score
#: model:ir.model.fields,field_description:website_crm_score.field_team_user_create_uid
#: model:ir.model.fields,field_description:website_crm_score.field_website_crm_pageview_create_uid
#: model:ir.model.fields,field_description:website_crm_score.field_website_crm_score_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: website_crm_score
#: model:ir.model.fields,field_description:website_crm_score.field_team_user_create_date
#: model:ir.model.fields,field_description:website_crm_score.field_website_crm_pageview_create_date
#: model:ir.model.fields,field_description:website_crm_score.field_website_crm_score_create_date
msgid "Created on"
msgstr "Creado en"

#. module: website_crm_score
#: model:ir.actions.server,name:website_crm_score.action_score_now
msgid "Crm Score: Apply marked scores"
msgstr ""

#. module: website_crm_score
#: model:ir.actions.server,name:website_crm_score.ir_cron_lead_assign_ir_actions_server
#: model:ir.cron,cron_name:website_crm_score.ir_cron_lead_assign
#: model:ir.cron,name:website_crm_score.ir_cron_lead_assign
msgid "Crm Score: lead assignation"
msgstr ""

#. module: website_crm_score
#: model:ir.actions.server,name:website_crm_score.ir_cron_lead_scoring_ir_actions_server
#: model:ir.cron,cron_name:website_crm_score.ir_cron_lead_scoring
#: model:ir.cron,name:website_crm_score.ir_cron_lead_scoring
msgid "Crm Score: lead scoring "
msgstr ""

#. module: website_crm_score
#: model:ir.model.fields,help:website_crm_score.field_website_crm_score_last_run
msgid "Date from the last scoring on all leads."
msgstr ""

#. module: website_crm_score
#: model:ir.model.fields,help:website_crm_score.field_crm_lead_assign_date
msgid ""
"Date when the lead has been assigned via the auto-assignation mechanism"
msgstr ""

#. module: website_crm_score
#: selection:website.crm.score,rule_type:0
msgid "Delete"
msgstr ""

#. module: website_crm_score
#: model:ir.model.fields,field_description:website_crm_score.field_team_user_display_name
#: model:ir.model.fields,field_description:website_crm_score.field_team_user_name
#: model:ir.model.fields,field_description:website_crm_score.field_website_crm_pageview_display_name
#: model:ir.model.fields,field_description:website_crm_score.field_website_crm_score_display_name
msgid "Display Name"
msgstr ""

#. module: website_crm_score
#: model:ir.model.fields,field_description:website_crm_score.field_crm_team_score_team_domain
#: model:ir.model.fields,field_description:website_crm_score.field_team_user_team_user_domain
#: model:ir.model.fields,field_description:website_crm_score.field_website_crm_score_domain
msgid "Domain"
msgstr ""

#. module: website_crm_score
#: model:ir.model.fields,field_description:website_crm_score.field_website_crm_score_event_based
msgid "Event-based rule"
msgstr ""

#. module: website_crm_score
#: model:ir.model,name:website_crm_score.model_ir_http
msgid "HTTP routing"
msgstr ""

#. module: website_crm_score
#: model:ir.model.fields,field_description:website_crm_score.field_team_user_id
#: model:ir.model.fields,field_description:website_crm_score.field_website_crm_pageview_id
#: model:ir.model.fields,field_description:website_crm_score.field_website_crm_score_id
msgid "ID"
msgstr "ID"

#. module: website_crm_score
#: model:ir.model.fields,field_description:website_crm_score.field_crm_lead_lang_id
msgid "Language"
msgstr ""

#. module: website_crm_score
#: model:ir.model.fields,help:website_crm_score.field_crm_lead_lang_id
msgid "Language from the website when lead has been created"
msgstr ""

#. module: website_crm_score
#: model:ir.model.fields,field_description:website_crm_score.field_team_user___last_update
#: model:ir.model.fields,field_description:website_crm_score.field_website_crm_pageview___last_update
#: model:ir.model.fields,field_description:website_crm_score.field_website_crm_score___last_update
msgid "Last Modified on"
msgstr ""

#. module: website_crm_score
#: model:ir.model.fields,field_description:website_crm_score.field_team_user_write_uid
#: model:ir.model.fields,field_description:website_crm_score.field_website_crm_pageview_write_uid
#: model:ir.model.fields,field_description:website_crm_score.field_website_crm_score_write_uid
msgid "Last Updated by"
msgstr "Última actualización de"

#. module: website_crm_score
#: model:ir.model.fields,field_description:website_crm_score.field_team_user_write_date
#: model:ir.model.fields,field_description:website_crm_score.field_website_crm_pageview_write_date
#: model:ir.model.fields,field_description:website_crm_score.field_website_crm_score_write_date
msgid "Last Updated on"
msgstr "Última actualización en"

#. module: website_crm_score
#: model:ir.model.fields,field_description:website_crm_score.field_website_crm_score_last_run
msgid "Last run"
msgstr ""

#. module: website_crm_score
#: model:ir.model.fields,field_description:website_crm_score.field_website_crm_pageview_lead_id
msgid "Lead"
msgstr ""

#. module: website_crm_score
#: model:ir.model,name:website_crm_score.model_crm_lead
msgid "Lead/Opportunity"
msgstr ""

#. module: website_crm_score
#: model:ir.actions.act_window,name:website_crm_score.score_leads
#: model:ir.ui.view,arch_db:website_crm_score.view_crm_score_form
msgid "Leads"
msgstr ""

#. module: website_crm_score
#: model:ir.ui.menu,name:website_crm_score.team_user
msgid "Leads Assignation"
msgstr ""

#. module: website_crm_score
#: model:ir.model.fields,field_description:website_crm_score.field_crm_team_leads_count
#: model:ir.model.fields,field_description:website_crm_score.field_website_crm_score_leads_count
msgid "Leads Count"
msgstr ""

#. module: website_crm_score
#: model:ir.ui.menu,name:website_crm_score.leads_menu
msgid "Leads Management"
msgstr ""

#. module: website_crm_score
#: model:ir.model.fields,field_description:website_crm_score.field_team_user_maximum_user_leads
msgid "Leads Per Month"
msgstr ""

#. module: website_crm_score
#: model:ir.actions.act_window,help:website_crm_score.team_user_action
msgid ""
"Link users to salesteam, set a per 30 days lead capacity for each of them "
"and set filters to auto assign your leads."
msgstr ""

#. module: website_crm_score
#: model:ir.model.fields,help:website_crm_score.field_crm_lead_score_pageview_ids
msgid "List of (tracked) pages seen by the owner of this lead"
msgstr ""

#. module: website_crm_score
#: model:ir.ui.view,arch_db:website_crm_score.view_crm_team_user_form
msgid "Maximum Leads / 30 days"
msgstr ""

#. module: website_crm_score
#: model:ir.model.fields,field_description:website_crm_score.field_crm_team_min_for_assign
msgid "Minimum score"
msgstr ""

#. module: website_crm_score
#: model:ir.model.fields,help:website_crm_score.field_crm_team_min_for_assign
msgid "Minimum score to be automatically assign (>=)"
msgstr ""

#. module: website_crm_score
#: model:ir.model.fields,field_description:website_crm_score.field_website_crm_score_name
#: model:ir.ui.view,arch_db:website_crm_score.view_crm_score_form
msgid "Name"
msgstr ""

#. module: website_crm_score
#: model:ir.actions.act_window,name:website_crm_score.crm_score_views_action_table
#: model:ir.ui.view,arch_db:website_crm_score.crm_case_graph_view_leads_sales
#: model:ir.ui.view,arch_db:website_crm_score.crm_case_table_view_leads_sales
#: model:ir.ui.view,arch_db:website_crm_score.sales_team_form_view_assign
msgid "Opportunities"
msgstr ""

#. module: website_crm_score
#: model:ir.model.fields,field_description:website_crm_score.field_crm_lead_score_pageview_ids
#: model:ir.ui.view,arch_db:website_crm_score.crm_score_pageview_form
#: model:ir.ui.view,arch_db:website_crm_score.crm_score_pageview_graph
#: model:ir.ui.view,arch_db:website_crm_score.website_crm_pageview_tree
msgid "Page Views"
msgstr ""

#. module: website_crm_score
#: model:ir.ui.view,arch_db:website_crm_score.lead_score_form
msgid "Page views"
msgstr ""

#. module: website_crm_score
#: model:ir.model.fields,field_description:website_crm_score.field_team_user_percentage_leads
msgid "Percentage leads"
msgstr ""

#. module: website_crm_score
#: model:ir.model.fields,field_description:website_crm_score.field_crm_team_ratio
msgid "Ratio"
msgstr ""

#. module: website_crm_score
#: model:ir.model.fields,field_description:website_crm_score.field_website_crm_score_rule_type
msgid "Rule Type"
msgstr ""

#. module: website_crm_score
#: model:ir.model.fields,field_description:website_crm_score.field_team_user_running
msgid "Running"
msgstr ""

#. module: website_crm_score
#: model:ir.model.fields,field_description:website_crm_score.field_team_user_team_id
msgid "SaleTeam"
msgstr ""

#. module: website_crm_score
#: model:ir.model.fields,field_description:website_crm_score.field_team_user_user_id
msgid "Saleman"
msgstr ""

#. module: website_crm_score
#: model:ir.model,name:website_crm_score.model_crm_team
msgid "Sales Channel"
msgstr ""

#. module: website_crm_score
#: model:ir.actions.act_window,name:website_crm_score.team_action
msgid "Sales Channels"
msgstr ""

#. module: website_crm_score
#: model:ir.actions.act_window,name:website_crm_score.team_user_action
#: model:ir.ui.view,arch_db:website_crm_score.view_crm_team_user_form
#: model:ir.ui.view,arch_db:website_crm_score.view_crm_team_user_tree
msgid "Sales Men"
msgstr ""

#. module: website_crm_score
#: model:ir.model.fields,field_description:website_crm_score.field_res_users_team_user_ids
msgid "Sales Records"
msgstr ""

#. module: website_crm_score
#: model:ir.model.fields,field_description:website_crm_score.field_crm_team_team_user_ids
msgid "Salesman"
msgstr ""

#. module: website_crm_score
#: model:ir.model.fields,field_description:website_crm_score.field_crm_lead_score
msgid "Score"
msgstr ""

#. module: website_crm_score
#: model:ir.actions.act_window,name:website_crm_score.score_action
#: model:ir.ui.view,arch_db:website_crm_score.view_crm_score_form
#: model:ir.ui.view,arch_db:website_crm_score.view_crm_score_tree
msgid "Scores"
msgstr ""

#. module: website_crm_score
#: model:ir.ui.view,arch_db:website_crm_score.lead_score_form
#: model:ir.ui.view,arch_db:website_crm_score.score_opp_form_view
#: selection:website.crm.score,rule_type:0
msgid "Scoring"
msgstr ""

#. module: website_crm_score
#: model:ir.ui.menu,name:website_crm_score.pageview_menu
msgid "Scoring Page Views"
msgstr ""

#. module: website_crm_score
#: model:ir.model.fields,field_description:website_crm_score.field_crm_lead_score_ids
#: model:ir.ui.menu,name:website_crm_score.scores_menu
msgid "Scoring Rules"
msgstr ""

#. module: website_crm_score
#: model:ir.model.fields,help:website_crm_score.field_website_crm_score_rule_type
msgid ""
"Scoring will add a score of `value` for this lead.\n"
"Archive will set active = False on the lead (archived)\n"
"Delete will delete definitively the lead\n"
"\n"
"Actions are done in sql and bypass the access rights and orm mechanism (create `score`, write `active`, unlink `crm_lead`)"
msgstr ""

#. module: website_crm_score
#: model:ir.ui.view,arch_db:website_crm_score.crm_score_pageview_filter
msgid "Search PageViews"
msgstr ""

#. module: website_crm_score
#: model:ir.ui.menu,name:website_crm_score.team
msgid "Teams Assignation"
msgstr ""

#. module: website_crm_score
#: model:ir.model.fields,field_description:website_crm_score.field_ir_ui_view_track
#: model:ir.model.fields,field_description:website_crm_score.field_website_page_track
msgid "Track"
msgstr ""

#. module: website_crm_score
#. openerp-web
#: code:addons/website_crm_score/static/src/xml/track_page.xml:9
#, python-format
msgid "Track Page"
msgstr ""

#. module: website_crm_score
#: model:ir.actions.act_window,name:website_crm_score.crm_score_views_action_graph
msgid "Unassigned leads"
msgstr ""

#. module: website_crm_score
#: model:ir.model.fields,field_description:website_crm_score.field_website_crm_pageview_url
msgid "Url"
msgstr ""

#. module: website_crm_score
#: model:ir.actions.act_window,help:website_crm_score.team_action
msgid ""
"Use sales channels to organize your sales departments.\n"
"                    Each channel will work with a separate pipeline."
msgstr ""

#. module: website_crm_score
#: model:ir.model.fields,field_description:website_crm_score.field_website_crm_pageview_user_id
msgid "User"
msgstr ""

#. module: website_crm_score
#: model:ir.model,name:website_crm_score.model_res_users
msgid "Users"
msgstr ""

#. module: website_crm_score
#: model:ir.model.fields,field_description:website_crm_score.field_website_crm_score_value
msgid "Value"
msgstr ""

#. module: website_crm_score
#: model:ir.model.fields,field_description:website_crm_score.field_website_crm_pageview_view_date
msgid "Viewing Date"
msgstr ""

#. module: website_crm_score
#: model:ir.actions.act_window,name:website_crm_score.website_crm_score_pageviews
msgid "Website Pages"
msgstr ""

#. module: website_crm_score
#: model:ir.model.fields,help:website_crm_score.field_website_crm_score_event_based
msgid ""
"When checked, the rule will be re-evaluated every time, even for leads that "
"have already been checked previously. This option incurs a large performance"
" penalty, so it should be checked only for rules that depend on dynamic "
"events"
msgstr ""

#. module: website_crm_score
#: model:ir.ui.view,arch_db:website_crm_score.sales_team_form_view_assign
#: model:ir.ui.view,arch_db:website_crm_score.team_user_kanban
msgid "fa-check"
msgstr ""

#. module: website_crm_score
#: model:ir.ui.view,arch_db:website_crm_score.sales_team_form_view_assign
#: model:ir.ui.view,arch_db:website_crm_score.team_user_kanban
msgid "fa-times"
msgstr ""

#. module: website_crm_score
#: model:ir.model,name:website_crm_score.model_ir_ui_view
msgid "ir.ui.view"
msgstr ""

#. module: website_crm_score
#: model:ir.ui.view,arch_db:website_crm_score.website_crm_score_view_kanban
msgid "leads"
msgstr ""

#. module: website_crm_score
#: model:ir.ui.view,arch_db:website_crm_score.sales_team_form_view_assign
#: model:ir.ui.view,arch_db:website_crm_score.team_user_kanban
msgid "o_assignation_button_active"
msgstr ""

#. module: website_crm_score
#: model:ir.ui.view,arch_db:website_crm_score.sales_team_form_view_assign
#: model:ir.ui.view,arch_db:website_crm_score.team_user_kanban
msgid "o_assignation_button_inactive"
msgstr ""

#. module: website_crm_score
#: model:ir.model,name:website_crm_score.model_team_user
msgid "team.user"
msgstr ""

#. module: website_crm_score
#. openerp-web
#: code:addons/website_crm_score/static/src/xml/track_page.xml:9
#, python-format
msgid "to better score your leads:"
msgstr ""

#. module: website_crm_score
#: model:ir.model,name:website_crm_score.model_website_crm_pageview
msgid "website.crm.pageview"
msgstr ""

#. module: website_crm_score
#: model:ir.model,name:website_crm_score.model_website_crm_score
msgid "website.crm.score"
msgstr ""
