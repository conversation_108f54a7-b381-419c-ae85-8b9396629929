# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_password_policy
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~13.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-09-01 07:28+0000\n"
"PO-Revision-Date: 2020-09-01 07:28+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: auth_password_policy
#: model:ir.model,name:auth_password_policy.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: auth_password_policy
#: model:ir.model.fields,field_description:auth_password_policy.field_res_config_settings__display_name
#: model:ir.model.fields,field_description:auth_password_policy.field_res_users__display_name
msgid "Display Name"
msgstr ""

#. module: auth_password_policy
#: model:ir.model.fields,field_description:auth_password_policy.field_res_config_settings__id
#: model:ir.model.fields,field_description:auth_password_policy.field_res_users__id
msgid "ID"
msgstr ""

#. module: auth_password_policy
#: model:ir.model.fields,field_description:auth_password_policy.field_res_config_settings____last_update
#: model:ir.model.fields,field_description:auth_password_policy.field_res_users____last_update
msgid "Last Modified on"
msgstr ""

#. module: auth_password_policy
#: model:ir.model.fields,field_description:auth_password_policy.field_res_config_settings__minlength
msgid "Minimum Password Length"
msgstr ""

#. module: auth_password_policy
#: model:ir.model.fields,help:auth_password_policy.field_res_config_settings__minlength
msgid ""
"Minimum number of characters passwords must contain, set to 0 to disable."
msgstr ""

#. module: auth_password_policy
#: code:addons/auth_password_policy/models/res_users.py:0
#, python-format
msgid "Passwords must have at least %d characters, got %d."
msgstr ""

#. module: auth_password_policy
#. openerp-web
#: code:addons/auth_password_policy/static/src/js/password_gauge.js:0
#, python-format
msgid ""
"Required: %s.\n"
"\n"
"Hint: increase length, use multiple words and use non-letter characters to increase your password's strength."
msgstr ""

#. module: auth_password_policy
#: model:ir.model,name:auth_password_policy.model_res_users
msgid "Users"
msgstr ""

#. module: auth_password_policy
#. openerp-web
#: code:addons/auth_password_policy/static/src/js/password_gauge.js:0
#, python-format
msgid "at least %d character classes"
msgstr ""

#. module: auth_password_policy
#. openerp-web
#: code:addons/auth_password_policy/static/src/js/password_gauge.js:0
#, python-format
msgid "at least %d characters"
msgstr ""

#. module: auth_password_policy
#. openerp-web
#: code:addons/auth_password_policy/static/src/js/password_gauge.js:0
#, python-format
msgid "at least %d words"
msgstr ""

#. module: auth_password_policy
#. openerp-web
#: code:addons/auth_password_policy/static/src/js/password_gauge.js:0
#, python-format
msgid "no requirements"
msgstr ""
