# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_iot
# 
# Translators:
# <PERSON>, 2020
# <AUTHOR> <EMAIL>, 2020
# <PERSON> <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~13.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-09-01 07:39+0000\n"
"PO-Revision-Date: 2020-09-07 08:22+0000\n"
"Last-Translator: <PERSON> <<PERSON>.<EMAIL>>, 2021\n"
"Language-Team: Chinese (Taiwan) (https://www.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Accept payments with an Ingenico payment terminal"
msgstr ""

#. module: pos_iot
#. openerp-web
#: code:addons/pos_iot/static/src/xml/IoTErrorPopup.xml:0
#, python-format
msgid "Access your"
msgstr ""

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.pos_iot_config_view_form
msgid "Barcode Scanners/Card Readers"
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,help:pos_iot.field_pos_config__iface_print_via_proxy
msgid "Bypass browser printing and prints via the hardware proxy."
msgstr "繞過瀏覽器列印，通過硬體代理進行列印。"

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.pos_iot_config_view_form
msgid "Cashdrawer"
msgstr "收銀機"

#. module: pos_iot
#. openerp-web
#: code:addons/pos_iot/static/src/xml/IoTErrorPopup.xml:0
#, python-format
msgid "Click on Advanced/Show Details/Details/More information"
msgstr ""

#. module: pos_iot
#. openerp-web
#: code:addons/pos_iot/static/src/xml/IoTErrorPopup.xml:0
#, python-format
msgid ""
"Click on Proceed to .../Add Exception/Visit this website/Go on to the "
"webpage"
msgstr ""

#. module: pos_iot
#. openerp-web
#: code:addons/pos_iot/static/src/xml/IoTErrorPopup.xml:0
#, python-format
msgid "Close this window and try again"
msgstr ""

#. module: pos_iot
#: model:ir.model,name:pos_iot.model_res_config_settings
msgid "Config Settings"
msgstr "配置設定"

#. module: pos_iot
#. openerp-web
#: code:addons/pos_iot/static/src/js/payment.js:0
#, python-format
msgid "Configuration of payment terminal failed"
msgstr ""

#. module: pos_iot
#. openerp-web
#: code:addons/pos_iot/static/src/js/iot_longpolling.js:0
#: code:addons/pos_iot/static/src/js/payment.js:0
#, python-format
msgid "Connection to IoT Box failed"
msgstr "連接到IoT Box失敗"

#. module: pos_iot
#. openerp-web
#: code:addons/pos_iot/static/src/js/payment.js:0
#, python-format
msgid "Connection to terminal failed"
msgstr ""

#. module: pos_iot
#. openerp-web
#: code:addons/pos_iot/static/src/js/ScaleScreen.js:0
#, python-format
msgid "Could not connect to IoT scale"
msgstr ""

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.pos_iot_config_view_form
msgid "Customer Display"
msgstr "客戶顯示"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_customer_facing_display
msgid "Customer Facing Display"
msgstr "面向客戶的顯示"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__display_name
#: model:ir.model.fields,field_description:pos_iot.field_pos_payment_method__display_name
#: model:ir.model.fields,field_description:pos_iot.field_res_config_settings__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Documentation"
msgstr "系統使用說明"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_electronic_scale
#: model_terms:ir.ui.view,arch_db:pos_iot.pos_iot_config_view_form
msgid "Electronic Scale"
msgstr "電子秤"

#. module: pos_iot
#: model:ir.model.fields,help:pos_iot.field_pos_config__iface_scan_via_proxy
#: model:ir.model.fields,help:pos_iot.field_pos_config__iface_scanner_ids
msgid ""
"Enable barcode scanning with a remotely connected barcode scanner and card "
"swiping with a Vantiv card reader."
msgstr "使用遠端連接的條碼掃描器啟用條碼掃描，並使用 Vantiv 讀卡器刷卡."

#. module: pos_iot
#: model:ir.model.fields,help:pos_iot.field_pos_config__iface_electronic_scale
msgid "Enables Electronic Scale integration."
msgstr "啟用電子秤集成。"

#. module: pos_iot
#. openerp-web
#: code:addons/pos_iot/static/src/xml/IoTErrorPopup.xml:0
#, python-format
msgid "Firefox only : Click on Confirm Security Exception"
msgstr ""

#. module: pos_iot
#. openerp-web
#: code:addons/pos_iot/static/src/xml/ScaleScreen.xml:0
#, python-format
msgid "Get Weight"
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__id
#: model:ir.model.fields,field_description:pos_iot.field_pos_payment_method__id
#: model:ir.model.fields,field_description:pos_iot.field_res_config_settings__id
msgid "ID"
msgstr "ID"

#. module: pos_iot
#. openerp-web
#: code:addons/pos_iot/static/src/xml/IoTErrorPopup.xml:0
#, python-format
msgid ""
"If you are on a secure server (HTTPS) check if you accepted the certificate:"
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_display_id
msgid "Iface Display"
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_printer_id
msgid "Iface Printer"
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_scale_id
msgid "Iface Scale"
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_scanner_ids
msgid "Iface Scanner"
msgstr ""

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Ingenico (BENELUX)"
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_res_config_settings__ingenico_payment_terminal
msgid "Ingenico Payment Terminal"
msgstr ""

#. module: pos_iot
#. openerp-web
#: code:addons/pos_iot/static/src/xml/IoTErrorPopup.xml:0
#, python-format
msgid "IoT Box Homepage"
msgstr "IoT Box主頁"

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.pos_iot_config_view_form
msgid "IoT Devices"
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iot_device_ids
msgid "Iot Device"
msgstr ""

#. module: pos_iot
#. openerp-web
#: code:addons/pos_iot/static/src/js/ProductScreen.js:0
#, python-format
msgid ""
"It seems that no scale was detected.\n"
"Make sure that the scale is connected and visible in the IoT app."
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config____last_update
#: model:ir.model.fields,field_description:pos_iot.field_pos_payment_method____last_update
#: model:ir.model.fields,field_description:pos_iot.field_res_config_settings____last_update
msgid "Last Modified on"
msgstr "最後修改於"

#. module: pos_iot
#. openerp-web
#: code:addons/pos_iot/static/src/js/ProductScreen.js:0
#, python-format
msgid "No Scale Detected"
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_payment_method__payment_terminal_ids
msgid "Payment Terminal"
msgstr "付款終端"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__payment_terminal_device_ids
#: model:ir.model.fields,field_description:pos_iot.field_pos_payment_method__iot_device_id
msgid "Payment Terminal Device"
msgstr ""

#. module: pos_iot
#. openerp-web
#: code:addons/pos_iot/static/src/js/payment.js:0
#, python-format
msgid "Payment terminal error"
msgstr ""

#. module: pos_iot
#. openerp-web
#: code:addons/pos_iot/static/src/js/payment.js:0
#: code:addons/pos_iot/static/src/xml/IoTErrorPopup.xml:0
#, python-format
msgid "Please check if the IoT Box is still connected."
msgstr "請檢查IoT Box是否仍連接。"

#. module: pos_iot
#. openerp-web
#: code:addons/pos_iot/static/src/js/payment.js:0
#, python-format
msgid "Please check if the terminal is still connected."
msgstr ""

#. module: pos_iot
#: model:ir.model,name:pos_iot.model_pos_config
msgid "Point of Sale Configuration"
msgstr "POS配置"

#. module: pos_iot
#: model:ir.model,name:pos_iot.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr "POS付款條件"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_print_via_proxy
msgid "Print via Proxy"
msgstr "通過代理列印"

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.pos_iot_config_view_form
msgid "Receipt Printer"
msgstr "票據列印機"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_scan_via_proxy
msgid "Scan via Proxy"
msgstr "通過代理掃瞄"

#. module: pos_iot
#: model:ir.model.fields,help:pos_iot.field_pos_config__iface_customer_facing_display
msgid "Show checkout to customers with a remotely-connected screen."
msgstr "通過遠程連接的螢幕向客戶顯示結帳。"

#. module: pos_iot
#. openerp-web
#: code:addons/pos_iot/static/src/js/ScaleScreen.js:0
#, python-format
msgid "The IoT scale is not responding. You should check your connection."
msgstr ""

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid ""
"The transactions are processed by Ingenico. Set your Ingenico device on the "
"related payment method."
msgstr ""

#. module: pos_iot
#. openerp-web
#: code:addons/pos_iot/static/src/js/payment.js:0
#, python-format
msgid "You must select a payment terminal in your POS config."
msgstr ""
