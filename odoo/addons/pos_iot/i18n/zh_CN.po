# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_iot
# 
# Translators:
# <PERSON>, 2020
# <PERSON><PERSON> CHEN <<EMAIL>>, 2020
# <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <AUTHOR> <EMAIL>, 2020
# <PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2020
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~13.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-09-01 07:39+0000\n"
"PO-Revision-Date: 2020-09-07 08:22+0000\n"
"Last-Translator: 山西清水欧度(QQ:54773801) <<EMAIL>>, 2022\n"
"Language-Team: Chinese (China) (https://www.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Accept payments with an Ingenico payment terminal"
msgstr "接受Ingenico支付终端的付款"

#. module: pos_iot
#. openerp-web
#: code:addons/pos_iot/static/src/xml/IoTErrorPopup.xml:0
#, python-format
msgid "Access your"
msgstr "访问您的"

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.pos_iot_config_view_form
msgid "Barcode Scanners/Card Readers"
msgstr "条形码扫描器/读卡器"

#. module: pos_iot
#: model:ir.model.fields,help:pos_iot.field_pos_config__iface_print_via_proxy
msgid "Bypass browser printing and prints via the hardware proxy."
msgstr "绕过浏览器打印，通过硬件代理进行打印。"

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.pos_iot_config_view_form
msgid "Cashdrawer"
msgstr "钱箱"

#. module: pos_iot
#. openerp-web
#: code:addons/pos_iot/static/src/xml/IoTErrorPopup.xml:0
#, python-format
msgid "Click on Advanced/Show Details/Details/More information"
msgstr "点击高级／显示详细资料／细节／更多信息"

#. module: pos_iot
#. openerp-web
#: code:addons/pos_iot/static/src/xml/IoTErrorPopup.xml:0
#, python-format
msgid ""
"Click on Proceed to .../Add Exception/Visit this website/Go on to the "
"webpage"
msgstr "单击继续 ...／添加异常／访问此网站／转到网页"

#. module: pos_iot
#. openerp-web
#: code:addons/pos_iot/static/src/xml/IoTErrorPopup.xml:0
#, python-format
msgid "Close this window and try again"
msgstr "关闭窗口并重试"

#. module: pos_iot
#: model:ir.model,name:pos_iot.model_res_config_settings
msgid "Config Settings"
msgstr "配置设置"

#. module: pos_iot
#. openerp-web
#: code:addons/pos_iot/static/src/js/payment.js:0
#, python-format
msgid "Configuration of payment terminal failed"
msgstr "支付终端的配置失败"

#. module: pos_iot
#. openerp-web
#: code:addons/pos_iot/static/src/js/iot_longpolling.js:0
#: code:addons/pos_iot/static/src/js/payment.js:0
#, python-format
msgid "Connection to IoT Box failed"
msgstr "连接到IoT Box失败"

#. module: pos_iot
#. openerp-web
#: code:addons/pos_iot/static/src/js/payment.js:0
#, python-format
msgid "Connection to terminal failed"
msgstr "与终端的连接失败"

#. module: pos_iot
#. openerp-web
#: code:addons/pos_iot/static/src/js/ScaleScreen.js:0
#, python-format
msgid "Could not connect to IoT scale"
msgstr "无法连接到物联网盒子"

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.pos_iot_config_view_form
msgid "Customer Display"
msgstr "客户显示"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_customer_facing_display
msgid "Customer Facing Display"
msgstr "面向客户的显示"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__display_name
#: model:ir.model.fields,field_description:pos_iot.field_pos_payment_method__display_name
#: model:ir.model.fields,field_description:pos_iot.field_res_config_settings__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Documentation"
msgstr "文档"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_electronic_scale
#: model_terms:ir.ui.view,arch_db:pos_iot.pos_iot_config_view_form
msgid "Electronic Scale"
msgstr "电子秤"

#. module: pos_iot
#: model:ir.model.fields,help:pos_iot.field_pos_config__iface_scan_via_proxy
#: model:ir.model.fields,help:pos_iot.field_pos_config__iface_scanner_ids
msgid ""
"Enable barcode scanning with a remotely connected barcode scanner and card "
"swiping with a Vantiv card reader."
msgstr "使用远程连接的条码扫描器启用条码扫描，并使用 Vantiv 读卡器刷卡."

#. module: pos_iot
#: model:ir.model.fields,help:pos_iot.field_pos_config__iface_electronic_scale
msgid "Enables Electronic Scale integration."
msgstr "启用电子秤集成。"

#. module: pos_iot
#. openerp-web
#: code:addons/pos_iot/static/src/xml/IoTErrorPopup.xml:0
#, python-format
msgid "Firefox only : Click on Confirm Security Exception"
msgstr "仅火狐：在确认安全异常上点击"

#. module: pos_iot
#. openerp-web
#: code:addons/pos_iot/static/src/xml/ScaleScreen.xml:0
#, python-format
msgid "Get Weight"
msgstr "获取重量"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__id
#: model:ir.model.fields,field_description:pos_iot.field_pos_payment_method__id
#: model:ir.model.fields,field_description:pos_iot.field_res_config_settings__id
msgid "ID"
msgstr "ID"

#. module: pos_iot
#. openerp-web
#: code:addons/pos_iot/static/src/xml/IoTErrorPopup.xml:0
#, python-format
msgid ""
"If you are on a secure server (HTTPS) check if you accepted the certificate:"
msgstr "如果您是通过安全服务器（HTTPS），检查是否接受了证书："

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_display_id
msgid "Iface Display"
msgstr "Iface显示"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_printer_id
msgid "Iface Printer"
msgstr "Iface打印机"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_scale_id
msgid "Iface Scale"
msgstr "Iface Scale"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_scanner_ids
msgid "Iface Scanner"
msgstr "Iface扫描仪"

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Ingenico (BENELUX)"
msgstr "Ingenico (BENELUX)"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_res_config_settings__ingenico_payment_terminal
msgid "Ingenico Payment Terminal"
msgstr "Ingenico支付终端的付款"

#. module: pos_iot
#. openerp-web
#: code:addons/pos_iot/static/src/xml/IoTErrorPopup.xml:0
#, python-format
msgid "IoT Box Homepage"
msgstr "IoT Box主页"

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.pos_iot_config_view_form
msgid "IoT Devices"
msgstr "物联网设备"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iot_device_ids
msgid "Iot Device"
msgstr "物联网设备"

#. module: pos_iot
#. openerp-web
#: code:addons/pos_iot/static/src/js/ProductScreen.js:0
#, python-format
msgid ""
"It seems that no scale was detected.\n"
"Make sure that the scale is connected and visible in the IoT app."
msgstr ""
"似乎没有检测到秤。\n"
"确保衡器已连接并在物联网应用程序中可见。"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config____last_update
#: model:ir.model.fields,field_description:pos_iot.field_pos_payment_method____last_update
#: model:ir.model.fields,field_description:pos_iot.field_res_config_settings____last_update
msgid "Last Modified on"
msgstr "最后修改日"

#. module: pos_iot
#. openerp-web
#: code:addons/pos_iot/static/src/js/ProductScreen.js:0
#, python-format
msgid "No Scale Detected"
msgstr "没有检测到称"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_payment_method__payment_terminal_ids
msgid "Payment Terminal"
msgstr "付款终端"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__payment_terminal_device_ids
#: model:ir.model.fields,field_description:pos_iot.field_pos_payment_method__iot_device_id
msgid "Payment Terminal Device"
msgstr "支付终端设备"

#. module: pos_iot
#. openerp-web
#: code:addons/pos_iot/static/src/js/payment.js:0
#, python-format
msgid "Payment terminal error"
msgstr "支付终端错误"

#. module: pos_iot
#. openerp-web
#: code:addons/pos_iot/static/src/js/payment.js:0
#: code:addons/pos_iot/static/src/xml/IoTErrorPopup.xml:0
#, python-format
msgid "Please check if the IoT Box is still connected."
msgstr "请检查IoT Box是否仍连接。"

#. module: pos_iot
#. openerp-web
#: code:addons/pos_iot/static/src/js/payment.js:0
#, python-format
msgid "Please check if the terminal is still connected."
msgstr "请检查打印机是否仍连接。"

#. module: pos_iot
#: model:ir.model,name:pos_iot.model_pos_config
msgid "Point of Sale Configuration"
msgstr "POS配置"

#. module: pos_iot
#: model:ir.model,name:pos_iot.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr "POS付款方式"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_print_via_proxy
msgid "Print via Proxy"
msgstr "通过代理打印"

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.pos_iot_config_view_form
msgid "Receipt Printer"
msgstr "票据打印机"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_scan_via_proxy
msgid "Scan via Proxy"
msgstr "通过代理浏览"

#. module: pos_iot
#: model:ir.model.fields,help:pos_iot.field_pos_config__iface_customer_facing_display
msgid "Show checkout to customers with a remotely-connected screen."
msgstr "通过远程连接的屏幕向客户显示结账。"

#. module: pos_iot
#. openerp-web
#: code:addons/pos_iot/static/src/js/ScaleScreen.js:0
#, python-format
msgid "The IoT scale is not responding. You should check your connection."
msgstr "物联网秤没有反应。你应该检查你的连接。"

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid ""
"The transactions are processed by Ingenico. Set your Ingenico device on the "
"related payment method."
msgstr "这些交易由Ingenico处理。将Ingenico设备设置为相关的付款方式。"

#. module: pos_iot
#. openerp-web
#: code:addons/pos_iot/static/src/js/payment.js:0
#, python-format
msgid "You must select a payment terminal in your POS config."
msgstr "你必须在你的POS机配置中选择一个支付终端。"
