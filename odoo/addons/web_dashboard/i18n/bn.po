# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_dashboard
# 
# Translators:
# <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~13.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-09-01 07:40+0000\n"
"PO-Revision-Date: 2020-09-07 08:25+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2021\n"
"Language-Team: Bengali (https://www.transifex.com/odoo/teams/41243/bn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: web_dashboard
#. openerp-web
#: code:addons/web_dashboard/static/src/js/dashboard_controller.js:0
#, python-format
msgid "%s Analysis"
msgstr ""

#. module: web_dashboard
#: model:ir.model,name:web_dashboard.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr ""

#. module: web_dashboard
#: model:ir.model,name:web_dashboard.model_base
msgid "Base"
msgstr "ভিত্তি"

#. module: web_dashboard
#. openerp-web
#: code:addons/web_dashboard/static/src/xml/dashboard.xml:0
#, python-format
msgid "Column:"
msgstr ""

#. module: web_dashboard
#. openerp-web
#: code:addons/web_dashboard/static/src/js/dashboard_view.js:0
#: model:ir.model.fields.selection,name:web_dashboard.selection__ir_actions_act_window_view__view_mode__dashboard
#: model:ir.model.fields.selection,name:web_dashboard.selection__ir_ui_view__type__dashboard
#, python-format
msgid "Dashboard"
msgstr "ড্যাশবোর্ড"

#. module: web_dashboard
#: model:ir.model.fields,field_description:web_dashboard.field_ir_actions_act_window_view__display_name
#: model:ir.model.fields,field_description:web_dashboard.field_ir_http__display_name
#: model:ir.model.fields,field_description:web_dashboard.field_ir_ui_view__display_name
msgid "Display Name"
msgstr "প্রদর্শন নাম"

#. module: web_dashboard
#. openerp-web
#: code:addons/web_dashboard/static/src/xml/dashboard.xml:0
#, python-format
msgid "Domain Label:"
msgstr ""

#. module: web_dashboard
#. openerp-web
#: code:addons/web_dashboard/static/src/xml/dashboard.xml:0
#, python-format
msgid "Domain:"
msgstr "এলাকা:"

#. module: web_dashboard
#. openerp-web
#: code:addons/web_dashboard/static/src/xml/dashboard.xml:0
#, python-format
msgid "Field:"
msgstr "ক্ষেত্র:"

#. module: web_dashboard
#. openerp-web
#: code:addons/web_dashboard/static/src/xml/dashboard.xml:0
#, python-format
msgid "Group Operator:"
msgstr ""

#. module: web_dashboard
#: model:ir.model,name:web_dashboard.model_ir_http
msgid "HTTP Routing"
msgstr "এইচটিটিপি রাউটিং"

#. module: web_dashboard
#. openerp-web
#: code:addons/web_dashboard/static/src/xml/dashboard.xml:0
#, python-format
msgid "Help:"
msgstr ""

#. module: web_dashboard
#: model:ir.model.fields,field_description:web_dashboard.field_ir_actions_act_window_view__id
#: model:ir.model.fields,field_description:web_dashboard.field_ir_http__id
#: model:ir.model.fields,field_description:web_dashboard.field_ir_ui_view__id
msgid "ID"
msgstr "আইডি "

#. module: web_dashboard
#: model:ir.model.fields,field_description:web_dashboard.field_ir_actions_act_window_view____last_update
#: model:ir.model.fields,field_description:web_dashboard.field_ir_http____last_update
#: model:ir.model.fields,field_description:web_dashboard.field_ir_ui_view____last_update
msgid "Last Modified on"
msgstr "সর্বশেষ সংশোধিত"

#. module: web_dashboard
#. openerp-web
#: code:addons/web_dashboard/static/src/xml/dashboard.xml:0
#, python-format
msgid "Modifiers:"
msgstr "পরিবর্তকসমূহ:"

#. module: web_dashboard
#. openerp-web
#: code:addons/web_dashboard/static/src/xml/dashboard.xml:0
#: code:addons/web_dashboard/static/src/xml/dashboard.xml:0
#, python-format
msgid "Name:"
msgstr ""

#. module: web_dashboard
#. openerp-web
#: code:addons/web_dashboard/static/src/xml/dashboard.xml:0
#, python-format
msgid "String:"
msgstr ""

#. module: web_dashboard
#. openerp-web
#: code:addons/web_dashboard/static/src/xml/dashboard.xml:0
#, python-format
msgid "Value Label:"
msgstr ""

#. module: web_dashboard
#. openerp-web
#: code:addons/web_dashboard/static/src/xml/dashboard.xml:0
#, python-format
msgid "Value:"
msgstr ""

#. module: web_dashboard
#: model:ir.model,name:web_dashboard.model_ir_ui_view
msgid "View"
msgstr "দৃশ্য"

#. module: web_dashboard
#: model:ir.model.fields,field_description:web_dashboard.field_ir_actions_act_window_view__view_mode
#: model:ir.model.fields,field_description:web_dashboard.field_ir_ui_view__type
msgid "View Type"
msgstr "প্রকার দেখুন"

#. module: web_dashboard
#. openerp-web
#: code:addons/web_dashboard/static/src/xml/dashboard.xml:0
#, python-format
msgid "Widget:"
msgstr "উইজেট:"
