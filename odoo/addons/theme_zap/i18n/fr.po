# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* theme_zap
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-12-13 07:04+0000\n"
"PO-Revision-Date: 2021-12-13 07:14+0000\n"
"Last-Translator: Geoffrey_GM<PERSON> <<EMAIL>>, 2021\n"
"Language-Team: French (https://www.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_banner
msgid "<b>Digital Transformation</b>"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_masonry_block
msgid ""
"<em>Maintain a position of constant change and evolution, while always "
"aiming for your success.</em>"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_numbers
msgid "<span class=\"s_number display-4\">4500</span>"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_features
msgid "All our data centers are emission-free."
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_features
msgid "All types of files are allowed."
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_numbers
msgid "Available Languages"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_numbers
msgid "Business Solutions"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_color_blocks_2
msgid "Cloud <b>Solution</b>"
msgstr ""

#. module: theme_zap
#: model:ir.model.fields,field_description:theme_zap.field_theme_utils__display_name
msgid "Display Name"
msgstr "Nom affiché"

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_features
msgid "Eco-Friendly"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_numbers
msgid "Happy Customers"
msgstr ""

#. module: theme_zap
#: model:ir.model.fields,field_description:theme_zap.field_theme_utils__id
msgid "ID"
msgstr "ID"

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_color_blocks_2
msgid ""
"Keep your files in one place. Manage documents online with an easy to use "
"interface. Rely on an highly secure cloud storage."
msgstr ""

#. module: theme_zap
#: model:ir.model.fields,field_description:theme_zap.field_theme_utils____last_update
msgid "Last Modified on"
msgstr "Dernière modification le"

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_color_blocks_2
msgid "Learn more"
msgstr "En savoir plus"

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_numbers
msgid "Ongoing Projects"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_references
msgid "Our <b>References</b>"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_banner
msgid "Our Solutions"
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_features
msgid "Security"
msgstr "Sécurité"

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_banner
msgid ""
"Software Innovation as its best.<br/> Harness the power of disruptive "
"technologies to increase your day-to-day business operations."
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_masonry_block
msgid "Super <b>Easy</b>."
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_masonry_block
msgid "Super <b>Fast</b>."
msgstr ""

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_features
msgid "Support"
msgstr "Assistance"

#. module: theme_zap
#: model:ir.model,name:theme_zap.model_theme_utils
msgid "Theme Utils"
msgstr "Thèmes utiles"

#. module: theme_zap
#: model_terms:theme.ir.ui.view,arch:theme_zap.s_features
msgid "We keep you safe in the cloud."
msgstr ""
