# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* iap
# 
# Translators:
# C<PERSON><PERSON>le <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~13.3\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-04-21 10:46+0000\n"
"PO-Revision-Date: 2020-09-07 08:13+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2021\n"
"Language-Team: Spanish (Mexico) (https://www.transifex.com/odoo/teams/41243/es_MX/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_MX\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_form
msgid "Account Information"
msgstr "Información de la cuenta"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__account_token
msgid "Account Token"
msgstr "Token de la cuenta"

#. module: iap
#. openerp-web
#: code:addons/iap/static/src/js/crash_manager.js:0
#: code:addons/iap/static/src/xml/iap_templates.xml:0
#, python-format
msgid "Buy credits"
msgstr "Comprar créditos"

#. module: iap
#. openerp-web
#: code:addons/iap/static/src/js/crash_manager.js:0
#, python-format
msgid "Cancel"
msgstr "Cancelar"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__company_ids
msgid "Company"
msgstr "Empresa"

#. module: iap
#: model:ir.model,name:iap.model_res_config_settings
msgid "Config Settings"
msgstr "Opciones de configuración"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__create_date
msgid "Created on"
msgstr "Creado el"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__display_name
msgid "Display Name"
msgstr "Nombre en pantalla"

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.res_config_settings_view_form
msgid "Documentation"
msgstr "Documentación"

#. module: iap
#: model:ir.ui.menu,name:iap.iap_root_menu
msgid "IAP"
msgstr "IAP"

#. module: iap
#: model:ir.actions.act_window,name:iap.iap_account_action
#: model:ir.model,name:iap.model_iap_account
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_form
msgid "IAP Account"
msgstr "Cuenta IAP"

#. module: iap
#: model:ir.ui.menu,name:iap.iap_account_menu
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_tree
msgid "IAP Accounts"
msgstr "Cuentas IAP"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__id
msgid "ID"
msgstr "ID"

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.res_config_settings_view_form
msgid "In-App Purchases"
msgstr "Compras dentro de la aplicación"

#. module: iap
#. openerp-web
#: code:addons/iap/static/src/js/crash_manager.js:0
#, python-format
msgid "Insufficient Balance"
msgstr "Saldo insuficiente"

#. module: iap
#. openerp-web
#: code:addons/iap/static/src/xml/iap_templates.xml:0
#, python-format
msgid "Insufficient credit to perform this service."
msgstr "Crédito insuficiente para ejecutar este servicio."

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account____last_update
msgid "Last Modified on"
msgstr "Última modificación el"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.res_config_settings_view_form
msgid "Odoo IAP"
msgstr "Odoo IAP"

#. module: iap
#: model:ir.actions.server,name:iap.open_iap_account
msgid "Open IAP Account"
msgstr "Abrir cuenta IAP"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__service_name
msgid "Service Name"
msgstr "Nombre del servicio"

#. module: iap
#. openerp-web
#: code:addons/iap/static/src/js/crash_manager.js:0
#, python-format
msgid "Start a Trial at Odoo"
msgstr "Comenzar una prueba en Odoo"

#. module: iap
#: code:addons/iap/models/iap.py:0
#, python-format
msgid ""
"The url that this service requested returned an error. Please contact the "
"author of the app. The url it tried to contact was %s"
msgstr ""
"La url que solicitó este servicio reportó un error. Por favor, póngase en "
"contacto con el autor de la aplicación. La url que intentó utilizar es%s "

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.res_config_settings_view_form
msgid "View My Services"
msgstr "Ver mis servicios"

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.res_config_settings_view_form
msgid "View your IAP Services and recharge your credits"
msgstr "Vea sus servicios IAP y recarge créditos"
