# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_delivery_ups
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON> (Quartile) <<EMAIL>>, 2020
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON>, 2020
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~13.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-09-01 07:40+0000\n"
"PO-Revision-Date: 2020-09-07 08:25+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2021\n"
"Language-Team: Japanese (https://www.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_delivery_ups
#: model_terms:ir.ui.view,arch_db:website_delivery_ups.payment_delivery_methods_inherit_website_sale_delivery
msgid "(Bill My Account)"
msgstr ""

#. module: website_delivery_ups
#: model_terms:ir.ui.view,arch_db:website_delivery_ups.payment_delivery_methods_inherit_website_sale_delivery
msgid "<i class=\"fa fa-trash-o\" role=\"img\" aria-label=\"Delete\" title=\"Delete\"/>"
msgstr ""

#. module: website_delivery_ups
#: model_terms:payment.acquirer,pending_msg:website_delivery_ups.payment_acquirer_ups_cod
msgid ""
"<i>Pending</i>, Thanks for choosing COD(Collect on Delivery/Cash on "
"Delivery) option. Delivery boy will collect the payment on delivery."
msgstr ""

#. module: website_delivery_ups
#: model_terms:ir.ui.view,arch_db:website_delivery_ups.payment_delivery_methods_inherit_website_sale_delivery
msgid "<span>(UPS Billing will remain to the customer)</span>"
msgstr ""

#. module: website_delivery_ups
#: model_terms:ir.ui.view,arch_db:website_delivery_ups.ups_bill_my_account_dialog
msgid "Apply"
msgstr "適用"

#. module: website_delivery_ups
#: model:payment.acquirer,name:website_delivery_ups.payment_acquirer_ups_cod
msgid "COD"
msgstr ""

#. module: website_delivery_ups
#: model_terms:ir.ui.view,arch_db:website_delivery_ups.ups_bill_my_account_dialog
msgid "Cancel"
msgstr "取消"

#. module: website_delivery_ups
#: model_terms:ir.ui.view,arch_db:website_delivery_ups.ups_bill_my_account_dialog
msgid "Close"
msgstr "クローズ"

#. module: website_delivery_ups
#: model:ir.model.fields,field_description:website_delivery_ups.field_sale_order__display_name
msgid "Display Name"
msgstr "表示名"

#. module: website_delivery_ups
#: model:ir.model.fields,field_description:website_delivery_ups.field_sale_order__id
msgid "ID"
msgstr "ID"

#. module: website_delivery_ups
#: model:ir.model.fields,field_description:website_delivery_ups.field_sale_order____last_update
msgid "Last Modified on"
msgstr "最終更新日"

#. module: website_delivery_ups
#: model:ir.model,name:website_delivery_ups.model_sale_order
msgid "Sales Order"
msgstr "販売オーダ"

#. module: website_delivery_ups
#: model_terms:ir.ui.view,arch_db:website_delivery_ups.ups_bill_my_account_dialog
msgid "UPS Bill My Account"
msgstr ""

#. module: website_delivery_ups
#: model_terms:ir.ui.view,arch_db:website_delivery_ups.property_ups_carrier_account_inherit_portal_details
msgid "UPS Number Account"
msgstr ""

#. module: website_delivery_ups
#: model_terms:ir.ui.view,arch_db:website_delivery_ups.res_config_settings_view_form_inherit_website_delivery_ups
msgid "UPS Shipping Methods"
msgstr "UPS配送方法"

#. module: website_delivery_ups
#: model_terms:ir.ui.view,arch_db:website_delivery_ups.payment_delivery_methods_inherit_website_sale_delivery
msgid "Using Account"
msgstr ""

#. module: website_delivery_ups
#: model_terms:ir.ui.view,arch_db:website_delivery_ups.ups_bill_my_account_dialog
msgid "Your UPS Account Number"
msgstr ""

#. module: website_delivery_ups
#: model_terms:payment.acquirer,auth_msg:website_delivery_ups.payment_acquirer_ups_cod
msgid "Your payment has been authorized."
msgstr "お支払いが承認されました。"

#. module: website_delivery_ups
#: model_terms:payment.acquirer,cancel_msg:website_delivery_ups.payment_acquirer_ups_cod
msgid "Your payment has been cancelled."
msgstr "お支払いはキャンセルされました。"

#. module: website_delivery_ups
#: model_terms:payment.acquirer,done_msg:website_delivery_ups.payment_acquirer_ups_cod
msgid "Your payment has been successfully processed. Thank you!"
msgstr "お支払いは無事処理されました。ありがとうございました。"
