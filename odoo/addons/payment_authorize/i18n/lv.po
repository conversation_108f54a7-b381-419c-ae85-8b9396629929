# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_authorize
# 
# Translators:
# <PERSON>, 2020
# <PERSON><PERSON><PERSON> <arn<PERSON>@allegro.lv>, 2020
# <AUTHOR> <EMAIL>, 2020
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~13.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-09-01 07:28+0000\n"
"PO-Revision-Date: 2020-09-07 08:15+0000\n"
"Last-Translator: <PERSON>ī<PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Latvian (https://www.transifex.com/odoo/teams/41243/lv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lv\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n != 0 ? 1 : 2);\n"

#. module: payment_authorize
#: code:addons/payment_authorize/controllers/main.py:0
#, python-format
msgid ""
" If you don't have any account, ask your salesperson to grant you a portal "
"access. "
msgstr ""

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__authorize_client_key
msgid "API Client Key"
msgstr ""

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__authorize_login
msgid "API Login Id"
msgstr ""

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__authorize_signature_key
msgid "API Signature Key"
msgstr ""

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__authorize_transaction_key
msgid "API Transaction Key"
msgstr ""

#. module: payment_authorize
#: model:ir.model.fields.selection,name:payment_authorize.selection__payment_acquirer__provider__authorize
msgid "Authorize.Net"
msgstr ""

#. module: payment_authorize
#: code:addons/payment_authorize/models/authorize_request.py:0
#, python-format
msgid ""
"Authorize.net Error:\n"
"Code: %s\n"
"Message: %s"
msgstr ""

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_token__authorize_profile
msgid "Authorize.net Profile ID"
msgstr ""

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment.py:0
#, python-format
msgid ""
"Authorize: received data with missing reference (%s) or trans_id (%s) or "
"fingerprint (%s)"
msgstr ""

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__display_name
#: model:ir.model.fields,field_description:payment_authorize.field_payment_token__display_name
#: model:ir.model.fields,field_description:payment_authorize.field_payment_transaction__display_name
msgid "Display Name"
msgstr "Attēlotais nosaukums"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.acquirer_form_authorize
msgid "Generate Client Key"
msgstr ""

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.acquirer_form_authorize
msgid "How to get paid with Authorize.Net"
msgstr ""

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__id
#: model:ir.model.fields,field_description:payment_authorize.field_payment_token__id
#: model:ir.model.fields,field_description:payment_authorize.field_payment_transaction__id
msgid "ID"
msgstr "ID"

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment.py:0
#, python-format
msgid ""
"Invalid token found: the Authorize profile is missing.Please make sure the "
"token has a valid acquirer reference."
msgstr ""

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer____last_update
#: model:ir.model.fields,field_description:payment_authorize.field_payment_token____last_update
#: model:ir.model.fields,field_description:payment_authorize.field_payment_transaction____last_update
msgid "Last Modified on"
msgstr "Pēdējoreiz modificēts"

#. module: payment_authorize
#: model:ir.model,name:payment_authorize.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Maksājuma saņēmējs"

#. module: payment_authorize
#: model:ir.model,name:payment_authorize.model_payment_token
msgid "Payment Token"
msgstr ""

#. module: payment_authorize
#: model:ir.model,name:payment_authorize.model_payment_transaction
msgid "Payment Transaction"
msgstr "Maksājuma darījums"

#. module: payment_authorize
#: code:addons/payment_authorize/controllers/main.py:0
#, python-format
msgid "Please complete your profile. "
msgstr ""

#. module: payment_authorize
#: code:addons/payment_authorize/controllers/main.py:0
#, python-format
msgid "Please sign in to complete the payment."
msgstr ""

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__provider
#: model:ir.model.fields,field_description:payment_authorize.field_payment_token__provider
msgid "Provider"
msgstr "Sniedzējs"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_token__save_token
msgid "Save Cards"
msgstr ""

#. module: payment_authorize
#. openerp-web
#: code:addons/payment_authorize/static/src/js/payment_form.js:0
#: code:addons/payment_authorize/static/src/js/payment_form.js:0
#, python-format
msgid "Server Error"
msgstr "Servera kļūda"

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment.py:0
#, python-format
msgid "The Customer Profile creation in Authorize.NET failed."
msgstr ""

#. module: payment_authorize
#: code:addons/payment_authorize/controllers/main.py:0
#, python-format
msgid ""
"The transaction cannot be processed because some contact details are missing"
" or invalid: "
msgstr ""

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_token__authorize_profile
msgid ""
"This contains the unique reference for this partner/payment token "
"combination in the Authorize.net backend"
msgstr ""

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_token__save_token
msgid ""
"This option allows customers to save their credit card as a payment token "
"and to reuse it for a later purchase. If you manage subscriptions (recurring"
" invoicing), you need it to automatically charge the customer when you issue"
" an invoice."
msgstr ""

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment.py:0
#, python-format
msgid ""
"Unable to fetch Client Key, make sure the API Login and Transaction Key are "
"correct."
msgstr ""

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment.py:0
#, python-format
msgid "Warning"
msgstr "Brīdinājums"

#. module: payment_authorize
#. openerp-web
#: code:addons/payment_authorize/static/src/js/payment_form.js:0
#, python-format
msgid "We are not able to add your payment method at the moment."
msgstr ""
