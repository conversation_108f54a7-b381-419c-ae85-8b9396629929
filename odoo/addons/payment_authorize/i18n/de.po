# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_authorize
# 
# Translators:
# <PERSON>, 2020
# <PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2021
# Friederi<PERSON> Fasterling-Nesselbosch, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~13.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-09-01 07:28+0000\n"
"PO-Revision-Date: 2020-09-07 08:15+0000\n"
"Last-Translator: Friederike Fasterling-Nesselbosch, 2022\n"
"Language-Team: German (https://www.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment_authorize
#: code:addons/payment_authorize/controllers/main.py:0
#, python-format
msgid ""
" If you don't have any account, ask your salesperson to grant you a portal "
"access. "
msgstr ""
"Wenn Sie noch kein Konto haben, bitten Sie Ihren Verkäufer, Ihnen einen "
"Portalzugang zu gewähren."

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__authorize_client_key
msgid "API Client Key"
msgstr "API Client Key"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__authorize_login
msgid "API Login Id"
msgstr "API Anmeldungs Id"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__authorize_signature_key
msgid "API Signature Key"
msgstr "API Signature Key"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__authorize_transaction_key
msgid "API Transaction Key"
msgstr "API Transaction Key"

#. module: payment_authorize
#: model:ir.model.fields.selection,name:payment_authorize.selection__payment_acquirer__provider__authorize
msgid "Authorize.Net"
msgstr "Authorize.Net"

#. module: payment_authorize
#: code:addons/payment_authorize/models/authorize_request.py:0
#, python-format
msgid ""
"Authorize.net Error:\n"
"Code: %s\n"
"Message: %s"
msgstr ""
"Authorize.net Fehler:\n"
"Code: %s\n"
"Nachricht: %s"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_token__authorize_profile
msgid "Authorize.net Profile ID"
msgstr "Authorize.net Profil-ID"

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment.py:0
#, python-format
msgid ""
"Authorize: received data with missing reference (%s) or trans_id (%s) or "
"fingerprint (%s)"
msgstr ""
"Autorisieren: empfangene Daten mit fehlender Referenz (%s) oder trans_id "
"(%s) oder Fingerabdruck (%s)"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__display_name
#: model:ir.model.fields,field_description:payment_authorize.field_payment_token__display_name
#: model:ir.model.fields,field_description:payment_authorize.field_payment_transaction__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.acquirer_form_authorize
msgid "Generate Client Key"
msgstr "Client-Schlüssel generieren"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.acquirer_form_authorize
msgid "How to get paid with Authorize.Net"
msgstr "Wie Sie mit Authorize.Net bezahlt werden"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__id
#: model:ir.model.fields,field_description:payment_authorize.field_payment_token__id
#: model:ir.model.fields,field_description:payment_authorize.field_payment_transaction__id
msgid "ID"
msgstr "ID"

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment.py:0
#, python-format
msgid ""
"Invalid token found: the Authorize profile is missing.Please make sure the "
"token has a valid acquirer reference."
msgstr ""
"Ungültiges Token gefunden: Das Berechtigungsprofil fehlt. Stellen Sie "
"sicher, dass das Token eine gültige Erwerberreferenz hat."

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer____last_update
#: model:ir.model.fields,field_description:payment_authorize.field_payment_token____last_update
#: model:ir.model.fields,field_description:payment_authorize.field_payment_transaction____last_update
msgid "Last Modified on"
msgstr "Zuletzt geändert am"

#. module: payment_authorize
#: model:ir.model,name:payment_authorize.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Zahlungsanbieter"

#. module: payment_authorize
#: model:ir.model,name:payment_authorize.model_payment_token
msgid "Payment Token"
msgstr "Zahlungs-Token"

#. module: payment_authorize
#: model:ir.model,name:payment_authorize.model_payment_transaction
msgid "Payment Transaction"
msgstr "Zahlungstransaktion"

#. module: payment_authorize
#: code:addons/payment_authorize/controllers/main.py:0
#, python-format
msgid "Please complete your profile. "
msgstr "Bitte vervollständigen Sie Ihr Profil."

#. module: payment_authorize
#: code:addons/payment_authorize/controllers/main.py:0
#, python-format
msgid "Please sign in to complete the payment."
msgstr "Bitte melden Sie sich an, um die Zahlung abzuschließen."

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__provider
#: model:ir.model.fields,field_description:payment_authorize.field_payment_token__provider
msgid "Provider"
msgstr "Anbieter"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_token__save_token
msgid "Save Cards"
msgstr "Karten speichern"

#. module: payment_authorize
#. openerp-web
#: code:addons/payment_authorize/static/src/js/payment_form.js:0
#: code:addons/payment_authorize/static/src/js/payment_form.js:0
#, python-format
msgid "Server Error"
msgstr "Server Fehler"

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment.py:0
#, python-format
msgid "The Customer Profile creation in Authorize.NET failed."
msgstr "Die Erstellung des Kundenprofils bei Autorize.NET ist fehlgeschlagen."

#. module: payment_authorize
#: code:addons/payment_authorize/controllers/main.py:0
#, python-format
msgid ""
"The transaction cannot be processed because some contact details are missing"
" or invalid: "
msgstr ""
"Der Vorgang kann nicht abgeschlossen werden, weil Details fehlen oder "
"fehlerhaft sind:"

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_token__authorize_profile
msgid ""
"This contains the unique reference for this partner/payment token "
"combination in the Authorize.net backend"
msgstr "Dies enthält die eindeutige Referenz"

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_token__save_token
msgid ""
"This option allows customers to save their credit card as a payment token "
"and to reuse it for a later purchase. If you manage subscriptions (recurring"
" invoicing), you need it to automatically charge the customer when you issue"
" an invoice."
msgstr ""
"Diese Option ermöglicht es Kunden, Ihre Kreditkarte als Zahlungs-Token zu "
"speichern und sie für eine spätere Zahlung wiederzuverwenden. Wenn Sie "
"Abonnements verwalten (wiederkehrende Rechnungsstellung), müssen Sie dem "
"Kunden automatisch etwas berechnen, wenn Sie eine Rechnung ausstellen."

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment.py:0
#, python-format
msgid ""
"Unable to fetch Client Key, make sure the API Login and Transaction Key are "
"correct."
msgstr ""
"Client-Schlüssel kann nicht abgeholt werden, stellen Sie sicher, dass der "
"API-Login und der Transaktionsschlüssel korrekt sind."

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment.py:0
#, python-format
msgid "Warning"
msgstr "Warnung"

#. module: payment_authorize
#. openerp-web
#: code:addons/payment_authorize/static/src/js/payment_form.js:0
#, python-format
msgid "We are not able to add your payment method at the moment."
msgstr "Wir können Ihren Zahlweg momentan nicht einpflegen."
