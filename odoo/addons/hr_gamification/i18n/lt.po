# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_gamification
# 
# Translators:
# <PERSON>, 2021
# UAB "Draugi<PERSON><PERSON> sprendimai" <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> Versada <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~13.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-09-01 07:29+0000\n"
"PO-Revision-Date: 2020-09-07 08:12+0000\n"
"Last-Translator: <PERSON><PERSON> Versada <<EMAIL>>, 2021\n"
"Language-Team: Lithuanian (https://www.transifex.com/odoo/teams/41243/lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: hr_gamification
#: model_terms:ir.actions.act_window,help:hr_gamification.goals_menu_groupby_action2
msgid ""
"A goal is defined by a user and a goal type.\n"
"                    Goals can be created automatically by using challenges."
msgstr ""
"Tikslas yra nustatomas pagal vartotoją ir tikslo tipą.\n"
"Tikslai gali būti sukuriami automatiškai, naudojant iššūkius."

#. module: hr_gamification
#: model:ir.model.fields,help:hr_gamification.field_hr_employee__badge_ids
#: model:ir.model.fields,help:hr_gamification.field_hr_employee_base__badge_ids
#: model:ir.model.fields,help:hr_gamification.field_hr_employee_public__badge_ids
msgid ""
"All employee badges, linked to the employee either directly or through the "
"user"
msgstr ""
"Visi darbuotojo ženkleliai, susieti su darbuotoju tiesiogiai arba per "
"vartotoją"

#. module: hr_gamification
#: model_terms:ir.actions.act_window,help:hr_gamification.challenge_list_action2
msgid ""
"Assign a list of goals to chosen users to evaluate them.\n"
"                    The challenge can use a period (weekly, monthly...) for automatic creation of goals.\n"
"                    The goals are created for the specified users or member of the group."
msgstr ""
"Kad galėtumėte juos įvertinti, pasirinktiems vartotojams priskirkite tikslų sąrašą.\n"
"Iššūkis gali naudoti laiko tarpą (savaitė, mėnuo...) automatiniam tikslų kūrimui.\n"
"Tikslai yra sukuriami konkretiems nariams arba grupės nariams."

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_res_users__badge_ids
#: model:ir.ui.menu,name:hr_gamification.gamification_badge_menu_hr
msgid "Badges"
msgstr "Ženkleliai"

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_hr_employee_view_form
msgid ""
"Badges are rewards of good work. Give them to people you believe deserve it."
msgstr ""
"ženkleliai yra apdovanojimas už gerą darbą. Duokite juos žmonėms, kurie jų "
"nusipelno."

#. module: hr_gamification
#: model:ir.model.fields,help:hr_gamification.field_hr_employee__direct_badge_ids
#: model:ir.model.fields,help:hr_gamification.field_hr_employee_base__direct_badge_ids
#: model:ir.model.fields,help:hr_gamification.field_hr_employee_public__direct_badge_ids
msgid "Badges directly linked to the employee"
msgstr "Ženkleliai, tiesiogiai susiję su darbuotoju"

#. module: hr_gamification
#: model:ir.model,name:hr_gamification.model_hr_employee_base
msgid "Basic Employee"
msgstr ""

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.view_badge_wizard_reward
msgid "Cancel"
msgstr "Atšaukti"

#. module: hr_gamification
#: model:ir.actions.act_window,name:hr_gamification.challenge_list_action2
#: model:ir.ui.menu,name:hr_gamification.gamification_challenge_menu_hr
#: model:ir.ui.menu,name:hr_gamification.menu_hr_gamification
msgid "Challenges"
msgstr "Iššūkiai"

#. module: hr_gamification
#: model_terms:ir.actions.act_window,help:hr_gamification.challenge_list_action2
msgid "Create a new challenge"
msgstr "Sukurkite naują iššūkį"

#. module: hr_gamification
#: model_terms:ir.actions.act_window,help:hr_gamification.goals_menu_groupby_action2
msgid "Create a new goal"
msgstr "Sukurkite naują tikslą"

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.view_badge_wizard_reward
msgid "Describe what they did and why it matters (will be public)"
msgstr "Apibūdinkite, ką jie padarė ir kodėl tai svarbu (bus paviešinta)"

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee__direct_badge_ids
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee_base__direct_badge_ids
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee_public__direct_badge_ids
msgid "Direct Badge"
msgstr "Tiesioginis ženklelis"

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_gamification_badge__display_name
#: model:ir.model.fields,field_description:hr_gamification.field_gamification_badge_user__display_name
#: model:ir.model.fields,field_description:hr_gamification.field_gamification_badge_user_wizard__display_name
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee_base__display_name
#: model:ir.model.fields,field_description:hr_gamification.field_res_users__display_name
msgid "Display Name"
msgstr "Rodomas pavadinimas"

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_gamification_badge_user__employee_id
#: model:ir.model.fields,field_description:hr_gamification.field_gamification_badge_user_wizard__employee_id
msgid "Employee"
msgstr "Darbuotojas"

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee__badge_ids
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee_base__badge_ids
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee_public__badge_ids
msgid "Employee Badges"
msgstr "Darbuotojo ženkleliai"

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee__goal_ids
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee_base__goal_ids
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee_public__goal_ids
msgid "Employee HR Goals"
msgstr "Darbuotojo tikslai"

#. module: hr_gamification
#: model:ir.model,name:hr_gamification.model_gamification_badge
msgid "Gamification Badge"
msgstr "Sužaidybinimo ženklelis"

#. module: hr_gamification
#: model:ir.model,name:hr_gamification.model_gamification_badge_user
msgid "Gamification User Badge"
msgstr "Sužaidybinimo vartotojo ženklelis"

#. module: hr_gamification
#: model:ir.model,name:hr_gamification.model_gamification_badge_user_wizard
msgid "Gamification User Badge Wizard"
msgstr "Sužaidybinimo vartotojo ženklelio vedlys"

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_res_users__goal_ids
msgid "Goal"
msgstr "Tikslas"

#. module: hr_gamification
#: model:ir.actions.act_window,name:hr_gamification.goals_menu_groupby_action2
#: model:ir.ui.menu,name:hr_gamification.gamification_goal_menu_hr
msgid "Goals History"
msgstr "Tikslų istorija"

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_hr_employee_view_form
msgid "Grant a Badge"
msgstr "Suteikti ženklelį"

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_hr_employee_view_form
msgid "Grant this employee his first badge"
msgstr "Suteikite šiam darbuotojui jo pirmąjį ženklelį"

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_badge_form_view
msgid "Granted"
msgstr "Suteikta"

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_gamification_badge__granted_employees_count
msgid "Granted Employees Count"
msgstr "Apdovanotų darbuotojų kiekis"

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee__has_badges
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee_base__has_badges
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee_public__has_badges
msgid "Has Badges"
msgstr "Turi ženklelių"

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_gamification_badge__id
#: model:ir.model.fields,field_description:hr_gamification.field_gamification_badge_user__id
#: model:ir.model.fields,field_description:hr_gamification.field_gamification_badge_user_wizard__id
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee_base__id
#: model:ir.model.fields,field_description:hr_gamification.field_res_users__id
msgid "ID"
msgstr "ID"

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_gamification_badge____last_update
#: model:ir.model.fields,field_description:hr_gamification.field_gamification_badge_user____last_update
#: model:ir.model.fields,field_description:hr_gamification.field_gamification_badge_user_wizard____last_update
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee_base____last_update
#: model:ir.model.fields,field_description:hr_gamification.field_res_users____last_update
msgid "Last Modified on"
msgstr "Paskutinį kartą keista"

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_hr_employee_view_form
msgid "Received Badges"
msgstr "Gauti ženkleliai"

#. module: hr_gamification
#: model:ir.model.fields,help:hr_gamification.field_gamification_badge_user_wizard__user_id
msgid "Related user name for the resource to manage its access."
msgstr "Susijusio vartotojo vardas ištekliaus prieigai valdyti"

#. module: hr_gamification
#: model:ir.actions.act_window,name:hr_gamification.action_reward_wizard
#: model_terms:ir.ui.view,arch_db:hr_gamification.view_badge_wizard_reward
msgid "Reward Employee"
msgstr "Apdovanoti darbuotoją"

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.view_badge_wizard_reward
msgid "Reward Employee with"
msgstr "Apdovanoti darbuotoją su"

#. module: hr_gamification
#: code:addons/hr_gamification/models/gamification.py:0
#, python-format
msgid "The selected employee does not correspond to the selected user."
msgstr "Pasirinktas darbuotojas nesutampa su pasirinktu vartotoju."

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_gamification_badge_user_wizard__user_id
msgid "User"
msgstr "Vartotojas"

#. module: hr_gamification
#: model:ir.model,name:hr_gamification.model_res_users
msgid "Users"
msgstr "Vartotojai"

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.view_badge_wizard_reward
msgid "What are you thankful for?"
msgstr "Už ką esi dėkingas?"

#. module: hr_gamification
#: code:addons/hr_gamification/wizard/gamification_badge_user_wizard.py:0
#, python-format
msgid "You can not send a badge to yourself."
msgstr "Negalite siųsti ženklelio sau."

#. module: hr_gamification
#: code:addons/hr_gamification/wizard/gamification_badge_user_wizard.py:0
#, python-format
msgid "You can send badges only to employees linked to a user."
msgstr "Galite siųsti ženklelius tik darbuotojams, susietiems su vartotoju."

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_hr_employee_view_form
msgid "to reward this employee for a good action"
msgstr "apdovanoti šį darbuotoją už gerą darbą"
