# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_stripe
# 
# Translators:
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2020
# <PERSON><PERSON> <man<PERSON><EMAIL>>, 2020
# <PERSON>, 2020
# j<PERSON><PERSON><PERSON>, 2021
# ma<PERSON><PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~13.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-09-01 07:28+0000\n"
"PO-Revision-Date: 2020-09-07 08:15+0000\n"
"Last-Translator: ma<PERSON><PERSON>, 2022\n"
"Language-Team: Catalan (https://www.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment_stripe
#. openerp-web
#: code:addons/payment_stripe/static/src/xml/stripe_templates.xml:0
#, python-format
msgid "&times;"
msgstr "&times;"

#. module: payment_stripe
#: model:ir.model.fields,help:payment_stripe.field_payment_acquirer__stripe_image_url
msgid ""
"A relative or absolute URL pointing to a square image of your brand or "
"product. As defined in your Stripe profile. See: "
"https://stripe.com/docs/checkout"
msgstr ""

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_acquirer__stripe_image_url
msgid "Checkout Image URL"
msgstr ""

#. module: payment_stripe
#. openerp-web
#: code:addons/payment_stripe/static/src/xml/stripe_templates.xml:0
#: code:addons/payment_stripe/static/src/xml/stripe_templates.xml:0
#, python-format
msgid "Close"
msgstr "Tancar"

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_acquirer__display_name
#: model:ir.model.fields,field_description:payment_stripe.field_payment_token__display_name
#: model:ir.model.fields,field_description:payment_stripe.field_payment_transaction__display_name
msgid "Display Name"
msgstr "Nom a mostrar"

#. module: payment_stripe
#. openerp-web
#: code:addons/payment_stripe/static/src/xml/stripe_templates.xml:0
#, python-format
msgid "Error"
msgstr "Error"

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_acquirer__id
#: model:ir.model.fields,field_description:payment_stripe.field_payment_token__id
#: model:ir.model.fields,field_description:payment_stripe.field_payment_transaction__id
msgid "ID"
msgstr "ID"

#. module: payment_stripe
#: model:ir.model.fields,help:payment_stripe.field_payment_acquirer__stripe_webhook_secret
msgid ""
"If you enable webhooks, this secret is used to verify the electronic "
"signature of events sent by Stripe to Odoo. Failing to set this field in "
"Odoo will disable the webhook system for this acquirer entirely."
msgstr ""

#. module: payment_stripe
#. openerp-web
#: code:addons/payment_stripe/static/src/js/stripe.js:0
#, python-format
msgid "Just one more second, We are redirecting you to Stripe..."
msgstr ""

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_acquirer____last_update
#: model:ir.model.fields,field_description:payment_stripe.field_payment_token____last_update
#: model:ir.model.fields,field_description:payment_stripe.field_payment_transaction____last_update
msgid "Last Modified on"
msgstr "Última modificació el "

#. module: payment_stripe
#: model:ir.model,name:payment_stripe.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Pagament de compradors"

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_token__stripe_payment_method
msgid "Payment Method ID"
msgstr ""

#. module: payment_stripe
#: model:ir.model,name:payment_stripe.model_payment_token
msgid "Payment Token"
msgstr "Token de pagament"

#. module: payment_stripe
#: model:ir.model,name:payment_stripe.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transacció de pagament"

#. module: payment_stripe
#. openerp-web
#: code:addons/payment_stripe/static/src/js/stripe.js:0
#, python-format
msgid "Payment error"
msgstr ""

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment.py:0
#, python-format
msgid ""
"Perhaps the problem can be solved by double-checking your credit card "
"details, or contacting your bank?"
msgstr ""

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_acquirer__provider
msgid "Provider"
msgstr "Proveïdor"

#. module: payment_stripe
#: model:ir.model.fields.selection,name:payment_stripe.selection__payment_acquirer__provider__stripe
msgid "Stripe"
msgstr "Stripe"

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_transaction__stripe_payment_intent
msgid "Stripe Payment Intent ID"
msgstr "ID d'intent de pagament de Stripe"

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_transaction__stripe_payment_intent_secret
msgid "Stripe Payment Intent Secret"
msgstr ""

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_acquirer__stripe_publishable_key
msgid "Stripe Publishable Key"
msgstr "Clau publicable de Stripe"

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_acquirer__stripe_secret_key
msgid "Stripe Secret Key"
msgstr "Clau secreta de Stripe"

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_acquirer__stripe_webhook_secret
msgid "Stripe Webhook Secret"
msgstr ""

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment.py:0
#: code:addons/payment_stripe/models/payment.py:0
#, python-format
msgid "Stripe gave us the following info about the problem: '%s'"
msgstr ""

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment.py:0
#, python-format
msgid "Stripe: %(count)s orders found for reference %(reference)s"
msgstr ""

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment.py:0
#, python-format
msgid "Stripe: no order found for reference %s"
msgstr ""

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment.py:0
#, python-format
msgid ""
"Unable to convert Stripe customer for SCA compatibility. Is there at least "
"one card for this customer in the Stripe backend?"
msgstr ""

#. module: payment_stripe
#. openerp-web
#: code:addons/payment_stripe/static/src/js/payment_form.js:0
#, python-format
msgid "Unable to save card"
msgstr ""

#. module: payment_stripe
#. openerp-web
#: code:addons/payment_stripe/static/src/js/payment_form.js:0
#, python-format
msgid "We are not able to add your payment method at the moment. "
msgstr ""

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment.py:0
#, python-format
msgid "We're sorry to report that the transaction has failed."
msgstr ""
