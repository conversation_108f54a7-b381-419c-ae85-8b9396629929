# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_renting
# 
# Translators:
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2020
# <PERSON> <<EMAIL>>, 2020
# <AUTHOR> <EMAIL>, 2020
# <PERSON> <<EMAIL>>, 2020
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-11-27 13:47+0000\n"
"PO-Revision-Date: 2020-09-07 08:23+0000\n"
"Last-Translator: 山西清水欧度(QQ:54773801) <<EMAIL>>, 2022\n"
"Language-Team: Chinese (China) (https://www.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sale_renting
#: code:addons/sale_renting/models/product.py:0
#: code:addons/sale_renting/models/product.py:0
#, python-format
msgid "(Rental)"
msgstr "(租赁)"

#. module: sale_renting
#: model:ir.actions.report,print_report_name:sale_renting.action_report_rental_saleorder
msgid ""
"(object.rental_status not in ('draft', 'sent') and 'Delivery or Return "
"Receipt - %s' %(object.name)) or 'Order - %s' % (object.name)"
msgstr ""
"(object.rental_status not in ('draft', 'sent') and '借出/归还单 - %s' "
"%(object.name)) or '租赁订单 - %s' % (object.name)"

#. module: sale_renting
#: code:addons/sale_renting/wizard/rental_configurator.py:0
#, python-format
msgid "/day"
msgstr "/天"

#. module: sale_renting
#: code:addons/sale_renting/wizard/rental_configurator.py:0
#, python-format
msgid "/hour"
msgstr "/小时"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Default Delay Costs</span>"
msgstr "<span class=\"o_form_label\">默认逾期费用</span>"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.product_product_form_view_rental_gantt
#: model_terms:ir.ui.view,arch_db:sale_renting.product_template_form_view_rental_gantt
msgid "<span class=\"o_stat_text\">in Rental</span>"
msgstr "<span class=\"o_stat_text\">租赁中</span>"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_configurator_view_form
msgid "<span> to </span>"
msgstr "<span> 至 </span>"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
msgid "<strong>Order #: </strong>"
msgstr "<strong>订单 #: </strong>"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
msgid "<strong>Pickup : </strong>"
msgstr "<strong>借出 : </strong>"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
msgid "<strong>Return : </strong>"
msgstr "<strong>归还 : </strong>"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "<strong>Salesperson:</strong>"
msgstr "<strong>销售员:</strong>"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "<strong>Shipping Address:</strong>"
msgstr "<strong>配送地址:</strong>"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
msgid "<strong>Status : </strong>"
msgstr "<strong>状态 : </strong>"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_configurator_view_form
msgid "Add"
msgstr "添加"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.product_template_form_view_rental
msgid "Add a price"
msgstr "添加一个价格"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.res_config_settings_view_form
msgid "Additional costs for late returns"
msgstr "逾期归还的费用"

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_product_product__rent_ok
#: model:ir.model.fields,help:sale_renting.field_product_template__rent_ok
#: model:ir.model.fields,help:sale_renting.field_sale_order_line__is_product_rentable
msgid "Allow renting of this product."
msgstr "允许租赁本产品"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__analytic_account_id
msgid "Analytic Account"
msgstr "分析账户"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.res_config_settings_view_form
msgid "Apply after"
msgstr "此后计逾期"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.res_config_settings_view_form
msgid "Ask customer to sign documents on the spot."
msgstr "要求客户现场签字."

#. module: sale_renting
#. openerp-web
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
#, python-format
msgid "At first, let's create some products to rent."
msgstr "首选，让我们创建一些可租赁产品。"

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_rental_wizard__pricing_id
msgid "Best Pricing Rule based on duration"
msgstr "基于租期最佳定价规则"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_product__rent_ok
#: model:ir.model.fields,field_description:sale_renting.field_product_template__rent_ok
#: model:ir.model.fields,field_description:sale_renting.field_sale_order_line__is_product_rentable
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_product_template_search_view
msgid "Can be Rented"
msgstr "可租赁"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_configurator_view_form
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_wizard_view_form
msgid "Cancel"
msgstr "取消"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_order__rental_status__cancel
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_report__state__cancel
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__rental_status__cancel
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__state__cancel
msgid "Cancelled"
msgstr "已取消"

#. module: sale_renting
#. openerp-web
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
#, python-format
msgid "Click here to create a new quotation."
msgstr "点此创建一个新的报价单"

#. module: sale_renting
#. openerp-web
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
#, python-format
msgid "Click here to register the pickup."
msgstr "点此创建一个新的归还单"

#. module: sale_renting
#. openerp-web
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
#, python-format
msgid "Click here to set up your first rental product."
msgstr "点此设置你的第一个可租赁产品"

#. module: sale_renting
#. openerp-web
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
#, python-format
msgid "Click here to start filling the quotation."
msgstr "点此开始填写你的租赁订单"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__color
msgid "Color"
msgstr "颜色"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_res_company
msgid "Companies"
msgstr "公司"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_pricing__company_id
#: model:ir.model.fields,field_description:sale_renting.field_rental_wizard__company_id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__company_id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__company_id
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Company"
msgstr "公司"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_res_config_settings
msgid "Config Settings"
msgstr "配置设置"

#. module: sale_renting
#: model:ir.ui.menu,name:sale_renting.menu_rental_config
msgid "Configuration"
msgstr "基础配置"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_rental_wizard
msgid "Configure the rental of a product"
msgstr "设置租赁的产品"

#. module: sale_renting
#. openerp-web
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
#, python-format
msgid "Confirm the order when the customer agrees with the terms."
msgstr "客户同意后，确认订单"

#. module: sale_renting
#. openerp-web
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
#, python-format
msgid "Confirm the returned quantities and hit Validate."
msgstr "确认归还的数量并审核"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_order__rental_status__pickup
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
msgid "Confirmed"
msgstr "已确认"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Confirmed Orders"
msgstr "已确认订单"

#. module: sale_renting
#: code:addons/sale_renting/models/rental_pricing.py:0
#, python-format
msgid "Conversion between Months and another duration unit are not supported!"
msgstr "跨月或跨期暂时不支持"

#. module: sale_renting
#: model:ir.actions.act_window,name:sale_renting.action_create_rental_order
msgid "Create Rental Orders"
msgstr "创建租赁订单"

#. module: sale_renting
#: model_terms:ir.actions.act_window,help:sale_renting.rental_order_action
msgid "Create a new quotation, the first step of a new rental!"
msgstr "创建一个新的报价单，租赁的第一步"

#. module: sale_renting
#: model_terms:ir.actions.act_window,help:sale_renting.action_rental_report
msgid "Create a new rental order"
msgstr "创建新的租赁订单"

#. module: sale_renting
#: model_terms:ir.actions.act_window,help:sale_renting.rental_product_template_action
msgid "Create a new rental product!"
msgstr "创建一个可租赁产品"

#. module: sale_renting
#. openerp-web
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
#, python-format
msgid "Create or select a customer here."
msgstr "在此创建或选择一个客户"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__is_rental_order
msgid "Created In App Rental"
msgstr "在租赁模块中创建"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__create_uid
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__create_uid
#: model:ir.model.fields,field_description:sale_renting.field_rental_pricing__create_uid
#: model:ir.model.fields,field_description:sale_renting.field_rental_wizard__create_uid
msgid "Created by"
msgstr "创建人"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__create_date
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__create_date
#: model:ir.model.fields,field_description:sale_renting.field_rental_pricing__create_date
#: model:ir.model.fields,field_description:sale_renting.field_rental_wizard__create_date
msgid "Created on"
msgstr "创建时间"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_pricing__currency_id
#: model:ir.model.fields,field_description:sale_renting.field_rental_wizard__currency_id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__currency_id
msgid "Currency"
msgstr "币种"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__partner_id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__partner_id
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Customer"
msgstr "客户"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__country_id
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Customer Country"
msgstr "客户国家"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__commercial_partner_id
msgid "Customer Entity"
msgstr "客户实体"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__card_name
msgid "Customer Name"
msgstr "客户名称"

#. module: sale_renting
#: model:ir.ui.menu,name:sale_renting.menu_orders_customers
msgid "Customers"
msgstr "客户"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__price
msgid "Daily Amount"
msgstr "每日金额"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__quantity
msgid "Daily Ordered Qty"
msgstr "每日订购数量"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__qty_delivered
msgid "Daily Picked-Up Qty"
msgstr "每日取件数量"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__qty_returned
msgid "Daily Returned Qty"
msgstr "每日归还数量"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__date
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Date"
msgstr "日期"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_configurator_view_form
msgid "Dates"
msgstr "日期"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__rental_pricing__unit__day
#: model:ir.model.fields.selection,name:sale_renting.selection__rental_wizard__duration_unit__day
msgid "Days"
msgstr "天数"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_res_config_settings__extra_product
msgid "Delay Product"
msgstr "逾期归还的产品"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__description
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "Description"
msgstr "说明"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_res_config_settings__module_sale_renting_sign
msgid "Digital Documents"
msgstr "数字签名文档"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_product__display_name
#: model:ir.model.fields,field_description:sale_renting.field_product_template__display_name
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__display_name
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__display_name
#: model:ir.model.fields,field_description:sale_renting.field_rental_pricing__display_name
#: model:ir.model.fields,field_description:sale_renting.field_rental_wizard__display_name
#: model:ir.model.fields,field_description:sale_renting.field_res_company__display_name
#: model:ir.model.fields,field_description:sale_renting.field_res_config_settings__display_name
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__display_name
#: model:ir.model.fields,field_description:sale_renting.field_sale_order_line__display_name
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__display_name
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_report__state__draft
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__state__draft
msgid "Draft Quotation"
msgstr "草稿报价单"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_pricing__duration
#: model:ir.model.fields,field_description:sale_renting.field_rental_wizard__duration
msgid "Duration"
msgstr "时长"

#. module: sale_renting
#. openerp-web
#: code:addons/sale_renting/static/src/js/rental_configurator_widget.js:0
#: code:addons/sale_renting/static/src/js/rental_configurator_widget.js:0
#, python-format
msgid "Edit dates"
msgstr "编辑时间"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_configurator_view_form
msgid "Edited Rental Line"
msgstr "编辑租凭订单行"

#. module: sale_renting
#. openerp-web
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
#, python-format
msgid "Enter the product name."
msgstr "输入产品名称"

#. module: sale_renting
#. openerp-web
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
#, python-format
msgid ""
"Enter the requested dates and check the price.\n"
" Then, click here to add the product."
msgstr ""
"输入要求的时期并检查价格.\n"
" 然后点此增加产品"

#. module: sale_renting
#: code:addons/sale_renting/models/sale.py:0
#, python-format
msgid "Expected"
msgstr "预期"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "Expected Return"
msgstr "预计归还"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_product__extra_daily
#: model:ir.model.fields,field_description:sale_renting.field_product_template__extra_daily
msgid "Extra Day"
msgstr "额外每天"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_product__extra_hourly
#: model:ir.model.fields,field_description:sale_renting.field_product_template__extra_hourly
msgid "Extra Hour"
msgstr "额外每小时"

#. module: sale_renting
#: code:addons/sale_renting/wizard/rental_configurator.py:0
#, python-format
msgid "Extras:"
msgstr "其它"

#. module: sale_renting
#: code:addons/sale_renting/models/product.py:0
#, python-format
msgid "Fallback on Sales price"
msgstr "恢复原销售价"

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_product_product__extra_daily
#: model:ir.model.fields,help:sale_renting.field_product_template__extra_daily
msgid "Fine by day overdue"
msgstr "逾期一天滞纳金"

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_product_product__extra_hourly
#: model:ir.model.fields,help:sale_renting.field_product_template__extra_hourly
msgid "Fine by hour overdue"
msgstr "逾期一小时滞纳金"

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_product_product__display_price
#: model:ir.model.fields,help:sale_renting.field_product_template__display_price
msgid "First rental pricing of the product"
msgstr "首次租赁价格"

#. module: sale_renting
#: code:addons/sale_renting/wizard/rental_configurator.py:0
#, python-format
msgid "Fixed rental price"
msgstr "固定租金价格"

#. module: sale_renting
#. openerp-web
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
#, python-format
msgid "Go to the orders menu."
msgstr "转到订单菜单"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Group By"
msgstr "分组"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__has_late_lines
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__has_late_lines
msgid "Has Late Lines"
msgstr "延迟行"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__has_pickable_lines
msgid "Has Pickable Lines"
msgstr "待借出的租赁明细"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__has_returnable_lines
msgid "Has Returnable Lines"
msgstr "待归还的租赁明细"

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_rental_wizard__pricing_explanation
msgid "Helper text to understand rental price computation."
msgstr "关于如何计算租赁价格的说明信息"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__rental_pricing__unit__hour
#: model:ir.model.fields.selection,name:sale_renting.selection__rental_wizard__duration_unit__hour
msgid "Hours"
msgstr "小时"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_product__id
#: model:ir.model.fields,field_description:sale_renting.field_product_template__id
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__id
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__id
#: model:ir.model.fields,field_description:sale_renting.field_rental_pricing__id
#: model:ir.model.fields,field_description:sale_renting.field_rental_wizard__id
#: model:ir.model.fields,field_description:sale_renting.field_res_company__id
#: model:ir.model.fields,field_description:sale_renting.field_res_config_settings__id
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__id
#: model:ir.model.fields,field_description:sale_renting.field_sale_order_line__id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__id
msgid "ID"
msgstr "ID"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "Invoicing Address:"
msgstr "发票地址"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "Invoicing and Shipping Address:"
msgstr "发票与送货地址"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__is_late
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__late
msgid "Is Late"
msgstr "延迟中"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order_line__is_rental
msgid "Is Rental"
msgstr "租赁中"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order_line__is_late
msgid "Is overdue"
msgstr "逾期"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_product____last_update
#: model:ir.model.fields,field_description:sale_renting.field_product_template____last_update
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard____last_update
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line____last_update
#: model:ir.model.fields,field_description:sale_renting.field_rental_pricing____last_update
#: model:ir.model.fields,field_description:sale_renting.field_rental_wizard____last_update
#: model:ir.model.fields,field_description:sale_renting.field_res_company____last_update
#: model:ir.model.fields,field_description:sale_renting.field_res_config_settings____last_update
#: model:ir.model.fields,field_description:sale_renting.field_sale_order____last_update
#: model:ir.model.fields,field_description:sale_renting.field_sale_order_line____last_update
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report____last_update
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule____last_update
msgid "Last Modified on"
msgstr "最后修改日"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__write_uid
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__write_uid
#: model:ir.model.fields,field_description:sale_renting.field_rental_pricing__write_uid
#: model:ir.model.fields,field_description:sale_renting.field_rental_wizard__write_uid
msgid "Last Updated by"
msgstr "最后更新人"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__write_date
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__write_date
#: model:ir.model.fields,field_description:sale_renting.field_rental_pricing__write_date
#: model:ir.model.fields,field_description:sale_renting.field_rental_wizard__write_date
msgid "Last Updated on"
msgstr "最后更新时间"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_kanban
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
msgid "Late"
msgstr "迟到"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_form
msgid "Late Delivery"
msgstr "配送延误"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
msgid "Late Pickup"
msgstr "借出延误"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_form
msgid "Late Return"
msgstr "归还逾时"

#. module: sale_renting
#. openerp-web
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
#, python-format
msgid "Let's now create an order."
msgstr "让我们创建一张新订单"

#. module: sale_renting
#: model:product.product,name:sale_renting.rental_product_2
#: model:product.template,name:sale_renting.rental_product_2_product_template
msgid "Meeting Room"
msgstr "会议室"

#. module: sale_renting
#: model:ir.model.constraint,message:sale_renting.constraint_res_company_min_extra_hour
msgid "Minimal delay time before applying fines has to be positive."
msgstr "产生逾期费用前的最短延迟时间必须为正。"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_res_company__min_extra_hour
#: model:ir.model.fields,field_description:sale_renting.field_res_config_settings__min_extra_hour
msgid "Minimum delay time before applying fines."
msgstr "产生逾期费用前的最短延迟时间。"

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_rental_pricing__duration
msgid ""
"Minimum duration before this rule is applied. If set to 0, it represents a "
"fixed rental price."
msgstr "这条规则实施前的最短时间。如果设为0，则表示固定租金价格。"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__rental_pricing__unit__month
#: model:ir.model.fields.selection,name:sale_renting.selection__rental_wizard__duration_unit__month
msgid "Months"
msgstr "月份"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "My Orders"
msgstr "我的订单"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__next_action_date
msgid "Next Action"
msgstr "下一行动"

#. module: sale_renting
#: model_terms:ir.actions.act_window,help:sale_renting.action_rental_report
msgid "No data yet!"
msgstr "还没有数据耶！"

#. module: sale_renting
#: code:addons/sale_renting/wizard/rental_configurator.py:0
#, python-format
msgid ""
"No rental price is defined on the product.\n"
"The price used is the sales price."
msgstr ""
"没有为此产品定义租赁价格时，\n"
"将使用销售价格。"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_form
msgid "Ok"
msgstr "确定"

#. module: sale_renting
#: model_terms:ir.actions.act_window,help:sale_renting.rental_order_action
msgid ""
"Once the quotation is confirmed, it becomes a rental order.<br> You will be "
"able to create an invoice and collect the payment."
msgstr "一旦报价被确认，它就变成了租赁订单。<br> 你将能够创建结算单并收取款项。"

#. module: sale_renting
#. openerp-web
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
#, python-format
msgid "Once the rental is done, you can register the return."
msgstr "一旦产品被借出，你可以创建一个归还单。"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__order_id
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
msgid "Order"
msgstr "订单"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__order_id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__order_id
msgid "Order #"
msgstr "订单 #"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__order_date
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Order Date"
msgstr "单据日期"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__order_line_id
msgid "Order Line"
msgstr "订单行"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__name
msgid "Order Reference"
msgstr "订单关联"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__order_line_id
msgid "Order line #"
msgstr "订单行"

#. module: sale_renting
#: model:ir.ui.menu,name:sale_renting.rental_order_menu
#: model:ir.ui.menu,name:sale_renting.rental_orders_all
msgid "Orders"
msgstr "订单"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
msgid "PICKUP"
msgstr "归还"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_res_company__extra_day
#: model:ir.model.fields,field_description:sale_renting.field_res_config_settings__extra_day
msgid "Per Day"
msgstr "每天"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_res_company__extra_hour
#: model:ir.model.fields,field_description:sale_renting.field_res_config_settings__extra_hour
msgid "Per Hour"
msgstr "每小时"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_rental_order_wizard
msgid "Pick-up/Return products"
msgstr "借出/归还产品"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
msgid "Picked-Up"
msgstr "已借出"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__qty_delivered
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_order__rental_status__return
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_form
msgid "Picked-up"
msgstr "已借出"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__rental_status__return
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__report_line_status__pickedup
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "Pickedup"
msgstr "已借出"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_wizard__pickup_date
#: model:ir.model.fields,field_description:sale_renting.field_sale_order_line__pickup_date
#: model:ir.model.fields.selection,name:sale_renting.selection__rental_order_wizard__status__pickup
#: model:ir.ui.menu,name:sale_renting.rental_orders_pickup
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
msgid "Pickup"
msgstr "借出"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__pickup_date
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "Pickup Date"
msgstr "借出时间"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "Pickup Receipt #"
msgstr "借出签收"

#. module: sale_renting
#: model:ir.actions.report,name:sale_renting.action_report_rental_saleorder
msgid "Pickup and Return Receipt"
msgstr "借出/归还签收单"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order_line__reservation_begin
msgid "Pickup date - padding time"
msgstr "借出时间-缓冲时间"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_kanban
msgid "Pickup:"
msgstr "借出："

#. module: sale_renting
#: model:ir.model.constraint,message:sale_renting.constraint_rental_wizard_rental_period_coherence
#: model:ir.model.constraint,message:sale_renting.constraint_sale_order_line_rental_period_coherence
msgid "Please choose a return date that is after the pickup date."
msgstr "请选择一个迟于取货时间的归还时间"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_pricing__price
msgid "Price"
msgstr "价格"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_wizard__pricing_explanation
msgid "Price Computation"
msgstr "价格计算"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_pricing__pricelist_id
#: model:ir.model.fields,field_description:sale_renting.field_rental_wizard__pricelist_id
msgid "Pricelist"
msgstr "价格表"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_wizard__pricing_id
msgid "Pricing"
msgstr "价格"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_rental_pricing
msgid "Pricing rule of rentals"
msgstr "租赁价格规则"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_product_product
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__product_id
#: model:ir.model.fields,field_description:sale_renting.field_rental_wizard__product_id
#: model:ir.model.fields,field_description:sale_renting.field_res_company__extra_product
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__product_id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__product_id
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.res_config_settings_view_form
msgid "Product"
msgstr "产品"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__categ_id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__categ_id
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Product Category"
msgstr "产品类别"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__product_name
msgid "Product Reference"
msgstr "产品参照"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_product_template
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__product_tmpl_id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__product_tmpl_id
msgid "Product Template"
msgstr "产品模板"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_pricing__product_template_id
msgid "Product Templates"
msgstr "产品模板"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_pricing__product_variant_ids
msgid "Product Variants"
msgstr "产品变体"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.res_config_settings_view_form
msgid "Product to charge extra time"
msgstr "逾期费用对应的产品"

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_rental_wizard__product_id
msgid "Product to rent (has to be rentable)"
msgstr "产品出租(必须设置为可出租)"

#. module: sale_renting
#: model:ir.actions.act_window,name:sale_renting.rental_product_template_action
#: model:ir.ui.menu,name:sale_renting.menu_rental_products
msgid "Products"
msgstr "产品"

#. module: sale_renting
#: model:product.product,name:sale_renting.rental_product_1
#: model:product.template,name:sale_renting.rental_product_1_product_template
msgid "Projector"
msgstr "投影仪"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__product_uom_qty
msgid "Qty Ordered"
msgstr "订购数量"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__qty_delivered
msgid "Qty Picked-Up"
msgstr "借出数量"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__qty_returned
msgid "Qty Returned"
msgstr "归还数量"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_wizard__quantity
msgid "Quantity"
msgstr "数量"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_product__qty_in_rent
#: model:ir.model.fields,field_description:sale_renting.field_product_template__qty_in_rent
msgid "Quantity currently in rent"
msgstr "当前在租数量"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_order__rental_status__draft
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__rental_status__draft
msgid "Quotation"
msgstr "报价"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_order__rental_status__sent
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_report__state__sent
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__rental_status__sent
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__state__sent
msgid "Quotation Sent"
msgstr "报价已发送"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Quotations"
msgstr "报价单"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
msgid "RETURN"
msgstr "确认归还"

#. module: sale_renting
#: model:ir.actions.act_window,name:sale_renting.rental_configurator_action
msgid "Rent a product"
msgstr "租赁一个产品"

#. module: sale_renting
#: model:ir.ui.menu,name:sale_renting.rental_menu_root
#: model_terms:ir.ui.view,arch_db:sale_renting.product_template_form_view_rental
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_configurator_view_form
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
#: model_terms:ir.ui.view,arch_db:sale_renting.res_config_settings_view_form
msgid "Rental"
msgstr "出租"

#. module: sale_renting
#: model:ir.actions.act_window,name:sale_renting.action_rental_report
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_graph_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_pivot_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Rental Analysis"
msgstr "租赁分析"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_sale_rental_report
msgid "Rental Analysis Report"
msgstr "租赁分析报告"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_form
msgid "Rental Order"
msgstr "租赁订单"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_wizard__rental_order_line_id
msgid "Rental Order Line"
msgstr "租赁订单行"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__rental_order_wizard_id
msgid "Rental Order Wizard"
msgstr "租赁订单向导"

#. module: sale_renting
#: model:ir.actions.act_window,name:sale_renting.rental_order_action
#: model:ir.actions.act_window,name:sale_renting.rental_order_today_pickup_action
#: model:ir.actions.act_window,name:sale_renting.rental_order_today_return_action
msgid "Rental Orders"
msgstr "租赁订单"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.product_template_form_view_rental
msgid "Rental Pricing"
msgstr "租赁价格"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_product__rental_pricing_ids
#: model:ir.model.fields,field_description:sale_renting.field_product_template__rental_pricing_ids
msgid "Rental Pricings"
msgstr "租赁价格"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_sale_rental_schedule
msgid "Rental Schedule"
msgstr "租赁日程"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__rental_status
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__rental_status
msgid "Rental Status"
msgstr "租赁状态"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__report_line_status
msgid "Rental Status (advanced)"
msgstr "租赁状态(高级)"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order_line__rental_updatable
msgid "Rental Updatable"
msgstr "可更新租赁"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__rental_wizard_line_ids
msgid "Rental Wizard Line"
msgstr "租赁向导行"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_product__display_price
#: model:ir.model.fields,field_description:sale_renting.field_product_template__display_price
msgid "Rental price"
msgstr "租赁价格"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_rental_order_wizard_line
msgid "RentalOrderLine transient representation"
msgstr "当前租赁情况一览 "

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
msgid "Rentals"
msgstr "租赁"

#. module: sale_renting
#: model:ir.ui.menu,name:sale_renting.menu_rental_reporting
msgid "Reporting"
msgstr "报告"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.product_template_form_view_rental
msgid "Reservations"
msgstr "预留"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__qty_reserved
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__rental_status__pickup
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__report_line_status__reserved
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_form
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
msgid "Reserved"
msgstr "已预留"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_wizard__return_date
#: model:ir.model.fields,field_description:sale_renting.field_sale_order_line__return_date
#: model:ir.model.fields.selection,name:sale_renting.selection__rental_order_wizard__status__return
#: model:ir.ui.menu,name:sale_renting.rental_orders_return
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
msgid "Return"
msgstr "退回"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__return_date
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Return Date"
msgstr "归还日期"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_kanban
msgid "Return:"
msgstr "归还"

#. module: sale_renting
#: code:addons/sale_renting/models/sale.py:0
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__qty_returned
#: model:ir.model.fields,field_description:sale_renting.field_sale_order_line__qty_returned
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_order__rental_status__returned
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__rental_status__returned
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__report_line_status__returned
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_form
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
#, python-format
msgid "Returned"
msgstr "已归还"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_report__state__done
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__state__done
msgid "Sales Done"
msgstr "完成销售"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_sale_order
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_report__state__sale
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__state__sale
msgid "Sales Order"
msgstr "销售订单"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_sale_order_line
msgid "Sales Order Line"
msgstr "销售订单行"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__team_id
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Sales Team"
msgstr "销售团队"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__user_id
msgid "Salesman"
msgstr "销售员"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__user_id
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Salesperson"
msgstr "销售员"

#. module: sale_renting
#. openerp-web
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
#, python-format
msgid "Save the product."
msgstr "保存产品"

#. module: sale_renting
#. openerp-web
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
#, python-format
msgid "Save the quotation."
msgstr "保存报价单"

#. module: sale_renting
#: model:ir.ui.menu,name:sale_renting.menu_rental_schedule
msgid "Schedule"
msgstr "计划"

#. module: sale_renting
#: code:addons/sale_renting/models/product.py:0
#: code:addons/sale_renting/models/product.py:0
#: model:ir.actions.act_window,name:sale_renting.action_rental_order_schedule
#, python-format
msgid "Scheduled Rentals"
msgstr "计划出租"

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_rental_pricing__product_variant_ids
msgid ""
"Select Variants of the Product for which this rule applies.\n"
" Leave empty if this rule applies for any variant of this template."
msgstr ""
"选择适用此规则的产品模板的产品模板产品变体。\n"
"如果此规则适用于此模板的任何产品模板产品变体，则保持为空。"

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_rental_pricing__product_template_id
msgid "Select products on which this pricing will be applied."
msgstr "选择适用该定价的产品"

#. module: sale_renting
#. openerp-web
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
#, python-format
msgid "Select your rental product."
msgstr "选择你的租赁产品"

#. module: sale_renting
#: model:ir.actions.act_window,name:sale_renting.action_rental_config_settings
#: model:ir.ui.menu,name:sale_renting.menu_rental_settings
msgid "Settings"
msgstr "设置"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_wizard_view_form
msgid "Some delay cost will be added to the sales order."
msgstr "逾期费用会计算入订单中"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
msgid "State"
msgstr "状态"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__status
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__status
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__state
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__state
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Status"
msgstr "状态"

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_rental_wizard__duration
msgid "The duration unit is based on the unit of the rental pricing rule."
msgstr "持续时间单位基于租金定价规则的单位。"

#. module: sale_renting
#: model:ir.model.constraint,message:sale_renting.constraint_rental_pricing_rental_pricing_duration
msgid "The pricing duration has to be greater or equal to 0."
msgstr "定价持续时间必须大于或等于0"

#. module: sale_renting
#: model:ir.model.constraint,message:sale_renting.constraint_rental_pricing_rental_pricing_price
msgid "The pricing price has to be greater or equal to 0."
msgstr "价格必须大于等于0"

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_res_company__extra_product
msgid "The product is used to add the cost to the sales order"
msgstr "该产品将作为租赁费用添加到销售订单中"

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_sale_order_line__is_late
msgid "The products haven't been returned in time"
msgstr "产品没有及时退回。"

#. module: sale_renting
#. openerp-web
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
#, python-format
msgid "The rental configuration is available here."
msgstr "租赁配置不可用"

#. module: sale_renting
#: model_terms:ir.actions.act_window,help:sale_renting.rental_order_today_pickup_action
#: model_terms:ir.actions.act_window,help:sale_renting.rental_order_today_return_action
msgid "There isn't any scheduled pickup or return."
msgstr "没有计划中的借出或归还。"

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_res_config_settings__extra_day
msgid ""
"This is the default extra cost per day set on newly created products. You "
"can change this value for existing products directly on the product itself."
msgstr "这是新建租赁产品时，默认的每天附加价格。 对已存在产品，你可以直接在产品窗体直接更改金额。"

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_res_config_settings__extra_hour
msgid ""
"This is the default extra cost per hour set on newly created products. You "
"can change this value for existing products directly on the product itself."
msgstr "这是新建租赁产品时，默认的每小时附加价格。 对已存在产品，你可以直接在产品窗体直接更改金额。"

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_rental_wizard__unit_price
msgid ""
"This price is based on the rental price rule that gives the cheapest price "
"for requested duration."
msgstr "此价格是基于租赁价格策略计算，在该要求租期内的最低价格。"

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_res_config_settings__extra_product
msgid "This product will be used to add fines in the Rental Order."
msgstr "此产品用于租赁订单的逾期费用"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.res_config_settings_view_form
msgid ""
"Those values are applied to any new rental product and can be changed on "
"product forms."
msgstr "这些值将应用于新的可租赁产品，可以在该产品窗体中对这些值进行更改。"

#. module: sale_renting
#: model:ir.ui.menu,name:sale_renting.rental_orders_today
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "To Do Today"
msgstr "今日计划"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search_without_searchpanel
msgid "To Pickup"
msgstr "借出计划"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search_without_searchpanel
msgid "To Return"
msgstr "归还计划"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_tree
msgid "Total Tax Included"
msgstr "含税总金额"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_pricing__unit
#: model:ir.model.fields,field_description:sale_renting.field_rental_wizard__duration_unit
msgid "Unit"
msgstr "件"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_wizard__unit_price
msgid "Unit Price"
msgstr "单价"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_wizard__uom_id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__product_uom
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__product_uom
msgid "Unit of Measure"
msgstr "计量单位"

#. module: sale_renting
#: model:product.product,uom_name:sale_renting.rental_product_1
#: model:product.product,uom_name:sale_renting.rental_product_2
#: model:product.template,uom_name:sale_renting.rental_product_1_product_template
#: model:product.template,uom_name:sale_renting.rental_product_2_product_template
msgid "Units"
msgstr "单位"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_wizard_view_form
msgid "Validate"
msgstr "验证"

#. module: sale_renting
#: code:addons/sale_renting/models/sale.py:0
#, python-format
msgid "Validate a pickup"
msgstr "确认借出"

#. module: sale_renting
#: code:addons/sale_renting/models/sale.py:0
#, python-format
msgid "Validate a return"
msgstr "确认归还"

#. module: sale_renting
#. openerp-web
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
#, python-format
msgid "Validate the operation after checking the picked-up quantities."
msgstr "检查借出数量后，请进行确认操作。"

#. module: sale_renting
#. openerp-web
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
#, python-format
msgid ""
"Want to <b>rent products</b>? \n"
" Let's discover Odoo Rental App."
msgstr ""
"想 <b>租赁产品</b> 吗? \n"
" 让我们来研究下Odoo租赁模块。"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__rental_pricing__unit__week
#: model:ir.model.fields.selection,name:sale_renting.selection__rental_wizard__duration_unit__week
msgid "Weeks"
msgstr "周"

#. module: sale_renting
#: model_terms:ir.actions.act_window,help:sale_renting.rental_product_template_action
msgid ""
"You can also create additional products or services to sell by checking *Can"
" be Sold* in the product form (e.g. insurance)."
msgstr "您也可以创建额外的产品或服务用于销售，只需勾选 *可销售* (例如保险)。"

#. module: sale_renting
#: model_terms:ir.actions.act_window,help:sale_renting.rental_order_today_pickup_action
#: model_terms:ir.actions.act_window,help:sale_renting.rental_order_today_return_action
msgid ""
"You can search on a larger period using the filters here above\n"
"              <br>\n"
"              or create a new rental order."
msgstr "您可以使用上面的过滤器搜索更大的周期< br > \\ n或者创建一个新的租赁订单。"

#. module: sale_renting
#: code:addons/sale_renting/wizard/rental_processing.py:0
#, python-format
msgid "You can't return more than what's been picked-up."
msgstr "归还数量不得大于借出数量。"

#. module: sale_renting
#: model:ir.model.constraint,message:sale_renting.constraint_sale_order_line_rental_stock_coherence
msgid "You cannot return more than what has been picked up."
msgstr "您不能归还高于借出数的产品"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.res_config_settings_view_form
msgid "hours"
msgstr "小时"

#. module: sale_renting
#: code:addons/sale_renting/models/sale.py:0
#, python-format
msgid "to"
msgstr "按"
