# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_ponto
# 
# Translators:
# <PERSON>, 2020
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2020
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~13.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-09-01 07:39+0000\n"
"PO-Revision-Date: 2020-09-07 08:18+0000\n"
"Last-Translator: Linkup <<EMAIL>>, 2020\n"
"Language-Team: Korean (https://www.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_ponto
#: code:addons/account_ponto/models/ponto.py:0
#, python-format
msgid ""
"Access to ponto using token is being deprecated. Please follow migration "
"process on "
"https://docs.google.com/document/d/1apzAtCgZl5mfEz5-Z8iETqd6WXGbV0R2KuAvEL87rBI"
msgstr ""
"토큰을 사용하여 ponto에 대한 접근이 더 이상 사용되지 않습니다. "
"https://docs.google.com/document/d/1apzAtCgZl5mfEz5-Z8iETqd6WXGbV0R2KuAvEL87rBI"
" 의 마이그레이션 프로세스를 따르십시오"

#. module: account_ponto
#: code:addons/account_ponto/models/ponto.py:0
#, python-format
msgid "Account"
msgstr "계정"

#. module: account_ponto
#: code:addons/account_ponto/models/ponto.py:0
#, python-format
msgid "An error has occurred: %s"
msgstr ""

#. module: account_ponto
#: code:addons/account_ponto/models/ponto.py:0
#, python-format
msgid "An error occurred during online synchronization"
msgstr "온라인 동기화 중 오류 발생"

#. module: account_ponto
#: model:ir.model.fields,field_description:account_ponto.field_account_online_journal__display_name
#: model:ir.model.fields,field_description:account_ponto.field_account_online_provider__display_name
#: model:ir.model.fields,field_description:account_ponto.field_account_online_wizard__display_name
msgid "Display Name"
msgstr "이름 표시"

#. module: account_ponto
#: code:addons/account_ponto/models/ponto.py:0
#, python-format
msgid "Fetching transactions took too much time."
msgstr "거래 내역을 가져 오는 데 너무 많은 시간이 걸렸습니다."

#. module: account_ponto
#: model:ir.model.fields,field_description:account_ponto.field_account_online_journal__id
#: model:ir.model.fields,field_description:account_ponto.field_account_online_provider__id
#: model:ir.model.fields,field_description:account_ponto.field_account_online_wizard__id
msgid "ID"
msgstr "ID"

#. module: account_ponto
#: model:ir.model,name:account_ponto.model_account_online_journal
msgid "Interface for Online Account Journal"
msgstr "온라인 계정 분개장을 위한 인터페이스"

#. module: account_ponto
#: code:addons/account_ponto/models/ponto.py:0
#, python-format
msgid "Invalid access keys"
msgstr "잘못된 액세스 키"

#. module: account_ponto
#: model:ir.model.fields,field_description:account_ponto.field_account_online_journal____last_update
#: model:ir.model.fields,field_description:account_ponto.field_account_online_provider____last_update
#: model:ir.model.fields,field_description:account_ponto.field_account_online_wizard____last_update
msgid "Last Modified on"
msgstr "최근 수정"

#. module: account_ponto
#: code:addons/account_ponto/models/ponto.py:0
#, python-format
msgid "Link your Ponto account"
msgstr "Ponto 계정 연결"

#. module: account_ponto
#: code:addons/account_ponto/models/ponto.py:0
#: model:ir.model.fields.selection,name:account_ponto.selection__account_online_provider__provider_type__ponto
#, python-format
msgid "Ponto"
msgstr "Ponto"

#. module: account_ponto
#: model:ir.model.fields,field_description:account_ponto.field_account_online_journal__ponto_last_synchronization_identifier
msgid "Ponto Last Synchronization Identifier"
msgstr "Ponto 마지막 동기화 식별자"

#. module: account_ponto
#: model:ir.model.fields,field_description:account_ponto.field_account_online_provider__ponto_token
msgid "Ponto Token"
msgstr "Ponto 토큰"

#. module: account_ponto
#: model:ir.model.fields,field_description:account_ponto.field_account_online_provider__provider_type
msgid "Provider Type"
msgstr "공급업체 유형"

#. module: account_ponto
#: model:ir.model,name:account_ponto.model_account_online_provider
msgid "Provider for online account synchronization"
msgstr "온라인 계정 동기화 공급업체"

#. module: account_ponto
#: code:addons/account_ponto/models/ponto.py:0
#, python-format
msgid "Server not reachable, please try again later"
msgstr "서버에 연결할 수 없습니다. 나중에 다시 시도하십시오"

#. module: account_ponto
#: model:ir.model.fields,help:account_ponto.field_account_online_provider__ponto_token
msgid "Technical field that contains the ponto token"
msgstr "Ponto 토큰을 포함하는 기술 분야"

#. module: account_ponto
#: code:addons/account_ponto/models/ponto.py:0
#, python-format
msgid "The following error happened during the synchronization: %s"
msgstr "동기화 중 다음 오류 발생 : %s"

#. module: account_ponto
#: code:addons/account_ponto/models/ponto.py:0
#, python-format
msgid "Timeout: the server did not reply within 60s"
msgstr "시간 초과 : 서버가 60 초 이내에 응답하지 않았습니다"

#. module: account_ponto
#: model_terms:ir.ui.view,arch_db:account_ponto.ponto_online_provider_account_form_view
msgid "Update Accounts"
msgstr "계정 업데이트"

#. module: account_ponto
#: model:ir.model,name:account_ponto.model_account_online_wizard
msgid "Wizard to link synchronized accounts to journal"
msgstr "동기화된 계정을 분개장에 연결하는 마법사"

#. module: account_ponto
#: model:ir.model.fields,help:account_ponto.field_account_online_journal__ponto_last_synchronization_identifier
msgid "id of ponto synchronization"
msgstr "Ponto 동기화 ID"
