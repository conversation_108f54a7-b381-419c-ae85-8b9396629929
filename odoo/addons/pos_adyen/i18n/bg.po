# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_adyen
# 
# Translators:
# <PERSON>, 2020
# <PERSON> <<EMAIL>>, 2020
# <PERSON> <igor.shelud<PERSON>@gmail.com>, 2020
# <PERSON><PERSON><PERSON><PERSON>, 2020
# <PERSON> <<EMAIL>>, 2020
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-11-27 15:29+0000\n"
"PO-Revision-Date: 2020-09-07 08:16+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2020\n"
"Language-Team: Bulgarian (https://www.transifex.com/odoo/teams/41243/bg/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bg\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: pos_adyen
#: model_terms:ir.ui.view,arch_db:pos_adyen.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o mr8\" title=\"Values set here are "
"company-specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""

#. module: pos_adyen
#: model_terms:ir.ui.view,arch_db:pos_adyen.res_config_settings_view_form
msgid "Account"
msgstr "Сметка"

#. module: pos_adyen
#: model_terms:ir.ui.view,arch_db:pos_adyen.pos_config_view_form
msgid "Add tip through payment terminal (Adyen)"
msgstr ""

#. module: pos_adyen
#: model_terms:ir.ui.view,arch_db:pos_adyen.adyen_store_view_form
msgid "Address"
msgstr "Адрес"

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_payment_method__adyen_api_key
msgid "Adyen API key"
msgstr ""

#. module: pos_adyen
#: model:ir.actions.server,name:pos_adyen.action_pos_adyen_account
#: model:ir.model.fields,field_description:pos_adyen.field_adyen_store__adyen_account_id
#: model:ir.model.fields,field_description:pos_adyen.field_adyen_terminal__adyen_account_id
#: model:ir.model.fields,field_description:pos_adyen.field_pos_payment_method__adyen_account_id
#: model:ir.model.fields,field_description:pos_adyen.field_res_config_settings__adyen_account_id
#: model:ir.ui.menu,name:pos_adyen.menu_pos_adyen_account
msgid "Adyen Account"
msgstr ""

#. module: pos_adyen
#. openerp-web
#: code:addons/pos_adyen/static/src/js/payment_adyen.js:0
#, python-format
msgid "Adyen Error"
msgstr ""

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_payment_method__adyen_latest_diagnosis
msgid "Adyen Latest Diagnosis"
msgstr ""

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_payment_method__adyen_latest_response
msgid "Adyen Latest Response"
msgstr ""

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_payment_method__adyen_payout_id
msgid "Adyen Payout"
msgstr ""

#. module: pos_adyen
#: model_terms:ir.ui.view,arch_db:pos_adyen.adyen_store_view_form
msgid "Adyen Store"
msgstr ""

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_payment_method__adyen_terminal_id
msgid "Adyen Terminal"
msgstr ""

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_payment_method__adyen_terminal_identifier
msgid "Adyen Terminal Identifier"
msgstr ""

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_payment_method__adyen_test_mode
msgid "Adyen Test Mode"
msgstr ""

#. module: pos_adyen
#: model:ir.model,name:pos_adyen.model_adyen_account
msgid "Adyen for Platforms Account"
msgstr ""

#. module: pos_adyen
#: model:ir.model,name:pos_adyen.model_adyen_store
msgid "Adyen for Platforms Store"
msgstr ""

#. module: pos_adyen
#: model:ir.model,name:pos_adyen.model_adyen_terminal
msgid "Adyen for Platforms Terminal"
msgstr ""

#. module: pos_adyen
#. openerp-web
#: code:addons/pos_adyen/static/src/js/payment_adyen.js:0
#, python-format
msgid "An unexpected error occured. Message from Adyen: %s"
msgstr ""

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_config__adyen_ask_customer_for_tip
msgid "Ask Customers For Tip"
msgstr ""

#. module: pos_adyen
#. openerp-web
#: code:addons/pos_adyen/static/src/js/payment_adyen.js:0
#, python-format
msgid "Authentication failed. Please check your Adyen credentials."
msgstr ""

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_adyen_store__city
#: model_terms:ir.ui.view,arch_db:pos_adyen.adyen_store_view_form
msgid "City"
msgstr "Град"

#. module: pos_adyen
#: model:ir.model,name:pos_adyen.model_res_config_settings
msgid "Config Settings"
msgstr "Настройки конфигурация"

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_adyen_store__country_id
#: model_terms:ir.ui.view,arch_db:pos_adyen.adyen_store_view_form
msgid "Country"
msgstr "Държава"

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_adyen_store__country_code
msgid "Country Code"
msgstr "Код на държава"

#. module: pos_adyen
#: model_terms:ir.ui.view,arch_db:pos_adyen.res_config_settings_view_form
msgid "Create an account in 1 minute"
msgstr ""

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_adyen_store__create_uid
#: model:ir.model.fields,field_description:pos_adyen.field_adyen_terminal__create_uid
msgid "Created by"
msgstr "Създадено от"

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_adyen_store__create_date
#: model:ir.model.fields,field_description:pos_adyen.field_adyen_terminal__create_date
msgid "Created on"
msgstr "Създадено на"

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_adyen_account__display_name
#: model:ir.model.fields,field_description:pos_adyen.field_adyen_store__display_name
#: model:ir.model.fields,field_description:pos_adyen.field_adyen_terminal__display_name
#: model:ir.model.fields,field_description:pos_adyen.field_pos_config__display_name
#: model:ir.model.fields,field_description:pos_adyen.field_pos_payment_method__display_name
#: model:ir.model.fields,field_description:pos_adyen.field_res_config_settings__display_name
msgid "Display Name"
msgstr "Име за показване"

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_adyen_store__house_number_or_name
msgid "House Number Or Name"
msgstr ""

#. module: pos_adyen
#: model_terms:ir.ui.view,arch_db:pos_adyen.adyen_store_view_form
msgid "House number or name"
msgstr ""

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_adyen_account__id
#: model:ir.model.fields,field_description:pos_adyen.field_adyen_store__id
#: model:ir.model.fields,field_description:pos_adyen.field_adyen_terminal__id
#: model:ir.model.fields,field_description:pos_adyen.field_pos_config__id
#: model:ir.model.fields,field_description:pos_adyen.field_pos_payment_method__id
#: model:ir.model.fields,field_description:pos_adyen.field_res_config_settings__id
msgid "ID"
msgstr "ID"

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_adyen_account____last_update
#: model:ir.model.fields,field_description:pos_adyen.field_adyen_store____last_update
#: model:ir.model.fields,field_description:pos_adyen.field_adyen_terminal____last_update
#: model:ir.model.fields,field_description:pos_adyen.field_pos_config____last_update
#: model:ir.model.fields,field_description:pos_adyen.field_pos_payment_method____last_update
#: model:ir.model.fields,field_description:pos_adyen.field_res_config_settings____last_update
msgid "Last Modified on"
msgstr "Последно променено на"

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_adyen_store__write_uid
#: model:ir.model.fields,field_description:pos_adyen.field_adyen_terminal__write_uid
msgid "Last Updated by"
msgstr "Последно обновено от"

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_adyen_store__write_date
#: model:ir.model.fields,field_description:pos_adyen.field_adyen_terminal__write_date
msgid "Last Updated on"
msgstr "Последно обновено на"

#. module: pos_adyen
#. openerp-web
#: code:addons/pos_adyen/static/src/js/payment_adyen.js:0
#, python-format
msgid "Message from Adyen: %s"
msgstr ""

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_adyen_store__name
msgid "Name"
msgstr "Name"

#. module: pos_adyen
#: model_terms:ir.ui.view,arch_db:pos_adyen.adyen_account_view_form
msgid "Order a Terminal"
msgstr ""

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_adyen_store__terminal_ids
msgid "Payment Terminals"
msgstr ""

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_adyen_store__phone_number
msgid "Phone Number"
msgstr "Телефонен номер"

#. module: pos_adyen
#: code:addons/pos_adyen/models/pos_config.py:0
#, python-format
msgid ""
"Please configure a tip product for POS %s to support tipping with Adyen."
msgstr ""

#. module: pos_adyen
#: code:addons/pos_adyen/models/adyen_account.py:0
#, python-format
msgid "Please create a store first."
msgstr ""

#. module: pos_adyen
#: model:ir.model,name:pos_adyen.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Конфигурация на център за продажби"

#. module: pos_adyen
#: model:ir.model,name:pos_adyen.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr ""

#. module: pos_adyen
#: model:ir.model.fields,help:pos_adyen.field_pos_config__adyen_ask_customer_for_tip
msgid "Prompt the customer to tip."
msgstr ""

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_adyen_store__store_reference
msgid "Reference"
msgstr "Референция"

#. module: pos_adyen
#: model:ir.model.fields,help:pos_adyen.field_pos_payment_method__adyen_test_mode
msgid "Run transactions in the test environment."
msgstr ""

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_adyen_store__state_id
#: model_terms:ir.ui.view,arch_db:pos_adyen.adyen_store_view_form
msgid "State"
msgstr "Област"

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_adyen_store__state_code
msgid "State Code"
msgstr "Код на областта "

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_adyen_account__store_ids
#: model:ir.model.fields,field_description:pos_adyen.field_adyen_terminal__store_id
msgid "Store"
msgstr ""

#. module: pos_adyen
#: model_terms:ir.ui.view,arch_db:pos_adyen.adyen_account_view_form
msgid "Stores"
msgstr ""

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_adyen_store__street
#: model_terms:ir.ui.view,arch_db:pos_adyen.adyen_store_view_form
msgid "Street"
msgstr "Улица"

#. module: pos_adyen
#: model:ir.model.fields,help:pos_adyen.field_pos_payment_method__adyen_latest_response
msgid ""
"Technical field used to buffer the latest asynchronous notification from "
"Adyen."
msgstr ""

#. module: pos_adyen
#: model:ir.model.fields,help:pos_adyen.field_pos_payment_method__adyen_latest_diagnosis
msgid "Technical field used to determine if the terminal is still connected."
msgstr ""

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_adyen_account__terminal_ids
msgid "Terminal"
msgstr ""

#. module: pos_adyen
#: code:addons/pos_adyen/models/pos_payment_method.py:0
#, python-format
msgid "Terminal %s is already used on payment method %s."
msgstr ""

#. module: pos_adyen
#: code:addons/pos_adyen/models/pos_payment_method.py:0
#, python-format
msgid "Terminal %s is already used in company %s on payment method %s."
msgstr ""

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_adyen_terminal__terminal_uuid
msgid "Terminal ID"
msgstr ""

#. module: pos_adyen
#: model:ir.model.fields,help:pos_adyen.field_adyen_store__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"The ISO country code in two chars. \n"
"Можете да използвате това поле за бързо търсене."

#. module: pos_adyen
#. openerp-web
#: code:addons/pos_adyen/static/src/js/payment_adyen.js:0
#, python-format
msgid ""
"The connection to your payment terminal failed. Please check if it is still "
"connected to the internet."
msgstr ""

#. module: pos_adyen
#: model:ir.model.fields,help:pos_adyen.field_adyen_store__state_code
msgid "The state code."
msgstr "The state code."

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_adyen_store__store_uuid
msgid "UUID"
msgstr "ИН на универсален потребител - UUID"

#. module: pos_adyen
#: model:ir.model.fields,help:pos_adyen.field_pos_payment_method__adyen_api_key
msgid ""
"Used when connecting to Adyen: https://docs.adyen.com/user-management/how-"
"to-get-the-api-key/#description"
msgstr ""

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_adyen_store__zip
#: model_terms:ir.ui.view,arch_db:pos_adyen.adyen_store_view_form
msgid "ZIP"
msgstr "ZIP формат"

#. module: pos_adyen
#: model:ir.model.fields,help:pos_adyen.field_pos_payment_method__adyen_terminal_identifier
msgid "[Terminal model]-[Serial number], for example: P400Plus-123456789"
msgstr ""
